<?php
session_start();
include("classes/connection.php");
include("classes/mywebsite-config-class.php");
$objmyConfig 	= new myConfig;

if($_REQUEST['ajaxaction']=="addInfoAjax")
{
   // referMobile,refererEmail
  // $sqlCheck="select * from customer where referMobile and  refererEmail or " ". "
/*
     $sqlCheck1       =   "SELECT * FROM customer WHERE referMobile='".trim($_REQUEST['refMob'])."' AND (refererEmail='".trim($_REQUEST['firstRefEmail'])."' OR refererPhone='".trim($_REQUEST['firstRefPhone'])."')";
    $sqlCheckQuery1  =   mysql_query($sqlCheck1);
    $countfirst      =   mysql_num_rows($sqlCheckQuery1);
    
    if($countfirst == 1)
    {
        echo "First Refered Friend email or phone no. already exist";
        exit();
    }
    
     $sqlCheck2        =   "SELECT * FROM customer WHERE referMobile='".$_REQUEST['refMob']."' AND (refererEmail='".trim($_REQUEST['secondRefEmail'])."' OR refererPhone='".trim($_REQUEST['secondRefPhone'])."')";
    $sqlCheckQuery2   =   mysql_query($sqlCheck2);
    $countsecond      =   mysql_num_rows($sqlCheckQuery2);
    
    if($countsecond == 1)
    {
        echo "Second  Refered Friend email or phone no. already exist";
        exit();
    }
    
    $sqlCheck3       =   "SELECT * FROM customer WHERE referMobile='".trim($_REQUEST['refMob'])."' AND (refererEmail='".trim($_REQUEST['thirdRefEmail'])."' OR refererPhone='".trim($_REQUEST['thirdRefPhone'])."')";
    $sqlCheckQuery3  =   mysql_query($sqlCheck3);
    $countthird      =   mysql_num_rows($sqlCheckQuery3);
    
    if($countthird == 1)
    {
       echo "Third Refered Friend email or phone no. already exist";
        exit();
    }
    
*/
 if(trim($_REQUEST['firstRefName'])==""  && trim($_REQUEST['secondRefName'])=="" && trim($_REQUEST['thirdRefName'])=="" )
 {
    echo "&nbsp;No Action, Please Add any atleast One Dost Details";
   exit();  
     
 }
    
    
    
        if(trim($_REQUEST['firstRefName']))
        {
        $objmyConfig 	-> 	tableName 				=	"campaign_detail";
	$objmyConfig	->	fieldValues['REFERER_NAME']		=	trim($_REQUEST['firstRefName']);
	$objmyConfig	->	fieldValues['REFERER_PHONE']		=	trim($_REQUEST['firstRefPhone']);
	$objmyConfig	->	fieldValues['REFERER_EMAIL']             =	trim($_REQUEST['firstRefEmail']);
	
	$objmyConfig	->	fieldValues['REFERRED_BY_NAME']		=	trim($_REQUEST['myName']);
	$objmyConfig	->	fieldValues['REFERRED_BY_PHONE']	=	trim($_REQUEST['myPhone']);
	$objmyConfig	->	fieldValues['ADD_TIME']                 =	$objmyConfig->getCurrentDateTime();
	$objmyConfig	->	fieldValues['REFERRAL_CODE']		=	"CHAI-BREAK";
        $objmyConfig	->	fieldValues['REFERRAL_STATUS']		=	"Y";
        $result         						= 	$objmyConfig-> insert("notFree");
        $aff=  mysql_affected_rows();
 
	 $res                                                            =	$objmyConfig->query;
        }
        
        if(trim($_REQUEST['secondRefName']))
        {
        $objmyConfig 	-> 	tableName 				=	"campaign_detail";
        $objmyConfig	->	fieldValues['REFERER_NAME']		=	trim($_REQUEST['secondRefName']);
	$objmyConfig	->	fieldValues['REFERER_PHONE']		=	trim($_REQUEST['secondRefPhone']);
	$objmyConfig	->	fieldValues['REFERER_EMAIL']             =	trim($_REQUEST['secondRefEmail']);

	$objmyConfig	->	fieldValues['REFERRED_BY_NAME']		=	trim($_REQUEST['myName']);
	$objmyConfig	->	fieldValues['REFERRED_BY_PHONE']	=	trim($_REQUEST['myPhone']);
	$objmyConfig	->	fieldValues['ADD_TIME']                 =	$objmyConfig->getCurrentDateTime();
	$objmyConfig	->	fieldValues['REFERRAL_STATUS']		=	"Y";
        $result1         						= 	$objmyConfig-> insert("notFree");
        $aff=  mysql_affected_rows();      
// $res                                                            =	$objmyConfig->query;
        }
        
        if(trim($_REQUEST['secondRefName']))
        {
        $objmyConfig 	-> 	tableName 				=	"campaign_detail";
        $objmyConfig	->	fieldValues['REFERER_NAME']		=	trim($_REQUEST['thirdRefName']);
	$objmyConfig	->	fieldValues['REFERER_PHONE']		=	trim($_REQUEST['thirdRefPhone']);
	$objmyConfig	->	fieldValues['REFERER_EMAIL']             =	trim($_REQUEST['thirdRefEmail']);
	
	$objmyConfig	->	fieldValues['REFERRED_BY_NAME']		=	trim($_REQUEST['myName']);
	$objmyConfig	->	fieldValues['REFERRED_BY_PHONE']	=	trim($_REQUEST['myPhone']);
	$objmyConfig	->	fieldValues['ADD_TIME']                 =	$objmyConfig->getCurrentDateTime();
	$objmyConfig	->	fieldValues['REFERRAL_STATUS']		=	"Y";
        $result2         						= 	$objmyConfig-> insert("notFree");
        $aff=  mysql_affected_rows();
        }
       
        if($aff)
        {
            echo "&nbsp;Successfully Added";
            
        }
        else
        {
            echo "&nbsp;Records not Added";
        }
        
    }