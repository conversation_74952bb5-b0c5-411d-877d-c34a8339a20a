# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
android.useAndroidX=true
org.gradle.jvmargs=-Xmx4608m
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
MYAPP_UPLOAD_STORE_FILE=mykeystore.keystore
MYAPP_UPLOAD_KEY_ALIAS=mykeyalias
MYAPP_UPLOAD_STORE_PASSWORD=123in!@#
MYAPP_UPLOAD_KEY_PASSWORD=123in!@#
android.enableR8 = false
