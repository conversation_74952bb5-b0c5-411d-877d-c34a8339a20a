import React, {Component} from 'react';
import {
    TextInput,
    StyleSheet,
} from 'react-native';
export default class CustomTextinput extends Component {
    constructor(props) {
        super(props);
    }
    render() {
        return (
            <TextInput style={[styles.defaultStyle, this.props.style]}>
                {this.props.children}
            </TextInput>
        );
    }
}
const styles = StyleSheet.create({
    // ... add your default style here
    defaultStyle: {
        fontFamily: 'Nunito-Regular'
    },
});