import React, {Component} from 'react';
import {connect} from "react-redux";
import {ImageBackground, StyleSheet, View,TouchableOpacity,} from 'react-native';
import * as CustomerActions from "../store/actions/CustomerActions";
import SourceAcknowledgeButton from "./SourceAcknowledgeButton";
import CustomText from "./CustomText";

class CustomerAcknowledgeScreen extends Component {

    constructor() {
        super();
        this.state = {
            greenColor: '#00cc44',
            whiteColor: '#FFFFFF',
            source: '',

        }
    }


    render() {
        return (
            <View style={styles.container}>


                {/*banner screen starts*/}
                <ImageBackground width='100%' height='100%'
                                 source={require("../assets/img/sourceAcknowledge.jpg")}
                                 style={{width: '100%', height: '100%'}}>
                    <View style={styles.modeContainer}>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='TIMES OF INDIA'
                                                 name='Times of India'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'TIMES OF INDIA')}
                                                 image={require('../assets/img/sourceTimesOfIndia.png')}/>

                        <SourceAcknowledgeButton states={this.state}
                                                 code='ECONOMIC TIMES'
                                                 name='Economic Times'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'ECONOMIC TIMES')}
                                                 image={require('../assets/img/sourceEconomicTimes.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='INSHORTS'
                                                 name='Inshorts'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'INSHORTS')}
                                                 image={require('../assets/img/sourceInshorts.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='YOUTUBE'
                                                 name='Youtube'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'Youtube')}
                                                 image={require('../assets/img/sourceYoutube.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='FACEBOOK'
                                                 name='Facebook'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'FACEBOOK')}
                                                 image={require('../assets/img/sourceFacebook.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='INSTAGRAM'
                                                 name='Instagram'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'INSTAGRAM')}
                                                 image={require('../assets/img/sourceInstagram.png')}/>

                        <SourceAcknowledgeButton states={this.state}
                                                 code='GOOGLE SEARCH'
                                                 name='Google Search'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'GOOGLE SEARCH')}
                                                 image={require('../assets/img/sourceGoogle.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='HOTSTAR'
                                                 name='Hotstar'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'HOTSTAR')}
                                                 image={require('../assets/img/sourceHotstar.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='HOARDING'
                                                 name='Hoarding'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'HOARDING')}
                                                 image={require('../assets/img/sourceHoarding.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='READ SOMEWHERE'
                                                 name='Read Somewhere'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'READ SOMEWHERE')}
                                                 image={require('../assets/img/sourceReadSomewhere.png')}/>

                        <SourceAcknowledgeButton states={this.state}
                                                 code='FRIENDS REFERRED'
                                                 name='Friends Referred'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'FRIENDS REFERRED')}
                                                 image={require('../assets/img/sourceReferred.png')}/>
                        <SourceAcknowledgeButton states={this.state}
                                                 code='WALK-IN TO STORE'
                                                 name='Walk-in to Store'
                                                 onPress={() => this.props.submitSourceAcknowledge(this.props, 'WALK-IN TO STORE')}
                                                 image={require('../assets/img/sourceWalkIn.png')}/>


                    </View>

                    {/*<View style={{flex: 0, justifyContent: 'center', flexDirection: 'row', marginBottom: 30}}>*/}
                        {/*<TouchableOpacity*/}
                            {/*style={{*/}
                                {/*backgroundColor: '#4A8132',*/}
                                {/*borderRadius: 30,*/}
                                {/*width: 500,*/}
                                {/*alignItems: 'center',*/}
                                {/*// marginLeft: 50,*/}
                                {/*// marginRight: 50,*/}

                                {/*marginTop:75,*/}
                            {/*}}*/}
                            {/*onPress={() => {*/}
                                {/*this.props.submitSourceAcknowledge(this.props,'OTHERS')*/}
                            {/*}}>*/}
                            {/*<CustomText style={{*/}
                                {/*textAlign: "center",*/}
                                {/*color: '#FFFFFF',*/}
                                {/*textTransform: 'uppercase',*/}
                                {/*fontSize: 15,*/}
                                {/*fontFamily: "Nunito-Regular",*/}
                                {/*paddingTop: 10,*/}
                                {/*paddingBottom: 10,*/}
                            {/*}}>OTHERS</CustomText>*/}
                        {/*</TouchableOpacity>*/}
                    {/*</View>*/}
                </ImageBackground>

            </View>
            // </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        // flexDirection: "row",
        // marginTop: 240,
        // backgroundColor: '#FFF',
        flex: 1
    },
    modeContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        // alignItems: 'center',
        // borderColor: '#577C39',
        // borderRadius: 10,
        // borderWidth: 4,
        width: 740,
        height: 500,
        marginTop: 625,
        marginLeft: 40,
        // marginRight: 400,
        // paddingTop: 50
    },
    green: {
        color: '#00cc44'
    },
    silver: {
        color: '#F7F7F7'
    },
    mode: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        // backgroundColor: '#F7F7F7',
        borderRadius: 30,
        borderWidth: 2,
        borderColor: '#F7F7F7',
        width: 150,
        height: 50,
        padding: 10,
        marginTop: 40

    },
    headerStyle: {
        flexDirection: "column",
        // justifyContent: "flex-start",
        // alignItems: "center",
        borderColor: '#F7F7F7',
        shadowColor: '#000000',
        height: '100%',
        // elevation: 1,
        // borderWidth: 1,
        // borderTopWidth: 0,
        // borderBottomWidth: 0,
        // shadowOffset: {width: 0, height: 1},
        // shadowOpacity: 0.5,
        shadowRadius: 2,
        backgroundColor: '#FFFFFF',
        borderTopLeftRadius: 35,
        borderTopRightRadius: 35,
    },
    smallImage: {
        height: 25,
        width: 25
    },
    text: {
        fontSize: 15,
        fontFamily: "Nunito-Regular"
    },
    touchToCopy: {
        position: 'absolute',
        bottom: 0,
        backgroundColor: 'transparent',
        width: '50%',
        height: 70
    },
    buttonStyle: {
        flex: 0,
        height: 50,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#5e7e47',
        borderRadius: 4,
        padding: 10,
    }, buttonTitleStyle: {
        textDecorationColor: '#5e7e47',
        textAlign: 'center',
        padding: 10,
        color: '#fff',
        fontSize: 25
    },text:{
        fontSize:17,
        flexWrap:'wrap',
        width:100,
        justifyContent: 'center',
        alignItems: 'center',
        fontWeight:'bold',
        marginTop:100,
        marginLeft:350



    }
});

const mapStateToProps = state => {
    return {
        showLoader: state.customerReducer.showLoader,
        loadingMessage: state.customerReducer.loadingMessage,
        contactNumber: state.customerReducer.contactNumber,
        customerDetails: state.customerReducer.customerDetails,
        contactEntered: state.customerReducer.contactEntered,
        showEmailInput: state.customerReducer.showEmailInput,
        showOtp: state.customerReducer.showOtp,
        otpSentCount: state.customerReducer.otpSentCount,
        pubnub: state.loginReducer.pubnubObject,
        channelName: state.loginReducer.channelName,
        showUpdateUsername: state.customerReducer.showUpdateUsername,
        unitId: state.loginReducer.unitId,
        selectedTerminal: state.loginReducer.selectedTerminal,
        interval: state.paymentReducer.interval,
        timeout: state.paymentReducer.timeout,
        secondsInterval: state.paymentReducer.secondsInterval,
        faceDetectionMode: state.loginReducer.faceDetectionMode,
        faceString: state.faceRecognitionReducer.faceString,
        sessionId: state.faceRecognitionReducer.sessionId
    }
};

const mapDispatchToProps = dispatch => {
    return {
        submitSourceAcknowledge: (props, source) => dispatch(CustomerActions.submitSourceAcknowledge(props, source))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(CustomerAcknowledgeScreen);