import React, {Component} from 'react';
import {connect} from "react-redux";
import {
    ActivityIndicator,
    Image,
    ImageBackground,
    Keyboard,
    StyleSheet,
    Text,
    TextInput,
    ToastAndroid,
    TouchableOpacity,
    View
} from 'react-native';
import * as CustomerActions from "../store/actions/CustomerActions";
import Ionicons from "react-native-vector-icons/Ionicons"
import QRCode from 'react-native-qrcode-svg';
import UtilityService from "../store/services/UtilityService";
import CustomText from "./CustomText";

class CustomerDataScreen extends Component {

    constructor() {
        super();
        this.state = {
            qrString: null,
            name:null,
            email:null
        }
    }

    componentWillMount() {
        this.setState({
            qrString: UtilityService.getTruecallerQrString()
        });
        clearInterval(this.props.interval);
        clearInterval(this.props.secondsInterval);
        clearTimeout(this.props.timeout);
        this.props.resetCustomerData(this.props.interval);
        this.props.screenOpened(this.props);
    }

    componentDidMount() {
        var _this = this;
        this.props.navigation.addListener("didFocus", () => {
            clearInterval(_this.props.interval);
            clearInterval(_this.props.secondsInterval);
            clearTimeout(this.props.timeout);
            this.props.resetCustomerData();
            this.props.screenOpened(_this.props);
        });
    }

    componentWillUnmount() {
        Keyboard.dismiss();
    }

    setContactNumber(number, props) {
        const invalidRegex = /^[012345]$/;
        const validRegex = /^[6789]\d{9}$/;
        if (invalidRegex.test(number)) {
            this.textInputContact.clear();
            ToastAndroid.show("Please enter a valid number!", ToastAndroid.LONG);
        }
        if (validRegex.test(number) && number.length == 10) {
            this.props.setContactNumber(number, props);
        } else if (!validRegex.test(number) && number.length == 10) {
            ToastAndroid.show('Please enter a valid number !', ToastAndroid.LONG);
        }
    }

    checkContactEntered(source) {
        // const nameRegex = /^[A-Z a-z]*$/;
        const validRegex = /^[6789]\d{9}$/;
        if (source == 'name' && this.props.contactNumber && this.props.contactNumber.length == 10 && validRegex.test(this.props.contactNumber)) {
            return true;
        }
        // else  if(source == 'email' && this.props.name && nameRegex.test(this.props.name)) {
        //     return true;
        // }
        // else {
        //     ToastAndroid.show('Please enter contact number!', ToastAndroid.LONG);
        //     return false;
        // }
    }

    setCustomerName(name, props) {
        const nameRegex = /^[A-Z a-z]*$/;
        if (nameRegex.test(name)) {
            this.setState({name:name});
            this.props.setCustomerName(name, props);
        } else {
            ToastAndroid.show('Please enter a valid name!', ToastAndroid.LONG);
            this.textInputName.clear();
        }
    }

    setCustomerEmail(email) {
        this.setState({email:email});
        this.props.setCustomerEmail(email, this.props);
    }

    render() {
        return (
            <ImageBackground width='100%' height='100%' source={require("../assets/img/regularUser.jpg")}
                             style={{width: '100%', height: '100%'}}>
                <View style={styles.container}>
                    {/*{(this.props.faceDetectionMode != true && this.props.faceDetectionMode != "true") ? (*/}
                       {/* <View style={styles.leftContainer}>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}>
                                <View style={styles.truecallerSignInHead}>
                                    <CustomText style={styles.truecallerSignInText}>Login
                                    </CustomText>
                                    <CustomText style={styles.truecallerSignInText}>via</CustomText>
                                    <Image source={require('../assets/img/truecallerlogo.png')}
                                           style={{width: 100, height: 25}}/>
                                </View>
                            </View>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                flex: 1
                            }}>
                                {this.state.qrString != null ? (
                                    <QRCode
                                        value={this.state.qrString}
                                        size={200}
                                        color='black'
                                        backgroundColor='white'/>
                                ):null}
                            </View>
                        </View>
                    ):null}
                    {(this.props.faceDetectionMode != true && this.props.faceDetectionMode != "true") ? (
                        <View style={styles.middleContainer}>
                            <View style={styles.labelOr}><Text style={{color: '#FFF'}}>OR</Text></View>
                        </View>*/}
                    {/*) : null}*/}
                    <View style={styles.rightContainer}>
                        <View style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}>
                            <View style={[styles.truecallerSignInHead, {marginTop: -55}]}>
                                <CustomText style={styles.truecallerSignInText}>Login Via
                                    {/*<Image source={require('../assets/img/truecallerlogoa.png')}*/}
                                    {/*style={{width: 800, height: 400}}/>*/}
                                </CustomText>
                                <CustomText style={styles.truecallerSignInText}>Contact No.</CustomText>
                            </View>
                        </View>
                        {this.props.contactEntered === true ? (
                            <View style={{
                                flex: 0,
                                flexDirection: "row",
                                alignItems: 'flex-end',
                                justifyContent: "center"
                            }}>
                                <CustomText style={{color: "#000"}}>Contact:</CustomText>
                                <CustomText style={{
                                    color: "#5e7e47",
                                    fontSize: 21,
                                    margin: 15,
                                    marginBottom: 0,
                                    marginTop: 0
                                }}>{this.props.contactNumber}</CustomText>
                                {(this.props.customerDetails != null && this.props.customerDetails.contactVerified === true) ? (
                                    <Ionicons name="ios-checkmark-circle" style={{
                                        color: "green",
                                        fontSize: 21,
                                        marginLeft: 5,
                                        marginRight: 5
                                    }}/>
                                ) : null}
                                <TouchableOpacity onPress={() => this.props.editContact()}>
                                    <CustomText
                                        style={{color: "#000", textDecorationLine: "underline"}}>Edit</CustomText>
                                </TouchableOpacity>
                            </View>
                        ) : (
                            <View>
                                <CustomText style={styles.inputLabel}>Contact</CustomText>
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        ref={input => {
                                            this.textInputContact = input
                                        }}
                                        style={styles.inputBox}
                                        autoFocus={true}
                                        placeholder="Enter your contact here"
                                        keyboardType="number-pad"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#ddd'
                                        value={this.props.contactNumber}
                                        onChangeText={(contactNumber) => this.setContactNumber(contactNumber, this.props)}
                                    />
                                </View>
                            </View>
                        )}
                        {this.props.showLoader === true ? (
                            <View style={styles.loaderContainer}>
                                <ActivityIndicator size="large" color='#5e7e47'/>
                                <CustomText style={styles.loaderMessage}>{this.props.loadingMessage}</CustomText>
                            </View>
                        ) : (
                            <View>
                                <CustomText style={styles.inputLabel}>Name</CustomText>
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        ref={input => {
                                            this.textInputName = input
                                        }}
                                        style={styles.inputBox}
                                        placeholder="Enter your name here"
                                        keyboardType="default"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#ddd'
                                        editable={this.props.contactNumber && this.props.contactNumber.length == 10}
                                        onChangeText={(name) => this.setCustomerName(name, this.props)}
                                        value={this.state.name}
                                    />
                                </View>
                                {/*<View>
                                    <CustomText style={styles.inputLabel}>Email</CustomText>
                                    <View style={styles.inputContainer}>
                                        <TextInput
                                            style={styles.inputBox}
                                            placeholder="Enter your email here"
                                            keyboardType="email-address"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#ddd'
                                            autoCompleteType="off"
                                            autoCorrect={false}
                                            autoCapitalize="none"
                                            editable={this.props.customerDetails != null && this.state.name != null && this.state.name != ''}
                                            value={this.state.email}
                                            onChangeText={(email) => this.setCustomerEmail(email)}
                                        />
                                    </View>
                                </View>*/}
                                <CustomText>{this.props.showUpdateUsername}</CustomText>
                                {this.props.showOtp &&
                                <View>
                                    <View>
                                        <CustomText style={styles.inputLabel}>One Time Password</CustomText>
                                        <View style={styles.inputContainer}>
                                            <TextInput
                                                /*ref={input => {
                                                    this.textInputOtp = input
                                                }}*/
                                                style={styles.inputBox}
                                                placeholder="Enter OTP sent on your phone"
                                                keyboardType="number-pad"
                                                underlineColorAndroid='transparent'
                                                placeholderTextColor='#ddd'
                                                editable={this.props.customerDetails && this.state.name != null && this.state.name != ''}
                                                value={this.props.otp}
                                                onChangeText={(otp) => this.props.setOTP(otp, this.props)}
                                            />
                                        </View>

                                    </View>
                                    {this.props.contactEntered === true &&
                                    <TouchableOpacity onPress={() => this.props.resendOTPStrategy(this.props)}>
                                        <CustomText style={{
                                            color: "#000",
                                            textDecorationLine: "underline",
                                            textAlign: 'right'
                                        }}>Resend OTP</CustomText>
                                    </TouchableOpacity>
                                    }
                                </View>
                                }
                                {this.props.showUpdateUsername &&
                                <View>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => this.props.updateUserName(this.props)}>
                                        <CustomText style={styles.buttonTitleStyle}>Submit</CustomText>
                                    </TouchableOpacity>
                                </View>
                                }
                            </View>
                        )}

                    </View>
                </View>
                {(this.props.faceDetectionMode != true && this.props.faceDetectionMode != "true") ? (
                    <View style={{justifyContent: 'center', alignItems: 'center'}}>
                        <Image width={717} height={516} source={require("../assets/img/truecallerLoginInfo.png")} style={{width: 717, height: 516}} />
                    </View>
                ): null}
            </ImageBackground>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flexDirection: "row",
        marginTop: 240
    },
    leftContainer: {
        // width: 400,
        height: 420,
        backgroundColor: '#FFF',
        flex: 30,
        borderWidth: 2,
        borderColor: "#5e7e47",
        marginLeft: 20,
        marginRight: 20,
        borderRadius: 30,
    },
    middleContainer: {
        flex: 0.5,
        backgroundColor: 'lightgrey',
    },
    labelOr: {
        color: '#FFF',
        backgroundColor: 'green',
        fontSize: 25,
        justifyContent: 'center',
        alignItems: 'center',
        width: 40,
        height: 40,
        borderRadius: 50,
        marginTop: 180,
        borderWidth: 5,
        borderColor: '#FFF',
        marginLeft: -18
    },
    rightContainer: {
        flex: 30,
        // width: 400,
        height: 420,
        alignItems: "stretch",
        justifyContent: "flex-start",
        borderWidth: 2,
        padding: 20,
        backgroundColor: '#FFF',
        borderColor: "#5e7e47",
        marginLeft: 20,
        marginRight: 20,
        borderRadius: 30,
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
    },
    truecallerSignInHead: {
        marginTop: -30,
        justifyContent: 'center',
        alignItems: 'center',
        width: 180,
        backgroundColor: '#FFF',
        borderRadius: 10,
    },
    truecallerSignInText: {
        textTransform: 'capitalize',
        fontWeight: 'bold',
        backgroundColor: '#FFF',
        borderRadius: 5,
        // marginBottom: -50,
        justifyContent: 'center',
        alignItems: 'center',
        fontSize: 20,
        color: 'black'
    },
    inputLabel: {
        color: "#5e7e47",
        marginBottom: 5,
        fontWeight: "bold",
        fontSize: 18
    },
    inputContainer: {
        flex: 0,
        borderBottomColor: '#5e7e47',
        borderBottomWidth: 1,
        height: 45,
        marginBottom: 15,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start'
    },
    inputBox: {
        height: 45,
        marginLeft: 10,
        flex: 1,
        color: '#5e7e47',
        fontSize: 20,
        marginBottom: 5,
        fontFamily: 'Nunito-Regular'
    },
    loaderContainer: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'center',
        marginBottom: 20,
    },
    loaderMessage: {
        color: '#5e7e47',
        fontSize: 21,
        textAlign: 'center',
        paddingLeft: 30,
        paddingRight: 30
    },
    buttonStyle: {
        flex: 0,
        height: 50,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#5e7e47',
        borderRadius: 4,
        padding: 10,
    },
    buttonTitleStyle: {
        textDecorationColor: '#5e7e47',
        textAlign: 'center',
        padding: 10,
        color: '#fff',
        fontSize: 18
    },
});

const mapStateToProps = state => {
    return {
        showLoader: state.customerReducer.showLoader,
        loadingMessage: state.customerReducer.loadingMessage,
        contactNumber: state.customerReducer.contactNumber,
        customerDetails: state.customerReducer.customerDetails,
        contactEntered: state.customerReducer.contactEntered,
        showEmailInput: state.customerReducer.showEmailInput,
        showOtp: state.customerReducer.showOtp,
        otpSentCount: state.customerReducer.otpSentCount,
        pubnub: state.loginReducer.pubnubObject,
        channelName: state.loginReducer.channelName,
        showUpdateUsername: state.customerReducer.showUpdateUsername,
        unitId: state.loginReducer.unitId,
        selectedTerminal: state.loginReducer.selectedTerminal,
        interval: state.paymentReducer.interval,
        timeout: state.paymentReducer.timeout,
        secondsInterval: state.paymentReducer.secondsInterval,
        faceDetectionMode: state.loginReducer.faceDetectionMode,
        faceString: state.faceRecognitionReducer.faceString,
        sessionId: state.faceRecognitionReducer.sessionId
    }
};

const mapDispatchToProps = dispatch => {
    return {
        resetCustomerData: () => dispatch(CustomerActions.resetCustomerData(true)),
        setContactNumber: (number, props) => dispatch(CustomerActions.setContactNumber(number, props)),
        setCustomerName: (name, props) => dispatch(CustomerActions.setCustomerName(name, props)),
        setCustomerEmail: (email, props) => dispatch(CustomerActions.setCustomerEmail(email, props)),
        setOTP: (otp, props) => dispatch(CustomerActions.setOTP(otp, props)),
        verifyNumber: (props) => dispatch(CustomerActions.verifyNumber(props)),
        editContact: () => dispatch(CustomerActions.editContact()),
        updateUserName: (props) => dispatch(CustomerActions.updateUserName(props)),
        screenOpened: (props) => dispatch(CustomerActions.screenOpened(props)),
        resendOTPStrategy: (props) => dispatch(CustomerActions.resendOTPStrategy(props))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(CustomerDataScreen);