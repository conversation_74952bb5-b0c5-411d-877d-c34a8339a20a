import React from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import CustomText from "./CustomText";


const app = (props) => {

    return (
        <View style={styles.container}>
                 <TouchableOpacity style={styles.startBtn} onPress={() =>
                    props.onPress()}>
                     <Image source={require("../assets/img/send.png")} style={{resizeMode:'contain',height:250,width:250,marginBottom:-10,marginLeft:-100}}/>
                    <CustomText style={{fontSize: 30, color: "#FFF",fontFamily: 'Nunito-Regular',marginLeft:-100}}>{props.description}</CustomText>
                </TouchableOpacity>
        </View>
    )
}

const styles = StyleSheet.create({

    startBtn: {
        flex: 0,
        // borderWidth:10,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: "center",
        backgroundColor: '#5e7e47',
        borderRadius: 30,
        width: 229,
        height: 63,
        alignSelf: 'center',
        // marginBottom: 5000,
        color: "#FFF",
        // elevation: 2,
    },
    container:{
        marginBottom:279,
        marginRight:400,
        // borderWidth:10,
        flex: 0,
        fontSize: 30,
    }
});

export default app;
