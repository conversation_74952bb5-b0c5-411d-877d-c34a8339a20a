import React, { Component } from 'react';
import { connect } from "react-redux";
import { StyleSheet, View, ImageBackground, Text, TextInput, TouchableOpacity } from 'react-native';
import * as CustomerActions from "../store/actions/CustomerActions";
import * as HomeActions from "../store/actions/HomeActions";
import * as FaceRecognitionActions from "../store/actions/FaceRecognitionActions";
import CustomText from "./CustomText";
import CustomTextinput from "./CustomTextinput";
import UtilityService from "../store/services/UtilityService";
import { withNavigationFocus } from 'react-navigation';
import * as UtilityActions from "../store/actions/UtilityActions";

class HomeScreen extends Component {

    constructor() {
        super();
        this.state = {
            image: null
        };
    }

    interval = null;

    componentWillMount() {
        console.log(this.props);
        // this.setState({image:"https://internal.chaayos.com/kettle-crm/img/"+UtilityService.getImageFromRegion(this.props.autoConfigData.region)});
        this.setState({ image: UtilityService.getHomeScreenUrl().name });
        if (this.props.customerDetails && this.props.customerDetails.newCustomer) {
            this.props.overrideContactVerification(this.props);
        }
    }

    componentDidMount() {
        this.focusListener = this.props.navigation.addListener("didFocus", () => {
            if (this.props.customerDetails && this.props.customerDetails.newCustomer) {
                this.props.overrideContactVerification(this.props);
            }
            this.props.resetCustomerData(this.props.restCustomerDetails);
        });
        this.props.setFaceSync(true);
    }

    componentDidUpdate(prevProps) {
        if (prevProps.isFocused == true && this.props.isFocused == false) {
            this.props.setFaceSync(true);
        }
        if (prevProps.isFocused == false && this.props.isFocused == true) {
            this.props.setFaceSync(true);
        }
    }

    componentWillUnmount() {
        if (this.focusListener != null) {
            this.focusListener.remove();
        }
    }

    render() {
        return (
            <ImageBackground source={{ uri: this.state.image }} style={{ width: '100%', height: '100%' }}>
                <View style={{ flex: 0, alignItems: 'stretch', marginTop: 20 }}>
                    {/*<TouchableOpacity style={styles.buttonStyle} onPress={() =>*/}
                    {/*this.props.navigation.navigate("CustomerData")}>*/}
                    {/*<CustomText style={styles.buttonTitleStyle}>Order Start</CustomText>*/}
                    {/*</TouchableOpacity>*/}
                    {/*<TouchableOpacity style={styles.buttonStyle} onPress={() =>
                        this.props.updateApp()}>
                        <CustomText style={styles.buttonTitleStyle}>Update</CustomText>
                    </TouchableOpacity>*/}
                </View>
            </ImageBackground>
        );
    }
}

const styles = StyleSheet.create({
    buttonStyle: {
        flex: 0,
        height: 50,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#5e7e47',
        borderRadius: 4,
        padding: 10,
    },
    buttonTitleStyle: {
        textDecorationColor: '#5e7e47',
        textAlign: 'center',
        padding: 10,
        color: '#fff',
        fontSize: 18
    },
});

const mapStateToProps = state => {
    return {
        internetConnected: state.loginReducer.internetConnected,
        customerDetails: state.customerReducer.customerDetails,
        restCustomerDetails: state.customerReducer.restCustomerDetails,
        autoConfigData: state.loginReducer.autoConfigData,
    }
};

const mapDispatchToProps = dispatch => {
    return {
        resetCustomerData: (resetCustomerDetails) => dispatch(CustomerActions.resetCustomerData(resetCustomerDetails)),
        overrideContactVerification: (props) => dispatch(HomeActions.overrideContactVerification(props)),
        setFaceSync: (props) => dispatch(FaceRecognitionActions.setFaceSync(props)),
        updateApp: () => dispatch(UtilityActions.updateApp()),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(withNavigationFocus(HomeScreen));