import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
    ActivityIndicator,
    Dimensions,
    Image,
    KeyboardAvoidingView,
    Modal,
    Picker,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import * as LoginActions from "./../store/actions/LoginActions";
import ConfigService from "./../store/services/ConfigService";
import CustomText from "./CustomText";
import DeviceInfo from 'react-native-device-info';
var { deviceWidth } = Dimensions.get('window');

class LoginScreen extends Component {
    constructor(props) {
        super(props);
        this.state = { selectedOutletName: '' };
        
    }

    componentWillMount() {
        this.props.resetLogin(this.props);
        this.setState({ appVersion: DeviceInfo.getVersion(), env: ConfigService.getEnvironment() });
    };

    renderOutletList() {
        const outletList = [];
        if (this.props.outletList != null) {
            this.props.outletList.map(function (item) {
                outletList.push(<Picker.Item key={item.id} label={item.name} value={item} />)
            })
        }
        return outletList;
    }

    renderTerminals() {
        const terminals = [];
        if (this.props.selectedOutlet != null && this.props.selectedOutlet.terminals != null) {
            for (let i = 1; i <= this.props.selectedOutlet.terminals; i++) {
                terminals.push(<Picker.Item key={i} label={"T" + i} value={i} />)
            }
        }
        return terminals;
    }

    setChangePasswordData(key, value) {
        let changePasswordObj = {};
        if (this.props.changePasswordObj != null) {
            changePasswordObj = this.props.changePasswordObj;
        }
        changePasswordObj[key] = value;
        this.props.setChangePasswordData(changePasswordObj);
    }

    setShowChangePasswordModal(val) {
        if (val === true) {
            this.props.setChangePasswordData(null);
        }
        this.props.setShowChangePasswordModal(val);
    }

    setSelectedOutlet(itemValue) {
        this.props.setSelectedOutlet(itemValue);
        this.setState({
            selectedOutletName: itemValue
        });
    }

    toggleFaceDetectionMode() {
        var mode = (this.props.faceDetectionMode == true || this.props.faceDetectionMode == "true");
        this.props.setFaceDetectionMode(!mode);
    }

    render() {
        return (
            <View style={styles.container}>
                <KeyboardAvoidingView style={styles.keyboardContainer}>
                    <View style={styles.loginContainer}>
                        <Image resizeMode="contain" style={styles.logo}
                            source={require('../assets/img/loginLogo.jpg')} />
                    </View>
                    {this.props.showLoader ? (
                        <View style={styles.loaderContainer}>
                            <ActivityIndicator size="large" color='#5e7e47' />
                            <CustomText style={styles.loaderMessage}>{this.props.loadingMessage}</CustomText>
                        </View>
                    ) : null}
                    {!this.props.showLoader ? (
                        <View style={styles.formContainer}>
                            {this.props.autoConfigData != null ? (
                                <CustomText style={{
                                    fontSize: 23,
                                    textAlign: 'center',
                                    color: '#5e7e47',
                                    padding: 10,
                                    marginBottom: 20,
                                    backgroundColor: "#ddd",
                                    borderRadius: 5
                                }}>
                                    {this.props.autoConfigData.unitName} ({"T" + this.props.autoConfigData.selectedTerminalId})
                                </CustomText>
                            ) : null}
                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.inputBox}
                                    placeholder="UserId"
                                    keyboardType="number-pad"
                                    underlineColorAndroid='transparent'
                                    placeholderTextColor='#CCC'
                                    value={this.props.userId}
                                    onChangeText={(userId) => this.props.setUserId(userId)}
                                    onEndEditing={() => this.props.autoConfigData!=null? {}:this.props.setOutletList(this.props)}
                                />
                            </View>
                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.inputBox}
                                    placeholder="Password"
                                    keyboardType="default"
                                    underlineColorAndroid="transparent"
                                    placeholderTextColor='#CCC'
                                    secureTextEntry={true}
                                    value={this.props.password}
                                    onChangeText={(password) => this.props.setPassword(password)}
                                />
                            </View>
                            {this.props.autoConfigData === null ? (
                                <View>
                                    <Picker
                                        selectedValue={this.state.selectedOutletName}
                                        style={[styles.pickerStyle, { marginTop: 0 }]}
                                        onValueChange={(itemValue) =>
                                            this.setSelectedOutlet(itemValue)
                                        }>
                                        {this.renderOutletList()}
                                    </Picker>
                                    <View style={{ height: 1, borderWidth: .5, borderColor: "#5e7e47" }}></View>
                                    <Picker selectedValue={this.props.selectedTerminal} style={styles.pickerStyle}
                                        onValueChange={(itemValue, itemIndex) => this.props.setSelectedTerminal(itemValue)}>
                                        {this.renderTerminals()}
                                    </Picker>
                                    <View style={{ height: 1, borderWidth: .5, borderColor: "#5e7e47" }}></View>
                                </View>
                            ) : null}
                            <View style={{ flex: 0, alignItems: 'stretch', marginTop: 20 }}>
                                <TouchableOpacity style={styles.buttonStyle} onPress={() =>
                                    this.props.login(this.props)}>
                                    <CustomText style={styles.buttonTitleStyle}>Sign In</CustomText>
                                </TouchableOpacity>
                            </View>
                            <View style={{
                                flex: 0,
                                flexDirection: "row",
                                alignItems: "stretch",
                                justifyContent: "space-between"
                            }}>
                                <TouchableOpacity style={styles.transparentBtn} onPress={() => {
                                    this.setShowChangePasswordModal(true);
                                }}>
                                    <CustomText style={styles.transparentBtnTxt}>Change password</CustomText>
                                </TouchableOpacity>
                                <TouchableOpacity style={styles.transparentBtn}
                                    onPress={() => this.props.resetConfig(this.props)}>
                                    <CustomText style={styles.transparentBtnTxt}>Reset</CustomText>
                                </TouchableOpacity>
                            </View>
                            {/*<View style={{
                                flex: 0,
                                flexDirection: "row",
                                alignItems: "stretch",
                                justifyContent: "space-between"
                            }}>
                                <TouchableOpacity style={styles.buttonStyle} onPress={() =>
                                    this.toggleFaceDetectionMode()
                                }>
                                    <CustomText style={styles.buttonTitleStyle}>
                                        {(this.props.faceDetectionMode == true || this.props.faceDetectionMode == "true") ? "Skip Face Detection" : "Start Face Detection"}
                                    </CustomText>
                                </TouchableOpacity>
                            </View>*/}
                            <View style={{
                                flex: 0,
                                flexDirection: "row",
                                alignItems: "stretch",
                                justifyContent: "space-between",
                                marginTop: 20
                            }}>
                                <CustomText style={styles.textStyle}>Environment: {this.state.env}</CustomText>
                                <CustomText style={styles.textStyle}>Version: {this.state.appVersion}</CustomText>
                            </View>
                        </View>
                    ) : null}
                    {this.props.loginError ? (
                        <View style={{ flex: 0 }}>
                            <CustomText style={[styles.errorMessage]}>{this.props.loginErrorMsg}</CustomText>
                        </View>
                    ) : null}
                </KeyboardAvoidingView>
                <Modal
                    animationType="slide"
                    transparent={false}
                    presentationStyle={"formSheet"}
                    visible={this.props.showChangePasswordModal == true}
                    onRequestClose={() => {
                    }}>
                    <View style={[styles.keyboardContainer, { marginTop: 100 }]}>
                        <View>
                            <Text style={{ fontSize: 22, margin: 10, marginBottom: 50, textAlign: 'center' }}>Change
                                password</Text>
                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.inputBox}
                                    placeholder="UserId"
                                    keyboardType="number-pad"
                                    underlineColorAndroid='transparent'
                                    placeholderTextColor='#5e7e47'
                                    value={(this.props.changePasswordObj != null) ? this.props.changePasswordObj.userId : null}
                                    onChangeText={(userId) => this.setChangePasswordData("userId", userId)}
                                />
                            </View>
                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.inputBox}
                                    placeholder="UnitId"
                                    keyboardType="number-pad"
                                    underlineColorAndroid='transparent'
                                    placeholderTextColor='#5e7e47'
                                    value={(this.props.changePasswordObj != null) ? this.props.changePasswordObj.unitId : null}
                                    onChangeText={(unitId) => this.setChangePasswordData("unitId", unitId)}
                                />
                            </View>
                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.inputBox}
                                    placeholder="Old Password"
                                    underlineColorAndroid="transparent"
                                    placeholderTextColor='#5e7e47'
                                    secureTextEntry={true}
                                    value={(this.props.changePasswordObj != null) ? this.props.changePasswordObj.oldPassword : null}
                                    onChangeText={(password) => this.setChangePasswordData("password", password)}
                                />
                            </View>
                            <View style={styles.inputContainer}>
                                <TextInput
                                    style={styles.inputBox}
                                    placeholder="New Password"
                                    underlineColorAndroid="transparent"
                                    placeholderTextColor='#5e7e47'
                                    secureTextEntry={true}
                                    value={(this.props.changePasswordObj != null) ? this.props.changePasswordObj.newPassword : null}
                                    onChangeText={(password) => this.setChangePasswordData("newPassword", password)}
                                />
                            </View>
                            <View style={{
                                flex: 0,
                                flexDirection: "row",
                                alignItems: "stretch",
                                justifyContent: "space-between"
                            }}>
                                <TouchableOpacity style={styles.transparentBtn} onPress={() => {
                                    this.props.setShowChangePasswordModal(false);
                                }}>
                                    <CustomText style={styles.transparentBtnTxt}>Hide Modal</CustomText>
                                </TouchableOpacity>
                                <TouchableOpacity style={styles.transparentBtn} onPress={() => {
                                    this.props.changePassword(this.props);
                                }}>
                                    <CustomText style={styles.transparentBtnTxt}>Submit</CustomText>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'stretch',
        justifyContent: 'center',
        backgroundColor: '#fff',
        // position: 'relative'
    },
    keyboardContainer: {
        flex: 0,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: "#ddd",
        borderRadius: 5,
        marginLeft: 100,
        marginRight: 100,
        padding: 20
    },
    loginContainer: {
        flex: 0,
        alignItems: 'center',
        justifyContent: 'flex-start',
    },
    logo: {
        height: 70,
        marginBottom: 30
    },
    loaderContainer: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'center',
        marginBottom: 20,
    },
    loaderMessage: {
        color: '#5e7e47',
        fontSize: 21,
        textAlign: 'center',
        paddingLeft: 30,
        paddingRight: 30
    },
    formContainer: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        /*margin: 20,
        marginLeft: 30,
        marginRight: 30,
        padding: 20*/
    },
    inputContainer: {
        flex: 0,
        borderBottomColor: '#5e7e47',
        borderBottomWidth: 1,
        height: 45,
        marginBottom: 20,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start'
    },
    inputBox: {
        height: 45,
        marginLeft: 10,
        flex: 1,
        color: '#5e7e47',
        fontSize: 20,
        marginBottom: 5,
        fontFamily: 'Nunito-Regular'
    },
    pickerStyle: {
        height: 45,
        marginLeft: 10,
        color: '#5e7e47',
        marginTop: 20,
        borderBottomColor: '#5e7e47',
        borderBottomWidth: 1,
    },
    buttonStyle: {
        flex: 0,
        height: 50,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#5e7e47',
        borderRadius: 4,
        padding: 10,
    },
    buttonTitleStyle: {
        textDecorationColor: '#5e7e47',
        textAlign: 'center',
        padding: 10,
        color: '#fff',
        fontSize: 25
    },
    errorMessage: {
        color: 'red',
        marginLeft: 20,
        marginRight: 20,
        textDecorationLine: 'underline',
        textAlign: 'center',
        fontSize: 21
    },
    transparentBtn: {
        padding: 10,
        margin: 5
    },
    transparentBtnTxt: {
        color: "#5e7e47",
        fontSize: 23
    },
    textStyle: {
        color: "#000000",
        fontSize: 23,
        marginTop: 150
    }
});

const mapStateToProps = state => {
    return {
        userId: state.loginReducer.userId,
        password: state.loginReducer.password,
        autoConfigData: state.loginReducer.autoConfigData,
        loginError: state.loginReducer.loginError,
        loginErrorMsg: state.loginReducer.loginErrorMsg,
        selectedOutlet: state.loginReducer.selectedOutlet,
        unitId: state.loginReducer.unitId,
        selectedTerminal: state.loginReducer.selectedTerminal,
        outletList: state.loginReducer.outletList,
        showLoader: state.loginReducer.showLoader,
        loadingMessage: state.loginReducer.loadingMessage,
        showChangePasswordModal: state.loginReducer.showChangePasswordModal,
        changePasswordObj: state.loginReducer.changePasswordObj,
        pubnub: state.loginReducer.pubnubObject,
        channelName: state.loginReducer.channelName,
        faceDetectionMode: state.loginReducer.faceDetectionMode
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setPubnub: (pubnub) => dispatch({ type: "SET_PUB_NUB_OBJECT", payload: pubnub }),
        setUserId: (userId) => dispatch({ type: "SET_USER_ID", payload: userId }),
        setPassword: (password) => dispatch({ type: "SET_PASSWORD", payload: password }),
        resetLogin: (props) => dispatch(LoginActions.resetLogin(props)),
        login: (props) => dispatch(LoginActions.login(props)),
        setOutletList: (props) => dispatch(LoginActions.fetchUserOutlets(props.userId)),
        setSelectedOutlet: (outletObj) => dispatch(LoginActions.setSelectedOutlet(outletObj)),
        setUnitId: (unitId) => dispatch(LoginActions.setUnitId(unitId)),
        setSelectedTerminal: (terminal) => dispatch({ type: "SET_SELECTED_TERMINAL", payload: terminal }),
        setShowChangePasswordModal: (showChangePasswordModal) => dispatch({
            type: 'SET_SHOW_CHANGE_PASSWORD_MODAL',
            payload: showChangePasswordModal
        }),
        setChangePasswordData: (changePasswordObj) => dispatch({
            type: "SET_CHANGE_PASSWORD_DATA",
            payload: { ...changePasswordObj }
        }),
        resetConfig: (props) => dispatch(LoginActions.resetConfig(props)),
        changePassword: (props) => dispatch(LoginActions.changePassword(props)),
        setFaceDetectionMode: (faceDetectionMode) => dispatch(LoginActions.setFaceDetectionMode(faceDetectionMode))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(LoginScreen);