import React, {Component} from 'react';
import {connect} from "react-redux";
import {StyleSheet, Text, View, Image, ActivityIndicator} from 'react-native';
import * as LoginActions from "./../store/actions/LoginActions";
import SplashScreen from "react-native-splash-screen";
import NetInfo from "@react-native-community/netinfo";
import CustomText from "./CustomText";

class MainRoutingScreen extends Component {

    static navigationOptions = {
        header: null
    };

    componentWillMount() {
        NetInfo.fetch().then(state => {
            console.log("Connection type", state.type);
            console.log("Is connected?", state.isConnected);
            this.props.handleRouting(this.props);
        });
    }

    componentDidMount(){
        SplashScreen.hide();
    }

    render() {
        return (
            <View style={styles.container}>
                <View style={styles.loginContainer}>
                    <Image resizeMode="contain" style={styles.logo}
                           source={require('../assets/img/loginLogo.jpg')}/>
                </View>
                <View style={styles.formContainer}>
                    <View style={{height: 300, marginTop: 100}}>
                        <ActivityIndicator size="large" color='#5e7e47'/>
                        <CustomText style={{color: '#5e7e47', fontSize: 21, marginTop: 10}}>
                            Please wait while we load your app.
                        </CustomText>
                    </View>
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        //backgroundColor: 'rgba(45,61,84,1)',
        backgroundColor: '#fff',
        top: 0
    },
    loginContainer: {
        alignItems: 'center',
        flexGrow: 1,
        justifyContent: 'center'
    },
    logo: {
        marginTop: 100,
        //width: 120,
        height: 70
    },
    formContainer: {
        alignItems: 'center',
        flexGrow: 1,
        justifyContent: 'center'
    },
    errorMessage: {
        color: 'red'
    }
});

const mapStateToProps = state => {
    return {
        locale: state.loginReducer.locale,
        userId: state.loginReducer.userId,
        password: state.loginReducer.password,
        authDetail: state.loginReducer.authDetail,
        internetConnected: state.loginReducer.internetConnected
    }
};

const mapDispatchToProps = dispatch => {
    return {
        handleRouting: (props) => dispatch(LoginActions.handleRouting(props)),
        setIsConnected: (connected) => dispatch(LoginActions.setIsConnected(connected))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(MainRoutingScreen);
