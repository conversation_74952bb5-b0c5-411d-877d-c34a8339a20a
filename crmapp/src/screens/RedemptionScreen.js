import React, {Component} from 'react';
import {connect} from 'react-redux';
import {Dimensions, Image, ImageBackground, Keyboard, StyleSheet, TouchableOpacity, View} from 'react-native';
import * as RedemptionActions from "../store/actions/RedemptionActions";
// import DismissKeyboard from 'dismissKeyboard';
import OtpScreen from "./OtpScreen";
import CustomText from "./CustomText";
import UtilityService from "../store/services/UtilityService";
import DownloadLinkButton from "./DownloadAppButton";
import PresentEarnedLoyalty from "../assets/img/svg/present_earned_loyaltea.svg";
import StageCompleted from "../assets/img/svg/stage_completed.svg";
import LoyaltyNextState from "../assets/img/svg/loyalty_next_state.svg";
import LoyaltyCup from "../assets/img/svg/loyalty_earned.svg";
import * as CustomerActions from "../store/actions/CustomerActions";


var {deviceWidth} = Dimensions.get('window');

class RedemptionScreen extends Component {
    constructor(props) {
        super(props);
        let count = parseInt(this.props.customerDetails.loyalityPoints / 60);
        this.state = {
            chaiCount: count > 5 ? 5 : count,
            loyalityPoints: parseInt(this.props.customerDetails.loyalityPoints),
            visitAway: (60 - parseInt(this.props.customerDetails.loyalityPoints)) / 10,
            openModal: false,
            redemptionFlag: UtilityService.getUnitPartnerBrandMetaData().LOYALTY_REDEMPTION_AVAILABLE,
            count:count
        };
        console.log(this.state);
        console.log(this.props);
    }

    componentWillMount() {
        Keyboard.dismiss();
        // DismissKeyboard();
    }

    renderButtons = () => {
        const views = [];
        for (let i = 1; i <= this.state.chaiCount; i++) {
            let title = i;
            views.push(
                <TouchableOpacity key={i} style={[styles.buttonStyle, {marginTop: 25}]} onPress={() =>
                    this.redeemChai(i)}>
                    <CustomText style={styles.buttonTitleStyle}>{title}</CustomText>
                </TouchableOpacity>
            );
        }
        views.push(
            <TouchableOpacity key={120} style={[styles.buttonStyle, {marginTop: 25}]} onPress={() =>
                this.redeemChai(0)}>
                <CustomText style={[styles.buttonTitleStyle,{fontSize: 20}]}>Have it Later</CustomText>
            </TouchableOpacity>
        );
        return views;
    };


    redeemChai(chaiCount) {
        if (!this.props.customerDetails.chaiRedeemed) {
            this.props.setRedeemChai(chaiCount, this.props);
        }
    }

    renderSlider = () => {
        const visitBullets = [];
        const points = this.props.customerDetails.loyalityPoints;
        let i = 1;
        while (i <= 5) {
            if ((i * 10) === points) {
                visitBullets.push(
                    <View style={{marginTop: -4}} key={"bullet" + i}>
                        <PresentEarnedLoyalty width={55} height={75}/>
                    </View>
                )
            } else if ((i * 10) < points) {
                visitBullets.push(
                    <View key={"bullet" + i}>
                        <StageCompleted width={35} height={45} fill={'#fff'}/>
                    </View>
                )
            } else {
                visitBullets.push(
                    <View style={{marginBottom: -4}} key={"bullet" + i}>
                        <LoyaltyNextState width={30} height={45}/>
                    </View>
                )
            }
            i++;
        }
        visitBullets.push(
            <LoyaltyCup width={55} height={65} key={"bullet" + 6}/>
        );
        return visitBullets;

    };

    encodeContact() {
        return '******' + this.props.customerDetails.contact.substring(6);
    }

    render() {

        return (
            <View>
                    {this.props.customerDetails.newCustomer ?
                        (<ImageBackground width='100%' height='100%'
                                          source={require("../assets/img/customerFirstVisit.jpg")}
                                          style={{
                                              width: '100%',
                                              height: '100%'
                                          }}>

                            <View style={styles.containers}>
                                <CustomText style={[styles.textStyle, {marginTop: 15}]}>
                                    <CustomText style={{
                                        color: '#5e7e47',
                                        fontSize: 23,
                                        // fontWeight: 'bold',
                                        fontFamily: 'Nunito-Regular',
                                        letterSpacing: 3
                                    }}>Hi {this.props.customerDetails.name}!
                                        Welcome to Chaayos </CustomText>
                                </CustomText>
                            </View>
                            <DownloadLinkButton description="Send Link"
                                                onPress={() => this.props.sendAppDownloadLink(this.props, 'FIRST_VISIT')}/>
                        </ImageBackground>) : null}

                    {(!this.props.customerDetails.newCustomer && this.state.chaiCount == 0) ? (

                        <ImageBackground width='100%' height='100%'
                                         source={require("../assets/img/regularCustomer.jpg")}
                                         style={{width: '100%', height: '100%'}}>

                            <View style={styles.containers}>
                                <CustomText style={styles.textStyle}>
                                    <CustomText style={{
                                        color: '#5e7e47',
                                        fontSize: 23,
                                        fontWeight: 'bold',
                                        fontFamily: 'Nunito-Regular',
                                        letterSpacing: 3
                                    }}>Hi {this.props.customerDetails.name}!
                                        Welcome back to Chaayos </CustomText>
                                </CustomText>

                                <CustomText style={[styles.textStyle, {marginTop: 40}]}>
                                    <CustomText style={{
                                        color: '#5e7e47',
                                        fontSize: 20,
                                        fontFamily: 'Nunito-Regular',
                                        letterSpacing: 3,

                                    }}>Your Registered number
                                        is {this.props.customerDetails && this.encodeContact()}
                                    </CustomText>
                                </CustomText>

                                <CustomText style={[styles.textStyle,
                                    {
                                        color: '#5e7e47', fontFamily: 'Nunito-Regular',
                                        marginTop: 60, fontWeight: 'bold', letterSpacing: 3
                                    }]}>You are
                                    <CustomText style={{
                                        fontSize: 40,
                                        color: '#5e7e47'
                                    }}> {this.state.visitAway}</CustomText>
                                    {this.state.visitAway == 1 &&
                                    <CustomText
                                        style={[styles.textStyle, {color: '#5e7e47'}]}> visit </CustomText>}
                                    {this.state.visitAway > 1 &&
                                    <CustomText
                                        style={[styles.textStyle, {color: '#5e7e47'}]}> visits </CustomText>}
                                    <CustomText style={[styles.textStyle, {color: '#5e7e47'}]}>away to unlock your
                                        Free
                                        Loyal</CustomText>
                                    <CustomText style={[styles.textStyle, {
                                        color: '#5e7e47',
                                        fontFamily: 'Caveat-Regular'
                                    }]}>Tea</CustomText>
                                </CustomText>
                                <View style={[styles.greyLine]}/>
                                <View style={[styles.bulletsContainer]}>
                                    {this.renderSlider()}

                                </View>
                            </View>

                            <DownloadLinkButton description="Send Link"
                                                onPress={() => this.props.sendAppDownloadLink(this.props, 'REGULAR_CUSTOMER_ZERO_FREE_CHAI')}/>
                        </ImageBackground>) : null}

                    {!this.props.customerDetails.newCustomer && this.state.chaiCount > 0 ?
                        [(this.state.redemptionFlag != 'N' ?
                            (<ImageBackground width='100%' height='100%'
                                              source={require("../assets/img/nLoyaltyTeaWithButton.jpg")}
                                              style={{width: '100%', height: '100%'}}>
                                <View style={styles.containers}>
                                    <View style={styles.userInfo}>
                                        <View style={{flex: 50, marginLeft: 15}}>
                                            <CustomText style={{
                                                color: '#5e7e47',
                                                fontSize: 23,
                                                fontWeight: 'bold',
                                                fontFamily: 'Nunito-Regular',
                                                textAlign: 'left'
                                            }}>Hi {this.props.customerDetails.name}
                                            </CustomText>
                                        </View>
                                        <View style={{flex: 50, marginRight: 15}}>
                                            <CustomText style={{
                                                color: '#5e7e47',
                                                fontSize: 20,
                                                textAlign: 'right',
                                                fontFamily: 'Nunito-Regular'
                                            }}> {this.props.customerDetails && this.encodeContact()}
                                            </CustomText>
                                        </View>
                                    </View>
                                    <CustomText style={[styles.textStyle, {
                                        marginTop: 110,
                                        color: '#5e7e47',
                                        fontFamily: 'Nunito-Regular',
                                        fontSize: 30
                                    }]}>Your
                                        Current
                                        Points: {this.state.loyalityPoints}</CustomText>
                                    <CustomText style={[styles.textStyle, {
                                        backgroundColor: '#7E4B06',
                                        marginLeft: 150,
                                        width: 500,
                                        color: '#fff',
                                        fontFamily: 'Nunito-Regular',
                                        fontSize: 35
                                    }]}>Your
                                        LoyalTea: {this.state.count} FREE CHAI</CustomText>


                                    <CustomText style={[styles.textStyle,
                                        {
                                            color: '#7E4B06',
                                            marginLeft: 150,
                                            width: 500,
                                            marginTop: 50,
                                            fontFamily: 'Nunito-Regular',
                                            fontSize: 22,
                                            fontWeight:'bold'
                                        }]}>
                                        How many Free Chai you want to redeem?</CustomText>
                                    <View style={{
                                        marginLeft: 150,
                                        marginRight: 150,
                                        marginTop: 50,
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        flexWrap: 'wrap',
                                    }}>
                                        {this.renderButtons()}
                                    </View>

                                </View>
                                <DownloadLinkButton description="Send Link"
                                                    onPress={() => this.props.sendAppDownloadLink(this.props, 'N_LOYALTEA')}/>
                            </ImageBackground>) :
                            (<ImageBackground width='100%' height='100%'
                                              source={require("../assets/img/nLoyaltyTea.jpg")}
                                              style={{width: '100%', height: '100%'}}>
                                <View style={styles.containers}>
                                    <View style={styles.userInfo}>
                                        <View style={{flex: 50, marginLeft: 15}}>
                                            <CustomText style={{
                                                color: '#5e7e47',
                                                fontSize: 23,
                                                fontWeight: 'bold',
                                                fontFamily: 'Nunito-Regular',
                                                textAlign: 'left'
                                            }}>Hi {this.props.customerDetails.name}
                                            </CustomText>
                                        </View>
                                        <View style={{flex: 50, marginRight: 15}}>
                                            <CustomText style={{
                                                color: '#5e7e47',
                                                fontSize: 20,
                                                textAlign: 'right',
                                                fontFamily: 'Nunito-Regular'
                                            }}> {this.props.customerDetails && this.encodeContact()}
                                            </CustomText>
                                        </View>
                                    </View>
                                    <CustomText style={[styles.textStyle, {
                                        marginTop: 110,
                                        color: '#5e7e47',
                                        fontFamily: 'Nunito-Regular',
                                        fontSize: 30
                                    }]}>Your
                                        Current
                                        Points: {this.state.loyalityPoints}</CustomText>
                                    <CustomText style={[styles.textStyle, {
                                        backgroundColor: '#7E4B06',
                                        marginLeft: 150,
                                        width: 500,
                                        color: '#fff',
                                        fontFamily: 'Nunito-Regular',
                                        fontSize: 35
                                    }]}>Your
                                        LoyalTea: {this.state.count} FREE CHAI</CustomText>
                                </View>
                                <DownloadLinkButton description="Send Link"
                                                    onPress={() => this.props.sendAppDownloadLink(this.props, 'N_LOYALTEA')}/>
                            </ImageBackground>))] : null}
                </View>);


    }

}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        fontSize: 30,
        marginTop: 300
    },
    buttonStyle: {
        flex: 0,
        backgroundColor: '#ADD3B0',
        borderRadius: 7,
        padding: 5,
        marginRight: 10,
        marginTop: 10,
        width: 125,
        height: 75,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius:25
        // justifyContent: 'flex-',
        // alignSelf: 'flex-end',
    //    #83C287

    },
    newButtonStyle: {
        flex: 0,
        backgroundColor: '#fff',
        borderRadius: 7,
        padding: 5,
        marginRight: 10,
        marginTop: 10,
        justifyContent: 'center',
        alignItems: 'center',
        height: 108,
        width: 130
        // justifyContent: 'flex-',
        // alignSelf: 'flex-end',

    },
    buttonTitleStyle: {
        textAlign: 'center',
        padding: 2,
        // color: '#fff',
        fontSize: 25,
        fontWeight:'bold'
    },
    newButtonTitleStyle: {
        textAlign: 'center',
        padding: 2,
        color: '#375125',
        fontSize: 15,
        fontFamily: 'Nunito-Regular'
    },
    textStyle: {
        fontSize: 22,
        textAlign: 'center',
        color: 'rgba(0,0,0,0.87)'
    },
    startBtn: {
        flex: 0,
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: "center",
        backgroundColor: 'green',
        borderRadius: 10,
        width: 450,
        height: 70,
        alignSelf: 'center',
        marginBottom: 150,
        color: "#FFF",
        elevation: 2,
    },
    containers: {
        flex: 1,
        fontSize: 30,
        marginTop: 25
    }, greyLine: {
        flex: 0,
        height: 8,
        marginTop: 120,
        marginLeft: 80,
        width: 575,
        backgroundColor: '#C4C4C4'
    },
    bulletsContainer: {
        marginTop: -30,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        // backgroundColor: '#4A8132',
        marginLeft: 75,
        width: 600,
        height: 45
    }, userInfo: {
        // backgroundColor: '#577C39',
        flexDirection: 'row',
        height: 45,
        alignItems: 'center',

    }
});

const mapStateToProps = state => {
    return {
        customerDetails: state.customerReducer.customerDetails,
        isModalVisible: state.otpReducer.isModalVisible,
        showLoader: state.customerReducer.showLoader,
        loadingMessage: state.customerReducer.loadingMessage,
        loginByFace: state.customerReducer.loginByFace
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setRedeemChai: (number, props) => dispatch(RedemptionActions.redeemChai(number, props)),
        sendAppDownloadLink: (props, msg) => dispatch(CustomerActions.sendAppDownloadLink(props, msg)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(RedemptionScreen);