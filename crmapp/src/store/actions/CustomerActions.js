import RestService from "./../services/RestService";
import UtilityService from "./../services/UtilityService";
import apis from "./../services/APIs";
import {ToastAndroid,Text} from "react-native";
import * as PubnubActions from "./PubnubActions";
import * as OtpActions from "./OtpActions";
import NavigationService from "../services/NavigationService";
import StorageService from "./../services/StorageService";

export function resetCustomerData(resetCustomerDetails) {
    return dispatch => {
        dispatch({type: "SET_CONTACT_NUMBER", payload: null});
        dispatch({type: "SET_CONTACT_ENTERED", payload: false});
        if (resetCustomerDetails) {
            dispatch({type: "SET_CUSTOMER_DETAILS", payload: null});
            UtilityService.setCustomerDetails(UtilityService.getEmptyCustomerObj());
        }
        dispatch({type: "SET_OTP_DISABLED", payload: false});
        dispatch({type: "SET_SHOW_OTP", payload: true});
        dispatch({type: "SET_USER_NAME_DISABLED", payload: false});
        dispatch({type: "SET_OTP_SENT_COUNT", payload: 0});
        dispatch({type: "SET_SHOW_EDIT_CONTACT", payload: false});
        dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        dispatch({type: "SET_SHOW_UPDATE_USER_NAME", payload: false});
        dispatch({type: 'IS_MODAL_VISIBLE', payload: false});
        dispatch({type: "SET_OTP_HEADLINE", payload: ""});
        dispatch({type: "OTP_SOURCE", payload: null});
    }
}


export function resendOTPStrategy(props) {
    return dispatch => {
        console.log('resendOTPStrategy props ', props);
        dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: true, loadingMessage: 'Re-sending OTP'}});
        RestService.postJSON(apis.getUrls().customer.resendAuthorizationOTP, props.customerDetails).then((response) => {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            if (response) {
                dispatch(PubnubActions.publish({OTP_RESENT: props.customerDetails}));
                ToastAndroid.show('One Time Password resent successfully. Please check your mobile!', ToastAndroid.LONG);
            } else {
                ToastAndroid.show('Error sending One Time Password. Please try again!', ToastAndroid.LONG);
            }
        }).catch((error) => {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }
}

export function screenOpened(props) {
    return dispatch => {
        const customerObject = {
            id: null,
            name: null,
            contact: null,
            email: null,
            loyalityPoints: null,
            contactVerified: null,
            emailVerified: null,
            unitId: UtilityService.getUnitId(),
            newCustomer: null,
            otp: null,
            chaiRedeemed: 0,
            productId: 10,
            otpVerified: false,
            customerRejected: false
        };
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: customerObject});
        UtilityService.setCustomerDetails(customerObject);
        dispatch(PubnubActions.publish({SCREEN_OPENED: customerObject}));
    }
}

export function setContactNumber(number, props) {
    return dispatch => {
        dispatch({type: "SET_CONTACT_NUMBER", payload: number});
        if (number != null && number.toString().length === 10) {
            dispatch({type: "SET_CONTACT_ENTERED", payload: true});
            dispatch({
                type: "SET_SHOW_LOADER",
                payload: {showLoader: true, loadingMessage: "Loading customer details."}
            });
            RestService.postJSON(apis.getUrls().customer.signin, {
                contact: number,
                signInMode: "DEFAULT"
            }).then((response) => {
                console.log('sign in response ', response);
                dispatch(PubnubActions.publish({SCREEN_OPENED: response}));
                if (response != null && response.contact != null) {
                    dispatch({type: "LOGIN_BY_FACE", payload: false});
                    UtilityService.setLoginByFace(false);
                    dispatch(PubnubActions.publish({PROCESS_STARTED: response}));
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: response});
                    UtilityService.setCustomerDetails(response);
                    dispatch(PubnubActions.publish({DETAILS_ENTERED: response}));

                    if (response.newCustomer && !response.eligibleForSignupOffer) {
                        dispatch(PubnubActions.publish({NEW_CUSTOMER: response}));

                        dispatch({type: "SET_OTP_DISABLED", payload: false});
                        dispatch({type: "SET_USER_NAME_DISABLED", payload: false});
                        dispatch({type: "SET_OTP_SENT_COUNT", payload: props.otpSentCount + 1});
                        // dispatch({type: "SET_SHOW_EMAIL_INPUT", payload: true});
                        dispatch({type: "SET_CONTACT_NUMBER", payload: response.contact});

                        NavigationService.navigate('CustomerData');
                        // }

                    } else if (response.name == "" || response.name == null) {
                        dispatch({type: "SET_SHOW_UPDATE_USER_NAME", payload: true});
                    }
                    // Removed email screen
                    // else if (response.contactVerified && (response.email == null || response.email == "")) {
                    //     dispatch({type: "SET_SHOW_EMAIL_INPUT", payload: true});
                    //     dispatch(PubnubActions.publish({SKIP_EMAIL_FORM: response}));
                    //     NavigationService.navigate("EnterEmail");
                    // }

                    if (response.contactVerified && response.name != null && response.name != "") {
                        if ((props.faceDetectionMode == true || props.faceDetectionMode == "true") &&
                            response.optOutOfFaceIt == false && props.faceString != null && (response.faceId == null || response.faceId == undefined)) {
                            dispatch(OtpActions.openOTPModal('fromFaceLogin', "to link your face data"));
                        } else {
                            if (response.eligibleForSignupOffer) {
                                console.log('eligibleForSignupOffer');
                                if (!response.otpVerified) {
                                    dispatch(OtpActions.openOTPModal('fromSecondChai', "to redeem your 2nd Free Chai"));
                                } else {
                                    props.customerDetails.chaiRedeemed = 1;
                                    props.customerDetails.productId = 10;
                                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: props.customerDetails});
                                    UtilityService.setCustomerDetails(props.customerDetails);
                                    dispatch(PubnubActions.publish({REDEMPTION: props.customerDetails}));
                                }
                            } else {
                                if (props.customerDetails.newCustomer) {

                                    NavigationService.navigate('AcknowledgeScreen');
                                } else {
                                    NavigationService.navigate("Redemption");
                                }
                            }
                        }
                    }
                    /*if (response.contactVerified && (props.faceDetectionMode == true || props.faceDetectionMode == "true") && props.faceString != null) {
                        dispatch(faceSignup(response.name, response.contact, props.faceString, props.sessionId));
                    }*/
                } else {
                    dispatch({type: "SET_CONTACT_NUMBER", payload: null});
                    if (response.errorMessage != null && response.errorMessage.indexOf('Customer') >= 0) {
                        ToastAndroid.show(response.data.errorMessage, ToastAndroid.LONG);
                    } else {
                        ToastAndroid.show('Please enter contact number again!', ToastAndroid.LONG);
                    }
                }
                dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            }).catch((error) => {
                dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            });
        }
    }
}

export function setCustomerName(name, props) {
    return dispatch => {
        if (props.customerDetails == null) {
            props.customerDetails = {
                name: name
            };
        }
        props.customerDetails.name = UtilityService.capitalize(name);
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: {...props.customerDetails}});
        UtilityService.setCustomerDetails(props.customerDetails);
    }
}

export function setCustomerEmail(email, props) {
    return dispatch => {
        if (props.customerDetails == null) {
            props.customerDetails = {};
        }
        props.customerDetails.email = email.toLowerCase();
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: {...props.customerDetails}});
        UtilityService.setCustomerDetails(props.customerDetails);
    }
}

export function setOTP(otp, props) {
    return dispatch => {
        if (props.customerDetails == null) {
            props.customerDetails = {};
        }
        props.customerDetails.otp = otp;
        if (props.customerDetails.otp.length == 4) {
            //props.verifyNumber(props);
            dispatch(verifyNumber(props));
        } else if (props.customerDetails.otp.toString().length > 4) {
            ToastAndroid.show('One Time Password should not be more than 4 digits!', ToastAndroid.LONG);
        }
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: {...props.customerDetails}});
        UtilityService.setCustomerDetails(props.customerDetails);
    }
}

export function verifyNumber(props) {
    return dispatch => {
        dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: true, loadingMessage: "Verifying OTP"}});
        if (props.customerDetails.name != null && props.customerDetails.name != "") {
            props.customerDetails.name = UtilityService.capitalize(props.customerDetails.name);
        }
        props.customerDetails.unitId = UtilityService.getUnitId();
        dispatch(PubnubActions.publish({OTP_SUBMITTED: props.customerDetails}));
        RestService.postJSON(apis.getUrls().customer.signup, props.customerDetails).then((response) => {
            props.customerDetails = response;
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});

            if (response.contactVerified == true) {
                dispatch({type: "LOGIN_BY_FACE", payload: false});
                UtilityService.setLoginByFace(false);
                props.customerDetails.contactVerified = true;
                props.customerDetails.otpVerified = true;
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: response});
                UtilityService.setCustomerDetails(response);
                dispatch(PubnubActions.publish({OTP_STATUS: props.customerDetails}));

                if (props.customerDetails.name == null || props.customerDetails.name == "") {
                    if (response.newCustomer) {
                        dispatch({type: "SET_SHOW_UPDATE_USER_NAME", payload: true});
                        dispatch({type: "SET_SHOW_OTP", payload: false});
                        dispatch(PubnubActions.publish({SKIP_EMAIL_FORM: response}));
                    }
                    // else {
                    //     props.navigation.navigate("EnterEmail");
                    // }
                } else if (response.eligibleForSignupOffer) {
                    if (!response.otpVerified) {
                        dispatch({
                            type: "SET_OTP_HEADLINE",
                            payload: "to redeem your free chai"
                        });
                    }
                    response.redeemChai = 1
                    response.productId = 10
                    props.customerDetails.redeemChai = 1;
                    props.customerDetails.productId = 10;
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: response});
                    UtilityService.setCustomerDetails(response);
                    dispatch(PubnubActions.publish({REDEMPTION: response}));
                } else {
                    if (props.customerDetails.newCustomer) {
                        NavigationService.navigate('AcknowledgeScreen');
                    } else {
                        NavigationService.navigate("Redemption");
                    }
                }
                if ((props.faceDetectionMode == true || props.faceDetectionMode == "true") && props.faceString != null) {
                    dispatch(faceSignup(response.name, response.contact, props.faceString, props.sessionId));
                }
            } else {
                ToastAndroid.show('Incorrect One Time Password. Please enter again!', ToastAndroid.LONG);
                props.customerDetails.otp = null;
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: {...props.customerDetails}});
                UtilityService.setCustomerDetails(props.customerDetails);
            }
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        }).catch((error) => {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }
}

export function editContact() {
    return dispatch => {
        dispatch({type: "SET_CONTACT_NUMBER", payload: null});
        dispatch({type: "SET_CONTACT_ENTERED", payload: false});
        dispatch({type: "SET_CUSTOMER_DETAILS", payload: null});
        UtilityService.setCustomerDetails(null);
        dispatch({type: "SET_SHOW_EMAIL_INPUT", payload: false});
    }
}

export function updateUserName(props) {
    return dispatch => {
        if (props.customerDetails.name == null || props.customerDetails.name == "") {
            ToastAndroid.show('Please enter your name', ToastAndroid.LONG);
        } else if (!UtilityService.validName(props.customerDetails.name)) {
            ToastAndroid.show('Please enter your real name.', ToastAndroid.LONG);
        }/* else if ((props.customerDetails.email == null || props.customerDetails.email.trim() == "")) {
            ToastAndroid.show('Please enter your email address', ToastAndroid.LONG);
        } else if (!UtilityService.validEmail(props.customerDetails.email)) {
            ToastAndroid.show('Please enter valid email address. All your receipts will be send to this address only!', ToastAndroid.LONG);
        }*/ else {
            props.customerDetails.name = UtilityService.capitalize(props.customerDetails.name);
            dispatch({
                type: "SET_SHOW_LOADER",
                payload: {showLoader: true, loadingMessage: "Updating user details"}
            });
            RestService.postJSON(apis.getUrls().customer.updateName, props.customerDetails).then((response) => {
                dispatch({type: "SET_CUSTOMER_DETAILS", payload: response});
                UtilityService.setCustomerDetails(response);

                ToastAndroid.show('User details updated', ToastAndroid.LONG);
                dispatch({type: "SET_SHOW_UPDATE_USER_NAME", payload: false});
                if (props.customerDetails.newCustomer) {
                    NavigationService.navigate('AcknowledgeScreen');
                } else {
                    NavigationService.navigate("Redemption");
                }
                if ((props.faceDetectionMode == true || props.faceDetectionMode == "true") && props.faceString != null) {
                    dispatch(faceSignup(response.customerDetails.name, response.customerDetails.contact, props.faceString, props.sessionId));
                }
                dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            }).catch((error) => {
                dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            });
        }
    }
}

export function lookupCustomer(contact, couponLogin, couponCode, truecallerProfile, sessionId) {
    return dispatch => {
        if (!couponLogin) {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: true, loadingMessage: 'Loading...'}});
        }
        const customerResponse = {
            contact: contact,
            name: truecallerProfile.name,
            email: truecallerProfile.email,
            signInMode: 'TRUE_CALLER_FIRST',
            unitId: UtilityService.getUnitId()
        };
        RestService.postJSON(apis.getUrls().customer.trueCallerSignin, customerResponse).then((response) => {
            console.log('trueCallerSignin response ', response);
            dispatch({type: "LOGIN_BY_FACE", payload: false});
            UtilityService.setLoginByFace(false);
            dispatch(PubnubActions.publish({PROCESS_STARTED: response}));
            dispatch({type: "SET_CUSTOMER_DETAILS", payload: {...response}});
            UtilityService.setCustomerDetails(response);

            dispatch(PubnubActions.publish({DETAILS_ENTERED: response}));
            if (!response.newCustomer) {

                if (response.eligibleForSignupOffer) {
                    let customerDetail = response;

                    customerDetail.chaiRedeemed = 1;
                    customerDetail.productId = 10;
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: customerDetail});
                    UtilityService.setCustomerDetails(customerDetail);

                    dispatch(PubnubActions.publish({REDEMPTION: customerDetail}));
                }
                if (response.name != null && response.name != "") {
                    if (props.customerDetails.newCustomer) {
                        NavigationService.navigate('AcknowledgeScreen');
                    } else {
                        NavigationService.navigate("Redemption");
                    }
                }
            } else {
                const customerResponse = {
                    contact: contact,
                    name: truecallerProfile.name,
                    email: truecallerProfile.email,
                    unitId: UtilityService.getUnitId()
                };
                RestService.postJSON(apis.getUrls().customer.trueCallerSignup, customerResponse).then((response) => {
                    console.log("trueCallerSignup response ", response);
                    dispatch({type: "SET_CUSTOMER_DETAILS", payload: response});
                    UtilityService.setCustomerDetails(response);

                    dispatch(PubnubActions.publish({NEW_CUSTOMER: response}));
                    dispatch(PubnubActions.publish({OTP_STATUS: response}));

                    // if (response.email == null || response.email == '') {
                    //     dispatch(PubnubActions.publish({SKIP_EMAIL_FORM: response}));
                    //     NavigationService.navigate("EnterEmail");
                    // }
                    if (response.newCustomer && response.eligibleForSignupOffer) {
                        let customerDetail = response;

                        customerDetail.chaiRedeemed = 1;
                        customerDetail.productId = 10;
                        dispatch({type: "SET_CUSTOMER_DETAILS", payload: customerDetail});
                        UtilityService.setCustomerDetails(customerDetail);

                        dispatch(PubnubActions.publish({REDEMPTION: customerDetail}));
                    } else if (response.name != null && response.name != "") {
                        if (props.customerDetails.newCustomer) {
                            NavigationService.navigate('AcknowledgeScreen');
                        } else {
                            NavigationService.navigate("Redemption");
                        }
                    }
                }).catch((error) => {
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                });
            }
            const faceString = UtilityService.getFaceString();
            StorageService.getFaceDetectionMode().then(function (mode) {
                if ((mode == true || mode == "true") && faceString != null) {
                    dispatch(faceSignup(truecallerProfile.name, contact, faceString, sessionId));
                }
            });
        }).catch((error) => {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
        });
    }
}

export function saveVerifiedCustomer(truecallerProfile, couponLogin, couponCode) {
    return dispatch => {
        if (!couponLogin) {
            dispatch({
                type: "SET_SHOW_LOADER",
                payload: {showLoader: true, loadingMessage: "Verifying contact number. Please wait."}
            });
        }
        dispatch(signUpCustomer(truecallerProfile.name, truecallerProfile.email, null, truecallerProfile.contact, false, false, null, -1));
    }
}

export function signUpCustomer(name, email, otp, contact, isInterState, couponLogin, couponCode, tcId) {
    return dispatch => {
        props.customerDetails.name = name;
        props.customerDetails.email = email;
        props.customerDetails.otp = otp;
        props.customerDetails.contact = contact;
        dispatch(verifyNumber(props));
    }
}

export function faceSignup(name, contact, faceString, sessionId) {
    return dispatch => {
        const reqObj = {
            customerName: name,
            faceDescription: "",
            environmentConditions: "",
            contact: contact,
            faceImage: faceString,
            quality: "1",
            unitId: UtilityService.getUnitId(),
            terminalId: UtilityService.getTerminalId(),
            batchId: "000001",
            sessionId: sessionId
        };
        RestService.postJSON(apis.getUrls().rekog.signup, reqObj).then((response) => {
            console.log("face signup response:", response);
            if (response != null) {
                //console.log("face signup response:::", response);
            }
        }).catch((error) => {
            console.log("face signup error:::", error);
        });
    }
}

export function submitSourceAcknowledge(props, source) {
    return dispatch => {
        const request = {
            id: UtilityService.getCustomerDetails().id,
            name: UtilityService.getCustomerDetailType().SOURCE_ACKNOWLEDGE,
            code: source,
        };
        RestService.postJSON(apis.getUrls().customer.saveCustomerSourceAcknowledge, request).then((response) => {
            console.log("acknowledge  response ", response);
            NavigationService.navigate('Redemption');
        }).catch((error) => {
            console.log("error in saving data", error)
        });

    }
}

export function sendAppDownloadLink(props,msg) {
    return dispatch => {
        const request = {
            id: UtilityService.getCustomerDetails().id,
            code: msg,
        };
        RestService.postJSON(apis.getUrls().customer.sendAppDownloadLink ,request).then((response) => {
            console.log("App link sent", response)
            ToastAndroid.show('Sms has been sent to your number', ToastAndroid.SHORT);
        }).catch((error) => {
            console.log("error in saving data", error)
        });
    }
}
