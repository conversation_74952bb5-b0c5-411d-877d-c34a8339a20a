import RestService from "./../services/RestService";
import apis from "./../services/APIs";
import {ToastAndroid} from "react-native";
import * as PubnubActions from "./PubnubActions";
import NavigationService from "../services/NavigationService";

export function setEmail(props) {
    return dispatch => {
        let email = props.email;
        props.customerDetails.email = email;
        dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: true, loadingMessage: 'Updating Email'}});

        let reg = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
        if (reg.test(email) === false) {
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            ToastAndroid.show('Please enter valid email and try again.', ToastAndroid.LONG);
        } else {
            RestService.postJSON(apis.getUrls().customer.updateName, props.customerDetails).then((response) => {
                dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                if (response.email == null || response.email.trim() == "") {
                    ToastAndroid.show('Connection Problem. Please try again.', ToastAndroid.LONG);
                } else {
                    dispatch(PubnubActions.publish({EMAIL_ENTERED: props.customerDetails}));
                    NavigationService.navigate('Redemption');
                    ToastAndroid.show("Please don't forget to verify your email using the verification link we will send with your e-bill receipt.", ToastAndroid.LONG);
                }
            }).catch((error) => {
                dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
            });
        }
    }
}

export function skipEmail(props) {
    props.customerDetails.email = '';
    return dispatch => {
        dispatch(PubnubActions.publish({EMAIL_FORM_SKIPPED_BY_CUSTOMER: props.customerDetails}));
        NavigationService.navigate('Redemption');
    }
}