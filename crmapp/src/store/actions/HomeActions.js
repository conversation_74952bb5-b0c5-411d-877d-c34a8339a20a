import RestService from "./../services/RestService";
import UtilityService from "./../services/UtilityService";
import apis from "./../services/APIs";
import {ToastAndroid} from "react-native";
import StorageService from "../services/StorageService";
import * as PubnubActions from "./PubnubActions";
import * as OtpActions from "./OtpActions";
import RedemptionScreen from "../../screens/RedemptionScreen";
import NavigationService from "../services/NavigationService";

export function overrideContactVerification(props) {
    return dispatch => {
        RestService.postJSON(apis.getUrls().customer.overrideContactVerification, props.customerDetails).then((response) => {
            console.log("overrideContactVerification response", response);
        });
    }
}