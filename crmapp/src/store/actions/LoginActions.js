import apis from "./../services/APIs";
import RestService from "./../services/RestService";
import StorageService from "./../services/StorageService";
import UtilityService from "./../services/UtilityService";
import ConfigService from "./../services/ConfigService";
import DeviceInfo from 'react-native-device-info';
import { Alert, ToastAndroid } from "react-native"
import NavigationService from "./../services/NavigationService";
import * as PubnubActions from "./PubnubActions";

export function handleRouting(props) {
    return dispatch => {
        StorageService.getAutoConfigData().then((configData) => {
            configData = configData != null ? JSON.parse(configData) : null;
            if (configData != null) {
                dispatch({ type: "SET_AUTO_CONFIG_DATA", payload: configData });
            }
            props.navigation.navigate("Login");
        });
    }
}

export function resetLogin(props, force) {
    return dispatch => {
        dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
        dispatch({ type: "SET_LOGIN_ERROR", payload: { loginError: false, loginErrorMsg: null } });
        dispatch({ type: "SET_USER_ID", payload: null });
        dispatch({ type: "SET_PASSWORD", payload: null });
        dispatch({ type: "SET_OUTLET_LIST", payload: [] });
        dispatch({ type: 'SET_SHOW_CHANGE_PASSWORD_MODAL', payload: false });
        // if ((props.autoConfigData == null && props.outletList == null) || force === true) {
        //     dispatch(fetchOutlets())
        // }
        StorageService.getFaceDetectionMode().then((mode) => {
            if (mode == null) {
                StorageService.setFaceDetectionMode(true);
            }
            mode = (mode != null) ? mode : true;
            dispatch({ type: "SET_FACE_DETECTION_MODE", payload: mode });
        });
    }
}

function compare(a, b) {
    // Use toUpperCase() to ignore character casing
    const genreA = a.name.toUpperCase();
    const genreB = b.name.toUpperCase();

    let comparison = 0;
    if (genreA > genreB) {
        comparison = 1;
    } else if (genreA < genreB) {
        comparison = -1;
    }
    return comparison;
}

export function setSelectedOutlet(outletObj) {
    console.log("setting outlet ")
    console.log(outletObj)
    return dispatch => {
        dispatch({
            type: "SET_SELECTED_OUTLET",
            payload: {
                id: outletObj.id,
                name: outletObj.name,
                terminals: outletObj.noOfTerminal,
                region: outletObj.region,
                googleMerchantId: outletObj.googleMerchantId
            }
        });
        dispatch({ type: "SET_SELECTED_TERMINAL", payload: 1 });
    }
}

export function fetchOutlets() {
    return dispatch => {
        dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: true, loadingMessage: "Loading outlets." } });
        RestService.getJSON(apis.getUrls().posMetaData.allUnits, [{ category: "CAFE" }]).then((response) => {
            if (response != null && response.length > 0) {
                response.sort(compare);

                dispatch({ type: "SET_OUTLET_LIST", payload: response });
                dispatch(setSelectedOutlet(response[0]));
            } else {
                dispatch({
                    type: "SET_LOGIN_ERROR",
                    payload: { loginError: true, loginErrorMsg: "Error loading outlets list." }
                });
            }
            dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
        }).catch((error) => {
            dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
            dispatch({
                type: "SET_LOGIN_ERROR",
                payload: { loginError: true, loginErrorMsg: "Error loading outlets list." }
            });
        });
    }
}

export function fetchUserOutlets(userId) {
    return dispatch => {
        dispatch({
            type: "SET_LOGIN_ERROR",
            payload: { loginError: false}
        });
        dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: true, loadingMessage: "Loading outlets." } });
        let reqObj = {
            employeeId: parseInt(userId),
            onlyActive: true
        };
        RestService.postJSON(apis.getUrls().posMetaData.userUnits, reqObj).then((response) => {
            if (response != null && response.length > 0) {
                response.sort(compare);
                let outletList = response.filter(outlets => outlets.category === "CAFE");
                dispatch({ type: "SET_OUTLET_LIST", payload: outletList });
                dispatch(setSelectedOutlet(outletList[0]));
            } else {
                dispatch({
                    type: "SET_LOGIN_ERROR",
                    payload: { loginError: true, loginErrorMsg: "Error loading outlets list." }
                });
            }
            dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
        }).catch((error) => {
            dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
            dispatch({
                type: "SET_LOGIN_ERROR",
                payload: { loginError: true, loginErrorMsg: "Error loading outlets list." }
            });
        });
    }
}

export function changePassword(props) {
    return dispatch => {
        RestService.postJSON(apis.getUrls().users.changePassCode, props.changePasswordObj).then((response) => {
            if (response) {
                ToastAndroid.show('Passcode changed successfully!', ToastAndroid.LONG);
                dispatch({
                    type: 'SET_SHOW_CHANGE_PASSWORD_MODAL',
                    payload: false
                });
            } else {
                ToastAndroid.show('Please provide correct old passcode!', ToastAndroid.LONG);
            }
        }).catch((error) => {
            dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
            dispatch({
                type: "SET_LOGIN_ERROR",
                payload: { loginError: true, loginErrorMsg: "Error signing in. Please try again" }
            });
        });
    }
}

export function login(props) {
    return dispatch => {
        dispatch({
            type: "SET_LOGIN_ERROR",
            payload: { loginError: false, loginErrorMsg: "" }
        });
        if (props.userId == null) {
            Alert.alert('User Id invalid', 'Please fill user Id',
                [{
                    text: 'OK', onPress: () => {
                    }
                },], { cancelable: false })
        } else if (props.password == null) {
            Alert.alert('Password invalid', 'Please fill password',
                [{
                    text: 'OK', onPress: () => {
                    }
                },], { cancelable: false })
        } else {
            dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: true, loadingMessage: "Signing in please wait." } });
            const mac = DeviceInfo.getMACAddress();
            const reqObj = {
                unitId: (props.autoConfigData === null) ? props.selectedOutlet.id : props.autoConfigData.unitId,
                userId: props.userId,
                password: props.password,
                terminalId: (props.autoConfigData === null) ? props.selectedTerminal : props.autoConfigData.selectedTerminalId,
                screenType: "CUSTOMER",
                macAddress: "",
                application: "KETTLE_CRM"
            };
            RestService.postJSON(apis.getUrls().users.login, reqObj).then((response) => {
                if (response != null && response.jwtToken != null) {
                    //setting autoConfig
                    if (props.autoConfigData == null) {

                        const autoConfig = {
                            unitId: reqObj.unitId,
                            selectedTerminalId: reqObj.terminalId,
                            unitName: props.selectedOutlet.name,
                            region: props.selectedOutlet.region,
                            googleMerchantId: props.selectedOutlet.googleMerchantId
                        };
                        StorageService.setAutoConfigData(autoConfig);
                        dispatch({ type: 'SET_AUTO_CONFIG_DATA', payload: autoConfig });
                    }
                    const channelName = ConfigService.getPubnubEnv() + '_' + 'CustomerScreenChannel_' + reqObj.unitId + '_' + reqObj.terminalId;
                    const trueCallerChannelName = ConfigService.getPubnubEnv() + '_' + 'CustomerScreenTCChannel_' + reqObj.unitId + '_' + reqObj.terminalId;
                    dispatch({ type: 'SET_CHANNEL_NAME', payload: channelName });
                    dispatch({ type: 'SET_TRUE_CALLER_CHANNEL_NAME', payload: trueCallerChannelName });
                    UtilityService.setPubNubChannel(channelName);
                    UtilityService.setUnitId(reqObj.unitId);
                    UtilityService.setTerminalId(reqObj.terminalId);
                    console.log("while login outlet")
                    console.log(props.selectedOutlet)
                    UtilityService.setSelectedOutlet(props.selectedOutlet == null ? props.autoConfigData : props.selectedOutlet);
                    dispatch(getHomeScreenUrl(UtilityService.getUnitId(), props))
                    UtilityService.setTrueCallerChannel(trueCallerChannelName);
                    UtilityService.setIngenicoStatusChannel(ConfigService.getPubnubEnv() + '_INGENICO_PAY_' + reqObj.unitId + '#' + reqObj.terminalId);
                    dispatch({ type: 'SET_SELECTED_TERMINAL', payload: reqObj.terminalId });
                    dispatch({ type: 'SET_SELECTED_UNIT_ID', payload: reqObj.unitId });

                    dispatch(PubnubActions.subscribe(props));
                    dispatch(PubnubActions.publish("Logged into CRM"));

                    UtilityService.setAuthDetail(response.jwtToken);


                } else {
                    dispatch({
                        type: "SET_LOGIN_ERROR",
                        payload: { loginError: true, loginErrorMsg: "User id & Passcode don't match!" }
                    });
                }

            }).catch((error) => {
                dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
                dispatch({
                    type: "SET_LOGIN_ERROR",
                    payload: { loginError: true, loginErrorMsg: "Error signing in. Please try again" }
                });
            });
        }
    }
}

export function logout(props) {
    return dispatch => {

    }
}

export function setIsConnected(connected) {
    return dispatch => {
        UtilityService.setConnected(connected);
        dispatch({ type: 'SET_INTERNET_CONNECTED', payload: connected });
        if (connected == false) {
            NavigationService.navigate('ConnectionError');
        }
    }
}

export function resetConfig(props) {
    return dispatch => {
        if (props.autoConfigData != null) {
            StorageService.removeAutoConfigData();
            dispatch({ type: "SET_AUTO_CONFIG_DATA", payload: null });
            dispatch(resetLogin(props, true));
        }
    }
}

export function setFaceDetectionMode(faceDetectionMode) {
    return dispatch => {
        StorageService.setFaceDetectionMode(faceDetectionMode);
        dispatch({ type: "SET_FACE_DETECTION_MODE", payload: faceDetectionMode });
    }
}


// getUnitPartnerBrandMetadata(vm.unitId, 1, 1);


function getUnitPartnerBrandMetadata(unitId, partnerId, brandId) {
    const reqObj = {
        unitId: unitId,
        partnerId: partnerId,
        brandId: brandId
    }
    RestService.postJSON(apis.getUrls().brandMetaData.getUnitPartnerBrandMetadata, reqObj).then((response) => {
        if (response != null) {
            console.log(response);
            UtilityService.setUnitPartnerBrandMetaData(response);
        }
    }).catch((error) => {

    });
};


export function getHomeScreenUrl(unitId, props) {
    return dispatch => {
        RestService.getJSON(apis.getUrls().posMetaData.crmIdleScreenUrl + "?unitId=" + unitId).then((response) => {
            if (response != null) {
                console.log(response);
                UtilityService.setHomeScreenUrl(response);
                console.log("url data from service:", UtilityService.getHomeScreenUrl())
                getUnitPartnerBrandMetadata(UtilityService.getUnitId(), 1, 1);
                dispatch({ type: "SET_SHOW_LOADER", payload: { showLoader: false, loadingMessage: null } });
                props.navigation.navigate("OrderFlow");
            }
        }).catch((error) => {
            console.log("Error occured while fetching home screen url", error.response.data);
        });
    }
};