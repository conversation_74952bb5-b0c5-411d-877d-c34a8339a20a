export default function reducer(state = {
    otpHeadline: null,
    isModalVisible: false,
    otpSource: null,
    showLoader: false,
    loadingMessage: null
}, action) {

    switch (action.type) {
        case "SET_OTP_HEADLINE": {
            return {...state, otpHeadline: action.payload};
            break;
        }
        case "IS_MODAL_VISIBLE": {
            return {...state, isModalVisible: action.payload};
            break;
        }
        case "SET_SHOW_LOADER": {
            return {...state, showLoader: action.payload.showLoader, loadingMessage: action.payload.loadingMessage};
            break;
        }
        case "OTP_SOURCE": {
            return {...state, otpSource: action.payload};
            break;
        }
        default:
            return state;
    }
    return state;
}
