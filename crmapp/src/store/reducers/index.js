import { combineReducers } from "redux";
import loginReducer from "./LoginReducer";
import customerReducer from "./CustomerReducer";
import utilityReducer from "./UtilityReducer";
import redemptionReducer from "./RedemptionReducer";
import otpReducer from "./OtpReducer";
import emailReducer from "./EmailReducer";
import paymentReducer from "./PaymentReducer";
import faceRecognitionReducer from "./FaceRecognitionReducer";

export default combineReducers({
    loginReducer,
    customerReducer,
    utilityReducer,
    redemptionReducer,
    otpReducer,
    emailReducer,
    paymentReducer,
    faceRecognitionReducer
})
