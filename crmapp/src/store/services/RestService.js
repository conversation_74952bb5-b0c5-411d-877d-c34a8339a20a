import UtilityService from "./UtilityService";
import fetchIntercept from 'fetch-intercept';
import NavigationService from "./NavigationService";
import { Alert, ToastAndroid } from "react-native";
import RestServiceAxios from "./RestServiceAxios";

class RestService {

    constructor() {
        //this.launchInterceptor();
    }

    launchInterceptor() {
        fetchIntercept.register({
            request: function (url, config) {
                console.log("calling url:", url);
                if(url.indexOf("8081") < 0) {
                    if(UtilityService.getConnected() == false){
                        ToastAndroid.show('From rest service intercept request!', ToastAndroid.LONG);
                        NavigationService.navigate('ConnectionError');
                    } else {
                        if (UtilityService.getAuthDetail() != null) {
                            config.headers.auth = UtilityService.getAuthDetail();
                        }
                        return [url, config];
                    }
                }
            },

            requestError: function (error) {
                // Called when an error occured during another 'request' interceptor call
                console.log("request error:::", error);
                return Promise.reject(error);
            },

            response: function (response) {
                // Modify the response object
                console.log("response::::", response);
                if (response.status == 401) {
                    Alert.alert(
                        'Session expired',
                        'You session has expired. Please login again.',
                        [
                            {text: 'OK', onPress: () => {NavigationService.navigate('Login');}},
                        ],
                        { cancelable: false }
                    );

                }
                return response;
            },

            responseError: function (error) {
                // Handle an fetch error
                if (error.toString().indexOf("Network request failed") >= 0) {
                    //ToastAndroid.show('From rest service intercept response error!', ToastAndroid.LONG);
                    ToastAndroid.show(error, ToastAndroid.LONG);
                    NavigationService.navigate('ConnectionError');
                    return {status: 0};
                } else {
                    return Promise.reject(error);
                }
            }
        });
    }

    /*postJSON(url, data = {}, params = []) {
        console.log("POST:::::::::::::::", url, data, params);
        let paramString = "";
        params.map((param) => {
            let key = Object.keys(param)[0];
            let value = param[key];
            paramString = paramString + key + "=" + value + "&";
        });
        paramString = paramString.substring(0, paramString.length - 1);
        url = url + "?" + paramString;
        return fetch(url, {
            method: "POST",
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        }).then(response => {
            console.log('response is :: ', response);
            if (response && response.status && response.status != 401) {
                var contentType = response.headers.get("content-type");
                if(contentType && contentType.includes("text/plain")) {
                    return response.text();
                } else if(contentType && contentType.includes("application/json")) {
                    return response.json();
                } else {
                    return response.text();
                }
            } else {
                return null;
            }
        }).catch(error => {
            throw error
        });
    }


    getJSON(url, params = []) {
        console.log("GET:::::::::::::::", url, params);
        let paramString = "";
        params.map((param) => {
            let key = Object.keys(param)[0];
            let value = param[key];
            paramString = paramString + key + "=" + value + "&";
        });
        paramString = paramString.substring(0, paramString.length - 1);
        url = url + "?" + paramString;
        return fetch(url, {
            method: "GET",
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json'
            },
        }).then(response => {
            if (response && response.status && response.status != 401) {
                var contentType = response.headers.get("content-type");
                if(contentType && contentType.includes("text/plain")) {
                    return response.text();
                } else {
                    return response.json();
                }
            } else{
                return null;
            }
        }).catch(error => {
            throw error
        });
    }*/

    postJSON(url, data, params) {
        return RestServiceAxios.postJSON(url, data, params);
    }

    getJSON(url, params) {
        return RestServiceAxios.getJSON(url, params);
    }

}

const restService = new RestService();
export default restService;
