import UtilityService from "./UtilityService";
import fetchIntercept from 'fetch-intercept';
import NavigationService from "./NavigationService";
import {<PERSON><PERSON>, ToastAndroid} from "react-native";
import axios from "axios";

class RestService {

    constructor() {
        this.instance = axios.create({
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json;charset=UTF-8'
            },
            validateStatus: function (status) {
                return status === 200; // default
            }
        });
        this.launchInterceptor();
    }

    launchInterceptor() {
        // Add a request interceptor
        var _this = this;
        this.instance.interceptors.request.use(function (config) {
            //return config;
            if (UtilityService.getConnected() == false) {
                NavigationService.navigate('ConnectionError');
            } else {
                if (UtilityService.getAuthDetail() != null) {
                    config.headers.auth = UtilityService.getAuthDetail();
                    _this.instance.defaults.headers.common['auth'] = UtilityService.getAuthDetail();
                }
                return config;
            }
        }, function (error) {
            // Do something with request error
            return Promise.reject(error);
        });

        this.instance.interceptors.response.use(function (response) {
            // Do something with response data
            //return response;
            console.log("response::::", response);
            _this.handleUnauthorizedResponse(response);
            return response;
        }, function (error) {
            // Do something with response error
            _this.handleUnauthorizedResponse(error.response);
            if (error.toString().indexOf("Network request failed") >= 0) {
                NavigationService.navigate('ConnectionError');
                return {status: 0};
            } else {
                return Promise.reject(error);
            }
        });
    }

    postJSON(url, data = {}, params = []) {
        console.log("POST:::::::::::::::", url, data, params);
        let paramString = "";
        params.map((param) => {
            let key = Object.keys(param)[0];
            let value = param[key];
            paramString = paramString + key + "=" + value + "&";
        });
        paramString = paramString.substring(0, paramString.length - 1);
        if (paramString.length > 0) {
            url = url + "?" + paramString;
        }
        return this.instance({
            method: "POST",
            url: url,
            data: data
        }).then(function (response) {
            return response.data;
        });
    }

    getJSON(url, params = []) {
        console.log("GET:::::::::::::::", url, params);
        let paramString = "";
        params.map((param) => {
            let key = Object.keys(param)[0];
            let value = param[key];
            paramString = paramString + key + "=" + value + "&";
        });
        paramString = paramString.substring(0, paramString.length - 1);
        if (paramString.length > 0) {
            url = url + "?" + paramString;
        }
        return this.instance({
            method: "GET",
            url: url,
            data:null
        }).then(function (response) {
            return response.data;
        });
    }

    handleUnauthorizedResponse(response) {
        if (response.status == 401) {
            Alert.alert(
                'Session expired',
                'Your session has expired. Please login again.',
                [
                    {
                        text: 'OK', onPress: () => {
                            NavigationService.navigate('Login');
                        }
                    },
                ],
                {cancelable: false}
            );
            return response;
        }
    }

}

const restServiceAxios = new RestService();
export default restServiceAxios;
