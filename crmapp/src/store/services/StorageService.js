//import {AsyncStorage} from 'react-native';
import AsyncStorage from '@react-native-community/async-storage';

class StorageService {

    checkEmpty(obj) {
        if (obj === undefined || obj === null || obj === {}) {
            return true;
        }
        if (typeof obj === "string" || typeof obj === "number") {
            return obj.toString().trim().length === 0;
        }
        for (var key in obj) {
            if (hasOwnProperty.call(obj, key)) return false;
        }
        return true;
    }


     getAsyncStoreItem = async (key) => {
        try {
            return await AsyncStorage.getItem(key);
        } catch (error) {
            // Error retrieving data
            console.log(error.message);
            return null;
        }
    };

    setAsyncStoreItem = async (key, value) => {
        try {
            if (value != null) {
                await AsyncStorage.setItem(key, JSON.stringify(value));
            }
        } catch (error) {
            // Error saving data
        }
    };

    removeAsyncStoreItem = async(key) => {
        try {
            await AsyncStorage.removeItem(key);
        } catch (error) {
            // Error removing data
        }
    };

    setAsyncStoreItemString = async(key, value) => {
        try {
            if (value != null) {
                await AsyncStorage.setItem(key, value);
            }
        } catch (error) {
            // Error saving data
        }
    };

    async getAutoConfigData () {
        return this.getAsyncStoreItem("acd");
    }

    setAutoConfigData(data) {
        this.setAsyncStoreItem("acd", data).then(function (response) {
        });
    }

    removeAutoConfigData() {
        this.removeAsyncStoreItem("acd").then(function (response) {
        });
    }

    async getFaceDetectionMode () {
        return this.getAsyncStoreItem("fdm");
    }

    setFaceDetectionMode(data) {
        this.setAsyncStoreItem("fdm", data).then(function (response) {
        });
    }

    removeFaceDetectionMode() {
        this.removeAsyncStoreItem("fdm").then(function (response) {
        });
    }

}

const storageService = new StorageService();
export default storageService;
