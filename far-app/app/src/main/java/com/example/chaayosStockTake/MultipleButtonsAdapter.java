package com.example.chaayosStockTake;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.example.chaayosStockTake.domain.SubCategoryFlattenData;

import java.util.List;
import java.util.Objects;

public class MultipleButtonsAdapter extends RecyclerView.Adapter<ButtonViewHolder>{
    private List<SubCategoryFlattenData> buttonDataList;
    private final Context context;

    public MultipleButtonsAdapter(List<SubCategoryFlattenData> buttonDataList, DayCloseSubCategorySelection dayCloseSubCategorySelectionClass) {
        this.buttonDataList = buttonDataList;
        this.context = dayCloseSubCategorySelectionClass;
    }

    @Override
    public ButtonViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.day_close_subcategory_button, parent, false);
        return new ButtonViewHolder(view, this.context);
    }

    @Override
    public void onBindViewHolder(ButtonViewHolder holder, int position) {
        SubCategoryFlattenData flattenData = buttonDataList.get(position);
        holder.getCreateBy().setText(Objects.nonNull(flattenData.getCompletedBy()) ? flattenData.getCompletedBy() : "-");
        holder.getSubCategoryName().setText(Objects.nonNull(flattenData.getSubCategoryName()) ? flattenData.getSubCategoryName() : "-");
        String subEventStatus = Objects.nonNull(flattenData.getStatus()) ? flattenData.getStatus() : "Not Started";
        holder.getSubEventStatus().setText(subEventStatus);
        if (subEventStatus.equalsIgnoreCase("Not Started")) {
            holder.getConstraintLayout().setBackgroundResource(R.drawable.rounded_shape_red_fill);
            holder.getStartButton().setText("START");
            holder.getStartButton().setVisibility(View.VISIBLE);
            holder.getSubEventStatus().setVisibility(View.INVISIBLE);
        } else if (subEventStatus.equalsIgnoreCase("IN_PROCESS")) {
            holder.getConstraintLayout().setBackgroundResource(R.drawable.rounded_shape_yellow_fill);
        } else {
            holder.getConstraintLayout().setBackgroundResource(R.drawable.rounded_shape_green_fill);
        }
    }

    @Override
    public int getItemCount() {
        return buttonDataList.size();
    }
}
