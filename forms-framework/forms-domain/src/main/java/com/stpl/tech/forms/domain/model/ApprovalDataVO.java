package com.stpl.tech.forms.domain.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelSheet(value = "Approval Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class ApprovalDataVO {

    @ExcelField
    private String cardNumber;

    @ExcelField
    private String cr;

    @ExcelField
    private Integer amount;

    @ExcelField
    private Integer claimExpenseId;

    @ExcelField
    private String corporateId;

    @ExcelField
    private String expenseSettlementId;
}
