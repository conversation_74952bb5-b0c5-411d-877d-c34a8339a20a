//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.22 at 06:06:04 PM IST 
//


package com.stpl.tech.forms.domain.model;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for AuditValues complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AuditValues"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="auditFormValue" type="{http://www.w3schools.com}AuditFormValues"/&gt;
 *         &lt;element name="employeeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="employeeName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="employeeDesignation" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option1Marks" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="option2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option2Marks" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="option3" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option3Marks" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="option4" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option4Marks" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="option5" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option5Marks" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="option6" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option6Marks" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="option7" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="option7Marks" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="textArea" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="yesNo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="dateValue" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="timeValue" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="numberValue" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="textValue" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="answerComment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attachedDoc" type="{http://www.w3schools.com}DocumentDetail"/&gt;
 *         &lt;element name="maxScore" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="acquiredScore" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="questionOpted" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AuditValues", propOrder = {
    "id",
    "auditFormValue",
    "employeeId",
    "employeeName",
    "employeeDesignation",
    "unitId",
    "unitName",
    "productId",
    "productName",
    "option1",
    "option1Marks",
    "option2",
    "option2Marks",
    "option3",
    "option3Marks",
    "option4",
    "option4Marks",
    "option5",
    "option5Marks",
    "option6",
    "option6Marks",
    "option7",
    "option7Marks",
    "textArea",
    "yesNo",
    "dateValue",
    "timeValue",
    "numberValue",
    "textValue",
    "answerComment",
    "attachedDoc",
    "maxScore",
    "acquiredScore",
    "questionOpted"
})
public class AuditValues {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    protected AuditFormValues auditFormValue;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer employeeId;
    @XmlElement(required = true, nillable = true)
    protected String employeeName;
    @XmlElement(required = true, nillable = true)
    protected String employeeDesignation;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer unitId;
    @XmlElement(required = true, nillable = true)
    protected String unitName;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer productId;
    @XmlElement(required = true, nillable = true)
    protected String productName;
    @XmlElement(required = true, nillable = true)
    protected String option1;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal option1Marks;
    @XmlElement(required = true, nillable = true)
    protected String option2;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal option2Marks;
    @XmlElement(required = true, nillable = true)
    protected String option3;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal option3Marks;
    @XmlElement(required = true, nillable = true)
    protected String option4;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal option4Marks;
    @XmlElement(required = true, nillable = true)
    protected String option5;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal option5Marks;
    @XmlElement(required = true, nillable = true)
    protected String option6;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal option6Marks;
    @XmlElement(required = true, nillable = true)
    protected String option7;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal option7Marks;
    @XmlElement(required = true, nillable = true)
    protected String textArea;
    @XmlElement(required = true, nillable = true)
    protected String yesNo;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date dateValue;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "time")
    protected Time timeValue;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer numberValue;
    @XmlElement(required = true, nillable = true)
    protected String textValue;
    @XmlElement(required = true, nillable = true)
    protected String answerComment;
    @XmlElement(required = true, nillable = true)
    protected DocumentDetail attachedDoc;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal maxScore;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal acquiredScore;
    @XmlElement(defaultValue = "true")
    protected boolean questionOpted;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the auditFormValue property.
     * 
     * @return
     *     possible object is
     *     {@link AuditFormValues }
     *     
     */
    public AuditFormValues getAuditFormValue() {
        return auditFormValue;
    }

    /**
     * Sets the value of the auditFormValue property.
     * 
     * @param value
     *     allowed object is
     *     {@link AuditFormValues }
     *     
     */
    public void setAuditFormValue(AuditFormValues value) {
        this.auditFormValue = value;
    }

    /**
     * Gets the value of the employeeId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getEmployeeId() {
        return employeeId;
    }

    /**
     * Sets the value of the employeeId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setEmployeeId(Integer value) {
        this.employeeId = value;
    }

    /**
     * Gets the value of the employeeName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getEmployeeName() {
        return employeeName;
    }

    /**
     * Sets the value of the employeeName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setEmployeeName(String value) {
        this.employeeName = value;
    }

    /**
     * Gets the value of the employeeDesignation property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getEmployeeDesignation() {
        return employeeDesignation;
    }

    /**
     * Sets the value of the employeeDesignation property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setEmployeeDesignation(String value) {
        this.employeeDesignation = value;
    }

    /**
     * Gets the value of the unitId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setUnitId(Integer value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the unitName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUnitName() {
        return unitName;
    }

    /**
     * Sets the value of the unitName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUnitName(String value) {
        this.unitName = value;
    }

    /**
     * Gets the value of the productId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setProductId(Integer value) {
        this.productId = value;
    }

    /**
     * Gets the value of the productName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Sets the value of the productName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setProductName(String value) {
        this.productName = value;
    }

    /**
     * Gets the value of the option1 property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption1() {
        return option1;
    }

    /**
     * Sets the value of the option1 property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption1(String value) {
        this.option1 = value;
    }

    /**
     * Gets the value of the option1Marks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getOption1Marks() {
        return option1Marks;
    }

    /**
     * Sets the value of the option1Marks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption1Marks(BigDecimal value) {
        this.option1Marks = value;
    }

    /**
     * Gets the value of the option2 property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption2() {
        return option2;
    }

    /**
     * Sets the value of the option2 property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption2(String value) {
        this.option2 = value;
    }

    /**
     * Gets the value of the option2Marks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getOption2Marks() {
        return option2Marks;
    }

    /**
     * Sets the value of the option2Marks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption2Marks(BigDecimal value) {
        this.option2Marks = value;
    }

    /**
     * Gets the value of the option3 property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption3() {
        return option3;
    }

    /**
     * Sets the value of the option3 property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption3(String value) {
        this.option3 = value;
    }

    /**
     * Gets the value of the option3Marks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getOption3Marks() {
        return option3Marks;
    }

    /**
     * Sets the value of the option3Marks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption3Marks(BigDecimal value) {
        this.option3Marks = value;
    }

    /**
     * Gets the value of the option4 property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption4() {
        return option4;
    }

    /**
     * Sets the value of the option4 property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption4(String value) {
        this.option4 = value;
    }

    /**
     * Gets the value of the option4Marks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getOption4Marks() {
        return option4Marks;
    }

    /**
     * Sets the value of the option4Marks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption4Marks(BigDecimal value) {
        this.option4Marks = value;
    }

    /**
     * Gets the value of the option5 property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption5() {
        return option5;
    }

    /**
     * Sets the value of the option5 property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption5(String value) {
        this.option5 = value;
    }

    /**
     * Gets the value of the option5Marks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getOption5Marks() {
        return option5Marks;
    }

    /**
     * Sets the value of the option5Marks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption5Marks(BigDecimal value) {
        this.option5Marks = value;
    }

    /**
     * Gets the value of the option6 property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption6() {
        return option6;
    }

    /**
     * Sets the value of the option6 property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption6(String value) {
        this.option6 = value;
    }

    /**
     * Gets the value of the option6Marks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getOption6Marks() {
        return option6Marks;
    }

    /**
     * Sets the value of the option6Marks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption6Marks(BigDecimal value) {
        this.option6Marks = value;
    }

    /**
     * Gets the value of the option7 property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOption7() {
        return option7;
    }

    /**
     * Sets the value of the option7 property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption7(String value) {
        this.option7 = value;
    }

    /**
     * Gets the value of the option7Marks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getOption7Marks() {
        return option7Marks;
    }

    /**
     * Sets the value of the option7Marks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOption7Marks(BigDecimal value) {
        this.option7Marks = value;
    }

    /**
     * Gets the value of the textArea property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTextArea() {
        return textArea;
    }

    /**
     * Sets the value of the textArea property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTextArea(String value) {
        this.textArea = value;
    }

    /**
     * Gets the value of the yesNo property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getYesNo() {
        return yesNo;
    }

    /**
     * Sets the value of the yesNo property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setYesNo(String value) {
        this.yesNo = value;
    }

    /**
     * Gets the value of the dateValue property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getDateValue() {
        return dateValue;
    }

    /**
     * Sets the value of the dateValue property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDateValue(Date value) {
        this.dateValue = value;
    }

    /**
     * Gets the value of the timeValue property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Time getTimeValue() {
        return timeValue;
    }

    /**
     * Sets the value of the timeValue property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTimeValue(Time value) {
        this.timeValue = value;
    }

    /**
     * Gets the value of the numberValue property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getNumberValue() {
        return numberValue;
    }

    /**
     * Sets the value of the numberValue property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setNumberValue(Integer value) {
        this.numberValue = value;
    }

    /**
     * Gets the value of the textValue property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTextValue() {
        return textValue;
    }

    /**
     * Sets the value of the textValue property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTextValue(String value) {
        this.textValue = value;
    }

    /**
     * Gets the value of the answerComment property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getAnswerComment() {
        return answerComment;
    }

    /**
     * Sets the value of the answerComment property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAnswerComment(String value) {
        this.answerComment = value;
    }

    /**
     * Gets the value of the attachedDoc property.
     *
     * @return
     *     possible object is
     *     {@link DocumentDetail }
     *
     */
    public DocumentDetail getAttachedDoc() {
        return attachedDoc;
    }

    /**
     * Sets the value of the attachedDoc property.
     *
     * @param value
     *     allowed object is
     *     {@link DocumentDetail }
     *
     */
    public void setAttachedDoc(DocumentDetail value) {
        this.attachedDoc = value;
    }

    /**
     * Gets the value of the maxScore property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getMaxScore() {
        return maxScore;
    }

    /**
     * Sets the value of the maxScore property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setMaxScore(BigDecimal value) {
        this.maxScore = value;
    }

    /**
     * Gets the value of the acquiredScore property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public BigDecimal getAcquiredScore() {
        return acquiredScore;
    }

    /**
     * Sets the value of the acquiredScore property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAcquiredScore(BigDecimal value) {
        this.acquiredScore = value;
    }

    /**
     * Gets the value of the questionOpted property.
     *
     */
    public boolean isQuestionOpted() {
        return questionOpted;
    }

    /**
     * Sets the value of the questionOpted property.
     *
     */
    public void setQuestionOpted(boolean value) {
        this.questionOpted = value;
    }

}
