package com.stpl.tech.forms.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BusinessCostCenters implements Serializable {

    private static final long serialVersionUID = -6473171962841366346L;
    protected Integer id;
    protected String name;
    protected String code;
    protected String type;
    protected IdCodeName location;
    protected IdCodeName company;
    protected String status;
    private IdCodeName state;
}
