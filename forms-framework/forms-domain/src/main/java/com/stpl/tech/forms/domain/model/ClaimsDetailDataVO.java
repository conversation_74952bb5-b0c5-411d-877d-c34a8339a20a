package com.stpl.tech.forms.domain.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelSheet(value = "Claims Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class ClaimsDetailDataVO {

    @ExcelField
    private String cardNumber;

    @ExcelField
    private String cr;

    @ExcelField
    private BigDecimal amount;

    @ExcelField
    private Integer claimExpenseId;

    @ExcelField
    private String corporateId;
}
