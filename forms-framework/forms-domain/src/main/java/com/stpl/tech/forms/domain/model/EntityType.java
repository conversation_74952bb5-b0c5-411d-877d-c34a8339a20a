//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.17 at 05:17:52 PM IST 
//


package com.stpl.tech.forms.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for EntityType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="EntityType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="QUESTION_GROUP"/&gt;
 *     &lt;enumeration value="TEXT"/&gt;
 *     &lt;enumeration value="HEADING"/&gt;
 *     &lt;enumeration value="SUBHEADING"/&gt;
 *     &lt;enumeration value="TEXTAREA"/&gt;
 *     &lt;enumeration value="BOOLEAN"/&gt;
 *     &lt;enumeration value="PRODUCT"/&gt;
 *     &lt;enumeration value="SELECT_BOX"/&gt;
 *     &lt;enumeration value="SNIPPET"/&gt;
 *     &lt;enumeration value="DATE"/&gt;
 *     &lt;enumeration value="TIME"/&gt;
 *     &lt;enumeration value="UNIT_EMPLOYEE"/&gt;
 *     &lt;enumeration value="UNIT"/&gt;
 *     &lt;enumeration value="NUMBER"/&gt;
 *     &lt;enumeration value="RADIO"/&gt;
 *     &lt;enumeration value="PRODUCT_EMPLOYEE_RADIO"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "EntityType")
@XmlEnum
public enum EntityType {

    @XmlEnumValue("QUESTION_GROUP")
    QUESTION_GROUP("QUESTION_GROUP"),
    TEXT("TEXT"),
    HEADING("HEADING"),
    SUBHEADING("SUBHEADING"),
    TEXTAREA("TEXTAREA"),
    BOOLEAN("BOOLEAN"),
    PRODUCT("PRODUCT"),
    @XmlEnumValue("SELECT_BOX")
    SELECT_BOX("SELECT_BOX"),
    SNIPPET("SNIPPET"),
    DATE("DATE"),
    TIME("TIME"),
    @XmlEnumValue("UNIT_EMPLOYEE")
    UNIT_EMPLOYEE("UNIT_EMPLOYEE"),
    UNIT("UNIT"),
    NUMBER("NUMBER"),
    RADIO("RADIO"),
    @XmlEnumValue("PRODUCT_EMPLOYEE_RADIO")
    PRODUCT_EMPLOYEE_RADIO("PRODUCT_EMPLOYEE_RADIO"),
    @XmlEnumValue("TEXTAREA_EMPLOYEE_RADIO")
    TEXTAREA_EMPLOYEE_RADIO("TEXTAREA_EMPLOYEE_RADIO");
    private final String value;

    EntityType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static EntityType fromValue(String v) {
        for (EntityType c: EntityType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
