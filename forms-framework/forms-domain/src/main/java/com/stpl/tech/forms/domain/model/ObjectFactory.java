//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.17 at 05:17:52 PM IST 
//


package com.stpl.tech.forms.domain.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.audit.domain.model package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.audit.domain.model
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link IdCodeName }
     * 
     */
    public IdCodeName createIdCodeName() {
        return new IdCodeName();
    }

    /**
     * Create an instance of {@link AuditForm }
     * 
     */
    public AuditForm createAuditForm() {
        return new AuditForm();
    }

    /**
     * Create an instance of {@link AuditFormValues }
     * 
     */
    public AuditFormValues createAuditFormValues() {
        return new AuditFormValues();
    }

}
