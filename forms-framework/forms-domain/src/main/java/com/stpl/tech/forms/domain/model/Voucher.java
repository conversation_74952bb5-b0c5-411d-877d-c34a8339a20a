package com.stpl.tech.forms.domain.model;

import com.stpl.tech.expense.domain.model.VoucherRejection;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Voucher {

    private Integer id;
    private String generatedVoucherId;
    private WalletAccountType accountType;
    private String accountNo;
    private Integer walletId;
    private IdCodeName wallet;
    private Date businessDate;
    private Integer expenseId;
    private String expenseType;
    private String expenseDetail;
    private VoucherStatus currentStatus;
    private BigDecimal issuedAmount;
    private BigDecimal expenseAmount;
    private IdCodeName issuedTo;
    private Date issuedTime;
    private IdCodeName issuedBy;
    private Date lastUpdatedTime;
    private Integer expenseMetadataId;
    private String expenseCategory;
    private Boolean accountableInPNL;
    private String budgetCategory;
    private Integer pnlDetailId;
    private Date pnlInclusionDate;
    private Integer grNumber;

    private List<VoucherCostCenterAllocation> voucherCostCenterAllocations;
    /**
     * This action comment will set into VoucherStatusData action comment
     */
    private String actionComment;
    /**
     * This entity will set into WalletTransactionData entity
     */
    private IssuingEntity entity;
    private Boolean isReimbursed;
    private List<VoucherFileDetail> fileDetails = new ArrayList<>();
    private List<VoucherStatusDetail> statusDetail = new ArrayList<>();
    private List<WalletTransaction> transactions = new ArrayList<>();
    private List<VoucherRejection> rejections = new ArrayList<>();
    private Date voucherDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGeneratedVoucherId() {
        return generatedVoucherId;
    }

    public void setGeneratedVoucherId(String generatedVoucherId) {
        this.generatedVoucherId = generatedVoucherId;
    }

    public WalletAccountType getAccountType() {
        return accountType;
    }

    public void setAccountType(WalletAccountType accountType) {
        this.accountType = accountType;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public Integer getWalletId() {
        return walletId;
    }

    public void setWalletId(Integer walletId) {
        this.walletId = walletId;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Integer getExpenseId() {
        return expenseId;
    }

    public void setExpenseId(Integer expenseId) {
        this.expenseId = expenseId;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getExpenseDetail() {
        return expenseDetail;
    }

    public void setExpenseDetail(String expenseDetail) {
        this.expenseDetail = expenseDetail;
    }

    public VoucherStatus getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(VoucherStatus currentStatus) {
        this.currentStatus = currentStatus;
    }

    public BigDecimal getIssuedAmount() {
        return issuedAmount;
    }

    public void setIssuedAmount(BigDecimal issuedAmount) {
        this.issuedAmount = issuedAmount;
    }

    public BigDecimal getExpenseAmount() {
        return expenseAmount;
    }

    public void setExpenseAmount(BigDecimal expenseAmount) {
        this.expenseAmount = expenseAmount;
    }

    public IdCodeName getIssuedTo() {
        return issuedTo;
    }

    public void setIssuedTo(IdCodeName issuedTo) {
        this.issuedTo = issuedTo;
    }

    public Date getIssuedTime() {
        return issuedTime;
    }

    public void setIssuedTime(Date issuedTime) {
        this.issuedTime = issuedTime;
    }

    public IdCodeName getIssuedBy() {
        return issuedBy;
    }

    public void setIssuedBy(IdCodeName issuedBy) {
        this.issuedBy = issuedBy;
    }

    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public String getActionComment() {
        return actionComment;
    }

    public void setActionComment(String actionComment) {
        this.actionComment = actionComment;
    }

    public List<VoucherFileDetail> getFileDetails() {
        return fileDetails;
    }

    public void setFileDetails(List<VoucherFileDetail> fileDetails) {
        this.fileDetails = fileDetails;
    }

    public List<VoucherStatusDetail> getStatusDetail() {
        return statusDetail;
    }

    public void setStatusDetail(List<VoucherStatusDetail> statusDetail) {
        this.statusDetail = statusDetail;
    }

    public List<WalletTransaction> getTransactions() {
        return transactions;
    }

    public void setTransactions(List<WalletTransaction> transactions) {
        this.transactions = transactions;
    }

    public IssuingEntity getEntity() {
        return entity;
    }

    public void setEntity(IssuingEntity entity) {
        this.entity = entity;
    }

    public Boolean getIsReimbursed() {
        return isReimbursed;
    }

    public void setIsReimbursed(Boolean isReimbursed) {
        this.isReimbursed = isReimbursed;
    }

    public IdCodeName getWallet() {
        return wallet;
    }

    public void setWallet(IdCodeName wallet) {
        this.wallet = wallet;
    }

    public List<VoucherRejection> getRejections() {
        return rejections;
    }

    public void setRejections(List<VoucherRejection> rejections) {
        this.rejections = rejections;
    }

    public Integer getExpenseMetadataId() {
        return expenseMetadataId;
    }

    public void setExpenseMetadataId(Integer expenseMetadataId) {
        this.expenseMetadataId = expenseMetadataId;
    }

    public String getExpenseCategory() {
        return expenseCategory;
    }

    public void setExpenseCategory(String expenseCategory) {
        this.expenseCategory = expenseCategory;
    }

    public Boolean getAccountableInPNL() {
        return accountableInPNL;
    }

    public void setAccountableInPNL(Boolean accountableInPNL) {
        this.accountableInPNL = accountableInPNL;
    }

    public String getBudgetCategory() {
        return budgetCategory;
    }

    public void setBudgetCategory(String budgetCategory) {
        this.budgetCategory = budgetCategory;
    }

    public Integer getPnlDetailId() {
        return pnlDetailId;
    }

    public void setPnlDetailId(Integer pnlDetailId) {
        this.pnlDetailId = pnlDetailId;
    }

    public Date getPnlInclusionDate() {
        return pnlInclusionDate;
    }

    public void setPnlInclusionDate(Date pnlInclusionDate) {
        this.pnlInclusionDate = pnlInclusionDate;
    }

    public List<VoucherCostCenterAllocation> getVoucherCostCenterAllocations() {
        return voucherCostCenterAllocations;
    }

    public void setVoucherCostCenterAllocations(List<VoucherCostCenterAllocation> voucherCostCenterAllocations) {
        this.voucherCostCenterAllocations = voucherCostCenterAllocations;
    }

    public Integer getGrNumber() {
        return grNumber;
    }

    public void setGrNumber(Integer grNumber) {
        this.grNumber = grNumber;
    }

    public Date getVoucherDate() {
        return voucherDate;
    }

    public void setVoucherDate(Date voucherDate) {
        this.voucherDate = voucherDate;
    }

}
