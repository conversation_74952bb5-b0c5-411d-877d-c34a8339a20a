package com.stpl.tech.forms.domain.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class Wallet {

	private Integer id;
	private WalletAccountType accountType;
	private String accountNo;
	private String accountHolderName;
	private String accountHolderContact;
	private String accountHolderEmail;
	private WalletType walletType;
	private BigDecimal openingAmount;
	private BigDecimal totalAmount;
	private BigDecimal currentBalance;
	private BigDecimal issuedAmount;
	private BigDecimal pendingApproval;
	private BigDecimal approvedAmount;
	private BigDecimal rejectedAmount;
	private BigDecimal spentAmount;
	private BigDecimal topupAmount;
	private Date lastUpdateTime;
	private WalletStatus status;
	private IdCodeName createdBy;
	private boolean canAllocateCostToCafes;
	private List<BusinessCostCenters> businessCostCentersList;
	private String lastUpdatedBy;
	private Integer associatedId;
	private String ifscCode;
	private String cardNumber;
	private String bankName;
	private List<Integer> approverList;
	private String cardHolderName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public WalletAccountType getAccountType() {
		return accountType;
	}

	public void setAccountType(WalletAccountType accountType) {
		this.accountType = accountType;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public String getAccountHolderName() {
		return accountHolderName;
	}

	public void setAccountHolderName(String accountHolderName) {
		this.accountHolderName = accountHolderName;
	}

	public String getAccountHolderContact() {
		return accountHolderContact;
	}

	public void setAccountHolderContact(String accountHolderContact) {
		this.accountHolderContact = accountHolderContact;
	}

	public String getAccountHolderEmail() {
		return accountHolderEmail;
	}

	public void setAccountHolderEmail(String accountHolderEmail) {
		this.accountHolderEmail = accountHolderEmail;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public WalletType getWalletType() {
		return walletType;
	}

	public void setWalletType(WalletType walletType) {
		this.walletType = walletType;
	}

	public BigDecimal getOpeningAmount() {
		return openingAmount;
	}

	public void setOpeningAmount(BigDecimal openingAmount) {
		this.openingAmount = openingAmount;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getCurrentBalance() {
		return currentBalance;
	}

	public void setCurrentBalance(BigDecimal currentBalance) {
		this.currentBalance = currentBalance;
	}

	public BigDecimal getIssuedAmount() {
		return issuedAmount;
	}

	public void setIssuedAmount(BigDecimal issuedAmount) {
		this.issuedAmount = issuedAmount;
	}

	public BigDecimal getPendingApproval() {
		return pendingApproval;
	}

	public void setPendingApproval(BigDecimal pendingApproval) {
		this.pendingApproval = pendingApproval;
	}

	public BigDecimal getApprovedAmount() {
		return approvedAmount;
	}

	public void setApprovedAmount(BigDecimal approvedAmount) {
		this.approvedAmount = approvedAmount;
	}

	public BigDecimal getRejectedAmount() {
		return rejectedAmount;
	}

	public void setRejectedAmount(BigDecimal rejectedAmount) {
		this.rejectedAmount = rejectedAmount;
	}

	public BigDecimal getSpentAmount() {
		return spentAmount;
	}

	public void setSpentAmount(BigDecimal spentAmount) {
		this.spentAmount = spentAmount;
	}

	public BigDecimal getTopupAmount() {
		return topupAmount;
	}

	public void setTopupAmount(BigDecimal topupAmount) {
		this.topupAmount = topupAmount;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public WalletStatus getStatus() {
		return status;
	}

	public void setStatus(WalletStatus status) {
		this.status = status;
	}

	public IdCodeName getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(IdCodeName createdBy) {
		this.createdBy = createdBy;
	}

	public boolean isCanAllocateCostToCafes() {
		return canAllocateCostToCafes;
	}

	public void setCanAllocateCostToCafes(boolean canAllocateCostToCafes) {
		this.canAllocateCostToCafes = canAllocateCostToCafes;
	}

	public List<BusinessCostCenters> getBusinessCostCentersList() {
		return businessCostCentersList;
	}

	public void setBusinessCostCentersList(List<BusinessCostCenters> businessCostCentersList) {
		this.businessCostCentersList = businessCostCentersList;
	}

	public String getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(String lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	public Integer getAssociatedId() {
		return associatedId;
	}

	public void setAssociatedId(Integer associatedId) {
		this.associatedId = associatedId;
	}

	public String getIfscCode() {
		return ifscCode;
	}

	public void setIfscCode(String ifscCode) {
		this.ifscCode = ifscCode;
	}

	public String getCardNumber() {
		return cardNumber;
	}

	public void setCardNumber(String cardNumber) {
		this.cardNumber = cardNumber;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public List<Integer> getApproverList() {
		return approverList;
	}

	public void setApproverListe(List<Integer> approverList) {
		this.approverList = approverList;
	}

	public String getCardHolderName() {
		return cardHolderName;
	}

	public void setCardHolderName(String cardHolderName) {
		this.cardHolderName = cardHolderName;
	}
}
