//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.03.26 at 02:35:38 PM IST 
//


package com.stpl.tech.warning.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.forms.domain.model.Adapter3;


/**
 * <p>Java class for EmployeeWarningReason complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="EmployeeWarningReason"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="warningId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="warningStatusId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="reasonId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="reasonName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="category" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="reasonAddedBy" type="{http://www.w3schools.com}ReasonOwner"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="updateRequired" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="updatedOn" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EmployeeWarningReason", propOrder = {
    "id",
    "warningId",
    "warningStatusId",
    "reasonId",
    "reasonName",
    "category",
    "reasonAddedBy",
    "status",
    "updateRequired",
    "updatedOn"
})
public class EmployeeWarningReason {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer warningId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer warningStatusId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer reasonId;
    @XmlElement(required = true, nillable = true)
    protected String reasonName;
    @XmlElement(required = true, nillable = true)
    protected String category;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected ReasonOwner reasonAddedBy;
    @XmlElement(required = true, nillable = true)
    protected String status;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean updateRequired;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date updatedOn;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the warningId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getWarningId() {
        return warningId;
    }

    /**
     * Sets the value of the warningId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setWarningId(Integer value) {
        this.warningId = value;
    }

    /**
     * Gets the value of the warningStatusId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getWarningStatusId() {
        return warningStatusId;
    }

    /**
     * Sets the value of the warningStatusId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setWarningStatusId(Integer value) {
        this.warningStatusId = value;
    }

    /**
     * Gets the value of the reasonId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getReasonId() {
        return reasonId;
    }

    /**
     * Sets the value of the reasonId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setReasonId(Integer value) {
        this.reasonId = value;
    }

    /**
     * Gets the value of the reasonName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReasonName() {
        return reasonName;
    }

    /**
     * Sets the value of the reasonName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReasonName(String value) {
        this.reasonName = value;
    }

    /**
     * Gets the value of the category property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategory(String value) {
        this.category = value;
    }

    /**
     * Gets the value of the reasonAddedBy property.
     * 
     * @return
     *     possible object is
     *     {@link ReasonOwner }
     *     
     */
    public ReasonOwner getReasonAddedBy() {
        return reasonAddedBy;
    }

    /**
     * Sets the value of the reasonAddedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link ReasonOwner }
     *     
     */
    public void setReasonAddedBy(ReasonOwner value) {
        this.reasonAddedBy = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the updateRequired property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isUpdateRequired() {
        return updateRequired;
    }

    /**
     * Sets the value of the updateRequired property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setUpdateRequired(Boolean value) {
        this.updateRequired = value;
    }

    /**
     * Gets the value of the updatedOn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getUpdatedOn() {
        return updatedOn;
    }

    /**
     * Sets the value of the updatedOn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdatedOn(Date value) {
        this.updatedOn = value;
    }

}
