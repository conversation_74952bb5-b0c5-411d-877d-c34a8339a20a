//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.03.24 at 04:54:59 PM IST 
//

package com.stpl.tech.warning.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for WarningStage.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="WarningStage"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="PENDING_AM_APPROVAL"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "WarningStage")
@XmlEnum
public enum WarningStage {

	BY_AUDIT, BY_AM, PENDING_AM_APPROVAL, PENDING_DGM_APPROVAL, PENDING_HR_APPROVAL_FOR_ACCEPT, PENDING_HR_APPROVAL_FOR_REJECT, HR_ACCEPT, HR_REJECT , CANCEL

}
