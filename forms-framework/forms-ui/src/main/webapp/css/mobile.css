/************************* loader styles *********************/.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: inline-block;
    -webkit-animation: spin 1s linear infinite; /* Safari */
    animation: spin 1s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loader.xxs {
    border: 1px solid #f3f3f3;
    border-top: 1px solid #3498db;
    width: 10px;
    height: 10px;
}

.loader.xs {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    width: 15px;
    height: 15px;
}

.loader.s {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    width: 20px;
    height: 20px;
}

.loader.l {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    width: 40px;
    height: 40px;
}

.loader.xl {
    border: 10px solid #f3f3f3;
    border-top: 10px solid #3498db;
    width: 60px;
    height: 60px;
}

.loader.xxl {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
}

.loader.primary {
    border: 2px solid #204d74;
    border-top: 2px solid #fff;
}

.loader.lfast {
    -webkit-animation: spin .5s linear infinite; /* Safari */
    animation: spin .5s linear infinite;
}

.loader.lslow {
    -webkit-animation: spin 2s linear infinite; /* Safari */
    animation: spin 2s linear infinite;
}

.rootLoader{
    position: fixed;
    top:0;
    right: 0;
    bottom:0;
    left: 0;
    z-index: 9999;
    background: rgba(0,0,0,0.3);
}

/************************* mobile styles *********************/
.checkbox, .radio {
    margin-top: 20px;
    margin-bottom: 20px;
}
.checkbox input[type=checkbox], .checkbox-inline input[type=checkbox], .radio input[type=radio], .radio-inline input[type=radio] {
    margin-top: 1px;
}
.navbar{
    border: none;
    margin-bottom: 0;
}
.navbar-primary{
    background-color: #4CAF50;
}
.navbar-primary .navbar-brand{
    color: #fff;
    float: none;
    display: inline-block;
    padding:15px 5px;
}

.navbar-toggle {
    position: relative;
    padding: 5px 15px;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
    background-color: transparent;
    background-image: none;
    border: none;
    float: none;
    color: #fff;
}
.navbar-toggle:focus, .navbar-toggle:hover {
    background-color: transparent !important;
}
.sidebar{
    position: fixed;
    width: 250px;
     left: -256px;
    transition: all .5s;
    top: 0;
    bottom: 0;
    box-shadow: #000 0 0 5px 0;
}

.sidebar.open{
    left: 0;
}

.sidebar .sidebar-nav.navbar-collapse {
    padding-right: 0;
    padding-left: 0;
}

.sidebar .sidebar-search {
    padding: 15px;
}

.sidebar ul li {
    border-bottom: 1px solid #e7e7e7;
}

.sidebar ul li a.active {
    background-color: #eee;
}

.sidebar .arrow {
    float: right;
}

.sidebar .fa.arrow:before {
    content: "\f104";
}

.sidebar .active > a > .fa.arrow:before {
    content: "\f107";
}

.sidebar .nav-second-level li,
.sidebar .nav-third-level li {
    border-bottom: 0 !important;
}

.sidebar .nav-second-level li a {
    padding-left: 37px;
}

.sidebar .nav-third-level li a {
    padding-left: 52px;
}

.nav > li > a {
    cursor: pointer;
}

.nav > li > a span.arrow {
    text-align: right;
}

.nav > li > a .arrow:before {
    content: "";
}

.nav > li > a.active .arrow:before {
    content: "";
}

#backdrop{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    background: rgba(0,0,0,0.7);
}

#backdrop.open{
    display: block;
}

.btn{
    border-radius: 0;
}

.btn.active, .btn:active {
    background-image: none;
    outline: 0;
    box-shadow: none;
}

.btn-*{
    border:none;
}

.form-control{
    border: none;
    border-bottom: #ccc 2px solid;
    box-shadow: none;
    border-radius: 0;
}

.form-control:focus{
    border: none;
    border-bottom: #4CAF50 2px solid;
    box-shadow: none;
    border-radius: 0;
}

.nav a.legitRipple .legitRipple-ripple, .listItem.legitRipple .legitRipple-ripple{
    background: #ddd;
}