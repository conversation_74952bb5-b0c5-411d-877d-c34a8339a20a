.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: inline-block;
    -webkit-animation: spin 1s linear infinite; /* Safari */
    animation: spin 1s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loader.xxs {
    border: 1px solid #f3f3f3;
    border-top: 1px solid #3498db;
    width: 10px;
    height: 10px;
}

.loader.xs {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    width: 15px;
    height: 15px;
}

.loader.s {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    width: 20px;
    height: 20px;
}

.loader.l {
    border: 7px solid #f3f3f3;
    border-top: 7px solid #3498db;
    width: 40px;
    height: 40px;
}

.loader.xl {
    border: 10px solid #f3f3f3;
    border-top: 10px solid #3498db;
    width: 60px;
    height: 60px;
}

.loader.xxl {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
}

.loader.primary {
    border: 2px solid #204d74;
    border-top: 2px solid #fff;
}

.loader.lfast {
    -webkit-animation: spin .5s linear infinite; /* Safari */
    animation: spin .5s linear infinite;
}

.loader.lslow {
    -webkit-animation: spin 2s linear infinite; /* Safari */
    animation: spin 2s linear infinite;
}

.rootLoader{
    position: fixed;
    top:0;
    right: 0;
    bottom:0;
    left: 0;
    z-index: 9999;
    background: rgba(0,0,0,0.3);
}

/****************** login styles ******************/
.loginContainer {
    max-width: 350px;
    margin: 0 auto;
    margin-top: 100px;
}

.loginForm {
    /*margin: 0 30px;*/
}

/****************** dashboard styles ****************/
.navbar {
    border-radius: 0px;
    margin-bottom: 0px;
}

#page-wrapper {
    padding: 0 15px;
    min-height: 568px;
    background-color: #fff;
}

@media (min-width: 768px) {
    #page-wrapper {
        position: relative;
        margin: 0 0 0 250px;
        padding: 0 30px;
        border-left: 1px solid #e7e7e7;
    }
}

.sidebar .sidebar-nav.navbar-collapse {
    padding-right: 0;
    padding-left: 0;
}

.sidebar .sidebar-search {
    padding: 15px;
}

.sidebar ul li {
    border-bottom: 1px solid #e7e7e7;
}

.sidebar ul li a.active {
    background-color: #eee;
}

.sidebar .arrow {
    float: right;
}

.sidebar .fa.arrow:before {
    content: "\f104";
}

.sidebar .active > a > .fa.arrow:before {
    content: "\f107";
}

.sidebar .nav-second-level li,
.sidebar .nav-third-level li {
    border-bottom: 0 !important;
}

.sidebar .nav-second-level li a {
    padding-left: 37px;
}

.sidebar .nav-third-level li a {
    padding-left: 52px;
}

.nav > li > a {
    cursor: pointer;
}

.nav > li > a span.arrow {
    text-align: right;
}

.nav > li > a .arrow:before {
    content: "";
}

.nav > li > a.active .arrow:before {
    content: "";
}

@media (min-width: 768px) {
    .sidebar {
        z-index: 1;
        position: absolute;
        width: 250px;
        margin-top: 1px;
    }

    .navbar-top-links .dropdown-messages,
    .navbar-top-links .dropdown-tasks,
    .navbar-top-links .dropdown-alerts {
        margin-left: auto;
    }
}

/***************** forms styles *********************/
.formPanel {
    width: 300px;
    border: #ddd 1px solid;
    border-radius: 3px;
    margin: 10px;
    display: inline-block;
}

.formPanel .head {
    padding: 5px;
    color: #5e7e47;
    font-size: 21px;
    border-bottom: #ddd 1px solid;
}

.formPanel .body {
    padding: 10px;
    height: 100px;
}

.formPanel .footer {
    padding: 5px 10px;
    text-align: right;
    border-top: #ddd 1px solid;
}

/******************* audit styles **********************/

.auditFormPanel {
    margin-top: 10px;
    border: #ddd 1px solid;
    border-radius: 3px;
}

.auditFormHeader {
    border-bottom: #ddd 1px solid;
    padding: 10px;
    font-size: 21px;
    font-weight: bold;
}

.auditFormBody {
    padding: 10px;
}

.auditFormFooter {
    border-top: #ddd 1px solid;
    padding: 10px;
    height: 51px;
    background: #fff;
}

.auditFormFooter .progress {
    height: 8px;
}

.headSection {
    border-bottom: #ddd 3px solid;
    margin: 20px 0;
}

.questionBlock {
    margin-bottom: 20px;
}

#modal-body .questionBlock {
    background: #ddd;
    margin: 10px;
    padding: 10px 0px;
    border-radius: 5px;
}

/**
**Wallet Css
*/

.wallet-container {
	padding: 7px;
}

.menu-highlight {
	background-color: #777;
}

.page-heading {
	font-size: 26px;
	color: red;
	margin-bottom: 13px;
}

.tab-container {
	margin-left: 7px;
	margin-right: 7px;
	margin-top: 10px;
}

.label-font {
	font-size: 20px;
}

.row-spacing {
	margin-top: 10px;
}

.wallet-detail-label {
	font-size: 20px;
	color: blue;
}

.invoiceUploadPlus {
	font-size: 23px;
	margin-left: 10px;
	color: green;
}

.invoiceUploadMinus {
	font-size: 23px;
	margin-left: 10px;
	color: red;
}

.wallet-detail-label {
	font-size: 15px;
	color: blue;
}

.wallet-detail-heading {
	font-size: 20px;
	color: red;
	margin-bottom: 13px;
}

.action-comment {
	font-family: sans-serif;
	color: blue;
	font-size: 18px;
}

.action-type {
	font-family: sans-serif;
	font-size: 20px;
	color: red;
}

.wallet-amount {
	font-family: sans-serif;
	font-size: 20px;
}

.scrollmenu {
    background-color: #4CAF50;
    overflow: auto;
    white-space: nowrap;
    border-radius: 3px;
    margin-bottom: 10px;
}

.scrollmenu a {
    display: inline-block;
    color: white;
    text-align: center;
    padding: 10px;
    text-decoration: none;
    font-size: 15px;
}

.btn {
    margin-bottom: 5px;
}