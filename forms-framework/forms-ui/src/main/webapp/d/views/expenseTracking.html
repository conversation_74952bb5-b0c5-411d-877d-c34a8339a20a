<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.expense-container {
	padding-right: 5px;
	padding-left: 5px;
	margin-right: 5%;
	margin-left: 5%;
}

.active-status {
	color: green;
}

.cancel-status {
	color: red;
}

.selected-view {
	background: green;
}

.free-kettle {
    color: #EFE8E8;
    background-color: #D82F2F;
    border-color: #ebccd1;
    font-size: 20px;
    padding: 10px;
    margin-bottom:0px;
}
.cost-label{
    font-size: 25px;
    color: green !important;
    border: 2px solid darkblue;
    margin-top: 2%;
}
.region-card {
	font-weight: 600;
	color: green;
}

.delete-unit {
	color: red;
	font-size: 20px;
	float: right;
}
</style>
<div data-ng-init="init()" class="expense-container">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center" style="font-family: 'typewriter';">
				{{expenseCategory.label}} Expenses</h2>
		</div>
	</div>
	<form name="expenseDetailForm" data-ng-if="viewName == 'add'">
	<div class="row" ng-if="errorDetail != null && errorDetail.errorMsg != null">
			<div class="col-xs-12 alert free-kettle  text-center">{{errorDetail.errorMsg}}
				</div>
		</div>
		<div class="row">
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label for="expenseType" class="control-label">Select
								Unit </label> <select class="form-control" id="unitAddExpense"
								name="unitAddExpense" data-ng-model="selectedUnit.unitDetail"
								data-ng-options="unit as unit.name for unit in units"
								required="required" data-ng-change="updateUnitAdd(selectedUnit.unitDetail)">
							</select>
						</div>
						<button class="btn btn-primary btm-sm"
								data-ng-click="addAllUnit('add')" data-ng-if="expenseCategory.name =='MARKETING_CORPORATE' && (expenseDetail.units === undefined || expenseDetail.units.length == 0)">Add all</button>
							<button class="btn btn-primary btm-sm"
								data-ng-click="addAllUnit('remove')" data-ng-if="expenseCategory.name =='MARKETING_CORPORATE' && expenseDetail.units.length > 3">Remove all</button>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label for="expenseType" class="control-label">Expense
								Type </label> <select class="form-control" id="expenseType"
								name="expenseType" data-ng-model="expenseDetailObj"
								data-ng-options="expense as expense.desc for expense in expenseType"
								required="required"
								data-ng-change="updateExpenseDetail(expenseDetailObj)">
							</select>
						</div>
					</div>

				</div>
			</div>
			<div class="col-xs-12" data-ng-if="expenseDetail.units != undefined && expenseDetail.units.length > 0">
			<div class="row">
					<div class="card"
						data-ng-repeat="unit in expenseDetail.units">
						<div class="col-xs-3 region-card">
							<ul class="list-group list-group-flush">
								<li class="list-group-item">{{unit.name}}
									<i class="far fa-trash-alt delete-unit" aria-hidden="true"
									data-ng-click="updateUnitlist(unit)"></i>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label for="channelPartner" class="control-label">Amount
							</label> <input type="number" data-ng-model="expenseDetail.amount"
								class='form-control' required="required" />
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12">
						<label for="expenseComment" class="control-label">Expense
							Comment </label>
						<textarea style="width: 100%;" rows="5" name="expenseComment"
							id="expenseComment" data-ng-model="expenseDetail.comment">
					</textarea>
					</div>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<button class="btn btn-primary pull-left"
								data-ng-click="saveExpenseDetail()" style="margin-top: 29px;"
								data-ng-disabled="expenseDetailForm.$invalid">Submit</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
	<div class="row" data-ng-if="viewName == 'display'">
		<div class="row">
			<div class="col-xs-6">
				<p>
					<strong style="font-size: 25px;">Expense Type Detail </strong>
				</p>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label for="expenseType" class="control-label">Select
								Unit </label> <select class="form-control" id="unitExpenseCancel"
								name="unitExpenseCancel" data-ng-model="selectedUnitCancel"
								data-ng-options="unit as unit.name for unit in units"
								required="required"
								data-ng-change="updateUnitCancel(selectedUnitCancel)">
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label for="expenseTypeRequest" class="control-label">Expense
								Type </label> <select class="form-control" id="expenseTypeRequest"
								name="expenseTypeRequest"
								data-ng-model="expenseRequest.expenseType"
								data-ng-options="expense.desc as expense.desc for expense in expenseType">
							</select>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label" for="expenseStatusRequest">Expense
								Status</label> <select data-ng-model="expenseRequest.status"
								class='form-control' id="expenseStatusRequest"
								name="expenseStatusRequest">
								<option value="ACTIVE">Active</option>
								<option value="CANCELLED">Cancelled</option>
							</select>

						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label for="expenseStartDate" class="control-label">Start
								Date </label>
							<p class="input-group">
								<input type="text" class="form-control"
									uib-datepicker-popup="yyyy-MM-dd"
									ng-model="dateObj.expenseStartdate" is-open="expenseStartDate.opened"
									datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
									ng-required="false" close-text="Close"
									 /> <span
									class="input-group-btn">
									<button type="button" class="btn btn-default"
										ng-click="expenseStartDate.opened=true">
										<i class="glyphicon glyphicon-calendar"></i>
									</button>
								</span>
							</p>
						</div>
					</div>
					<div class="col-xs-6">
						<label for="expenseEndDate" class="control-label">End date
						</label>
						<p class="input-group">
							<input type="text" class="form-control"
								uib-datepicker-popup="yyyy-MM-dd"
								ng-model="dateObj.expenseEndDate" is-open="expenseEndDate.opened"
								datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
								ng-required="false" close-text="Close"
								/> <span
								class="input-group-btn">
								<button type="button" class="btn btn-default"
									ng-click="expenseEndDate.opened=true">
									<i class="glyphicon glyphicon-calendar"></i>
								</button>
							</span>
						</p>
					</div>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<button class="btn btn-primary pull-left"
								data-ng-click="getExpenseDetail(false)" style="margin-top: 29px;">Search
								Expense</button>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<button class="btn btn-info pull-left"
								data-ng-click="getExpenseDetail(true)" style="margin-top: 29px;">Download</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="row">
				<div class="col-xs-12">
					<div class="col-xs-12" data-ng-if="expenseDetailList.length == 0"
						style="color: red; font-size: 30px;">No expense found</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-xs-6 cost-label" data-ng-if="totalCost > 0">Total cost :
			Rs.{{totalCost}}</div>
	</div>
	<div class="row">
		<div class="col-xs-12" style="margin-top: 20px;">
			<table class="table table-striped table-bordered"
				style="background: #fff;"
				data-ng-if="expenseDetailList != null && expenseDetailList.length > 0">
				<thead>
					<th scope="col">Expense Type</th>
					<th scope="col">Unit</th>
					<th scope="col">Amount</th>
					<th scope="col">Comment</th>
					<th scope="col">Status</th>
					<th scope="col">Created By</th>
					<th scope="col">Created On</th>
					<th scope="col">Cancelled By</th>
					<th scope="col">Cancel Comment</th>
					<th scope="col">Cancel Expense</th>
				</thead>
				<tbody>
					<tr data-ng-repeat="exp in expenseDetailList">
						<td>{{exp.expenseType}}</td>
						<td>{{exp.unitId.name}}</td>
						<td>{{exp.amount}}</td>
						<td>{{exp.comment}}</td>
						<td
							data-ng-class="{'active-status': (exp.status== 'ACTIVE'),'cancel-status': (exp.status== 'CANCELLED')}">{{exp.status}}</td>
						<td>{{exp.createdBy.name}}</td>
						<td>{{exp.createdOn | date:'dd/MM/yyyy'}}</td>
						<td>{{exp.cancelledBy.name}}</td>
						<td>{{exp.cancellationReason}}</td>
						<td><span
							data-ng-if="exp.cancel && exp.status == 'ACTIVE' && exp.expenseCategory  == expenseCategory.name"
							data-ng-click="cancelExpenseDetail(exp)" style="cursor: pointer"
							title="Cancel Expense" class="btn btn-danger"> Cancel</span></td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
</div>


