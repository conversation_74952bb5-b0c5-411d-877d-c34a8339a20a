<div data-ng-init="init()">
    <div class="container-fluid">
        <h2>Select audit</h2>
        <div class="text-center" style="margin-top: 100px" data-ng-if="loading">
            <div class="loader xl"></div>
        </div>
        <div data-ng-if="!loading">
            <div data-ng-if="forms.length==0">
                <div uib-alert class="alert alert-info">No forms found!</div>
            </div>
            <div data-ng-if="forms.length>=0">
                    <div class="formPanel" data-ng-repeat="form in forms | filter: (((formType == true) && {id: 5}) || (formType == false)) track by form.id">
                    <div class="head">{{form.name}}</div>
                    <div class="body">{{form.description}}</div>
                    <div class="footer">
                        <button class="btn btn-default" data-ng-click="startAudit(form)">Start &rsaquo;</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
