<div data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h1 class="text-center">Manage Claims</h1>
            <div class="col-xs-12">
                <h1>Search Claims</h1>
                <div class="row-spacing">
                    <div class="row">
                        <div class="col-xs-4">
                            <div class="form-group">
                                <label>Account Type</label>
                                <select class="form-control" data-ng-model="selectedAccountType"
                                        data-ng-options="accountType as accountType for accountType in accountTypes | orderBy"
                                        data-ng-change="getWalletsByAccountType(selectedAccountType)" required></select>
                            </div>
                        </div>
<!--                        <div data-ng-if="selectedAccountType =='EMPLOYEE'" class="col-xs-4">-->
                            <div class="form-group col-xs-4" >
                                <label class="col-xs-6">Select Accounts</label>
                                <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                     options="walletsNamesByAccType" selected-model="walletsByAccTypeMultiSelect">
                                </div>
                            </div>
<!--                        </div>-->
<!--                        <div data-ng-if="selectedAccountType =='UNIT'" class="col-xs-4">-->
<!--                        <div class="form-group">-->
<!--                            <label class="control-label">Select Unit </label>-->
<!--                            <div ng-dropdown-multiselect="" options="units"-->
<!--                                 selected-model="unitMultiSelect"-->
<!--                                 extra-settings="multiSelectSettings2"></div>-->
<!--                        </div>-->
<!--                        </div>-->

                        <div class="col-xs-4">
                            <div class="form-group">
                                <label>Claim Status</label>
                                <select data-ng-model="selectedClaimStatus"
                                        data-ng-selected="setSelectedClaimStatus(selectedClaimStatus)"
                                        class="form-control"
                                        data-ng-options="claimStatus as claimStatus.name for claimStatus in claimStatuses"></select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="col-xs-4">
                                <div class="form-group">
                                    <label>Claim Type</label>
                                    <select data-ng-model="selectedClaimType"
                                            data-ng-selected="setSelectedClaimType(selectedClaimType)"
                                            class="form-control"
                                            data-ng-options="claimType as claimType.name for claimType in claimTypes"></select>
                                </div>
                            </div>
                            <div class="col-xs-4">
                                <label>Start Date</label>
                                <div class="input-group">
                                    <input type="text" class="form-control"
                                           uib-datepicker-popup="yyyy-MM-dd"
                                           ng-model="startDate" is-open="popupStartDate.opened"
                                           datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                           ng-required="true" close-text="Close"/>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default"
                                                ng-click="popupStartDate.opened=true">
                                            <i class="glyphicon glyphicon-calendar"></i>
                                        </button>
                                    </span>
                                </div>
                            </div>
                            <div class="col-xs-4">
                                <label>End Date</label>
                                <div class="input-group">
                                    <input type="text" class="form-control"
                                           uib-datepicker-popup="yyyy-MM-dd"
                                           ng-model="endDate" is-open="popupEndDate.opened"
                                           datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                           ng-required="true" close-text="Close"/>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default"
                                                ng-click="popupEndDate.opened=true">
                                            <i class="glyphicon glyphicon-calendar"></i>
                                        </button>
                                    </span>
                                </div>
                            </div>
                                </p>
                            </div>
                        </div>
                    </div>

            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="form-actions">
                    <input type="button" class="btn btn-primary" value="Find Claims"
                           data-ng-click="findClaims()"/>
                </div>
            <div class="col-xs-12" data-ng-if="claimList.length>0">
                <input type="button" class="btn btn-primary pull-right"style="margin-left:30px" value="Download Template"
                           data-ng-click="downloadTemplate()"/>
                    <input type="button" class="btn btn-primary pull-right" style="margin-left:50px" value="Download Claims"
                           data-ng-click="downloadClaims()"/>
                    <input type="button" class="btn btn-primary pull-right"  value="Bulk Approves"
                           data-toggle="modal" data-target="#openBulkApprovalModal"modal/>
            </div>
        </div>
                </div>

                <div class="row">
                    <div class="col-xs-12">
                        <table class="table table-bordered table-striped" data-ng-if="claimList.length > 0">
                            <tr>
                                <th scope="col">Claim Id</th>
                                <th scope="col">Claim Type</th>
                                <th scope="col">Requested Amount</th>
                                <th scope="col">Created By</th>
                                <th scope="col">Creation Time</th>
                                <th scope="col">Status</th>
                                <th scope="col">Employee</th>
                                <th scope="col">Expense Settlement Id</th>
                                <th scope="col">Unit Id</th>
                                <th scope="col">Unit Email</th>
                                <th scope="col">Unit Name</th>
                                <th scope="col">Last Updated By</th>
                                <th scope="col">Update time</th>
                                <th scope="col">Actions</th>
                                <th scope="col">Details</th>
                            </tr>
                            <tr data-ng-repeat="claim in claimList track by claim.claimId">
                                <td>{{claim.claimId}}</td>
                                <td>{{claim.claimType}}</td>
                                <td>{{claim.claimRequestedAmount}}</td>
                                <td>{{claim.createdBy.name}}</td>
                                <td>{{claim.creationTime | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                                <td>{{claim.claimCurrentStatus}}</td>
                                <td>{{claim.employee!= null?claim.employee.name:""}}</td>
                                <td>{{claim.happayId}}</td>
                                <td>{{claim.unit!=null?claim.unit.id:""}}</td>
                                <td>{{claim.unit!=null?claim.unit.code:""}}</td>
                                <td>{{claim.unit!=null?claim.unit.name:""}}</td>
                                <td>{{claim.lastUpdatedBy.name}}</td>
                                <td>{{claim.lastUpdateTime | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                                <td>
                                    <input type="button" class="btn btn-success btn-sm" value="Download Uploader" data-ng-click="downloadClaimById(claim.claimId)"
                                           data-ng-if="claim.claimCurrentStatus == 'CREATED'" />
                                    <input type="button" class="btn btn-danger btn-sm" value="Reject" data-ng-click="openActionModal(claim,'REJECT')"
                                           data-ng-if="claim.claimCurrentStatus == 'CREATED'" />
                                </td>
                                <td>
                                    <input type="button" class="btn btn-primary btn-sm" value="View Logs" data-ng-click="viewClaimLogs(claim)" />
                                    <input type="button" class="btn btn-primary btn-sm" value="View Vouchers" data-ng-if="claim.claimType=='HAPPAY'||claim.claimType=='ICICI'"
                                           data-ng-click="viewClaimVouchers(claim)" />
                                    <input type="button" class="btn btn-primary btn-sm" value="Download Claim" data-ng-click="downloadClaim(claim.claimId)" />
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div
        class="modal fade"
        id="openBulkApprovalModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

                <h4
                        class="modal-title"
                        id="myModalLabel">Upload</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-6">Upload Claims Pending for approval</div>
                    <div class="col-xs-6">
                        <input class="btn btn-default" style="width: 100%;"
                               type="file" file-model="fileToUpload"
                               accept=""/>
                    </div>

                </div>
                <button class="btn btn-primary" style="margin-top: 5px"
                        data-toggle="modal"
                        data-ng-click="bulkApproves(fileToUpload)"
                        data-ng-disabled="fileToUpload==null">Upload
                </button>
            </div>

        </div>
    </div>
</div>
<script type="text/ng-template" id="claimAction.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title"><span style="text-transform: capitalize;">{{claimAction}}</span> Claim</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <label>Comment</label>
                    <textarea data-ng-model="comment" class="form-control"></textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Close</button>
        <button class="btn btn-danger" type="button" ng-click="submit()">{{claimAction}}</button>
    </div>
</script>

<script type="text/ng-template" id="claimVoucher.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title">Claim Vouchers</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-bordered table-striped" data-ng-if="claim.vouchers.length > 0">
                    <tr>
                        <th scope="col">Voucher Id</th>
                        <th scope="col">Account Type</th>
                        <th scope="col">Issued Amount</th>
                        <th scope="col">Expense Amount</th>
                        <th scope="col">Expense Type</th>
                        <th scope="col">Expense Detail</th>
                        <th scope="col">Business Date</th>
                        <th scope="col">Issued By</th>
                        <th scope="col">Issued To</th>
                    </tr>
                    <tr data-ng-repeat="voucher in claim.vouchers track by voucher.id">
                        <td>{{voucher.generatedVoucherId}}</td>
                        <td>{{voucher.accountType}}</td>
                        <td>{{voucher.issuedAmount}}</td>
                        <td>{{voucher.expenseAmount}}</td>
                        <td>{{voucher.expenseType}}</td>
                        <td>{{voucher.expenseDetail}}</td>
                        <td>{{voucher.businessDate | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{voucher.issuedBy.name}}</td>
                        <td>{{voucher.issuedTo.name}}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Close</button>
    </div>
</script>

<script type="text/ng-template" id="claimLog.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title">Claim Logs</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-bordered table-striped" data-ng-if="claim.claimLogs.length > 0">
                    <tr>
                        <th scope="col">Claim Id</th>
                        <th scope="col">From Status</th>
                        <th scope="col">To Status</th>
                        <th scope="col">Updated By</th>
                        <th scope="col">Update Time</th>
                        <th scope="col">Comments</th>
                    </tr>
                    <tr data-ng-repeat="log in claim.claimLogs track by log.id">
                        <td>{{log.claimId}}</td>
                        <td>{{log.fromStatus}}</td>
                        <td>{{log.toStatus}}</td>
                        <td>{{log.updatedBy.name}}</td>
                        <td>{{log.updateTime | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{log.comments}}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Close</button>
    </div>
</script>