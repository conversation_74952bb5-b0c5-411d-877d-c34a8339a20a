<div data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="text-align: center;">Reconciliation</h2>
    </div>
    <div class="row" style="margin-bottom: 20px; margin-top: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="tab in tabList track by $index"
                        data-ng-class="{'btn btn-default':selectedTab!=tab,'btn btn-primary':selectedTab==tab}"
                        data-ng-click="selectTab(tab)">{{tab}}
                </button>
            </div>
        </div>
    </div>
    <div data-ng-if="selectedTab=='CHANNEL PARTNER'">
       <div class="row-xs-12">
        <label>Select Channel Partner</label>    
        <select class="form-control" data-ng-model="channelPartner" data-ng-change="setChannelPartner(channelPartner)">
            <option value="ZOMATO">ZOMATO</option>
            <option value="SWIGGY">SWIGGY</option>
        </select>
       </div>
       <div class="row" style="margin-top: 20px;">
        <div class="col-xs-2">
            <label>Start Date</label>
            <input type="date" class="form-control" data-ng-model="startDate" data-ng-change="setStartDate(startDate)">
           </div>
           <div class="col-xs-2">
            <label>End Date</label>
            <input type="date" class="form-control" data-ng-model="endDate" data-ng-change="setEndDate(endDate)">
           </div>
       </div>
       <div class="row" style="margin-top: 30px;">
        <div class="col-xs-2"><button class="btn btn-primary" data-ng-click="setRunRecon()" >Run Reconciliation</button> </div>
        <!-- <div class="col-xs-2"><button class="btn btn-primary" data-ng-click="setReRunRecon()">Re-Calculate Commission</button> </div>  -->
     </div>
     
    <div class="row" style="margin-top: 20px;" data-ng-if="isRunRecon">
            <div class="col-xs-4">
            <label>Select Report</label>
            <input type="file" class="form-control" file-model="fileToUpload" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">
            <p>**only support .xlsx format</p>   
        </div>
           
           <div class="col-xs-12" style="margin-top: 20px;"><button class="btn btn-primary" data-ng-click="startRecon(fileToUpload)">Start Reconciliation</button></div>
        </div>  
          
    <div class="row" data-ng-if="isReRunRecon">
        <div class="col-xs-12" style="margin-top: 10px;">
            <label>Select Brand</label>
            <select class="form-control" data-ng-model="brand" data-ng-change="setBrand(brand)">
                <option value="chaayos">CHAAYOS</option>
                <option value="gnt">GNT</option>
            </select>
        </div>
        <div class="col-xs-12" style="margin-top: 10px;">
            <label>Enter AOV </label>
            <input type="number" class="form-control" data-ng-model="aov" style="width: 200px;" data-ng-change="setAov(aov)">
        </div>
        <div class="col-xs-12" style="margin-top: 20px;">
            <button class="btn btn-primary" data-ng-click="onClick()">Start Re-Calculate Commission</button>
        </div>
    </div>
    </div>
    <div class="row" data-ng-if="selectedTab=='CASH'">
        <div class="row" style="margin-top: 20px;">
            <div class="col-xs-2">
                <label>Start Date</label>
                <input type="date" class="form-control" data-ng-model="startDate" data-ng-change="setStartDate(startDate)">
               </div>
               <div class="col-xs-2">
                <label>End Date</label>
                <input type="date" class="form-control" data-ng-model="endDate" data-ng-change="setEndDate(endDate)">
               </div>
               <div class="col-xs-2">
                <label>Pickup Date</label>
                <input type="date" class="form-control" data-ng-model="pickupDate" data-ng-change="setPickupDate(pickupDate)">
               </div>
           </div>
           <div class="row" style="margin-top: 20px;">
            <div class="col-xs-4">
            <label>Select File</label>
            <input type="file" class="form-control" file-model="fileToUpload" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">
            <p>**only support .xlsx format</p>   
        </div>
        
        <div class="col-xs-12" ><p>Sheet should only contain these columns : ACCOUNT_NO, TOTAL_AMOUNT, SLIP_NUMBER, SHOP_ID</p></div>        
           <div class="col-xs-12" style="margin-top: 20px;"><button class="btn btn-primary" data-ng-click="startCashRecon(fileToUpload)">Start Cash Reconciliation</button></div>
        </div>  

    </div>
</div>