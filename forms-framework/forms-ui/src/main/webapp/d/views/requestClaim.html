<style type="text/css">
    .scrollmenu{
        margin-bottom: 10px;
    }
</style>

<div data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h1 class="text-center">Request Claims</h1>
            <div class="row" data-ng-if="otpVerificationPending == true">
                <div class="col-xs-12">
                    <h3>OTP Verification</h3>
                    <div class="form-group">
                        <label for="userId">User Id</label>
                        <input id="userId" class="form-control" type="text" data-ng-model="userDetails.id" disabled/>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" name="password" id="password" class="form-control"
                               data-ng-model="authDetails.password" required="required"/>
                    </div>
                    <div class="form-actions" data-ng-if="!otpDetails.otpSent">
                        <button type="button" class="btn btn-primary" data-ng-click="verifyUser()">
                            Generate OTP
                        </button>
                    </div>
                    <div data-ng-if="otpDetails.otpSent">
                        <div class="form-group">
                            <label for="OTP">Enter OTP </label>
                            <input type="text" name="OTP" id="OTP" class="form-control"
                                   data-ng-model="authDetails.otp" required="required" maxlength="4" />
                        </div>
                        <div class="form-group">
                            <div class="form-actions">
                                <div class= "col-xs-2">
                                <button type="button" class="btn btn-warning pull-right" data-ng-click="verifyOTP()">
                                    Verify OTP
                                </button>
                            </div>
                                <div class= "col-xs-2">
                                    <button type="button" data-ng-if="otpDetails.otpCount < 4" class="btn btn-success"
                                            data-ng-click="resendOtp()">
                                        Resend OTP
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" data-ng-if="otpVerificationPending != true">
                <div class="col-xs-12">
                    <div class="scrollmenu">
                        <a href="" data-ng-repeat="tab in tabData" data-ng-click="selectTab(tab)"
                           data-ng-class="{'menu-highlight' : tab == selectedTab}">
                            {{tab.name}}
                        </a>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div data-ng-if="selectedTab.name == 'Wallet Details'">
                                <div class="form-group">
                                    <label>Wallet Type</label>
                                    <select data-ng-model="selectedWalletType" data-ng-selected="setSelectedWalletType(selectedWalletType)"
                                            class="form-control" data-ng-options="walletType as walletType.name for walletType in walletTypes"></select>
                                </div>
                                <div class="form-group">
                                    <input type="button" class="btn btn-primary" value="Get Wallet Details" data-ng-click="getWalletDetails()" />
                                </div>
                                <div data-ng-if="loadedPettyCashWallet" data-ng-include="'views/walletView.html'"></div>
                            </div>
                            <div data-ng-if="selectedTab.name == 'Request Claim'">
                                <h3>Request New Claim</h3>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="row">
                                            <div class="col-xs-12">
                                                <div class="form-group">
                                                    <label>Select ACCOUNT*</label>
                                                    <select class="form-control"
                                                            data-ng-model="selectedAccount.accountNo"
                                                            data-ng-options="account as account.accountNo for account in accounts"
                                                            required="required"></select>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Select Claim Type</label>
                                    <select class="form-control" data-ng-model="selectedClaimType"
                                            data-ng-selected="setSelectedClaimType(selectedClaimType)"
                                            data-ng-options="claimType as claimType.name for claimType in claimTypes"></select>
                                </div>
                                <div class="form-group">
                                    <input type="button" class="btn btn-primary" data-ng-click="startClaimRequest()" value="Start Claim Process"/>
                                </div>
                                <div class="row" data-ng-if="selectedClaimType.name == 'HAPPAY' || selectedClaimType.name=='ICICI'">
                                    <div class="col-xs-12" data-ng-if="claimPendingVouchers.length > 0">
                                        <table class="table table-bordered table-striped">
                                            <tr>
                                                <th scope="col">Voucher Id</th>
                                                <th scope="col">Account Type</th>
                                                <th scope="col">Issued Amount</th>
                                                <th scope="col">Expense Amount</th>
                                                <th scope="col">Expense Type</th>
                                                <th scope="col">Expense Detail</th>
                                                <th scope="col">Business Date</th>
                                                <th scope="col">Issued By</th>
                                                <th scope="col">Issued To</th>
                                            </tr>
                                            <tr data-ng-repeat="voucher in claimPendingVouchers track by voucher.id">
                                                <td>{{voucher.generatedVoucherId}}</td>
                                                <td>{{voucher.accountType}}</td>
                                                <td>{{voucher.issuedAmount}}</td>
                                                <td>{{voucher.expenseAmount}}</td>
                                                <td>{{voucher.expenseType}}</td>
                                                <td>{{voucher.expenseDetail}}</td>
                                                <td>{{voucher.businessDate}}</td>
                                                <td>{{voucher.issuedBy.name}}</td>
                                                <td>{{voucher.issuedTo.name}}</td>
                                            </tr>
                                        </table>
                                        <p style="font-size: 21px;">Total Claim amount: {{claimAmount}}</p>
                                        <div class="form-group">
                                            <input type="button" class="btn btn-primary" value="Initiate Claim"
                                                   data-ng-click="createHappayClaimRequest()">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div data-ng-show="selectedTab.name == 'Search Claims'">
                                <h1>Search Claims</h1>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="row">
                                            <div class="col-xs-4">
                                                <div class="form-group">
                                                    <label>Select ACCOUNT*</label>
                                                    <select class="form-control"
                                                            data-ng-model="selectedAccount.accountNo"
                                                            data-ng-options="account as account.accountNo for account in accounts"
                                                            required="required"></select>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-xs-4">
                                                <label>Start Date</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control"
                                                           uib-datepicker-popup="yyyy-MM-dd"
                                                           data-ng-change="setDate(startDate,'startDate')"
                                                           ng-model="startDate" is-open="popupStartDate.opened"
                                                           datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                                           ng-required="true" close-text="Close"/>
                                                    <span class="input-group-btn">
                                                <button type="button" class="btn btn-default" ng-click="popupStartDate.opened=true">
                                                    <i class="glyphicon glyphicon-calendar"></i>
                                                </button>
                                            </span>
                                                </div>
                                            </div>
                                            <div class="col-xs-4">
                                                <label>End Date</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control"
                                                           uib-datepicker-popup="yyyy-MM-dd"
                                                           data-ng-change="setDate(endDate, 'endDate')"
                                                           ng-model="endDate" is-open="popupEndDate.opened"
                                                           datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                                           ng-required="true" close-text="Close"/>
                                                    <span class="input-group-btn">
                                                <button type="button" class="btn btn-default" ng-click="popupEndDate.opened=true">
                                                    <i class="glyphicon glyphicon-calendar"></i>
                                                </button>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-4">
                                                <div class="form-group">
                                                    <label>Claim Status</label>
                                                    <select data-ng-model="selectedClaimStatus" data-ng-selected="setSelectedClaimStatus(selectedClaimStatus)"
                                                            class="form-control" data-ng-options="claimStatus as claimStatus.name for claimStatus in claimStatuses"></select>
                                                </div>
                                            </div>
                                            <div class="col-xs-4">
                                                <div class="form-group">
                                                    <label>Claim Type</label>
                                                    <select data-ng-model="selectedClaimType" data-ng-selected="setSelectedClaimType(selectedClaimType)"
                                                            class="form-control" data-ng-options="claimType as claimType.name for claimType in claimTypes"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-12">
                                                <div class="form-actions">
                                                    <input type="button" class="btn btn-primary" value="Find Claims" data-ng-click="findClaims()" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 10px;">
                                    <div class="col-xs-12">
                                        <table class="table table-bordered table-striped" data-ng-if="claimList.length > 0">
                                            <tr>
                                                <th scope="col">Claim Id</th>
                                                <th scope="col">Claim Type</th>
                                                <th scope="col">Requested Amount</th>
                                                <th scope="col">Created By</th>
                                                <th scope="col">Creation Time</th>
                                                <th scope="col">Status</th>
                                                <th scope="col">Employee</th>
                                                <th scope="col">Happay Id</th>
                                                <th scope="col">Unit Id</th>
                                                <th scope="col">Unit Email</th>
                                                <th scope="col">Unit Name</th>
                                                <th scope="col">Last Updated By</th>
                                                <th scope="col">Update time</th>
                                                <th scope="col">Actions</th>
                                                <th scope="col">Details</th>
                                            </tr>
                                            <tr data-ng-repeat="claim in claimList track by claim.claimId">
                                                <td>{{claim.claimId}}</td>
                                                <td>{{claim.claimType}}</td>
                                                <td>{{claim.claimRequestedAmount}}</td>
                                                <td>{{claim.createdBy.name}}</td>
                                                <td>{{claim.creationTime | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                                                <td>{{claim.claimCurrentStatus}}</td>
                                                <td>{{claim.employee!= null?claim.employee.name:""}}</td>
                                                <td>{{claim.happayId}}</td>
                                                <td>{{claim.unit!=null?claim.unit.id:""}}</td>
                                                <td>{{claim.unit!=null?claim.unit.code:""}}</td>
                                                <td>{{claim.unit!=null?claim.unit.name:""}}</td>
                                                <td>{{claim.lastUpdatedBy.name}}</td>
                                                <td>{{claim.lastUpdateTime | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                                                <td>
                                                    <input type="button" class="btn btn-primary btn-sm" value="Download Claim" data-ng-click="downloadClaim(claim.claimId)" />
                                                    <input type="button" class="btn btn-primary btn-sm" value="Add Happay Id" data-ng-click="addHappayModal(claim)"
                                                           data-ng-if="claim.claimType=='HAPPAY' && claim.claimCurrentStatus == 'INITIATED'" />
                                                    <input type="button" class="btn btn-primary btn-sm" value="Acknowledge" data-ng-click="openActionModal(claim,'ACKNOWLEDGE')"
                                                           data-ng-if="claim.claimCurrentStatus == 'APPROVED' || claim.claimCurrentStatus == 'REJECTED'" />
                                                </td>
                                                <td>
                                                    <input type="button" class="btn btn-primary btn-sm" value="Show rejected by" data-ng-click="viewClaimLogs(claim)"
                                                           data-ng-if="claim.claimCurrentStatus == 'REJECTED'" />
                                                    <input type="button" class="btn btn-primary btn-sm" value="View Vouchers" data-ng-if="claim.claimType=='HAPPAY'"
                                                           data-ng-click="viewClaimVouchers(claim)" />
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="addHappayModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title">Set Happay Id</h3>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <label>Happay Id</label>
                    <input type="text" data-ng-model="happayId" class="form-control" />
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
        <button class="btn btn-primary" type="button" ng-click="addHappayId()" data-ng-if="happayId != null">Submit</button>
    </div>
</script>

<script type="text/ng-template" id="claimAction.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title"><span style="text-transform: capitalize;">{{claimAction}}</span> Claim</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <label>Comment</label>
                    <textarea data-ng-model="comment" class="form-control"></textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Close</button>
        <button class="btn btn-danger" type="button" ng-click="submit()">{{claimAction}}</button>
    </div>
</script>

<script type="text/ng-template" id="claimVoucher.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title">Claim Vouchers</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-bordered table-striped" data-ng-if="claim.vouchers.length > 0">
                    <tr>
                        <th scope="col">Voucher Id</th>
                        <th scope="col">Account Type</th>
                        <th scope="col">Issued Amount</th>
                        <th scope="col">Expense Amount</th>
                        <th scope="col">Expense Type</th>
                        <th scope="col">Expense Detail</th>
                        <th scope="col">Business Date</th>
                        <th scope="col">Issued By</th>
                        <th scope="col">Issued To</th>
                    </tr>
                    <tr data-ng-repeat="voucher in claim.vouchers track by voucher.id">
                        <td>{{voucher.generatedVoucherId}}</td>
                        <td>{{voucher.accountType}}</td>
                        <td>{{voucher.issuedAmount}}</td>
                        <td>{{voucher.expenseAmount}}</td>
                        <td>{{voucher.expenseType}}</td>
                        <td>{{voucher.expenseDetail}}</td>
                        <td>{{voucher.businessDate | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{voucher.issuedBy.name}}</td>
                        <td>{{voucher.issuedTo.name}}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Close</button>
    </div>
</script>

<script type="text/ng-template" id="claimLog.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title">Claim Logs</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-bordered table-striped" data-ng-if="claim.claimLogs.length > 0">
                    <tr>
                        <th scope="col">Claim Id</th>
                        <th scope="col">From Status</th>
                        <th scope="col">To Status</th>
                        <th scope="col">Updated By</th>
                        <th scope="col">Update Time</th>
                        <th scope="col">Comments</th>
                    </tr>
                    <tr data-ng-repeat="log in claim.claimLogs track by log.id">
                        <td>{{log.claimId}}</td>
                        <td>{{log.fromStatus}}</td>
                        <td>{{log.toStatus}}</td>
                        <td>{{log.updatedBy.name}}</td>
                        <td>{{log.updateTime | date : 'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{log.comments}}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Close</button>
    </div>
</script>

<script type="text/ng-template" id="messageModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title">{{message.title}}</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <p>{{message.detail}}</p>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-warning" type="button" ng-click="cancel()">Close</button>
    </div>
</script>