<div data-ng-init="init()">
    <div style="padding: 0 15px;">
        <div class="row">
            <div class="col-xs-12">
                <h2>Search Audits</h2>
                <div class="row">
                    <div class="col-xs-3">
                        <div class="form-group">
                            <label>Select Unit</label>
                            <input type="text" data-ng-model="selectedUnit" class="form-control"
                                   uib-typeahead="unit as unit.name for unit in units | filter:$viewValue"
                                   typeahead-show-hint="true" typeahead-min-length="0"
                                   typeahead-append-to-body="true"
                                   typeahead-on-select="setAuditUnit(selectedUnit)"/>
                        </div>
                    </div>
                    <div class="col-xs-3">
                        <div class="form-group">
                            <label>Select Form</label>
                            <input type="text" data-ng-model="selectedAuditForm" class="form-control"
                                   uib-typeahead="auditForm as auditForm.name for auditForm in auditFormList | filter:$viewValue"
                                   typeahead-show-hint="true" typeahead-min-length="0"
                                   typeahead-append-to-body="true"
                                   typeahead-on-select="setAuditForm(selectedAuditForm)"/>
                        </div>
                    </div>
                    <div class="col-xs-3">
                        <div class="form-group">
                            <label>Select Start Date</label>
                            <p class="input-group">
                                <input type="text" class="form-control"
                                       uib-datepicker-popup="yyyy-MM-dd"
                                       ng-model="startDate" is-open="popupStartDate.opened"
                                       datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                       ng-required="true" close-text="Close"/>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="popupStartDate.opened=true">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </button>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="col-xs-3">
                        <div class="form-group">
                            <label>Select End Date</label>
                            <p class="input-group">
                                <input type="text" class="form-control"
                                       uib-datepicker-popup="yyyy-MM-dd"
                                       ng-model="endDate" is-open="popupEndDate.opened"
                                       datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                       ng-required="true" close-text="Close"/>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="popupEndDate.opened=true">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </button>
                                </span>
                            </p>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-xs-12"><button class="btn btn-primary rippleCall" data-ng-click="searchAudit()">Search</button></div>
                </div>
                <div class="row" data-ng-if="auditList != null">
                    <div class="col-xs-12">
                        <table class="table" data-ng-if="auditList.length > 0">
                            <thead>
                                <tr><th scope="col">Audit Id</th><th scope="col">Audit Unit</th><th scope="col">Audit Type</th><th scope="col">Auditor</th><th scope="col">Audit/Max Score</th><th scope="col">Audit Date</th><th scope="col">Action</th></tr>
                            </thead>
                            <tbody>
                                <tr data-ng-repeat="item in auditList track by item.id">
                                    <td>{{item.id}}</td>
                                    <td>{{item.auditUnit.name}}</td>
                                     <td>{{item.auditForm.name}}</td>
                                    <td>{{item.auditor.name}}</td>
                                    <td>{{item.acquiredScore}}/{{item.totalScore}}</td>
                                    <td>{{item.auditSubmissionDate | date : 'dd/MM/yyyy hh:mm:ss a'}}</td>
                                    <td>
                                        <button class="btn btn-primary" data-ng-click="downloadAuditReport(item.id)">Download</button>
                                        <button class="btn btn-primary" data-ng-click="issueWarningLetter(item)" data-ng-if= "item.cancel == true">Warning</button>
                                        <button class="btn btn-primary" data-ng-click="generateAuditReport(item.id)" style="display: none;">Generate</button>
                                        <button class="btn btn-primary" data-ng-click="emailAuditReport(item.id)" style="display: none;">Email</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="alert alert-info" data-ng-if="auditList.length == 0">No audits found.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>