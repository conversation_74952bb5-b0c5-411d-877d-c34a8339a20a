<style>
    .reimbursed {
        background: #d8d31073;
    }

    .not-reimbursed {
        background: #f5f5f5;
    }
</style>
<div class="panel-heading" style="font-size: 17px;"
     data-ng-class="{'not-reimbursed' : voucher.isReimbursed != true , 'reimbursed' : voucher.isReimbursed == true}">
    <div class="row row-spacing">
        <div class="col-xs-6">
            Voucher Id : <label>{{voucher.generatedVoucherId}}</label>
            <!--<span>
				<i class="fas fa-download" style="color: green; font-size: 25px;" data-ng-click="showInvoice(voucher)"> </i>
            </span>-->
            <span style="display: inline-block;margin: 0 5px;" data-ng-repeat="img in voucher.fileDetails">
                <a data-ng-click="downloadInvoice(img.id,img.fileName, 'view')">{{img.fileType.toLowerCase()}}</a>
            </span>
        </div>
        <div class="col-xs-6">
            Current Status : {{voucher.currentStatus}}
            <span data-ng-if="voucher.isReimbursed == true">(Reimbursed)</span>
        </div>
    </div>
    <div class="row row-spacing" data-ng-if="actionDetail.type == 'settle'">
        <div class="col-xs-6">
            Issued Amount : {{voucher.issuedAmount}}
            <span data-ng-class="{'text-success' : voucher.expenseAmount <= voucher.issuedAmount ,
				'text-danger' : voucher.expenseAmount > voucher.issuedAmount}">
                ({{voucher.expenseAmount != null ? voucher.expenseAmount : '-'}})
            </span>
        </div>
        <div class="col-xs-6">Issue Time : {{voucher.issuedTime | date :'dd/MM/yyyy hh:mm:ss a'}}</div>
    </div>
    <div class="row row-spacing" data-ng-if="actionDetail.type != 'settle'">
        <div class="col-xs-6">
            Expense Amount :
            <span data-ng-class="{'text-success' : voucher.expenseAmount <= voucher.issuedAmount ,
				'text-danger' : voucher.expenseAmount > voucher.issuedAmount}">{{voucher.expenseAmount}}</span>
        </div>
        <div class="col-xs-6">
            Issue Time : {{voucher.issuedTime | date :'dd/MM/yyyy hh:mm:ss a'}}
        </div>
    </div>
    <div class="row row-spacing" data-ng-if="stateDetails.actionBy == 'AM'">
        <div class="col-xs-6">
            Issued By : {{voucher.issuedBy.name}} - {{voucher.issuedBy.code}}
        </div>
        <div class="col-xs-6">
            Issued To : {{voucher.issuedTo.name}} - {{voucher.issuedTo.code}}
        </div>
    </div>
    <div class="row row-spacing"
         data-ng-if="stateDetails.actionBy == 'AM' || stateDetails.actionBy == 'FM'">
        <div class="col-xs-6">A/c No : {{voucher.wallet.code}}</div>
        <div class="col-xs-6">A/c Holder Name : {{voucher.wallet.name}}</div>
    </div>
    <div class="row row-spacing">
        <div class="col-xs-6">Expense Type: {{voucher.expenseType}}</div>
    </div>
</div>