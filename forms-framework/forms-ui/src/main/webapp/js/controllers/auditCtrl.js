'use strict';

angular.module('formsApp')
    .controller('auditCtrl',
        ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil', 'MetadataService', 'toastr', '$uibModal', 'StorageUtil',
            function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil, MetadataService, toastr, $uibModal, StorageUtil) {
                var userData;
                $scope.init = function () {
                    $scope.progressValue = 0;
                    $scope.auditForm = {};
                    $scope.selectedUnit = null;
                    $scope.formId = $cookieStore.get("afid").id;
                    $scope.validateOnSubmit = true; //$cookieStore.get("afid").vend;
                    if ($scope.auditForm != null) {
                        $scope.getAuditFormData();
                    } else {
                        $location.path("dashboard/forms");
                    }
                    userData = StorageUtil.getUserMeta();
                    MetadataService.getUnitList(function (unitList) {
                        $scope.units = unitList;
                        if(userData.unitId != null) {
                            $scope.units.map(function (unit) {
                                if(unit.id == userData.unitId) {
                                    $scope.setAuditUnit(unit);
                                }
                            });
                        }
                    }, function (error) {
                        toastr.error(error);
                    });
                    $scope.questionsToShow = window.device == "mobile" ? 1 : 2;
                    $scope.formDataActive = 0;
                    $scope.autocompletePanel = false;
                    if ($rootScope.aclData.action != null && $rootScope.aclData.action['FSSTBD'] != null) {
                        $scope.minDate = null;
                    } else {
                        $scope.minDate = new Date();
                    }

                    $scope.imageDetails = {};
                };

                $scope.dateOptions = {
                    dateDisabled: function () {
                        return false;
                    },
                    formatYear: 'yyyy',
                    maxDate: new Date(),
                    minDate: $scope.minDate,
                    startingDay: 1
                };

                $scope.resetForm = function () {
                    $scope.progressValue = 0;
                    $scope.initAuditData();
                    $scope.auditData.auditTime = new Date();
                    $scope.auditData.auditDate = new Date();
                    $scope.formDataActive = 0;
                    $scope.calculateProgressValue();
                    $scope.showPreview = false;
                    $scope.auditTypes = [];
                    $scope.auditTypes.push({id: 1, type: 'TRIAL'});
                    if (userData.designation == "Area Manager" || userData.designation == "Deputy Area Manager" || userData.designation == "Zonal Manager") {
                        $scope.auditTypes.push({id: 2, type: 'AREA_MANAGER'});
                    } else {
                        $scope.auditTypes.push({id: 2, type: 'REGISTERED'});
                    }
                };

                $scope.backToForms = function () {
                    $location.path("dashboard/forms");
                };

                $scope.getAuditFormData = function () {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'GET',
                        url: APIJson.urls.auditFormManagement.getAuditForm + "?formId=" + $scope.formId
                    }).then(function success(response) {
                        if (response && response.data) {
                            $scope.progressValue = 0;
                            $scope.auditForm = response.data;
                            $scope.formatForm();
                            $scope.resetForm();
                        } else {
                            toastr.error('Error getting audit form.');
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        if (response && response.errorMsg) {
                            toastr.error(errorMsg);
                        } else {
                            toastr.error('Error getting audit form.');
                        }
                        $rootScope.rootLoading = false;
                    });
                };

                $scope.formatForm = function () {
                    var valuesMap = {};
                    $scope.auditForm.values.sort(function (a, b) {
                        return a.appearanceOrder - b.appearanceOrder;
                    });
                    $scope.auditForm.values.map(function (value) {
                        if (['HEADING', 'SUBHEADING', 'SNIPPET', 'QUESTION_GROUP'].indexOf(value.entityType) < 0) {
                            valuesMap[value.id] = value;
                        }
                    });
                    $scope.formValuesMap = valuesMap;
                    var formRenderObj = {};
                    $scope.auditForm.values.map(function (value) {
                        if (value.entityType == "QUESTION_GROUP") {
                            value.items = [];
                            formRenderObj[value.id] = value;
                        }
                    });
                    $scope.auditForm.values.map(function (value) {
                        if (value.entityType != "QUESTION_GROUP") {
                            if (value.entityValues != null) {
                                if (value.linkedDataType == "JSON") {
                                    //console.log(value.entityValues);
                                    value.entityValues = JSON.parse(value.entityValues);
                                }
                                if (value.linkedDataType == "INTEGER") {
                                    value.entityValues = parseInt(value.entityValues);
                                }
                            }
                            if (value.linkedEntity != null) {
                                formRenderObj[value.linkedEntity.id].items.push(value);
                            } else {
                                formRenderObj[value.id] = value;
                            }
                        }
                    });
                    $scope.formRenderObj = Object.values(formRenderObj);
                    $scope.formRenderObj.sort(function (a,b) {
                        return a.appearanceOrder - b.appearanceOrder;
                    });
                    $scope.formTabs = [];
                    var outItems = [];
                    $scope.formRenderObj.map(function (item) {
                        var QGData = null;
                        if (item.entityType == "QUESTION_GROUP") {
                            if (outItems.length > 0) {
                                $scope.formTabs.push({QGData: null, questions: outItems});
                                outItems = [];
                            }
                            var QGData = {
                                id: item.id, label: item.entityLabel, description: item.entityDescription,
                                questionOptional: item.questionOptional
                            };
                            var tabs = Math.ceil(item.items.length / $scope.questionsToShow);
                            for (var i = 0; i < tabs; i++) {
                                var startIndex = i * $scope.questionsToShow;
                                var items = item.items.slice(startIndex, startIndex + $scope.questionsToShow);
                                $scope.formTabs.push({QGData: QGData, questions: items});
                            }
                        } else {
                            if (outItems.length == 3) {
                                $scope.formTabs.push({QGData: null, questions: outItems});
                                outItems = [];
                            }
                            outItems.push(item);
                        }
                    });
                    $scope.answerMap = {};
                };

                $scope.setQuestionOpted = function (type, id, value) {
                    if (type == "GROUP") {
                        $scope.formRenderObj.map(function (item) {
                            if (item.id == id) {
                                item.na = value;
                            }
                        });
                    }
                    $scope.formTabs.map(function (item) {
                        if (type == "GROUP") {
                            if (item.QGData.id == id) {
                                item.QGData.na = value;
                                item.questions.map(function (q) {
                                    q.na = value;
                                    if (value == true) {
                                        $scope.answerMap[q.id] = {answer: null, comment: null, na: true}
                                    } else {
                                        $scope.answerMap[q.id] = {}
                                    }
                                });
                            }
                        } else {
                            item.questions.map(function (q) {
                                if (q.id == id) {
                                    q.na = value;
                                    if (value == true) {
                                        $scope.answerMap[q.id] = {answer: null, comment: null, na: true}
                                    } else {
                                        $scope.answerMap[q.id] = {}
                                    }
                                }
                            });
                        }
                    });
                    $scope.calculateProgressValue();
                };

                $scope.initAuditData = function () {
                    $scope.auditData = {
                        auditForm: $scope.auditForm,
                        values: []
                    }
                };

                $scope.setAuditUnit = function (selectedUnit) {
                    if (selectedUnit != null) {
                        $scope.selectedUnit = selectedUnit;
                        $rootScope.rootLoading = true;
                        MetadataService.getAuditUnitEmployees(selectedUnit.id, function (unitEmployees) {
                            $rootScope.rootLoading = false;
                            $scope.unitEmployees = unitEmployees;
                            $scope.auditData.auditUnit = {id: selectedUnit.id, code: "", name: selectedUnit.name};
                            $scope.calculateProgressValue();
                        }, function (error) {
                            $rootScope.rootLoading = false;
                            toastr.error(error);
                        });
                    } else {
                        $scope.resetForm();
                    }
                };

                $scope.setUnitManager = function (manager) {
                    $scope.cafeManager = manager;
                    $scope.auditData.cafeManager = {id: manager.id, code: manager.employeeCode, name: manager.name};
                    $scope.calculateProgressValue();
                };

                $scope.setManagerOnDuty = function (manager) {
                    $scope.managerOnDuty = manager;
                    $scope.auditData.managerOnDuty = {id: manager.id, code: manager.employeeCode, name: manager.name};
                    $scope.calculateProgressValue();
                };

                $scope.setEntityValue = function (id, data) {
                    if ($scope.answerMap[id] == null || $scope.answerMap[id] == undefined) {
                        $scope.answerMap[id] = {};
                    }
                    $scope.answerMap[id].answer = data;
                    //console.log($scope.answerMap);
                    $scope.calculateProgressValue();
                };

                $scope.loadProducts = function () {
                    $rootScope.rootLoading = false;
                    MetadataService.getAuditUnitProducts($scope.selectedUnit.id, function (unitProducts) {
                        $rootScope.rootLoading = false;
                        $scope.unitProducts = unitProducts;
                    }, function (error) {
                        $rootScope.rootLoading = false;
                        toastr.error(error);
                    });
                };

                $scope.calculateProgressValue = function () {
                    var totalFields = Object.keys($scope.formValuesMap).length + 6;
                    var valueFields = Object.keys($scope.answerMap).length;//$scope.auditData.values.length;
                    Object.keys($scope.auditData).map(function (key) {
                        if (['auditForm', 'values'].indexOf(key) < 0) {
                            if ($scope.auditData[key] != null) {
                                valueFields++;
                            }
                        }
                    });
                    $scope.progressValue = parseInt((valueFields / totalFields) * 100);
                    $scope.setProgressType();
                };

                $scope.setProgressType = function () {
                    if ($scope.progressValue < 25) {
                        $scope.progressType = 'danger';
                    } else if ($scope.progressValue < 50) {
                        $scope.progressType = 'warning';
                    } else if ($scope.progressValue < 75) {
                        $scope.progressType = 'info';
                    } else {
                        $scope.progressType = 'success';
                    }
                };

                $scope.prev = function () {
                    if ($scope.formDataActive == 1 && $scope.currentFormTabIndex == 0) {
                        $scope.formDataActive--;
                    } else {
                        if ($scope.currentFormTabIndex > 0) {
                            $scope.currentFormTabIndex--;
                            $scope.currentFormTab = $scope.formTabs[$scope.currentFormTabIndex];
                            if ($scope.showPreview) {
                                $scope.showPreview = false;
                            }
                        }
                    }
                };

                $scope.next = function () {
                    //console.log($scope.auditData);
                    if ($scope.formDataActive == 0) {
                        if ($scope.validFormData()) {
                            $scope.formDataActive++;
                            $scope.currentFormTabIndex = 0;
                            $scope.currentFormTab = $scope.formTabs[$scope.currentFormTabIndex];
                        }
                    } else {
                        if ($scope.validFormData()) {
                            if ($scope.currentFormTabIndex < $scope.formTabs.length - 1) {
                                $scope.currentFormTabIndex++;
                                $scope.currentFormTab = $scope.formTabs[$scope.currentFormTabIndex];
                            } else {
                                $scope.showPreview = true;
                            }
                        }
                    }
                    //console.log($scope.currentFormTabIndex);
                };

                $scope.validFormData = function () {
                    if ($scope.formDataActive == 0) {
                        var ret = false;
                        if (!$scope.auditData.auditUnit) {
                            toastr.error('Please fill audit unit.');
                        } else if (!$scope.auditData.auditDate) {
                            toastr.error('Please fill audit date.');
                        } else if (!$scope.auditData.auditTime) {
                            toastr.error('Please fill audit time.');
                        } else if (!$scope.auditData.cafeManager) {
                            toastr.error('Please select cafe manager.');
                        } else if (!$scope.auditData.managerOnDuty) {
                            toastr.error('Please select manager on duty.');
                        } else if (!$scope.auditData.auditType) {
                            toastr.error('Please select audit type.');
                        } else {
                            ret = true;
                        }
                        return ret;
                    } else {
                        var ret = true;
                        if (!$scope.validateOnSubmit) {
                            if ($scope.currentFormTab.QGData.na != true) {
                                for (var i = 0; i < $scope.currentFormTab.questions.length; i++) {
                                    if (["SNIPPET", "HEADING", "SUBHEADING"].indexOf($scope.currentFormTab.questions[i].entityType) < 0) {
                                        var answerValue = null;
                                        /*$scope.answerMap[$scope.currentFormTab.questions[i].id] != null ?
                                            $scope.answerMap[$scope.currentFormTab.questions[i].id].answer : null;*/
                                        if ($scope.currentFormTab.questions[i].na != true) {
                                            var comment = null;
                                            if ($scope.answerMap[$scope.currentFormTab.questions[i].id]) {
                                                answerValue = $scope.answerMap[$scope.currentFormTab.questions[i].id].answer;
                                                comment = $scope.answerMap[$scope.currentFormTab.questions[i].id].comment;
                                            }
                                            var formValue = $scope.formValuesMap[$scope.currentFormTab.questions[i].id];
                                            if (formValue != null && formValue.isMandatory == true && !answerValue) {
                                                toastr.error('Please fill ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel);
                                                ret = false;
                                                break;
                                            } else {
                                                if (formValue.entityType == "NUMBER" && answerValue > formValue.maxScore) {
                                                    toastr.error('Value for' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel +
                                                        " cannot be greater than " + formValue.maxScore);
                                                    ret = false;
                                                    break;
                                                }
                                            }
                                            if (formValue.entityType == "PRODUCT_EMPLOYEE_RADIO") {
                                                if (!$scope.answerMap[$scope.currentFormTab.questions[i].id].answer.radio) {
                                                    toastr.error('Please fill value for ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel + ' marks');
                                                    ret = false;
                                                    break;
                                                }
                                                if (!$scope.answerMap[$scope.currentFormTab.questions[i].id].answer.product) {
                                                    toastr.error('Please fill value for ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel + ' product');
                                                    ret = false;
                                                    break;
                                                }
                                                if (!$scope.answerMap[$scope.currentFormTab.questions[i].id].answer.employee) {
                                                    toastr.error('Please fill value for ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel + ' employee');
                                                    ret = false;
                                                    break;
                                                }
                                            }
                                            if (formValue.entityType == "TEXTAREA_EMPLOYEE_RADIO") {
                                                if (!$scope.answerMap[$scope.currentFormTab.questions[i].id].answer.radio) {
                                                    toastr.error('Please fill value for ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel + ' marks');
                                                    ret = false;
                                                    break;
                                                }
                                                if (!$scope.answerMap[$scope.currentFormTab.questions[i].id].answer.employee) {
                                                    toastr.error('Please fill value for ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel + ' employee');
                                                    ret = false;
                                                    break;
                                                }
                                                if (!$scope.answerMap[$scope.currentFormTab.questions[i].id].answer.textArea) {
                                                    toastr.error('Please fill value for ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel + ' text');
                                                    ret = false;
                                                    break;
                                                }
                                            }
                                            if (formValue.isMandatory == true && formValue.additionalComment == true && !comment && formValue.scoreCounted == true) {
                                                var acquiredScore = null;
                                                if (formValue.entityType == "NUMBER") {
                                                    acquiredScore = $scope.answerMap[$scope.currentFormTab.questions[i].id].answer;
                                                } else if (formValue.entityType == "RADIO" || formValue.entityType == "SELECT_BOX") {
                                                    acquiredScore = $scope.answerMap[$scope.currentFormTab.questions[i].id].answer.marks;
                                                } else if (formValue.entityType == "BOOLEAN") {
                                                    acquiredScore = $scope.answerMap[$scope.currentFormTab.questions[i].id].answer == "Y" ? formValue.maxScore : 0;
                                                } else if (formValue.entityType == "PRODUCT_EMPLOYEE_RADIO" || formValue.entityType == "TEXTAREA_EMPLOYEE_RADIO") {
                                                    acquiredScore = $scope.answerMap[$scope.currentFormTab.questions[i].id].answer.radio.marks;
                                                }
                                                if (formValue.maxScore != null && acquiredScore != null && acquiredScore < formValue.maxScore) {
                                                    toastr.error('Please fill comment for ' + $scope.formValuesMap[$scope.currentFormTab.questions[i].id].entityLabel);
                                                    ret = false;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        return ret;
                    }
                };

                $scope.previewModal = function () {
                    var modalInstance = $uibModal.open({
                        ariaLabelledBy: 'modal-title',
                        ariaDescribedBy: 'modal-body',
                        templateUrl: 'previewModal.html',
                        controller: 'AuditPreviewModalCtrl',
                        backdrop: 'static',
                        keyboard: false,
                        //controllerAs: '$ctrl',
                        size: 'lg',
                        resolve: {
                            items: function () {
                                return {
                                    answerMap: $scope.answerMap, formValuesMap: $scope.formValuesMap,
                                    auditForm: $scope.auditForm, formRenderObj: $scope.formRenderObj,
                                    auditData: $scope.auditData
                                }
                            }
                        }
                    });

                    modalInstance.result.then(function () {
                    }, function () {
                        //console.log("closed::::::::::::::::::::::::::::::::::::::")
                    });
                };

                /////////////////// mobile specific methods //////////////////

                $scope.hideAutocomplete = function () {
                    $scope.autocompletePanel = false;
                };

                $scope.showAutocomplete = function (list, name, model, id, data, dataName) {
                    $scope.autocompleteList = list;
                    $scope.autocompleteName = name;
                    $scope.autocompletePanel = true;
                    $scope.filteredList = list;
                    $scope.model = model;
                    $scope.qid = id;
                    $scope.ans = data || {};
                    if ($scope.model == "AUDIT_UNIT") {
                        $scope.autocompleteCallback = function (item) {
                            $scope.setAuditUnit(item);
                        };
                    } else if ($scope.model == "UNIT_MANAGER") {
                        $scope.autocompleteCallback = function (item) {
                            $scope.setUnitManager(item);
                        };
                    } else if ($scope.model == "MANAGER_ON_DUTY") {
                        $scope.autocompleteCallback = function (item) {
                            $scope.setManagerOnDuty(item);
                        };
                    } else {
                        $scope.autocompleteCallback = function (item) {
                            if (dataName != null && dataName != undefined) {
                                $scope.ans[dataName] = item;
                            } else {
                                $scope.ans = item;
                            }
                            $scope.setEntityValue($scope.qid, $scope.ans);
                        };
                    }
                };

                $scope.autocompleteInit = function () {
                    var elem = document.getElementById("autocompleteInput");
                    elem.focus();
                };

                $scope.selectItem = function (item) {
                    if ($scope.model == "AUDIT_UNIT") {
                        $scope.autocompleteCallback(item);
                    } else if ($scope.model == "UNIT_MANAGER") {
                        $scope.autocompleteCallback(item);
                    } else if ($scope.model == "MANAGER_ON_DUTY") {
                        $scope.autocompleteCallback(item);
                    } else {
                        $scope.autocompleteCallback(item);
                    }
                    $scope.autocompletePanel = false;
                };

                $scope.uploadImageFile = function (questionData, imageFiles) {
                    if (questionData == undefined) {
                        toastr.error("Please fill question options first!");
                        return false;
                    } else if (imageFiles == undefined || imageFiles.length == 0) {
                        toastr.error("Please chose images to upload for this question!");
                        return false;
                    }
                    var data = new FormData();
                    data.append("userId", userData.id);
                    data.append("file", imageFiles);
                    var config = {
                        transformRequest: angular.identity,
                        transformResponse: angular.identity,
                        headers: {
                            'Content-Type': undefined
                        }
                    }
                    $rootScope.rootLoading = true;
                    $http.post(APIJson.urls.warningManagement.uploadImage, data, config).then(
                        function (response) {
                            if (response.data != null) {
                                questionData.attachedDoc = response.data;
                                $rootScope.rootLoading = false;
                                //console.log("response",response);
                                toastr.success("Image uploaded Successfully.");
                            } else {
                                $rootScope.rootLoading = false;
                                toastr.error("Error while uploading image.Please try later!");
                            }
                        },
                        function (response) {
                            $rootScope.rootLoading = false;
                            toastr.error("Error while uploading image.Please try later!");
                        });
                };

            }
        ]
    );
