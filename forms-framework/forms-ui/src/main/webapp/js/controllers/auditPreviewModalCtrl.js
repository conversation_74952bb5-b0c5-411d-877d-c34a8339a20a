'use strict';

angular.module('formsApp')
    .controller('AuditPreviewModalCtrl', ['$rootScope', '$scope', 'APIJson', '$http', 'items', 'StorageUtil', 'toastr', '$location', '$uibModal', '$uibModalInstance', '$state', '$cookieStore',
        function ($rootScope, $scope, APIJson, $http, items, StorageUtil, toastr, $location, $uibModal, $uibModalInstance, $state, $cookieStore) {

            $scope.init = function () {
                $scope.answerMap = items.answerMap;
                $scope.formValuesMap = items.formValuesMap;
                $scope.auditForm = items.auditForm;
                $scope.formRenderObj = items.formRenderObj;
                $scope.auditData = items.auditData;
                $scope.scoreMap = {};
                $scope.scores = [];
                $scope.calculateScores();
                $scope.userData = StorageUtil.getUserMeta();
                $scope.showErrors = false;
                $scope.validateOnSubmit = true; //$cookieStore.get("afid").vend;
            };

            $scope.calculateScores = function () {
                var projectedMaxScore = 0;
                $scope.formRenderObj.map(function (head) {
                    head.items.map(function (item) {
                        if (item.scoreCounted == true && item.maxScore != null) {
                            if (head.na != true) {
                                if ($scope.scoreMap[head.id] == null || $scope.scoreMap[head.id] == undefined) {
                                    $scope.scoreMap[head.id] = {
                                        name: head.entityLabel,
                                        appearanceOrder: head.appearanceOrder,
                                        acquiredScore: 0,
                                        maxScore: 0,
                                        percentage: 0
                                    };
                                }
                                if ($scope.answerMap[item.id] != null && $scope.answerMap[item.id].na != true) {
                                    var acquiredScore = 0;
                                    if (item.entityType == "MARKS" || item.entityType == "NUMBER") {
                                        acquiredScore = $scope.answerMap[item.id].answer;
                                    }
                                    if (item.entityType == "PRODUCT_EMPLOYEE_RADIO" || item.entityType == "TEXTAREA_EMPLOYEE_RADIO") {
                                        acquiredScore = $scope.answerMap[item.id].answer.radio.marks;
                                    }
                                    if (item.entityType == "RADIO" || item.entityType == "SELECT_BOX") {
                                        acquiredScore = $scope.answerMap[item.id].answer.marks;
                                    }
                                    $scope.scoreMap[head.id].acquiredScore = parseFloat(parseFloat($scope.scoreMap[head.id].acquiredScore) + parseFloat(acquiredScore));
                                    $scope.scoreMap[head.id].maxScore = parseFloat(parseFloat($scope.scoreMap[head.id].maxScore) + parseFloat(item.maxScore));
                                }
                            }
                            if ($scope.formValuesMap[item.id].scoreCounted != null && $scope.formValuesMap[item.id].scoreCounted == true
                                && $scope.formValuesMap[item.id].maxScore != null) {
                                projectedMaxScore = parseFloat(parseFloat(projectedMaxScore) + parseFloat(item.maxScore));
                            }
                        }
                    });
                });
                var acquired = 0, max = 0, percentage = 0;
                Object.values($scope.scoreMap).map(function (value) {
                    if (value.acquiredScore != 0 || value.maxScore != 0) {
                        if (value.acquiredScore > 0 && value.maxScore > 0) {
                            value.percentage = parseFloat(parseFloat(parseFloat(value.acquiredScore) / parseFloat(value.maxScore)) * 100).toFixed(2);
                        } else {
                            value.percentage = 0;
                        }
                        acquired = parseFloat(parseFloat(acquired) + parseFloat(value.acquiredScore));
                        max = parseFloat(parseFloat(max) + parseFloat(value.maxScore));
                        $scope.scores.push(value);
                    }
                });
                percentage = parseFloat(parseFloat(parseFloat(acquired) / parseFloat(max)) * 100).toFixed(2);
                $scope.scores.push({name: "Total", acquiredScore: acquired, maxScore: max, percentage: percentage});
                $scope.scores.sort(function (a, b) {
                    return a.appearanceOrder - b.appearanceOrder;
                });
                $scope.projected = {
                    maxScore: projectedMaxScore,
                    percentage: percentage,
                    acquiredScore: (percentage * projectedMaxScore) / 100
                };

            };

            $scope.submitAudit = function () {
                if (validForm()) {
                    $scope.auditData.values = [];
                    Object.keys($scope.answerMap).map(function (key) {
                        var answer = $scope.answerMap[key].answer;
                        var comment = $scope.answerMap[key].comment;
                        var na = $scope.answerMap[key].na;
                        if (na == null || na == undefined) {
                            na = false;
                        }
                        var answerObj = {};
                        answerObj.auditFormValue = $scope.formValuesMap[key];
                        if (answerObj.auditFormValue.entityValues) {
                            if (answerObj.auditFormValue.linkedDataType == "JSON") {
                                answerObj.auditFormValue.entityValues = null;
                            }
                        }
                        answerObj.questionOpted = !na;
                        if ($scope.formValuesMap[key].scoreCounted == true && $scope.formValuesMap[key].maxScore != null) {
                            answerObj.maxScore = $scope.formValuesMap[key].maxScore;
                        }
                        if (na == false) {
                            if (comment != null) {
                                answerObj.answerComment = comment;
                            }
                            if(angular.isDefined($scope.answerMap[key].attachedDoc) ){
                            	answerObj.attachedDoc = JSON.parse($scope.answerMap[key].attachedDoc);	
                            }
                            if ($scope.formValuesMap[key].entityType == "MARKS") {
                                answerObj.numberValue = answer;
                                if ($scope.formValuesMap[key].maxScore != null && $scope.formValuesMap[key].scoreCounted == true) {
                                    answerObj.acquiredScore = answer;
                                }
                            }
                            if ($scope.formValuesMap[key].entityType == "PRODUCT") {
                                answerObj.productId = answer.product.id;
                                answerObj.productName = answer.product.name;
                            }
                            if ($scope.formValuesMap[key].entityType == "UNIT_EMPLOYEE") {
                                answerObj.employeeId = answer.employee.id;
                                answerObj.employeeName = answer.employee.name;
                                answerObj.employeeDesignation = answer.employee.designation;
                            }
                            if ($scope.formValuesMap[key].entityType == "TEXTAREA") {
                                answerObj.textArea = answer;
                            }
                            if ($scope.formValuesMap[key].entityType == "BOOLEAN") {
                                answerObj.yesNo = answer;
                                if ($scope.formValuesMap[key].maxScore != null &&
                                    $scope.formValuesMap[key].scoreCounted == true) {
                                    answerObj.acquiredScore = answerObj.yesNo == "Y" ? $scope.formValuesMap[key].maxScore : 0
                                }
                            }
                            if ($scope.formValuesMap[key].entityType == "NUMBER") {
                                answerObj.numberValue = answer;
                                if ($scope.formValuesMap[key].maxScore != null && $scope.formValuesMap[key].scoreCounted == true) {
                                    answerObj.acquiredScore = answer;
                                }
                            }
                            if ($scope.formValuesMap[key].entityType == "DATE") {
                                answerObj.dateValue = answer;
                            }
                            if ($scope.formValuesMap[key].entityType == "TIME") {
                                answerObj.timeValue = answer;
                            }
                            if ($scope.formValuesMap[key].entityType == "SELECT_BOX" ||
                                $scope.formValuesMap[key].entityType == "RADIO") {
                                answerObj["option" + answer.id] = answer.value;
                                if (answer.marks != null) {
                                    answerObj["option" + answer.id + "Marks"] = answer.marks;
                                }
                                if ($scope.formValuesMap[key].maxScore != null && $scope.formValuesMap[key].scoreCounted == true) {
                                    answerObj.acquiredScore = answer.marks;
                                }
                            }
                            if ($scope.formValuesMap[key].entityType == "PRODUCT_EMPLOYEE_RADIO") {
                                answerObj["option" + answer.radio.id] = answer.radio.value;
                                if (answer.radio.marks != null) {
                                    answerObj["option" + answer.radio.id + "Marks"] = answer.radio.marks;
                                }
                                if ($scope.formValuesMap[key].maxScore != null && $scope.formValuesMap[key].scoreCounted == true) {
                                    answerObj.acquiredScore = answer.radio.marks;
                                }
                                answerObj.employeeId = answer.employee.id;
                                answerObj.employeeName = answer.employee.name;
                                answerObj.employeeDesignation = answer.employee.designation;
                                answerObj.productId = answer.product.id;
                                answerObj.productName = answer.product.name;
                            }
                            if ($scope.formValuesMap[key].entityType == "TEXTAREA_EMPLOYEE_RADIO") {
                                answerObj["option" + answer.radio.id] = answer.radio.value;
                                if (answer.radio.marks != null) {
                                    answerObj["option" + answer.radio.id + "Marks"] = answer.radio.marks;
                                }
                                if ($scope.formValuesMap[key].maxScore != null && $scope.formValuesMap[key].scoreCounted == true) {
                                    answerObj.acquiredScore = answer.radio.marks;
                                }
                                answerObj.employeeId = answer.employee.id;
                                answerObj.employeeName = answer.employee.name;
                                answerObj.employeeDesignation = answer.employee.designation;
                                answerObj.textArea = answer.textArea;
                            }
                        }

                        $scope.auditData.values.push(answerObj);
                    });
                    $scope.auditData.auditor = {
                        id: StorageUtil.getUserMeta().id,
                        code: "",
                        name: StorageUtil.getUserMeta().name
                    };
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.auditManagement.audit,
                        data: $scope.auditData,
                    }).then(function success(response) {
                        if (response.data != null && typeof response.data == 'number') {
                            $scope.closeModal();
                            toastr.success("Audit submitted successfully!");
                            showAuditScore(response.data);
                            /*if($scope.auditForm.askWarningLetter == true){
                                issueWarningConfirmation(response.data);
                            }*/
                        } else {
                            toastr.error('Error submitting audit form.');
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        if (response && response.errorMsg) {
                            toastr.error(response.errorMsg);
                        } else {
                            toastr.error('Error submitting audit form.');
                        }
                        $rootScope.rootLoading = false;
                    });
                } else {
                    $scope.showErrors = true;
                }

            };

            function validForm() {
                if (!$scope.validateOnSubmit) {
                    return true;
                } else {
                    $scope.errorList = [];
                    $scope.formRenderObj.map(function (item) {
                        if (item.entityType == "QUESTION_GROUP") {
                            item.items.map(function (val) {
                                validValue(val, item.questionOptional);
                            });
                        }
                    });
                    //console.log($scope.errorList);
                    return $scope.errorList.length === 0;
                }
            }

            function validValue(question, headOptional) {
                var optional = headOptional || question.questionOptional;
                if (!optional) {
                    var formValue = $scope.formValuesMap[question.id];
                    if (formValue != null) {
                        var answerObj = $scope.answerMap[question.id];
                        if (!answerObj) {
                            if(formValue.isMandatory == true){
                                $scope.errorList.push('Please fill: ' + $scope.formValuesMap[question.id].entityLabel);
                            }
                        } else {
                            if (answerObj.na != true) {
                                var answerValue = answerObj.answer;
                                var comment = answerObj.comment;
                                if (!answerValue) {
                                    $scope.errorList.push('Please fill: ' + $scope.formValuesMap[question.id].entityLabel);
                                } else {
                                    if (formValue.entityType == "NUMBER" && formValue.scoreCounted && answerValue > formValue.maxScore) {
                                        $scope.errorList.push('Value for' + $scope.formValuesMap[question.id].entityLabel +
                                            " cannot be greater than " + formValue.maxScore);
                                    }
                                    if (formValue.entityType == "PRODUCT_EMPLOYEE_RADIO") {
                                        if (!answerValue.radio) {
                                            $scope.errorList.push('Please fill value for ' + $scope.formValuesMap[question.id].entityLabel + ' marks');
                                        }
                                        if (!answerValue.product) {
                                            $scope.errorList.push('Please fill value for ' + $scope.formValuesMap[question.id].entityLabel + ' product');
                                        }
                                        if (!answerValue.employee) {
                                            $scope.errorList.push('Please fill value for ' + $scope.formValuesMap[question.id].entityLabel + ' employee');
                                        }
                                    }
                                    if (formValue.entityType == "TEXTAREA_EMPLOYEE_RADIO") {
                                        if (!answerValue.radio) {
                                            $scope.errorList.push('Please fill value for ' + $scope.formValuesMap[question.id].entityLabel + ' marks');
                                        }
                                        if (!answerValue.employee) {
                                            $scope.errorList.push('Please fill value for ' + $scope.formValuesMap[question.id].entityLabel + ' employee');
                                        }
                                        if (!answerValue.textArea) {
                                            $scope.errorList.push('Please fill value for ' + $scope.formValuesMap[question.id].entityLabel + ' text');
                                        }
                                    }
                                    if (formValue.additionalComment == true && !comment) {
                                        var acquiredScore = null;
                                        if (formValue.entityType == "NUMBER") {
                                            acquiredScore = answerValue;
                                        } else if (formValue.entityType == "RADIO" || formValue.entityType == "SELECT_BOX") {
                                            acquiredScore = answerValue.marks;
                                        } else if (formValue.entityType == "BOOLEAN") {
                                            acquiredScore = answerValue == "Y" ? formValue.maxScore : 0;
                                        } else if (formValue.entityType == "PRODUCT_EMPLOYEE_RADIO" || formValue.entityType == "TEXTAREA_EMPLOYEE_RADIO") {
                                            acquiredScore = answerValue.radio.marks;
                                        }
                                        /*if (formValue.scoreCounted == true) {
                                            if (formValue.maxScore != null && acquiredScore != null && acquiredScore < formValue.maxScore) {
                                                $scope.errorList.push('Please fill comment for ' + $scope.formValuesMap[question.id].entityLabel);
                                            }
                                        } else {
                                            $scope.errorList.push('Please fill comment for ' + $scope.formValuesMap[question.id].entityLabel);
                                        }*/
                                        if (formValue.maxScore != null && acquiredScore != null && acquiredScore < formValue.maxScore) {
                                            $scope.errorList.push('Please fill comment for ' + $scope.formValuesMap[question.id].entityLabel);
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
            }

            function showAuditScore(auditId) {
                var scoreText;
                if($scope.projected.percentage>80) {
                    scoreText = "<span style='font-weight: bold;font-size: 21px; color:#1ca62c'>"+$scope.projected.percentage+"%</span>";
                } else if($scope.projected.percentage<=80 && $scope.projected.percentage>=60) {
                    scoreText = "<span style='font-weight: bold;font-size: 21px; color:#ff9127'>"+$scope.projected.percentage+"%</span>";
                }else if($scope.projected.percentage<0){
                    scoreText = "<span style='font-weight: bold;font-size: 21px; color:#ff0e0b'>"+0+"%</span>";
                } else{
                    scoreText = "<span style='font-weight: bold;font-size: 21px; color:#ff0e0b'>"+$scope.projected.percentage+"%</span>";
                }

                var details="Submitted by: <span style='font-weight: bold;'>"+$scope.auditData.auditor.name +"</span> on Unit <span style='font-weight: bold;'>"+$scope.auditData.auditUnit.name+" "+"<span style='font-weight: bold;'> at "+$scope.auditData.auditTime+"</span>";
                var message = "Your response has been submitted. You have achieved "+scoreText+" score";
                var modalHtml = '<div class="modal-body">' + message +'<br>'+details+ '</div>';
                modalHtml += '<div class="modal-footer"><button class="btn btn-primary" ng-click="ok()">Close</button></div>';

                console.log("$scope.auditData", $scope.auditData);

                var modalInstance = $uibModal.open({
                    template: modalHtml,
                    controller: ModalInstanceCtrl
                });
                modalInstance.result.then(function () {
                    if($scope.auditForm.askWarningLetter == true){
                        issueWarningConfirmation(auditId);
                    } else {
                        $state.go("dashboard.forms");
                    }
                });

            }

            function issueWarningConfirmation(auditId) {
                var message = "Do you want to issue warning to anyone ?";
                var modalHtml = '<div class="modal-body">' + message + '</div>';
                modalHtml += '<div class="modal-footer"><button class="btn btn-primary" ng-click="ok()">Yes</button><button class="btn btn-warning" ng-click="cancel()">Not Now</button></div>';

                console.log("$scope.auditData", $scope.auditData);

                var modalInstance = $uibModal.open({
                    template: modalHtml,
                    controller: ModalInstanceCtrl
                });
                $scope.auditData.id = auditId;
                modalInstance.result.then(function () {
                    $scope.issueWarningLetter($scope.auditData);
                });
            };

            $scope.issueWarningLetter = function (audit) {
                var auditDetails = {
                    unitDetails: audit.auditUnit,
                    mod: audit.managerOnDuty,
                    auditId: audit.id
                };
                var params = {
                    viewType: 'add',
                    audit: auditDetails,
                    warningId: null
                };
                $state.go("dashboard.warningDetail", params);
            };

            $scope.cancel = function () {
                $uibModalInstance.dismiss('cancel');
            };

            $scope.closeModal = function () {
                $uibModalInstance.close('success');
            }

        }
    ]);

var ModalInstanceCtrl = function ($scope, $uibModalInstance, $location) {
    $scope.ok = function () {
        $uibModalInstance.close();
    };

    $scope.cancel = function () {
        $uibModalInstance.dismiss('cancel');
        $location.path("dashboard/forms");
    };
};
