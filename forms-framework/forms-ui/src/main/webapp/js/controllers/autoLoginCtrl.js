
'use strict';

angular.module('formsApp')
    .controller('autoLoginCtrl', ['$rootScope','$scope','$location','APIJson','$cookieStore','$http', 'StorageUtil',
            function ($rootScope,$scope,$location, APIJson, $cookieStore, $http, StorageUtil) {

                $scope.init = function(){
                	$scope.loginType = "CAFE";
                    $scope.permissions = null;
                    $scope.userId = null;
                    $scope.passcode=null;
                    $scope.loading = false;
                    $scope.autoLogin();
                };

                $scope.autoLogin = function () {
                    var key = encodeURIComponent($location.search().key);
                    $rootScope.auth = key;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.users.sessionLogin,
                        data: "FORMS_SERVICE"
                    }).then(function success(response) {
                        $scope.loginResponse(response);
                    }, function error(response) {
                        $scope.loginError(response);
                    });
                };

                $scope.loginResponse = function (response) {
                    if (response.data == null || response.data.sessionKeyId == null) {
                        $scope.errorMessage = 'Credentials are not correct!';
                        $scope.error = true;
                    } else {
                        $scope.message = 'Authentication successful. Redirecting to dashboard!';
                        $scope.successMessage = true;
                        $cookieStore.put("auth",response.data.jwtToken);
                        $rootScope.auth = response.data.jwtToken;
                        $rootScope.aclData = response.data.acl;
                        var meta = StorageUtil.getUserMeta();
                        meta.name = response.data.user.name;
                        meta.id = response.data.user.id;
                        meta.unitId = ($scope.loginType === "CAFE" ? response.unitId : null);
                        meta.department = response.data.user.department.name;
                        meta.designation = response.data.user.designation.name;
                        StorageUtil.setUserMeta(meta);
                        StorageUtil.setAclData($rootScope.aclData);
                        $location.url($location.path);
                        $location.path("/dashboard");
                    }
                    $scope.loading = false;
                };

                $scope.loginError = function (response) {
                    $location.url($location.path);
                    $location.path("/login?type=CAFE");
                }
            }
        ]
    );