'use strict';

angular.module('formsApp')
    .controller('batchCodeManagementCtrl',['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil', 'MetadataService', 'toastr', '$uibModal','$stateParams', 'StorageUtil','$state',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil, MetadataService, toastr, $uibModal,$stateParams, StorageUtil,$state) {
            var searchParams = {};
            $scope.init = function () {
                $scope.userDetails = StorageUtil.getUserMeta();
                $scope.batchCodeDetails = {};
                $scope.selectedProduct = "";
                $scope.selectedUnit = "";
                $scope.batchCodeCount = 0;
                $scope.batchCodeLength = 0;
                $scope.isUnitMissing = false;
                $scope.isProductMissing = false;
                $scope.isCodeCountMissing = false;
                $scope.isCodeLengthMissing = false;
                $scope.isCodeCountInvalid = false;
                $scope.isCodeLengthInvalid = false;
                $scope.isDownloadButtonActive = false;
                $scope.batchCodeLength = 6;
                $scope.productPrefix = "";
                $scope.unitPrefix = "";
                $scope.packCodesFilePath = "";
                $scope.generatedBatchCode = "";

                MetadataService.getUnitSubCategories(function (subCategoriesList) {
                    $scope.subCategories = subCategoriesList;
                }, function (error) {
                    toastr.error(error);
                });
                MetadataService.getUnitListBySubCategory("WAREHOUSE", "DKC_PLANT", function (unitList) {
                    $scope.units = unitList;
                    console.log("The selected unit is:: ", $scope.selectedUnit);
                }, function (error) {
                    toastr.error(error);
                });
            };

            $scope.setBatchUnit = function (selectedUnit) {
                if (selectedUnit != null) {
                    $scope.selectedUnit = selectedUnit;
                    $scope.isUnitMissing = false;
                    $rootScope.rootLoading = true;
                    console.log("selected unit id is :: " + $scope.selectedUnit.id);
                    MetadataService.getCodePrefix($scope.selectedUnit.id,'UNIT', function (response) {
                    	$scope.unitPrefix = response;
                    }, function (error) {
                    	$scope.unitPrefix = "";
                        toastr.error(error);
                    });
                    MetadataService.getSKUForUnit($scope.selectedUnit.id, function (unitProducts) {
                        $rootScope.rootLoading = false;
                        $scope.unitProducts = unitProducts;
                    }, function (error) {
                        $rootScope.rootLoading = false;
                        toastr.error(error);
                    });
                    $rootScope.rootLoading = false;
                }
            };

            $scope.setBatchProduct = function (selectedProduct) {
                if (selectedProduct != null) {
                    $scope.selectedProduct = selectedProduct;
                    $scope.isProductMissing = false;
                    $rootScope.rootLoading = true;
                    console.log("selected Product id is :: " + $scope.selectedProduct.id);
                    MetadataService.getCodePrefix($scope.selectedProduct.id,'SKU', function (response) {
                        $rootScope.rootLoading = false;
                    	$scope.productPrefix = response;
                    }, function (error) {
                    	$scope.productPrefix = "";
                        $rootScope.rootLoading = false;
                        toastr.error(error);
                    });
                }
            };

            $scope.validateBatchCodeCount = function () {
                var count =  $scope.batchCodeCount;
                if(count === 0){
                    console.log("Please enter number of codes to generate");
                    $scope.isCodeCountMissing = true;
                }else{
                    $scope.isCodeCountMissing = false;

                    console.log("entered batch code count is :: ", count);
                    if(isNaN(count) || count < 1 || count % 1 !== 0 ){
                        $scope.isCodeCountInvalid = true;
                        console.log("Pack code count is not valid")
                    }else{
                        $scope.isCodeCountInvalid = false;
                        console.log("Pack code count is valid")
                    }
                }
            };

            $scope.validateBatchCodeLength = function () {
                var count =  $scope.batchCodeLength;
                if(count === 0){
                    console.log("Please enter number of codes to generate");
                    $scope.isCodeLengthMissing = true;
                }else{
                    $scope.isCodeLengthMissing = false;
                    console.log("entered batch code length is :: ", count);
                    if(isNaN(count) || count < 1 || count % 1 !== 0 || count > 15){
                        $scope.isCodeLengthInvalid = true;
                        console.log("Pack code's length is not valid");
                    }else{
                        $scope.isCodeLengthInvalid = false;
                        console.log("Pack code's length is valid");
                    }
                }
            };

            $scope.generatePackCodes = function () {
            	$scope.generatedBatchCode = "";
                $scope.packCodesFilePath = "";
                $scope.isDownloadButtonActive = false

                if($scope.selectedUnit === "" ||  $scope.unitPrefix == ""){
                    console.log("Please select a unit with valid prefix");
                    $scope.isUnitMissing = true;
                    return;
                }else{
                    $scope.isUnitMissing = false;
                }
                if($scope.selectedProduct === "" || $scope.productPrefix == ""){
                    console.log("Please select a product with valid prefix");
                    $scope.isProductMissing = true;
                    return;
                }else{
                    $scope.isProductMissing = false;
                }
                if($scope.batchCodeCount === 0){
                    console.log("Please enter number of codes to generate");
                    $scope.isCodeCountMissing = true;
                    return;
                }else{
                    $scope.isCodeCountMissing = false;
                }
                if($scope.batchCodeLength === 0){
                    console.log("Please enter length of a code to be generated");
                    $scope.isCodeLengthMissing = true;
                    return;
                }else{
                    $scope.isCodeLengthMissing = false;
                }

                $rootScope.rootLoading = true;

                $http({
                    method: 'GET',
                    url: APIJson.urls.batchManagement.generatePackCodes,
                    params: {unitId: $scope.selectedUnit.id,
                             productId: $scope.selectedProduct.id,
                             productName: $scope.selectedProduct.name,
                             count: $scope.batchCodeCount,
                             length: $scope.batchCodeLength}
                }).then(function success(response) {
                    if (response != null && response.data != null && response.data.error == null ) {
                    	console.log("pack codes csv is:: ", response.data.url);
                        toastr.success("Pack codes generated successfully!");
                        $scope.isDownloadButtonActive = true;
                        $scope.packCodesFilePath = response.data.url;
                        $scope.generatedBatchCode = response.data.codePrefix;
                    } else {
                        toastr.error('Pack codes generation failed with error '+response.data.error);
                        $scope.isDownloadButtonActive = false;
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error('Pack codes generation failed, please try after sometime');
                    $rootScope.rootLoading = false;
                });
            };

        }]);