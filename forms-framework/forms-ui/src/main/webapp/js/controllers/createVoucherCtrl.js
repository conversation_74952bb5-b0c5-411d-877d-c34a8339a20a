'use strict';

angular.module('formsApp')
    .controller('createVoucherCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil',
        'MetadataService', 'toastr', '$uibModal', '$stateParams', 'StorageUtil', '$state', '$anchorScroll', 'WalletService',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil,
                  MetadataService, toastr, $uibModal, $stateParams, StorageUtil, $state, $anchorScroll, WalletService) {
            var expenseCategory = {
                name: "PETTY_CASH",
                label: "PETTY CASH"
            };
            var transType = null;
            $scope.init = function () {
                $scope.menuList = [{name: "Detail", action: "detail"}, {name: "Create", action: "create"},
                    {name: "Settle", action: "settle"}, {name: "Acknowledge", action: "acknowledge"},
                    /*{name: "Top Up", action: "topup"}*/];
                $scope.expenseType = [];
                $scope.userDetails = StorageUtil.getUserMeta();
                $scope.authDetails = {userId: $scope.userDetails.id};
                $scope.actionDetail = {};

                $scope.otpDetails = {};
                $scope.otpDetails.otpSent = false;
                $scope.otpDetails.otpCount = 0;
                $scope.otpDetails.otpVerified = false;
                $scope.countMap = {};

                $scope.walletData = {};
                $scope.selectedAccountType=null;
                $scope.resetSearchDetails();
                $scope.associatedId=null;
                $scope.businessCostCenter=null;
                $scope.storeBccList=[];
                $scope.canAllocateCostToCafes=false;
                $scope.selectedAccount= {};
                $scope.selfEmployee =$scope.userDetails.id+'_'+$scope.userDetails.name;
                console.log("SelfEmployee:::",$scope.selfEmployee);
                $scope.selfEmployeeList=[];
                $scope.isSuccessfulAllocation=MetadataService.isSuccessfulAllocation;
                $scope.issuerDetails = {};
                $scope.loading = false;
                $scope.dateOptions = {
                    dateDisabled: function () {
                        return false;
                    },
                    formatYear: 'yyyy',
                    maxDate: new Date(),
                    startingDay: 1
                };
                $scope.errorMessage = '';
                $scope.error = false;
                    setSelectedAccountType();
                $scope.accounts = [];
                $scope.isAccountSelected=false;
                $scope.selectedWalletBccMappings =[];
                $scope.walletDataForPettyCash = {};
                MetadataService.getUnitList(function (unitList) {
                    for (var i = 0; i < unitList.length; i++) {
                        if (unitList[i].id == $scope.userDetails.unitId) {
                            $scope.selectedUnit = unitList[i];
                            break;
                        }
                    }
                }, function (error) {
                    toastr.error(error);
                });
                getEmployeeOfUnit();
                /**
                 * update action must be called after  declaring $scope.otpDetails
                 */
                getWalletDetails($scope.updateAction);
                //getExpenseType();
                AppUtil.getExpenseType(expenseCategory, function (expenses) {
                    $scope.expenseType = expenses;
                });
            };

            $scope.filterExpense = function(expense) {
                return expense.desc !== 'Water Supply Not coming' && expense.desc !== 'Conveyance - Others' && expense.desc !== 'Travelling Expenses';
            };

            function setSelectedAccount(){
                $scope.isAccountSelected=true;
                console.log("Selected account::", $scope.selectedAccount);
            }


            $scope.changeSelectAccount =function () {
                getWalletBusinessCostCenterMappings($scope.selectedAccount.accountNo);
                $scope.validateCanAllocateCostToCafes($scope.selectedAccount.accountNo);
            }

            function setSelectedAccountType (){
                if(!AppUtil.isEmptyObject($rootScope.loginType)){
                    if($rootScope.loginType==='AGENT'){
                        $scope.selectedAccountType='EMPLOYEE';
                        $scope.associatedId = $scope.userDetails.id;
                    }else{
                        $scope.selectedAccountType='UNIT';
                        $scope.associatedId = $scope.userDetails.unitId;
                    }
                }
            }
            $scope.getInvoiceArr = function () {
                return $scope.searchVoucher.invoiceArr;
            };

            $scope.changeInvoiceArr = function (action, invoiceArr) {
                if (action == 'plus') {
                    invoiceArr.push(invoiceArr.length);
                } else if (action == 'minus') {
                    if (typeof($scope.imageDetails.files) !== 'undefined' && $scope.imageDetails.files.length > 0) {
                        console.log("type : ", typeof $scope.imageDetails.files);
                        $scope.imageDetails.files.splice(invoiceArr.length - 1, 1);
                    }
                    invoiceArr.splice(invoiceArr.length - 1, 1);
                }
            };

            $scope.updateAction = function (type) {
                if (AppUtil.isEmptyObject($scope.walletData)) {
                    var s = "Wallet is not available for accountType:"+$scope.selectedAccountType+"for id :"+$scope.associatedId+".Please Contact finance!";
                    toastr.error(s);
                    return false;
                }
                if (type != 'detail' && !$scope.otpDetails.otpVerified && type != 'verify') {
                    errorDisplay(true, 'Please complete the OTP verification to proceed!');
                    $scope.updateAction('verify');
                    return false;
                }
                if (type != 'create') {
                    $scope.actionDetail.type = type;
                }
                if(type != 'verify' && type !='detail' ){
                    $scope.resetSearchDetails();
                }
                switch (type) {
                    case 'detail':
                        getWalletDetails(angular.noop);
                        break;
                    case 'create':
                        $rootScope.rootLoading = true;
                        $http({
                            method: 'GET',
                            url: APIJson.urls.walletManagement.checkPendingVoucher,
                            params: {
                                associatedId: $scope.associatedId
                            }
                        }).then(function success(response) {
                            if (response.data === true) {
                                $scope.actionDetail.type = type;
                                $scope.voucherDetail = getVoucherData();
                            } else {
                                toastr.error("Something Went Wrong.Please Try Again!");
                            }
                            $rootScope.rootLoading = false;
                        }, function error(response) {
                            var config = {
                                autoDismiss: false,
                                maxOpened: 1
                            };
                            toastr.error(response.errorMsg, "Not Allowed", config);
                            $rootScope.rootLoading = false;
                        });
                        break;
                    case 'settle':
                        initSearchParams();
                        break;
                    case 'acknowledge':
                        getWalletDetails(angular.noop);
                        setSelectedAccountType();
                        $scope.searchVoucher.status = ['PENDING_REJECTION_AM', 'PENDING_REJECTION_FINANCE'];
                        // $scope.getVoucherDetails(true, false);
                        break;
                }
            };

            function initSearchParams() {
                $scope.searchVoucher.startDate = AppUtil.getDate(-1);
                $scope.searchVoucher.endDate = AppUtil.getDate();
            }

            $scope.searchAction = function (voucher, type) {
                $scope.searchVoucher.actionType = type;
                $scope.searchVoucher.invoiceArr = [];
                $scope.voucherDetail = {};
                $scope.selectedVoucher = voucher;
                if (type == 'settle' || type == 'cancel' || type == 'acknowledge') {
                    $scope.voucherDetail = angular.copy(voucher);
                    $scope.voucherDetail.issuedBy = AppUtil.getIdCodeName($scope.authDetails.userId);
                    $scope.voucherDetail.entity = 'CAFE';
                    if (type == 'acknowledge') {
                        $scope.acknowledgeVoucherRejection();
                        //$scope.denominationModal(type, $scope.voucherDetail, $scope.voucherDetail.expenseAmount);
                    }
                }
                if (type == 'view') {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'GET',
                        url: APIJson.urls.walletManagement.voucherDetail,
                        params: {
                            voucherId: voucher.id
                        }
                    }).then(function success(response) {
                        $scope.selectedVoucher = response.data;
                        $scope.activeVoucherId = $scope.selectedVoucher.id;
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $rootScope.rootLoading = false;
                    });
                }
                if (type == 'back') {
                    scrollToElement();
                }
            };

            $scope.acknowledgeVoucherRejection = function () {
                /*if(AppUtil.isEmptyObject($scope.voucherDetail.actionComment)){
                    toastr.error("Please fill Comment.");
                    return false;
                }*/
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.acknowledgePendingRejectedVoucher,
                    data: $scope.voucherDetail
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        toastr.warning("Voucher acknowledged Successfully");
                        $scope.countMap.rejectedVoucher--;
                        $scope.getVoucherDetails(true, false);
                    } else {
                        toastr.error("Error while acknowledging voucher.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error while acknowledging voucher - ", response.errorMsg);
                    $rootScope.rootLoading = false;
                });
            };

            $scope.showInvoice = function (voucher) {
                WalletService.showInvoice(voucher);
            };

            $scope.verifyUser = function () {
                if ($scope.authDetails.userId == null) {
                    errorDisplay(true, 'Please fill user id');
                } else if ($scope.authDetails.password == null) {
                    errorDisplay(true, 'Please fill password');
                } else {
                    var userObj = {
                        userId: $scope.authDetails.userId,
                        password: $scope.authDetails.password,
                        unitId: $scope.userDetails.unitId,
                        terminalId: 0,
                        application: "FORMS_SERVICE"
                    };
                    $scope.loading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.users.login,
                        data: userObj
                    }).then(function success(response) {
                        if (response.data == null || response.data.sessionKeyId == null) {
                            errorDisplay(true, 'Credentials are not correct!');
                        } else {
                            var user = response.data.user;
                            if (user.primaryContact != null) {
                                errorDisplay(false, '');
                                $scope.issuerDetails.name = user.name;
                                $scope.otpDetails.otpSent = true;
                                $scope.issuerDetails.designation = {
                                    id: user.designation.id,
                                    name: user.designation.name
                                };
                                $scope.issuerDetails.contact = user.primaryContact;
                                console.log("$scope.issuerDetails.contact " + $scope.issuerDetails.contact);
                                generateOTP($scope.issuerDetails.contact, 'Wallet Transaction');
                            } else {
                                errorDisplay(true, 'Please Update user primary contact!');
                            }
                        }
                        $scope.loading = false;
                    }, function error(response) {
                        errorDisplay(true, 'Authentication failed! Please make sure you are authorised for access wallet!');
                        $scope.loading = false;
                    });
                }
            };

            $scope.resendOtp = function () {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.resendOTP,
                    data: getUserObject($scope.issuerDetails.contact, 'Wallet Transaction', null)
                }).then(function success(response) {
                    if (response.data) {
                        $scope.otpDetails.otpCount++;
                    } else {
                        errorDisplay(true, 'Network problem. Please try again!');
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = true;
                });
            };

            $scope.runPrimaryChecks = function(){
                if($scope.selectedAccount ==null){
                    toastr.error("Account selection is mandatory.Please select account first ! ")
                    return false;
                }

                if ($scope.voucherDetail.issuedAmount <= 0) {
                    toastr.error("Please Enter Valid Amount!");
                    return false;
                }

                if($scope.voucherDetail.expenseMetadataId == undefined || $scope.voucherDetail.expenseMetadataId==null){
                    toastr.error("Please select an issue reason!");
                    return false;
                }
            }
            $scope.createVoucher = function () {
                $scope.runPrimaryChecks();
                if($scope.selectedAccountType !=null && $scope.selectedAccountType ==='UNIT'  && AppUtil.isEmptyObject($scope.authDetails.issuedTo)){
                    toastr.error("Please select the issuer name !");
                    return false;
                }
                if($scope.canAllocateCostToCafes=== false){
                    var obj ={};
                    obj.businessCostCenterId=$scope.selectedWalletBccMappings[0].bccId;//person can book expense against the cost center he is allocated to
                    obj.businessCostCenter=$scope.selectedWalletBccMappings[0].bccName;
                    obj.allocatedIssuedAmount=$scope.voucherDetail.issuedAmount;
                    $scope.voucherDetail.voucherCostCenterAllocations=[];
                    $scope.voucherDetail.voucherCostCenterAllocations.push(obj);
                }
                if($scope.selectedAccountType !=null && $scope.selectedAccountType ==='EMPLOYEE'){
                    $scope.voucherDetail.issuedTo=AppUtil.getIdCodeName($scope.associatedId);
                }else{
                    $scope.voucherDetail.issuedTo = AppUtil.getIdCodeName($scope.authDetails.issuedTo);
                }
                $scope.voucherDetail.issuedBy = AppUtil.getIdCodeName($scope.authDetails.userId);
                $scope.expenseType.map(function (expense) {
                    if(expense.id == $scope.voucherDetail.expenseMetadataId) {
                        $scope.voucherDetail.expenseType = expense.desc;
                        $scope.voucherDetail.expenseCategory = expense.type;
                        $scope.voucherDetail.accountableInPNL = expense.accountable;
                        $scope.voucherDetail.budgetCategory = expense.budgetCat;
                    }
                });
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.issueVoucher,
                    data: $scope.voucherDetail
                }).then(function success(response) {
                    if (response.data != null || response.data != undefined) {
                        toastr.warning("Voucher Generated Successfully with id " + response.data);
                        resetVoucherDetails();
                        $scope.updateAction('settle');
                    } else {
                        toastr.error("Error while issuing voucher.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Voucher not issued : " + response.errorMsg);
                    $rootScope.rootLoading = false;
                });
            };

            $scope.verifyOTP = function () {
                if ($scope.authDetails.otp == null || $scope.authDetails.otp == undefined) {
                    errorDisplay(true, 'Please Enter OTP to verify!');
                    return false;
                }
                if ($scope.authDetails.otp.length == 4) {
                    errorDisplay(false, '');
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.walletManagement.verifyOTP,
                        data: getUserObject($scope.issuerDetails.contact, 'Wallet Transaction', $scope.authDetails.otp)
                    }).then(function success(response) {
                        if (response.data) {
                            $scope.otpDetails.otpVerified = true;
                            getAccounts();
                            setSelectedAccountType();
                            $scope.updateAction('detail');
                        } else {
                            errorDisplay(true, 'Incorrect One Time Password. Please enter again!');
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $rootScope.rootLoading = false;
                    });
                }
            };

            /**
             * click : if false then scroll to current voucher
             * dateCheck : if date range is required or not
             */
            $scope.getVoucherDetails = function (click, dateCheck) {
                var download = false;
                if (dateCheck) {
                    if (!$scope.searchVoucher.startDate || !$scope.searchVoucher.endDate) {
                        toastr.info("Please select start date and end date!");
                        return false;
                    }
                    download = WalletService.downloadVoucher($scope.searchVoucher, true);
                }
                if (!download) {
                    $scope.searchAction(null, 'back');
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'GET',
                        url: APIJson.urls.walletManagement.voucherDetails,
                        params: {
                            accountNo: ($scope.selectedAccount.accountNo.accountNo != undefined ? [$scope.selectedAccount.accountNo.accountNo] : null),
                            startDate: ($scope.searchVoucher.startDate != null ? AppUtil.formatDate($scope.searchVoucher.startDate, 'yyyy-MM-dd') : null),
                            endDate: ($scope.searchVoucher.endDate != null ? AppUtil.formatDate($scope.searchVoucher.endDate, 'yyyy-MM-dd') : null),
                            status: ($scope.searchVoucher.status != undefined ? getSearchStatus($scope.searchVoucher.status) : null),
                            isReimbursed: ($scope.searchVoucher.isReimbursed != undefined ? $scope.searchVoucher.isReimbursed : null)
                        }
                    }).then(function success(response) {

                        $scope.voucherList = response.data;
                        $scope.voucherList.map(function (voucher) {
                            if(voucher.fileDetails.length == 0) {
                                voucher.hasInvoice = false;
                                voucher.hasSupporting = false;
                            } else {
                                voucher.fileDetails.map(function (file) {
                                    if(file.fileType == "INVOICE"){
                                        voucher.hasInvoice = true;
                                    }
                                    if(file.fileType == "SUPPORTING"){
                                        voucher.hasSupporting = true;
                                    }
                                })
                            }
                            $scope.expenseType.map(function (expense) {
                                if(expense.id == voucher.expenseMetadataId) {
                                    expense.validations.map(function (validation) {
                                        if(validation.validationType == "RESTRICTION") {
                                            if(validation.validationName == "Invoice Mandatory"){
                                                voucher.invoiceMandatory = true;
                                            }
                                            if(validation.validationName == "Supporting Mandatory"){
                                                voucher.supportingMandatory = true;
                                            }
                                        }
                                    })
                                }
                            });
                        });
                        if (click === false) {
                            scrollToElement();
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $rootScope.rootLoading = false;
                    });
                }
            };

            function getSearchStatus(status) {
                if (typeof status == 'string') {
                    return [status];
                }
                return status;
            }

            function scrollToElement() {
                var newHash = 'voucher_' + $scope.activeVoucherId;
                if ($scope.activeVoucherId != -1) {
                    $location.hash(newHash);
                } else {
                    $anchorScroll();
                }
                $scope.activeVoucherId = -1;
            }

            $scope.uploadVoucherDoc = function (docType, voucher, doc) {
                if(doc == null){
                    toastr.error("Please select voucher " + docType.toLowerCase() + " for upload!");
                    return false;
                }
                var data = new FormData();
                data.append("entityId", $scope.selectedVoucher.id);
                data.append("fileType", docType);
                data.append("file", doc);
                var config = {
                    transformRequest: angular.identity,
                    transformResponse: angular.identity,
                    headers: {
                        'Content-Type': undefined
                    }
                };
                $rootScope.rootLoading = true;
                $http.post(APIJson.urls.walletManagement.uploadVoucherDoc, data, config).then(
                    function (response) {
                        if (response.data == "true") {
                            $rootScope.rootLoading = false;
                            toastr.success("Voucher " + docType.toLowerCase() + " uploaded Successfully.");
                            if(docType == "INVOICE") {
                                voucher.hasInvoice = true;
                            }
                            if(docType == "SUPPORTING") {
                                voucher.hasSupporting = true;
                            }
                        } else {
                            $rootScope.rootLoading = false;
                            toastr.error("Error while uploading invoice.Please try later!");
                        }
                    },
                    function (response) {
                        $rootScope.rootLoading = false;
                        toastr.error("Error while uploading image.Please try later!");
                    });
            };

            $scope.updateVoucher = function (cancel) {
                if (!cancel && $scope.voucherDetail.expenseAmount < 0) {
                    toastr.error("Please Enter Valid Amount!");
                    return false;
                }
                if(!cancel && !$scope.voucherDetail.voucherDate){
                    toastr.error("Please fill the invoice Date");
                    return false;
                }
                if (!cancel && $scope.voucherDetail.expenseType === 'COGS - Dairy' && $scope.voucherDetail.grNumber <= 0) {
                    toastr.error("Please Enter Valid GR Number!");
                    return false;
                }
                var url = null;
                if (cancel) {
                    if (AppUtil.isEmptyObject($scope.voucherDetail.actionComment)) {
                        toastr.error("Please fill comment.");
                        return false;
                    }
                    url = APIJson.urls.walletManagement.cancelVoucher;
                    processVoucher(url, true);
                } else {
                    if ($scope.voucherDetail.expenseType === 'COGS - Dairy') {
                        if (AppUtil.isEmptyObject($scope.voucherDetail.grNumber)) {
                            toastr.error("Please fill GR Number");
                            return false;
                        }

                        $scope.checkIsSpecialOrder($scope.voucherDetail.grNumber, function (isSpecialOrder) {
                            if (!isSpecialOrder) {
                                return;
                            }
                            console.log("Proceeding with special order...");
                            validateAndProcessVoucher();
                        });
                    } else {
                        validateAndProcessVoucher();
                    }
                }
            };

            $scope.checkIsSpecialOrder = function(grNumber, callback) {
                $rootScope.rootLoading = true;
                $http({
                    method: 'GET',
                    url: APIJson.urls.goodsOrderManagement.isSpecialOrder,
                    params: { grId: grNumber }
                }).then(function success(response) {
                    $rootScope.rootLoading = false;
                    if (response.data != null && response.data == true) {
                        console.log("GR Number verified");
                        callback(true);
                    } else {
                        toastr.error("This is not a specialized Order, please verify GR");
                        callback(false);
                    }
                }, function error(response) {
                    toastr.error("Error while updating voucher - " + response.errorMsg);
                    $rootScope.rootLoading = false;
                    callback(false);
                });
            };

            function validateAndProcessVoucher() {
                if (AppUtil.isEmptyObject($scope.voucherDetail.expenseAmount)) {
                    toastr.error("Please fill expense amount.");
                    return false;
                } else if ($scope.voucherDetail.expenseAmount > $scope.voucherDetail.issuedAmount) {
                    toastr.error("Expense amount should not be greater than the issued amount");
                    return false;
                } else if (!$scope.voucherDetail.voucherDate) {
                    toastr.error("Please fill the invoice Date");
                    return false;
                } else if ($scope.voucherDetail.expenseAmount > $scope.voucherDetail.issuedAmount &&
                    AppUtil.isEmptyObject($scope.voucherDetail.actionComment)) {
                    toastr.error("Please fill comment.");
                    return false;
                }

                processVoucher(APIJson.urls.walletManagement.settleVoucher, false);
            }

            function processVoucher(url, isCancel) {
                if (isCancel || $scope.validVoucher()) {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: url,
                        data: $scope.voucherDetail
                    }).then(function success(response) {
                        if (response.data != null && response.data == true) {
                            toastr.success("Voucher updated Successfully");
                            /*if ($scope.voucherDetail.expenseAmount < $scope.voucherDetail.issuedAmount) {
                                AppUtil.confirmModal("Update Petty Cash", "Have you deposited remaining amount to petty cash ?").then(function () {
                                });
                            }*/
                            $scope.activeVoucherId = $scope.voucherDetail.id;
                            $scope.getVoucherDetails(false, true);
                        } else {
                            toastr.error("Error while updating voucher.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error while updating voucher - ", response.errorMsg);
                        $rootScope.rootLoading = false;
                    });
                }
            }

            $scope.validVoucher = function () {
                var selectedVoucher = null;
                $scope.voucherList.map(function (voucher) {
                    if(voucher.id == $scope.voucherDetail.id) {
                        selectedVoucher = voucher;
                    }
                });
                var selectedExpense = null;
                $scope.expenseType.map(function (expense) {
                    if (expense.desc == $scope.voucherDetail.expenseType) {
                        selectedExpense = expense;
                    }
                });
                var valid = true;
                var invoiceMandatory = false;
                var supportingMandatory = false;
                selectedExpense.validations.map(function (validation) {
                    if (validation.validationType == "RESTRICTION") {
                        if(validation.validationName == "Invoice Mandatory"){
                            invoiceMandatory = true;
                        }
                        if (validation.validationName == "Supporting Mandatory"){
                            supportingMandatory = true;
                        }
                        /*if (valid && validation.validationName == "Invoice Mandatory") {
                            if(!selectedVoucher.hasInvoice) {
                                toastr.error("Please upload voucher invoice.");
                            }
                            valid = selectedVoucher.hasInvoice;
                        }
                        if (valid && validation.validationName == "Supporting Mandatory") {
                            if(!selectedVoucher.hasSupporting) {
                                toastr.error("Please upload voucher supporting document.");
                            }
                            valid = selectedVoucher.hasSupporting;
                        }*/
                    }
                });
                if(invoiceMandatory && supportingMandatory){
                    if(!selectedVoucher.hasInvoice && !selectedVoucher.hasSupporting){
                        valid = false;
                        toastr.error("Please upload either invoice or supporting document.");
                    }
                } else {
                    if (invoiceMandatory && !selectedVoucher.hasInvoice) {
                        valid = false;
                        toastr.error("Please upload invoice.");
                    }
                    if (supportingMandatory && !selectedVoucher.hasSupporting) {
                        valid = false;
                        toastr.error("Please upload supporting document.");
                    }
                }
                return valid;
            };

            $scope.resetSearchDetails = function () {
                $scope.voucherList = {};
                $scope.searchVoucher = {};
                $scope.searchVoucher.accountNo = $scope.userDetails.unitId;
                $scope.selectedVoucher = null;
                $scope.searchVoucher.invoiceArr = [];
                $scope.activeVoucherId = -1;
                // $scope.selectedAccountType=null;
                $scope.selectedAccount={};
                // $scope.associatedId=null;
                $scope.businessCostCenter=null;
                $scope.storeBccList=[];
                $scope.canAllocateCostToCafes=false;
                $scope.hasOneBccMapping=undefined;
                $scope.selectedAccount= {};
                $scope.selfEmployee =$scope.userDetails.id+'_'+$scope.userDetails.name;
                $scope.isSuccessfulAllocation=MetadataService.isSuccessfulAllocation;
                $scope.selectedWalletBccMappings =[];
                transType = null;
                resetVoucherDetails();
                MetadataService.resetTransactionalValues();
            };

            function resetVoucherDetails() {
                $scope.voucherDetail = {};
                $scope.authDetails.issuedTo = null;
            }

            $scope.downloadInvoice = function (imageId, imageName, actionType) {
                WalletService.downloadInvoice(imageId, imageName, actionType);
            };

            function generateOTP(contactNumber, transType) {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.sendOTP,
                    data: getUserObject(contactNumber, transType, null)
                }).then(function success(response) {
                    if (response.data) {
                        $scope.otpDetails.otpCount++;
                    } else {
                        errorDisplay(true, 'Network problem. Please try again!');
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = true;
                });
            };


            function errorDisplay(error, msg) {
                $scope.error = error;
                $scope.errorMessage = msg;
            }

            function getEmployeeOfUnit() {
                if ($scope.userDetails.unitId != null) {
                    $rootScope.rootLoading = true;
                    MetadataService.getAuditUnitEmployees($scope.userDetails.unitId, function (unitEmployees) {
                        $rootScope.rootLoading = false;
                        $scope.unitEmployees = unitEmployees;
                        isDev();
                    }, function (error) {
                        $rootScope.rootLoading = false;
                        toastr.error(error);
                    });
                }
            }

            function isDev() {
                var url = $location.absUrl();
                if (url.indexOf('dev.kettle.chaayos.com') >= 0) {
                    $scope.authDetails.password = 321321;
                    $scope.authDetails.otp = "1234";
                }
            }

            function getPendingVoucherCount() {
                $rootScope.rootLoading = true;
                $http({
                    method: 'GET',
                    url: APIJson.urls.walletManagement.pendingRejectedVoucherCount+'/'+$scope.associatedId,
                    params: {
                        walletId: $scope.walletData.id
                    }
                }).then(function success(response) {
                    $scope.countMap.rejectedVoucher = response.data;
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = false;
                });
            }

            function getWalletDetails(callback) {
                $rootScope.rootLoading = true;
                console.log("RootScope loginType ::", $rootScope.loginType);
                /*$http({
                    method: 'GET',
                    url: APIJson.urls.walletManagement.walletDetail,
                    params: {
                        accountNo: $scope.userDetails.unitId,
                        accountType: 'UNIT',
                        walletType: 'PETTY_CASH'
                    }
                }).then(function success(response) {
                    $scope.walletData = response.data;
                    if (AppUtil.isEmptyObject($scope.walletData)) {
                        toastr.error("Wallet not available for this unit.Please Contact finance!");
                    } else {
                        callback('verify');
                        getPendingVoucherCount();
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = false;
                });*/
                $http({
                    method: 'GET',
                    url: APIJson.urls.walletManagement.walletDetailByLoginType+'/'+$scope.selectedAccountType,
                    params: {
                        associatedId:$scope.selectedAccountType=='UNIT'?$scope.userDetails.unitId :$scope.userDetails.id,
                    }
                }).then(function success(response) {
                var isWalletPettyCash = false;
                    $scope.walletDataForPettyCash = response.data;
                    // getAccounts();
                    for(var i in response.data){
                    if(response.data[i].walletType == "PETTY_CASH"){
                    $scope.walletData = response.data[i];
                    isWalletPettyCash = true;
                    break;
                    }
                    }
                    if (AppUtil.isEmptyObject($scope.walletData)) {
                        toastr.error("Wallet not available for this unit.Please Contact finance!");
                    } else {
                        callback('verify');
                        getPendingVoucherCount();
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = false;
                });
            }
            function getAccounts(){
                if(!AppUtil.isEmptyObject($scope.walletDataForPettyCash)){
                    for(var wallet of $scope.walletDataForPettyCash){
                        $scope.accounts.push(wallet);
                    }
                }
                // if(!AppUtil.isEmptyObject($scope.accounts)){
                //     $scope.selectedAccount = $scope.accounts[0];
                // }
            }

            $scope.validateCanAllocateCostToCafes=function(selectedWallet){
                if(selectedWallet.canAllocateCostToCafes !=undefined &&  selectedWallet.canAllocateCostToCafes !==null && selectedWallet.canAllocateCostToCafes===true){
                    $scope.canAllocateCostToCafes=true;
                    findBusinessCostCentersByType ();
                }
            }

            function getWalletBusinessCostCenterMappings(selectedWallet){
                MetadataService.getWalletBusinessCostCenterMappings(selectedWallet.id,function(walletBusinessCostCenterMappings){
                    $scope.selectedWalletBccMappings = angular.copy(walletBusinessCostCenterMappings);
                    $scope.hasOneBccMapping = $scope.validateOneOrManyMappings($scope.selectedWalletBccMappings);
                },function(error){
                    toastr.error(error);
                })
            }

            $scope.validateOneOrManyMappings=function(mappingList){
                if(!AppUtil.isEmptyObject(mappingList)){
                    if(mappingList.length==0 ){
                        toastr.error("Wallet must be associated to one business cost center");
                    }else {
                        if(mappingList.length==1){
                            $scope.businessCostCenter=mappingList[0];
                            return true;
                        }
                    }
                }
                return false ;
            }

            $scope.allocateCost= function (){
                $scope.runPrimaryChecks();
                $scope.openAllocateCostToBCCModal();
            }

            $scope.openAllocateCostToBCCModal = function(){
                $scope.actionType ='ADD';
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'openAllocateCostToBCCModalView.html',
                    controller: 'OpenAllocateCostToBCCModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                storeBccList: $scope.storeBccList,
                                businessCostCenterlist:$scope.businessCostCenterlist,
                                walletData:$scope.walletData,
                                userDetails:StorageUtil.getUserMeta(),
                                voucherDetail:$scope.voucherDetail,
                                action:$scope.actionType,
                            }
                        }
                    }
                });
                modalInstance.result.then(function (voucherDetail) {
                    $scope.voucherDetail= angular.copy(voucherDetail);
                    $scope.isSuccessfulAllocation=MetadataService.isSuccessfulAllocation;
                    console.log("Voucher Detail after saving and modal closed ::",  $scope.voucherDetail);
                }, function () {
                });
            }

            $scope.editVoucherCostCenterAllocation = function(selectedVoucherCostCenterAllocation,selectedBusinessCostCenterId){
                $scope.openEditVoucherCostCenterAllocation(selectedVoucherCostCenterAllocation,selectedBusinessCostCenterId);
            }
            $scope.openEditVoucherCostCenterAllocation = function (selectedVoucherCostCenterAllocation,selectedBusinessCostCenterId){
                $scope.selectedBusinessCostCenter = getSelectedBusinessCostCenterById(selectedBusinessCostCenterId);
                console.log("Selected business cost center :::::::::", $scope.selectedBusinessCostCenter);
                $scope.selectedVoucherCostCenterAllocationAmt =getAllocationAmountByBusinessCostCenterId($scope.selectedBusinessCostCenter.bccId);
                $scope.actionType ='EDIT';
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'editAllocateCostToBCCModalView.html',
                    controller: 'EditAllocateCostToBCCModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                storeBccList: $scope.storeBccList,
                                businessCostCenterlist:$scope.businessCostCenterlist,
                                voucherDetail:$scope.voucherDetail,
                                selectedBusinessCostCenter:$scope.selectedBusinessCostCenter,
                                selectedVoucherCostCenterAllocationAmt:$scope.selectedVoucherCostCenterAllocationAmt,
                                selectedVoucherCostCenterAllocation:selectedVoucherCostCenterAllocation,
                                action:$scope.actionType,
                            }
                        }
                    }
                });
                modalInstance.result.then(function (voucherDetail) {
                    $scope.voucherDetail= angular.copy(voucherDetail);
                    $scope.isSuccessfulAllocation=MetadataService.isSuccessfulAllocation;
                    console.log("Is successful allocation::",$scope.isSuccessfulAllocation);
                    console.log("Voucher Detail after saving and modal closed ::",  $scope.voucherDetail);
                }, function () {
                });
            }
            function getSelectedBusinessCostCenterById(selectedBusinessCostCenterId){
                return $scope.businessCostCenterlist.find(function(businessCostCenter){
                    return businessCostCenter.bccId== selectedBusinessCostCenterId;
                });
            }

            function getAllocationAmountByBusinessCostCenterId(selectedBusinessCostCenterId){
                var obj ={};
                if(!AppUtil.isEmptyObject($scope.voucherDetail) && !AppUtil.isEmptyObject($scope.voucherDetail.voucherCostCenterAllocations) && $scope.voucherDetail.voucherCostCenterAllocations.length>0){
                    obj = angular.copy($scope.voucherDetail.voucherCostCenterAllocations.find(function(voucherCostCenterAllocation){
                        return voucherCostCenterAllocation.businessCostCenterId=== selectedBusinessCostCenterId;
                    }));
                }
                if(!AppUtil.isEmptyObject(obj)){
                    return obj.allocatedIssuedAmount;
                }
                return null;
            }

            function findBusinessCostCentersByType(){
                $rootScope.rootLoading=true;
                $http({
                    method:'GET',
                    url:APIJson.urls.unitManagement.businessCostCentersForType
                }).then(function success(response) {
                    $scope.businessCostCentersBytype = response.data;
                    if (AppUtil.isEmptyObject($scope.businessCostCentersBytype)) {
                        toastr.error("Business Cost Center not available for this account");
                    } else {
                        $scope.storeBccList=angular.copy(createAllBusinessCostCenterList($scope.businessCostCentersBytype));
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = false;
                });
            }

            function createAllBusinessCostCenterList (businessCostCentersBytype){
                var arr =[];
                $scope.businessCostCenterlist=[];
                if(!AppUtil.isEmptyObject($scope.businessCostCentersBytype)){
                    for(var wallet of $scope.businessCostCentersBytype){
                        arr.push(wallet.id+"_"+wallet.code+"_"+wallet.name);
                        $scope.businessCostCenterlist.push({bccId:wallet.id,bccCode:wallet.code,bccName:wallet.name});
                    }
                }
                return arr;
            }

            /*function getExpenseType() {
                $scope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.budgetManagement.getExpenseHeader,
                    data: expenseCategory.name
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        $scope.expenseType = response.data;
                    } else {
                        toastr.error("Error while getting expense header.");
                    }
                    $scope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error while getting expense header.");
                    $scope.rootLoading = false;
                });
            };*/

            function getUserObject(contactNumber, transType, otp) {
                var user = {
                    contactNumber: contactNumber,
                    unit: AppUtil.getIdCodeName($scope.userDetails.unitId),
                    terminalId: 0,
                    otpPin: otp,
                    transType: transType
                };
                return user;
            }

            function getVoucherData() {
                var voucherdata = {
                    accountType: $scope.walletData.accountType,
                    accountNo: $scope.walletData.accountNo,
                    walletId: $scope.walletData.id,
                    expenseType: null,
                    expenseDetail: null,
                    entity: 'CAFE',
                    voucherCostCenterAllocations:[],
                };
                return voucherdata;
            }


        }]).controller('OpenAllocateCostToBCCModalCtrl',['$scope', 'toastr', '$uibModal', '$uibModalInstance', 'data', 'AppUtil', '$rootScope','MetadataService',
    function ($scope, toastr, $uibModal, $uibModalInstance, data, AppUtil, $rootScope,MetadataService) {

        $scope.init = function () {
            $scope.storeBccList = data.storeBccList;
            $scope.userDetails=data.userDetails;
            $scope.selectedBusinessCostCenter=null;
            $scope.amountAllocated =null;
            $scope.voucherDetail=data.voucherDetail,
            $scope.businessCostCenterlist=data.businessCostCenterlist;
            $scope.action=data.action;
            console.log("Voucher DETAIL:::::::::",$scope.voucherDetail) ;
        };

        $scope.saveVoucherCostCenterAllocation=function(selectedBusinessCostCenter){
            if(AppUtil.isEmptyObject($scope.voucherDetail.voucherCostCenterAllocations)){
                $scope.voucherDetail.voucherCostCenterAllocations =[];
            }
            if(!AppUtil.isEmptyObject($scope.voucherDetail)){
                MetadataService.saveVoucherCostCenterAllocation($scope.voucherDetail.voucherCostCenterAllocations,$scope.businessCostCenterlist,selectedBusinessCostCenter,false,$scope.amountAllocated,$scope.voucherDetail.issuedAmount, function(voucherCostCenterList){
                    $scope.voucherDetail.voucherCostCenterAllocations= angular.copy(voucherCostCenterList);
                    $scope.isSuccessfulAllocation = MetadataService.isSuccessfulAllocation;
                    $scope.closeModal($scope.voucherDetail);
                },function(error){
                    toastr.error(error);
                });
            }
        }
        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };
        $scope.closeModal = function () {
            $uibModalInstance.close($scope.voucherDetail);
        }
    }

    ]).controller('EditAllocateCostToBCCModalCtrl',['$scope', 'toastr', '$uibModal', '$uibModalInstance', 'data', 'AppUtil', '$rootScope','MetadataService',
    function ($scope, toastr, $uibModal, $uibModalInstance, data, AppUtil, $rootScope,MetadataService) {

        $scope.init = function () {
            $scope.storeBccList = data.storeBccList;
            $scope.userDetails=data.userDetails;
            $scope.selectedBusinessCostCenter=data.selectedBusinessCostCenter;
            $scope.selectedBusinessCostCenterName= $scope.selectedBusinessCostCenter.bccId.toString()+'_'+$scope.selectedBusinessCostCenter.bccCode+'_'+$scope.selectedBusinessCostCenter.bccName;
            $scope.selectedVoucherCostCenterAllocationAmt =data.selectedVoucherCostCenterAllocationAmt;
            $scope.voucherDetail=data.voucherDetail;
            $scope.businessCostCenterlist=data.businessCostCenterlist;
            $scope.action=data.action;
            $scope.isDeleteBtnDisabled = false;
            $scope.selectedVoucherCostCenterAllocation=data.selectedVoucherCostCenterAllocation;
            console.log("Voucher DETAIL:::::::::",$scope.voucherDetail) ;
            console.log("selectedVoucherCostCenterAllocation :::::::::",data.selectedVoucherCostCenterAllocation) ;
        };

        $scope.updateVoucherCostCenterAllocation=function(selectedBusinessCostCenter,selectedVoucherCostCenterAllocation){
            if(!AppUtil.isEmptyObject($scope.voucherDetail) && !AppUtil.isEmptyObject($scope.voucherDetail.voucherCostCenterAllocations) && $scope.voucherDetail.voucherCostCenterAllocations.length>0){
                MetadataService.saveVoucherCostCenterAllocation($scope.voucherDetail.voucherCostCenterAllocations,$scope.businessCostCenterlist,selectedBusinessCostCenter,true,$scope.selectedVoucherCostCenterAllocationAmt,$scope.voucherDetail.issuedAmount, function(voucherCostCenterList){
                    $scope.voucherDetail.voucherCostCenterAllocations= angular.copy(voucherCostCenterList);
                    $scope.closeModal($scope.voucherDetail);
                },function(error){
                    toastr.error(error);
                },selectedVoucherCostCenterAllocation);
            }
        }


        $scope.removeVoucherCostCenterAllocation =function(selectedVoucherCostCenterAllocation){
            if(!AppUtil.isEmptyObject($scope.voucherDetail.voucherCostCenterAllocations)){
                var arr =[];
                var totalAllocatedAmt = 0;
                for (var voucherCostCenterAllocation of $scope.voucherDetail.voucherCostCenterAllocations ){
                    if(voucherCostCenterAllocation.businessCostCenterId !== selectedVoucherCostCenterAllocation.businessCostCenterId){
                        arr.push(voucherCostCenterAllocation);
                        totalAllocatedAmt=totalAllocatedAmt+voucherCostCenterAllocation.allocatedIssuedAmount;
                    }
                }
                $scope.voucherDetail.voucherCostCenterAllocations=angular.copy(arr);
            }
            MetadataService.checkIsSuccessFulAllocation(totalAllocatedAmt,$scope.voucherDetail.issuedAmount);
            $scope.isSuccessfulAllocation = MetadataService.isSuccessfulAllocation;
            MetadataService.totalAllocation=MetadataService.totalAllocation-selectedVoucherCostCenterAllocation.allocatedIssuedAmount;
            $scope.closeModal();
        }
        $scope.disableDeleteBtn=function(){
            $scope.isDeleteBtnDisabled= true;
        }
        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };
        $scope.closeModal = function () {
            $uibModalInstance.close($scope.voucherDetail);
        }
    }

])