/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular.module('formsApp')
.controller('expenseTrackingController', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', 
	'$http','$stateParams','MetadataService','StorageUtil','$state','AppUtil','toastr','$uibModal','$filter',
	function ($rootScope, $scope, $location, APIJson, $cookieStore, $http,$stateParams,MetadataService,StorageUtil,$state,AppUtil, toastr, $uibModal, $filter) {

	var expenseCategoryList = {
			maintenance :	{name : "MAINTENANCE",label : "MAINTENANCE"},
			marketing :	{name : "MARKETING_CORPORATE",label : "MARKETING CORPORATE"}
	};
	var fileName = null;
	var expenseSheetList = [] ;

	$scope.init = function () {
		$scope.errorDetail = null;
		MetadataService.getUnitList(function (unitList) {
			$scope.units = [];
			for (var i = 0; i < unitList.length; i++) {
				if($scope.expenseCategory.name == "MAINTENANCE"){
					if(unitList[i].name.indexOf("Odc") == -1 && unitList[i].name.indexOf("ODC") == -1 ){
						$scope.units.push(unitList[i]);
					}
				}else if($scope.expenseCategory.name == "MARKETING_CORPORATE"){
					if(unitList[i].live === undefined || unitList[i].live === true){
						if(unitList[i].name.indexOf("Odc") == -1 && unitList[i].name.indexOf("ODC") == -1 ){
							$scope.units.push(unitList[i]);
						}
					}
				}
			}
		}, function (error) {
			toastr.error(error);
		});
		$scope.expenseCategory = expenseCategoryList[$stateParams.expenseType];
		$scope.expenseType = [];
		getExpenseType();
		$scope.expenseDetail = {};
		$scope.expenseRequest = {};
		$scope.expenseDetailList = null;
		$scope.viewName = $stateParams.viewType;
		$scope.selectedUnit = {};
		$scope.expenseDetailObj = {};
		$scope.selectedUnitCancel = null;
		$scope.dateObj = {};
		$scope.dateObj.expenseStartdate = new Date();
		$scope.dateObj.expenseEndDate = new Date();
		$scope.totalCost = 0;
	};

	$scope.dateOptions = {
			dateDisabled: function () {
				return false;
			},
			formatYear: 'yyyy',
			maxDate: new Date(),
	//		minDate: new Date(2020, 5, 22),
			startingDay: 1
	};

	/**
	 * Update unit in add view
	 */
	$scope.updateUnitAdd = function(unit){
		if($scope.expenseCategory.name =="MARKETING_CORPORATE"){
			if(angular.isUndefined($scope.expenseDetail.units)){
				$scope.expenseDetail.units = [];
			}else if($scope.expenseDetail.units.length == $scope.units.length){
				toastr.error("You have selected all units.");
				return false;
			}else{
				for (var i = 0; i < $scope.expenseDetail.units.length; i++) {
					if ($scope.expenseDetail.units[i].id == unit.id) {
						toastr.error("You have already added this unit.");
						for (var i = 0; i < $scope.units.length; i++) {
							if($scope.units[i].id == $scope.expenseDetail.units[$scope.expenseDetail.units.length-1].id){
								$scope.selectedUnit.unitDetail = $scope.units[i];
								break;
							}
						}
						return false;
					}
				}
			}
			$scope.selectedUnit.unitDetail = unit;
			$scope.expenseDetail.units.push(getIdCodeName($scope.selectedUnit.unitDetail.id, $scope.selectedUnit.unitDetail.name));
		}
	};
	
	$scope.updateUnitlist = function(unit){
		for (var i = 0; i < $scope.expenseDetail.units.length; i++) {
			if ($scope.expenseDetail.units[i].id == unit.id) {
				$scope.expenseDetail.units.splice(i,1);
				if($scope.selectedUnit.unitDetail != null && $scope.selectedUnit.unitDetail.id == unit.id){
					$scope.selectedUnit.unitDetail = null;
				}
				break;
			}
		}
		
		if($scope.selectedUnit.unitDetail == null && $scope.expenseDetail.units.length > 0){
			for (var i = 0; i < $scope.units.length; i++) {
				if($scope.units[i].id == $scope.expenseDetail.units[$scope.expenseDetail.units.length-1].id){
					$scope.selectedUnit.unitDetail = $scope.units[i];
					break;
				}
			}
		}
	};
	
	$scope.addAllUnit = function(type){
		if(type == 'add'){
			$scope.expenseDetail.units = [];
			for (var i = 0; i < $scope.units.length; i++) {
				$scope.expenseDetail.units.push(getIdCodeName($scope.units[i].id, $scope.units[i].name));
			}
			$scope.selectedUnit.unitDetail = $scope.units[0];
		}else if(type == 'remove'){
			$scope.selectedUnit.unitDetail = null;
			$scope.expenseDetail.units = [];
		}
	};
	
	/**
	 * Update unit in cancel/display view
	 */
	$scope.updateUnitCancel = function(unit){
		$scope.selectedUnitCancel = unit;
	};

	$scope.resetView = function(view){
		$state.reload();
	};

	function getExpenseType() {
		$scope.loading = true;
		$http({
			method: 'POST',
			url: APIJson.urls.budgetManagement.getExpenseHeader,
			data : $scope.expenseCategory.name
		}).then(function success(response) {
			if (response.data != null && Array.isArray(response.data)) {
				$scope.expenseType = response.data;
			} else {
				toastr.error("Error while getting expense header.");
			}
			$scope.loading = false;
		}, function error(response) {
			toastr.error("Error while getting expense header.");
			$scope.loading = false;
		});
	};

	$scope.updateExpenseDetail= function(expenseDetailObj){
		$scope.expenseDetail.expenseType = expenseDetailObj.desc;
		$scope.expenseDetail.expenseCategory  = expenseDetailObj.type;
		$scope.expenseDetail.expenseTypeId = expenseDetailObj.id;
		$scope.expenseDetail.budgetCategoryId = expenseDetailObj.budgetCatId;
		$scope.expenseDetail.budgetCategory = expenseDetailObj.budgetCat;
		$scope.expenseDetail.accountableInPnL = expenseDetailObj.accountable;
	};

	$scope.saveExpenseDetail = function() {
		if ($scope.expenseDetail.amount <= 0) {
			toastr.error("Please enter valid expense amount!");
			return false;
		} else if ($scope.expenseCategory.name =="MAINTENANCE" && $scope.expenseDetail.amount > 10000) {
			toastr.error("Expense amount can not be greater than 10000.");
			return false;
		} else if ($scope.expenseCategory.name =="MARKETING_CORPORATE" && $scope.expenseDetail.amount > 250000) {
			toastr.error("Expense amount can not be greater than 250000.");
			return false;
		}
		$scope.errorDetail = null;
		$scope.expenseDetail.source = "FORMS_SERVICE";   
		$scope.expenseDetail.createdBy = getIdCodeName(StorageUtil.getUserMeta().id); 
		$scope.loading = true;

		if(!angular.isUndefined($scope.expenseDetail.units)){
			if($scope.expenseDetail.units.length < 1){
				toastr.error("Please enter at least one unit!");
				return false;
			}
			expenseDetailsConfirmation();
		}else{
			$scope.expenseDetail.unitId = getIdCodeName($scope.selectedUnit.unitDetail.id);  
			addExpense();
		}
	};
	
	function expenseDetailsConfirmation() {
		var amount = ($scope.expenseDetail.amount / $scope.expenseDetail.units.length);
	    var message = "<div class='modal-body'> Do you want to add following expense ? </div>";
	    var modalHtml = message +'<div class="modal-body"> Total Units = ' + $scope.expenseDetail.units.length + " </br>    Amount Per Unit = Rs. " + amount.toFixed(2) + '</div>';
	    modalHtml += '<div class="modal-footer"><button class="btn btn-primary pull-left" ng-click="ok()">Yes</button><button class="btn btn-warning" ng-click="cancel()">No</button></div>';

	    var modalInstance = $uibModal.open({
	      template: modalHtml,
	      controller: ModalInstanceCtrl,
	      backdrop: 'static',
	      keyboard: false
	    });
	    modalInstance.result.then(function() {
	    	addExpenses();
	    });
	  };
	  
	  var ModalInstanceCtrl = function($scope, $uibModalInstance, $location) {
		  $scope.ok = function() {
			  $uibModalInstance.close();
		  };

		  $scope.cancel = function() {
			  $uibModalInstance.dismiss('cancel');
		  };
	  };
	
	function addExpense(){
		$http({
			method: 'POST',
			url: APIJson.urls.expenseManagement.addExpense,
			data : $scope.expenseDetail
		}).then(function success(response) {
			if (response.data != null) {
				var result = response.data; 
				if(result.errorType != null && result.errorType != '' && result.errorType != "WARNING"){
					$scope.errorDetail ={
							errorMsg : 	result.errorMsg,
							errorType : result.errorType
					};
				}else{
					if(result.errorType == "WARNING"){
						toastr.warning(result.errorMsg);
					}
					toastr.success("Expense saved successfully.");
					$scope.resetView('');
				}
				
			} else {
				toastr.error("Error while saving expense details .");
			}
			$scope.loading = false;
		}, function error(response) {
			toastr.error("Error while saving expense details .");
			$scope.loading = false;
		});
	};
	
	function addExpenses(){
		var expenseDetails = [];
		var unitList = $scope.expenseDetail.units;
		delete $scope.expenseDetail.units;
		for (var i = 0; i < unitList.length; i++) {
			var expenseDetail = angular.copy($scope.expenseDetail);
			expenseDetail.unitId = getIdCodeName(unitList[i].id); 
			expenseDetails.push(expenseDetail);
		}
		$http({
			method: 'POST',
			url: APIJson.urls.expenseManagement.addExpenses,
			data : expenseDetails
		}).then(function success(response) {
			if (response.data != null && response.data === true) {
					toastr.success("Expense saved successfully.");
					$scope.resetView('');
			} else {
				toastr.error("Error while saving expense details .");
			}
			$scope.loading = false;
		}, function error(response) {
			toastr.error("Error while saving expense details .");
			$scope.loading = false;
		});
	}

	$scope.cancelExpenseDetail = function(expenseDetail){
		var modalInstance = $uibModal.open({
			ariaLabelledBy: 'modal-title',
			ariaDescribedBy: 'modal-body',
			templateUrl:  window.version + '/views/expenseCancellationModal.html',
			controller: 'expenseCancellationController',
			backdrop: 'static',
			keyboard: false,
			//controllerAs: '$ctrl',
			size: 'lg',
			resolve: {
				expenseDetail: function () {
					return expenseDetail;
				}
			}
		});

		modalInstance.result.then(function (action) {
			console.log("closed::::::::::::::::::::::::::::::::::::::"+action);
			if(action == 'success'){
				$scope.getExpenseDetail();
			}
		}, function () {
			console.log("cancelled::::::::::::::::::::::::::::::::::::::");
		});
	};

	$scope.getExpenseDetail = function(print) {
		fileName = null;
		expenseSheetList = [];
		if($scope.selectedUnitCancel == null || $scope.selectedUnitCancel == undefined){
			$scope.expenseRequest.unitId = null;
			fileName = "ExpenseDetail_All";
		}else{
			$scope.expenseRequest.unitId = $scope.selectedUnitCancel.id;
			fileName = "ExpenseDetail_"+$scope.selectedUnitCancel.id;
		}
		$scope.totalCost = 0;
		$scope.expenseRequest.expenseCategory = $scope.expenseCategory.name;
		$scope.expenseRequest.startdate = AppUtil.formatDate($scope.dateObj.expenseStartdate, 'yyyy-MM-dd');
		$scope.expenseRequest.endDate = AppUtil.formatDate($scope.dateObj.expenseEndDate, 'yyyy-MM-dd');
		//	console.log("$scope.expenseRequest ",$scope.expenseRequest);
		$scope.loading = true;
		$http({
			method: 'POST',
			url: APIJson.urls.expenseManagement.getExpenseDetail,
			data : $scope.expenseRequest
		}).then(function success(response) {
			if (response.data != null) {
				$scope.expenseDetailList = response.data;
				if(print){
					downloadExpenseSheet();
				}
				$scope.totalCost = 0;
				for(var i =0;i < $scope.expenseDetailList.length;i++){
					if($scope.expenseDetailList[i].status== 'ACTIVE'){
						$scope.totalCost = $scope.expenseDetailList[i].amount + $scope.totalCost;
					}
				}
			} else {
				toastr.error("Error while getting expense types list !");
			}
			$scope.loading = false;
		}, function error(response) {
			toastr.error("Error while getting expense types list !");
			$scope.loading = false;
		});
	};
	
	function downloadExpenseSheet (){
		for(var i =0;i < $scope.expenseDetailList.length;i++){
			var temp = {
				unitName : $scope.expenseDetailList[i].unitId.name,
				expenseType : $scope.expenseDetailList[i].expenseType,
				budgetCategory : $scope.expenseDetailList[i].budgetCategory,
				amount : $scope.expenseDetailList[i].amount,
				comment : $scope.expenseDetailList[i].comment,
				status : $scope.expenseDetailList[i].status,
				createdBy : $scope.expenseDetailList[i].createdBy.name,
				createdOn : $filter('date')($scope.expenseDetailList[i].createdOn, "dd/MM/yyyy"),
				cancelledBy : $scope.expenseDetailList[i].cancelledBy == null ? '-' : $scope.expenseDetailList[i].cancelledBy.name,
				cancelledOn : $scope.expenseDetailList[i].cancelledOn == null ? '-' : $filter('date')($scope.expenseDetailList[i].cancelledOn, "dd/MM/yyyy"),
				cancellationReason : $scope.expenseDetailList[i].cancellationReason == null ? '-' : $scope.expenseDetailList[i].cancellationReason
			};
			expenseSheetList.push(temp);
		}
		AppUtil.JSONToCSVConvertor(expenseSheetList, fileName, true);
	};
	
	function getIdCodeName(dataId,name){
		var idCodeName ={
				id : dataId,
				code : "",
				name : (name == undefined ? "" : name) 
		};
		return idCodeName;
	}

}]);


angular.module('formsApp').
controller('expenseCancellationController', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', 
	'$http','MetadataService','StorageUtil','expenseDetail','$uibModal','$uibModalInstance','toastr',
	function ($rootScope, $scope, $location, APIJson, $cookieStore, $http,MetadataService,StorageUtil,expenseDetail, $uibModal,$uibModalInstance,toastr) {  

	$scope.expenseDetail=expenseDetail;

	$scope.cancelExpenseDetail = function (reason) {
		if(reason == null || reason == undefined || reason.length == 0){
			alert("Please enter Expense detail cancellation reason!");
			return false;
		}
		var idCodeName ={
				id : StorageUtil.getUserMeta().id,
				code : "",
				name : ""
		};
		$scope.expenseDetail.cancelledBy = idCodeName;
		$scope.expenseDetail.cancellationReason = reason;
		$scope.loading = true;

		$http({
			method: 'POST',
			url: APIJson.urls.expenseManagement.cancelExpenseDetail,
			data : $scope.expenseDetail
		}).then(function success(response) {
			if (response.data != null) {
				toastr.success("Expense detail cancelled successfully!");
				$scope.closeModal();
			} else {
				toastr.error('Expense detail can not be cancelled.Please try after some time!');
			}
			$scope.loading = false;
		}, function error(response) {
			toastr.error('Expense detail can not be cancelled.Please try after some time!');
			$scope.loading = false;
		});
	};

    $scope.cancel = function () {
        $uibModalInstance.dismiss('cancel');
    };

    $scope.closeModal = function () {
        $uibModalInstance.close('success');
    }

}]
);
