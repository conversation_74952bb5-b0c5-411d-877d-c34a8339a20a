'use strict';

angular.module('formsApp')
    .controller('formsCtrl', ['$rootScope', '$scope', '$stateParams', '$location', 'APIJson', '$cookieStore', '$http', '$injector',
            function ($rootScope, $scope, $stateParams, $location, APIJson, $cookieStore, $http, $injector) {

                $scope.init = function () {
                    $scope.loading = false;
                    $scope.getForms();
                    $scope.formType = $stateParams.formType == 'checkList' ? true : false;
                    console.log($scope.formType)
                };

                $scope.getForms = function () {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'GET',
                        url: APIJson.urls.auditFormManagement.getActiveAuditForms
                    }).then(function success(response) {
                        if (response.data != null && Array.isArray(response.data)) {
                            $scope.forms = response.data;
                        } else {
                            $scope.errorMessage = 'Error getting audit list.';
                            $scope.error = true;
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $scope.errorMessage = 'Error getting audit list.';
                        $scope.error = true;
                        $rootScope.rootLoading = false;
                    });
                };

                $scope.startAudit = function (form) {
                    $cookieStore.put("afid", {id: form.id, vend: form.name !== "PQSC Audit"});
                    $location.path("dashboard/audit");
                }
            }
        ]
    );