'use strict';

angular.module('formsApp')
    .controller('manageClaimCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil',
        'MetadataService', 'toastr', '$uibModal', '$stateParams', 'StorageUtil',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil,
                  MetadataService, toastr, $uibModal, $stateParams, StorageUtil) {

            $scope.init = function () {
                $scope.accountTypes=["UNIT","EMPLOYEE"];
                $scope.selectedWalletDetail=[];
                $scope.claimTypes = [
//                    {id: 1, name: "HAPPAY"},
                    {id: 1, name: "ICICI" },
                    /* {id: 4, name: "Reimbursements"},
                     {id: 5, name: "Part of Salary"}*/
                ];
                $scope.claimStatuses = [
                    {id: 1, name: "CREATED"},
                    {id: 2, name: "APPROVED"},
                    {id: 3, name: "REJECTED"},
                    {id: 4, name: "SETTLED"}
                ];
                $scope.userDetails = StorageUtil.getUserMeta();
                $scope.walletsByAccTypeMultiSelect=[];
                $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option}}</b>'};
                $scope.multiSelectSettings2 = {showEnableSearchButton: false, template: '<b>{{option.name}}</b>'};
                $scope.units = [];
                $scope.unitMultiSelect = [];
                getUnits();
                $scope.startDate = AppUtil.getDate(-7);
                $scope.endDate = AppUtil.getDate();
            };

            function getUnits() {
                MetadataService.getUnitList(function (unitList) {
                    multiSelectUnitModal(unitList);
                }, function (error) {
                    toastr.error(error);
                });
            }

            function multiSelectUnitModal(unitsList) {
                for (var i = 0; i < unitsList.length; i++) {
                    $scope.units.push({name: unitsList[i].name, id: unitsList[i].id});
                }
            }
                $scope.getWalletsByAccountType = function (selectedAccountType){
                if(!AppUtil.isEmptyObject($scope.selectedAccountType)){
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'GET',
                        url: APIJson.urls.walletManagement.getWalletsByAccountType,
                        params: {
                            walletAccountType :$scope.selectedAccountType,
                            employeeCode :$scope.userDetails.id,
                            byPass : true,
                        }
                    }).then(function success(response) {
                        if (response.data != null) {
                            $scope.walletsByAccType = response.data;
                            $scope.walletsNamesByAccType= $scope.walletsByAccType.map(function(wallet){
                                $scope.selectedWalletDetail[wallet.associatedId+'_'+wallet.accountHolderName+'_'+wallet.walletType]=wallet;
                                return wallet.associatedId+'_'+wallet.accountHolderName+'_'+wallet.walletType;
                            });
                            // console.log('Wallets by Account Type ::', $scope.walletsNamesByAccType);
                        } else {
                            toastr.error("Error while getting accounts for this accountType.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error(response);
                        $rootScope.rootLoading = false;
                    });
                }
            }

            $scope.dateOptions = {
                dateDisabled: function () {
                    return false;
                },
                formatYear: 'yyyy',
                maxDate: new Date(),
                minDate: $scope.minDate,
                startingDay: 1
            };

            $scope.setSelectedClaimType = function (selectedClaimType) {
                $scope.selectedClaimType = selectedClaimType;
            };

            $scope.setSelectedClaimStatus = function (selectedClaimStatus) {
                $scope.selectedClaimStatus = selectedClaimStatus;
            };

            $scope.findClaims = function () {
                if ($scope.walletsByAccTypeMultiSelect.length == 0) {
                    toastr.error("Please select account");
                }else if ($scope.startDate == null) {
                    toastr.error("Please select start date.");
                } else if ($scope.endDate == null) {
                    toastr.error("Please select end date.");
                }else {
                    var wallet=[];
                    for(var i in $scope.selectedWalletDetail) {
                        wallet.push($scope.selectedWalletDetail[i].accountNo);
                    }
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.claimManagement.findClaims,
                        data: {
                            startDate: $scope.startDate,
                            endDate: AppUtil.addDate($scope.endDate, 1),
                            type: $scope.selectedClaimType != null ? $scope.selectedClaimType.name : null,
                            status: $scope.selectedClaimStatus != null ? $scope.selectedClaimStatus.name : null,
                            employeeId : $scope.walletsByAccTypeMultiSelect[0].split("_")[0],
                            unitIds: wallet
                        }
                    }).then(function success(response) {
                        if (response.status == 200 && response.data != null && Array.isArray(response.data)) {
                            $scope.claimList = response.data;
                        } else {
                            toastr.error("No claims.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error getting claims.");
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.downloadClaim = function (claimId) {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.claimManagement.downloadClaim,
                    data: claimId,
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/pdf'
                    }
                }).then(function success(response) {
                    if (response && response.data != null) {
                        var fileName = "claimDetail" + claimId + ".pdf";
                        var blob = new Blob([response.data], {
                            type: 'c'
                        }, fileName);
                        saveAs(blob, fileName);
                    } else {
                        toastr.error("Could not download claim.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error getting claims.");
                    $rootScope.rootLoading = false;
                });
            };

             $scope.downloadClaimById = function(claimId) {
                           $rootScope.rootLoading = true;
                           $http({
                               method: 'GET',
                               url: APIJson.urls.claimManagement.downloadById+"?claimId="+claimId,
                               responseType: 'arraybuffer',
                               headers: {
                                                       'Content-type': 'application/json'
                               }
                           }).then(function success(response) {
                               if (response && response.data != null) {
                                   var fileName = "claimDetail" + claimId + ".xlsx";
                                   var blob = new Blob([response.data], {
                                       type: 'c'
                                   }, fileName);
                                   saveAs(blob, fileName);
                               } else {
                                   toastr.error("Could not download claim.");
                               }
                               $rootScope.rootLoading = false;
                           }, function error(response) {
                               toastr.error("Error getting claims.");
                               $rootScope.rootLoading = false;
                           });
                      }


           $scope.downloadClaims=function(){
           if($scope.claimList == null){
                toastr.error("No claims present");
           }
           $rootScope.showDetailLoader = true;
            $http(
                {
                    method: 'POST',
                    url: APIJson.urls.claimManagement.bulkDownload,
                    data:{
                         startDate: $scope.startDate,
                         endDate: AppUtil.addDate($scope.endDate, 1),
                         status: $scope.selectedClaimStatus != null ? $scope.selectedClaimStatus.name : null,
                    },
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json'
                    }
                })
                .then(
                    function success(response) {
                        if (response !== undefined && response.status===200) {
                            var fileName ="Bulk_Claims_"
                                + Date.now() + ".xlsx";
                            var blob = new Blob(
                                [response.data],
                                {
                                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                }, fileName);
                            saveAs(blob, fileName);
                            }
                            else {
                                bootbox.alert("Error while downloading claims");
                            }
                            $rootScope.showDetailLoader = false;
                            },
                            function error(response) {
                                       console.log("error:" + response);
                                       alert("Unable to download bulk claims");
                                       $rootScope.showDetailLoader = false;
                                   });
           }

           $scope.downloadTemplate=function(){
           $rootScope.showDetailLoader = true;
                                      $http(
                                          {
                                              method: 'GET',
                                              url: APIJson.urls.claimManagement.downloadTemplate,
                                              responseType: 'arraybuffer',
                                              headers: {
                                                  'Content-type': 'application/json'
                                              }
                                          })
                                          .then(
                                              function success(response) {
                                                  if (response !== undefined && response.status===200) {
                                                      var fileName ="Download_Template_"
                                                          + Date.now() + ".xlsx";
                                                      var blob = new Blob(
                                                          [response.data],
                                                          {
                                                              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                                          }, fileName);
                                                      saveAs(blob, fileName);
                                                  }else {
                                                      bootbox.alert("Error while downloading claim template");
                                                  }
                                                  $rootScope.showDetailLoader = false;
                                              },
                                              function error(response) {
                                                  console.log("error:" + response);
                                                  alert("Unable to download claim template");
                                                  $rootScope.showDetailLoader = false;
                                              });
           }


           $scope.bulkApproves = function (doc){
                if(doc == null){
                    toastr.error("Please for upload!");
                    return false;
                }
                       var fd = new FormData();
                       fd.append("file", doc);
                       $rootScope.rootLoading = true;
                       $http({
                           url:APIJson.urls.claimManagement.bulkApprovals+"?requestBy="+$scope.userDetails.id,
                           method: 'POST',
                           data: fd,
                           headers: {
                               'Content-Type': undefined
                           },
                           transformRequest: angular.identity
                       }).success(function (response) {
                           $rootScope.rootLoading = false;
                           angular.element("input[type='file']").val(null);
                           toastr.alert("Approvals uploaded successfully");
                       }).error(function (response) {
                           $rootScope.rootLoading = false;
                           toastr.error("Error while uploading approvals");
                       });
           }

            $scope.openActionModal = function (claim, action) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'claimAction.html',
                    controller: 'ClaimActionModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        actionData: function () {
                            return {
                                action: action
                            }
                        }
                    }
                });
                modalInstance.result.then(function (comment) {
                    if (action == 'APPROVE') {
                        $scope.approveRejectClaim(claim, comment, true);
                    }
                    if (action == 'REJECT') {
                        $scope.approveRejectClaim(claim, comment, false);
                    }
                }, function () {
                    //console.log("closed::::::::::::::::::::::::::::::::::::::")
                });
            };



            $scope.approveRejectClaim = function (claim, comment, isApprove) {
                $rootScope.rootLoading = true;
                var url = APIJson.urls.claimManagement.approveClaim;
                if (!isApprove) {
                    url = APIJson.urls.claimManagement.rejectClaim
                }
                $http({
                    method: 'POST',
                    url: url,
                    data: {
                        requestBy: {id: $scope.userDetails.id},
                        comment: comment,
                        claimId: claim.claimId
                    }
                }).then(function success(response) {
                    if (response.status == 200 && response.data) {
                        toastr.success("Claim " + (isApprove ? "approved" : "rejected") + " successfully.");
                        $scope.findClaims();
                    } else {
                        toastr.error("Error " + (isApprove ? "approving" : "rejecting") + " claim.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error " + (isApprove ? "approving" : "rejecting") + " claim.");
                    toastr.error("Error " + (isApprove ? "approving" : "rejecting") + " claim.");
                    $rootScope.rootLoading = false;
                });
            };

            $scope.viewClaimVouchers = function (claim) {
                if (claim.vouchers.length > 0) {
                    $scope.showVoucherModal(claim);
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.claimManagement.getClaimVouchers,
                        data: claim.claimId
                    }).then(function success(response) {
                        if (response.status == 200) {
                            if (response.data.length > 0) {
                                claim.vouchers = response.data;
                                $scope.showVoucherModal(claim);
                            } else {
                                toastr.error("No vouchers found.");
                            }
                        } else {
                            toastr.error("Error getting claim vouchers.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error getting claim vouchers.");
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.viewClaimLogs = function (claim) {
                if (claim.claimLogs.length > 0) {
                    $scope.showLogModal(claim);
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.claimManagement.getClaimLogs,
                        data: claim.claimId
                    }).then(function success(response) {
                        if (response.status == 200) {
                            if (response.data.length > 0) {
                                claim.claimLogs = response.data;
                                $scope.showLogModal(claim);
                            } else {
                                toastr.error("No logs found.");
                            }
                        } else {
                            toastr.error("Error getting claim logs.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error acknowledging claim.");
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.showVoucherModal = function (claim) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'claimVoucher.html',
                    controller: 'ClaimVoucherModalCtrl',
                    backdrop: 'static',
                    size: 'lg',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                claim: claim
                            }
                        }
                    }
                });
                modalInstance.result.then(function () {
                }, function () {
                });
            };

            $scope.showLogModal = function (claim) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'claimLog.html',
                    controller: 'ClaimVoucherModalCtrl',
                    backdrop: 'static',
                    size: 'lg',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                claim: claim
                            }
                        }
                    }
                });
                modalInstance.result.then(function () {
                }, function () {
                });
            };


        }]);