'use strict';

angular.module('formsApp')
    .controller('manageWalletCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil',
        'MetadataService', 'toastr', '$uibModal', '$stateParams', 'StorageUtil',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil,
                  MetadataService, toastr, $uibModal, $stateParams, StorageUtil,) {

            $scope.init = function () {
                $scope.walletTypes = [
                    {id: 1, name: "PETTY_CASH"},
                    {id: 2, name: "POS"},
                    {id: 3, name: "OTHER"}
                ];
                $scope.selectedWalletType = $scope.walletTypes[0];
                $scope.accountTypes=["UNIT","EMPLOYEE"];
                $scope.selectedAccountType= $scope.accountTypes[0];
                $scope.userDetails = StorageUtil.getUserMeta();
                // $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option.name}}</b>'};
                $scope.units = [];
                $scope.companies=[];
                $scope.families=[];
                $scope.activeEmployees = [];
                $scope.unitMultiSelect = [];
                $scope.businessCostCenterMultiSelect = [];
                $scope.businessCostCenters=[];
                $scope.selectedFamily=null;
                $scope.selectedUnit=null;
                $scope.selectedCompany=null;
                $scope.selectedEmployee=null;
                $scope.showBusinessCostCentreList=null;
                $scope.approverNames=[];
                $scope.approver = [];
                $scope.approversMultiSelect=[];
                $scope.activeWalletApproverMappings=[];
                getCompanies();
                getFamilies();
                // getUnits();
                // getUnits($scope.selectedFamily);
                getActiveEmployees();
                $scope.initTabView();
                $scope.openingBalance = null;
                resetData();
            };

            function getUnits() {
                MetadataService.getUnitList(function (unitList) {
                    multiSelectUnitModal(unitList);
                }, function (error) {
                    toastr.error(error);
                });
            }

            function getCompanies(){
                MetadataService.getCompanyList(function (companyList){
                    $scope.companies=companyList;
                    // $scope.selectedCompany=angular.copy({"id":$scope.companies[0].id,"name":$scope.companies[0].name});
                    console.log("Companies ::::::::::: and selected company ::::::::::", $scope.companies, $scope.selectedCompany);
                },function (error){
                    toastr.error(error);
                });
            }
            $scope.getUnits=function(selectedFamily) {
                if($scope.selectedFamily !==undefined && $scope.selectedFamily !==null ){
                    MetadataService.getUnitList(function (unitList) {
                        multiSelectUnitModal(unitList);
                    }, function (error) {
                        toastr.error(error);
                    },selectedFamily);
                }
            }
            function getFamilies(){
                MetadataService.getFamilyList(function (familyList){
                    $scope.families=angular.copy(familyList);
                    $scope.selectedFamily=$scope.families[0];
                    $scope.getUnits($scope.selectedFamily);
                }, function(error){
                    toastr.error(error);
                });
            }

            function getActiveEmployees() {
                MetadataService.getActiveEmployeeList(function (unitList) {
                    addActiveEmployees(unitList);
                }, function (error) {
                    toastr.error(error);
                });
            }

            function multiSelectUnitModal(unitsList) {
                $scope.units=[];
                for (var i = 0; i < unitsList.length; i++) {
                    $scope.units.push({name: unitsList[i].name, id: unitsList[i].id});
                }
            }
            function addActiveEmployees(employeesList) {
                for (var element of employeesList) {
                    var name = element.id+"_"+element.name+(element.departmentName+"_"+element.designation);
                    $scope.activeEmployees.push(name);
                }
            }

            $scope.dateOptions = {
                dateDisabled: function () {
                    return false;
                },
                formatYear: 'yyyy',
                maxDate: new Date(),
                minDate: $scope.minDate,
                startingDay: 1
            };

            $scope.initTabView = function () {
                $scope.tabData = [
                    {id: 1, name: "Wallet Details"},
                    {id: 2, name: "Create Wallet"},
                    {id: 3, name: "Update Wallet"},
                ];
                $scope.selectedTab = $scope.tabData[0];
                $scope.startDate = AppUtil.getDate(-2);
                $scope.endDate = AppUtil.getDate();
                $scope.walletData = null;
            };

            $scope.selectTab = function (tab) {
                $scope.selectedTab = tab;
                if (tab.name == "Create Wallet") {
                    $scope.walletDetail = null;
                    $scope.showCreateWallet = false;
                }
                resetData();
            };

            function resetData(){
                if(!AppUtil.isEmptyObject($scope.selectedAccountType) || !AppUtil.isEmptyObject($scope.selectedWalletType)){
                    $scope.selectedAccountType=null;
                    $scope.selectedWalletType=null;
                    $scope.selectedUnit=null;
                    $scope.selectedEmployee=null;
                    $scope.selectedCompany=null;
                    $scope.selectedFamily=null;
                    $scope.walletData = null;
                    $scope.businessCostCenterMultiSelect=[];
                    $scope.showBusinessCostCenterBtn=false;
                    $scope.businessCostCenters=[];
                    $scope.showCreateWallet = false;
                    $scope.walletRequest = null;
                    $scope.showReportingManagersBtn = false;
                    $scope.approversMultiSelect=[];
                    $scope.activeWalletApproverMappings=[];
                }
            }

            $scope.getWalletList = function () {
                // if ($scope.selectedWalletType.name == "PETTY_CASH") {

                    var unitIds = [];
                    /*$scope.unitMultiSelect.map(function (unit) {
                        unitIds.push(unit.id);
                    });*/
                    var str="";
                    if(!AppUtil.isEmptyObject($scope.selectedAccountType)){
                        if($scope.selectedAccountType=='UNIT' && !AppUtil.isEmptyObject($scope.selectedUnit)){
                             str= 'U'+'_'+$scope.selectedWalletType.name+'_'+$scope.selectedUnit.id;
                        }else if($scope.selectedAccountType==='EMPLOYEE' && !AppUtil.isEmptyObject($scope.selectedEmployee)){
                             str = 'E'+'_'+$scope.selectedWalletType.name+'_'+$scope.selectedEmployee;
                        }
                    }
                    unitIds.push(str);
                    if ($scope.selectedUnit === null && $scope.selectedEmployee === null ) {
                        toastr.error("No unit selected. Please Please select at least one unit.");
                    } else {
                        $rootScope.rootLoading = true;
                        $http({
                            method: 'POST',
                            url: APIJson.urls.walletManagement.getWallets,
                            data: {
                                accountNos: unitIds,
                                accountType: $scope.selectedAccountType,
                                walletType: $scope.selectedWalletType.name
                            }
                        }).then(function success(response) {
                            $scope.walletList = response.data;
                            if ($scope.walletList.length == 0) {
                                toastr.info("No wallets found.");
                            }
                            $rootScope.rootLoading = false;
                            $scope.init();
                        }, function error(response) {
                            $rootScope.rootLoading = false;
                        });
                    }
                // }
            };

            $scope.getBusinessCostCenters = function (callback,err) {
                if( $scope.selectedAccountType == "UNIT " && ($scope.selectedUnit ===undefined || $scope.selectedUnit === null)){
                    toastr.error("Please select a unit first");
                }
                else if($scope.selectedAccountType == "EMPLOYEE" && ($scope.selectedEmployee ===undefined || $scope.selectedEmployee === null)){
                    toastr.error("Please select an employee first");
                }
                MetadataService.getBusinessCostCenters($scope.selectedAccountType,$scope.selectedCompany,$scope.selectedUnit,function (businessCostCenterList){
                    $scope.businessCostCenters= angular.copy(businessCostCenterList);
                    $scope.businessCostCentersNames =[];
                    for(var i in $scope.businessCostCenters){
                        $scope.businessCostCentersNames.push($scope.businessCostCenters[i].id +"_"+$scope.businessCostCenters[i].name);
                    }
                    if(!AppUtil.isEmptyObject($scope.businessCostCenters) && !AppUtil.isEmptyObject($scope.businessCostCentersNames) && callback !=undefined){
                        callback($scope.businessCostCenters,$scope.businessCostCentersNames);
                    }
                    $scope.showBusinessCostCentreList=true;
                },function (error){
                    toastr.error(error);
                })
                if(!AppUtil.isEmptyObject($scope.walletRequest)&& AppUtil.isEmptyObject($scope.businessCostCenters)){
                    $scope.walletRequest.businessCostCentersList= angular.copy($)
                }
            };

            $scope.multiSelectSettings = {
                showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
                scrollableHeight: '200px'
            };

            $scope.editBCCMappings= function (){
                $scope.storeSelectedBCCMappings=[];
                $scope.businessCostCenterMultiSelect=[];
                $scope.action = "Add/Remove";
                $scope.getBusinessCostCenters(function(businessCostCenterList,businessCostCenterNames){
                    $scope.businessCostCenters=angular.copy(businessCostCenterList);
                    $scope.businessCostCentersNames=angular.copy(businessCostCenterNames);
                    $scope.openEditWalletBccMappingModal();
                },function (error){
                    toastr.error(error);
                });
            }

            $scope.editApproverMapping= function (){
                $scope.storeSelectedApproverMappings=[];
                $scope.approversMultiSelect=[];
                $scope.action="Add/Remove";

                $scope.openEditWalletApprovalMappingModal();
            }

            $scope.openEditWalletBccMappingModal= function (){
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'updateWalletBCCMappings.html',
                    controller: 'UpdateWalletBCCMappingsCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                allWalletBccMappings: $scope.allWalletBccMappings,
                                activeWalletBccMappings:$scope.activeWalletBccMappings,
                                walletData:$scope.walletData,
                                storeSelectedBCCMappings:$scope.storeSelectedBCCMappings,
                                businessCostCentersNames: $scope.businessCostCentersNames,
                                businessCostCenters:$scope.businessCostCenters,
                                businessCostCenterMultiSelect:$scope.businessCostCenterMultiSelect,
                                action:$scope.action,
                                multiSelectSettings:$scope.multiSelectSettings
                            }
                        }
                    }
                });
                modalInstance.result.then(function (comment) {
                    if (action == 'ACKNOWLEDGE') {
                        $scope.acknowledgeClaim(claim, comment);
                    }
                }, function () {
                    //console.log("closed::::::::::::::::::::::::::::::::::::::")
                });
            }

            $scope.openEditWalletApprovalMappingModal= function (){
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'updateWalletApproverMappings.html',
                    controller: 'UpdateWalletApprovalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                allWalletApproverMappings: $scope.allWalletApproverMappings,
                                activeWalletApproverMappings:$scope.activeWalletApproverMappings,
                                walletData:$scope.walletData,
                                approverNames: $scope.approverNames,
                                approversMultiSelect:$scope.approversMultiSelect,
                                action:$scope.action,
                                multiSelectSettings:$scope.multiSelectSettings
                            }
                        }
                    }
                });
                modalInstance.result.then(function (comment) {
                    if (action == 'ACKNOWLEDGE') {
                        $scope.acknowledgeClaim(claim, comment);
                    }
                }, function () {
                    //console.log("closed::::::::::::::::::::::::::::::::::::::")
                });
            }

            $scope.getWalletBusinessCostCenterMappings = function (){
                MetadataService.getWalletBusinessCostCenterMappings($scope.walletData.id,function(walletBusinessCostCenterMappings){
                    $scope.allWalletBccMappings = angular.copy(walletBusinessCostCenterMappings);
                    $scope.activeWalletBccMappings =$scope.allWalletBccMappings.filter(function (walletBccMapping){
                        return walletBccMapping.mappingStatus ==='ACTIVE';
                    });
                    console.log("All Wallet BCC Mappings ::::::::", $scope.allWalletBccMappings);
                    console.log("Active Wallet BCC Mappings :::::::::", $scope.activeWalletBccMappings);
                },function(error){
                    toastr.error(error);
                });
            }
            $scope.getWalletApprovalMappings = function (){
                            $http({
                                method: 'GET',
                                url: APIJson.urls.walletManagement.getApprovalByWalletId,
                                params: {
                                    walletId:$scope.walletData.id
                                }
                                }).then(function success(response){
                                    $scope.allWalletApproverMappings = response.data;
                                    $scope.activeWalletApproverMappings=[];
                                    for(var i in $scope.allWalletApproverMappings){
                                    if($scope.allWalletApproverMappings[i].mappingStatus === "ACTIVE"){
                                        $scope.activeWalletApproverMappings.push($scope.allWalletApproverMappings[i]);
                                    }
              }
                                },function error(response) {
                                    error(response.errorMessage);
                                });
                        }


            $scope.getWalletApproverMappings = function(){
            $scope.getWalletApprovalMappings();
            }


            $scope.updateWalletApproverMapping = function (approverMapping, val){
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.updateApprovalStatusByWalletId,
                    params: {
                        walletMappingId:approverMapping.walletApproverMappingId,
                        status:val,
                        lastUpdatedBy:$scope.userDetails.id
                    }}).then(function success(response){
                        $scope.getWalletApprovalMappings();
                    },function error(response) {
                        error(response.errorMessage);
                    });
            }
            $scope.setSelectedDetails=function (selectedCompany,selectedUnit,selectedEmployee){
                $scope.selectedCompany=selectedCompany;
                $scope.selectedUnit=selectedUnit;
                $scope.selectedEmployee =selectedEmployee.split("_")[0];
            }

            $scope.resetWalletAndAccountDetails=function (selectedAccountType){
                $scope.selectedCompany= null;
                $scope.selectedWalletType=null;
                $scope.selectedAccountType=selectedAccountType;
                $scope.selectedUnit=null;
                $scope.selectedEmployee=null;
                $scope.showBusinessCostCentreList=false;
            }

            $scope.getWalletDetail = function () {
                // if ($scope.selectedWalletType.name == "PETTY_CASH") {
                var accountNo="";
                if(!AppUtil.isEmptyObject($scope.selectedAccountType) && !AppUtil.isEmptyObject($scope.selectedWalletType)){
                    if($scope.selectedAccountType==='UNIT'){
                        if(!AppUtil.isEmptyObject($scope.selectedUnit)){
                            accountNo= "U"+"_"+$scope.selectedWalletType.name+"_"+$scope.selectedUnit.id;
                            findWalletByAccountNo(accountNo);
                        }else {
                            toastr.error("No unit selected. Please select unit.");
                        }
                    }else if($scope.selectedAccountType ==='EMPLOYEE'){
                        if(!AppUtil.isEmptyObject($scope.selectedEmployee)){
                            accountNo= "E"+"_"+$scope.selectedWalletType.name+"_"+$scope.selectedEmployee;
                            findWalletByAccountNo(accountNo);
                        }else {
                            toastr.error("No employee selected. Please select employee.");
                        }
                    }
                }
                // }
            };

            function findWalletByAccountNo(accountNo){
                console.log("Account No ::::::", accountNo);
                $rootScope.rootLoading = true;
                $http({
                    method: 'GET',
                    url: APIJson.urls.walletManagement.getWalletByAccountNo,
                    params: {
                        accountNo: accountNo,
                        /*accountType: 'UNIT',
                        walletType: $scope.selectedWalletType.name*/
                    }
                }).then(function success(response) {
                    if(!AppUtil.isEmptyObject(response.data)){
                        $scope.walletData= response.data;
                        if(!AppUtil.isEmptyObject($scope.selectedTab) && $scope.selectedTab.name=="Create Wallet"){
                            toastr.error("Wallet for this accountType and WalletType already exists with walletId .If you want to update existingwallet, click on  update wallet");
                        }
                        $scope.getWalletBusinessCostCenterMappings();
                        $scope.getWalletApproverMappings();
                        getReportingManagers(accountNo);
                    }else{
                        $scope.walletData= null;
                    }
                    if ($scope.walletData == null) {
                        $scope.initWallet();
                        $scope.getBusinessCostCenters();
                        getReportingManagers(accountNo);
                    }

                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = false;
                });

            }

           function getReportingManagers(accountNo){
            $http({
                              method: 'GET',
                              url: APIJson.urls.walletManagement.getReportingManagers,
                              params: {
                                  accountNo: accountNo,
                              }
                          }).then(function success(response) {
                          if(!AppUtil.isEmptyObject(response.data)){
                          $scope.approverNames = response.data;
                          }else{
                          $toastr.error("No mapped Reporting Manager")
                          }

                          }, function error(response) {
                                 $rootScope.rootLoading = false;
                                 $toastr.error("Error in fetching Reporting Managers");
                           });
           }


            $scope.setDate = function (date, param) {
                $scope[param] = date;
            };

            $scope.setOpeningBalance= function (){
                console.log("Opening baalance ::::::", $scope.openingBalance);
                if(AppUtil.isEmptyObject($scope.walletRequest)){
                    $scope.walletRequest.openingAmount= $scope.openingBalance;
                }
            }

            $scope.setSelectedWalletType = function (selectedWalletType) {
                $scope.selectedWalletType = selectedWalletType;
            };

            $scope.setWalletUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
            };

            $scope.initWallet = function () {
                // if ($scope.selectedWalletType.name == 'PETTY_CASH') {
                    $scope.walletRequest = {};
                    if($scope.selectedWalletType !=undefined && $scope.selectedWalletType!=null && $scope.selectedAccountType !=undefined && $scope.selectedAccountType !=null){
                        if( $scope.selectedAccountType ==='UNIT' && $scope.selectedUnit!=undefined && $scope.selectedUnit !==null){
                            $scope.walletRequest.accountNo ='U'+'_'+$scope.selectedWalletType.name+'_'+$scope.selectedUnit.id;
                            $scope.walletRequest.openingAmount=0;//hardcoded
                        }else{
                            $scope.walletRequest.accountNo = 'E'+'_'+$scope.selectedWalletType.name+'_'+$scope.selectedEmployee;
                            $scope.walletRequest.openingAmount=$scope.openingBalance;//hardcoded
                        }
                    }
                    // $scope.walletRequest.accountHolderName = $scope.selectedUnit.name;
                    $scope.walletRequest.accountType = $scope.selectedAccountType;
                    $scope.walletRequest.walletType = $scope.selectedWalletType.name;
                    // $scope.walletRequest.businessCostCentersList=$scope.businessCostCenters;
                    $scope.showCreateWallet = true;
                    $scope.showBusinessCostCenterBtn=true;
                    $scope.showReportingManagersBtn=true;
                // }
            };

            $scope.updateWalletBusinessCostCenterMapping= function (walletBccMapping,statusFlag){
                if(!AppUtil.isEmptyObject(walletBccMapping)){
                    $rootScope.showFullScreenLoader = true;
                    var url = APIJson.urls.walletManagement.updateWalletBccMapping;
                    $http({
                        method: 'POST',
                        url: url,
                        params:{
                            walletBccMappingId:walletBccMapping.walletBccMappingId,
                            status:statusFlag==true?"ACTIVE":"IN_ACTIVE",
                            lastUpdatedBy:$scope.userDetails.id,
                        }
                    }).then(function success(response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.status === 200 && response.data != true) {
                            bootbox.alert("Error " + (statusFlag == true ? "activating" : "deactivating") + " mapping!");
                        } else {
                            walletBccMapping.mappingStatus = (statusFlag == true ? "ACTIVE" : "IN_ACTIVE");
                        }
                    }, function error(response) {
                        console.log('Error in getting response', response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            }

            $scope.createWallet = function () {
                // if ($scope.selectedWalletType.name == "PETTY_CASH") {
                    $scope.createPettyCashWallet();
                // }
            };
            $scope.setCafeCostAllocationFlag = function(flag){
                $scope.walletRequest.canAllocateCostToCafes = flag ;
                console.log("Can allocate Cost to cafes :::::", $scope.walletRequest.canAllocateCostToCafes);
            }

            $scope.createPettyCashWallet = function () {
                console.log("Business Cost Center multi select in line 443::::::", $scope.businessCostCenterMultiSelect);
                if(!AppUtil.isEmptyObject($scope.walletRequest)){
                    if(!AppUtil.isEmptyObject($scope.businessCostCenterMultiSelect) && $scope.businessCostCenterMultiSelect.length>0 ){
//                        getBusinessCostCenterDataById();
                        var newArray =[];
                        for(var mapping of $scope.businessCostCenterMultiSelect){
                            console.log("Mapping:::::", mapping.split("_")[0]);
                            console.log("Mapping:::::", mapping.split("_")[2]);
                            newArray.push($scope.businessCostCenters.find(function(businessCostCenterMapping){
                               return businessCostCenterMapping.id === parseInt(mapping.split("_")[0]);
                            }));
                        }
                        $scope.walletRequest.businessCostCentersList = angular.copy(newArray);
                        // $scope.walletRequest.businessCostCentersList = angular.copy($scope.businessCostCenterMultiSelect);
                    }
                    if(!AppUtil.isEmptyObject($scope.approversMultiSelect) && $scope.approversMultiSelect.length>0){
                    var newReportingArray = [];
                    for(var mapping of $scope.approversMultiSelect){
//                    newReportingArray.push($scope.approverNames.find(function(approverNamesMapping){
//                        return approverNamesMapping === ;
//                    }));
                        newReportingArray.push(parseInt(mapping.split("_")[0]));
                    }
                    $scope.walletRequest.approverList = angular.copy(newReportingArray);
                    }
                    $scope.walletRequest.canAllocateCostToCafes;
                    $scope.walletRequest.lastUpdatedBy=$scope.userDetails.id;
                }
                if ( $scope.selectedAccountType=='UNIT' && $scope.selectedUnit == null) {
                    toastr.error("No unit selected. Please select unit.");
                }else if ( $scope.selectedAccountType=='UNIT' && $scope.selectedUnit == null) {
                    toastr.error("No unit selected. Please select unit.");
                } else if($scope.walletRequest.openingAmount == null) {
                    toastr.error("Fill opening amount first.");
                }else if ($scope.businessCostCenterMultiSelect.length==0){
                    console.log("Business Cost Centers multi select :::::::", $scope.businessCostCenterMultiSelect);
                    toastr.error("Select atleast one associated business cost center !")
                }else if($scope.selectedAccountType!='UNIT' && $scope.approversMultiSelect.length == 0){
                    toastr.error("Select the approver");
                }else if($scope.walletRequest.ifscCode == null){
                    toastr.error("Fill IFSC code first.");
                }else if($scope.walletRequest.cardHolderName == null){
                    toastr.error("Fill Card Holder Name first.");
                }else if($scope.walletRequest.cardNumber == null){
                    toastr.error("Fill Card number first.");
                }else if($scope.walletRequest.bankName == null){
                    toastr.error("Fill Bank name first.");
                }
                /* else if($scope.walletRequest.accountHolderContact == null) {
                }
                    toastr.error("Fill account holder contact.");
                } else if($scope.walletRequest.accountHolderEmail == null) {
                    toastr.error("Fill account holder email.");
                }*/ else {
                    $rootScope.rootLoading = true;
                    $scope.walletRequest.createdBy = AppUtil.getIdCodeName(StorageUtil.getUserMeta().id);
                    $http({
                        method: 'POST',
                        url: APIJson.urls.walletManagement.createWallet,
                        data: $scope.walletRequest
                    }).then(function success(response) {
                        if (response.data != null) {
                            toastr.success("Wallet created Successfully");
                            $scope.walletData = response.data;
                            $scope.init();
                        } else {
                            toastr.error("Error occurred while creating wallet.Please try again!");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error occurred while creating wallet : " + response.errorMsg);
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.updateWallet = function () {
                if ($scope.walletUpdateType == null) {
                    toastr.error("Please select wallet update type.");
                } else if ($scope.walletUpdateAmount == null) {
                    toastr.error("Please fill update amount.");
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.claimManagement.addClaim,
                        data: {
                            claimType: ($scope.walletUpdateType == "UPGRADE" ? "WALLET_UPGRADE" : "WALLET_DOWNGRADE"),
                            requestBy: {id: $scope.userDetails.id, code: "", name: ""},
                            accountNo:$scope.walletData.accountNo,
                            claimAmount: $scope.walletUpdateAmount,
                            happayId:$scope.walletUpdateTransactionId
                        }
                    }).then(function success(response) {
                        if (response.data != null) {
                            toastr.success("Wallet updated Successfully");
                            $scope.getWalletDetail();
                            $scope.walletUpdateTransactionId = null;
                            $scope.walletUpdateAmount = null;
                            $scope.walletUpdateType = null;
                            $scope.walletData = response.data;
                        } else {
                            toastr.error("Error occurred while creating wallet.Please try again!");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error occurred while creating wallet : " + response.errorMsg);
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.openActionModal = function (claim, action) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'claimAction.html',
                    controller: 'ClaimActionModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        actionData: function () {
                            return {
                                action: action
                            }
                        }
                    }
                });
                modalInstance.result.then(function (comment) {
                    if (action == 'ACKNOWLEDGE') {
                        $scope.acknowledgeClaim(claim, comment);
                    }
                }, function () {
                    //console.log("closed::::::::::::::::::::::::::::::::::::::")
                });
            };


        }]).controller('UpdateWalletBCCMappingsCtrl', ['$scope', 'toastr', '$uibModal', '$uibModalInstance','data','AppUtil','$rootScope','$http','APIJson','StorageUtil',
    function ($scope, toastr, $uibModal, $uibModalInstance,data,AppUtil,$rootScope,$http,APIJson,StorageUtil) {

        $scope.init = function () {
            $scope.allWalletBccMappings = data.allWalletBccMappings;
            $scope.activeWalletBccMappings=data.activeWalletBccMappings;
            $scope.walletData=data.walletData;
            $scope.storeSelectedBCCMappings=data.storeSelectedBCCMappings;
            $scope.action=data.action;
            $scope.businessCostCentersNames= data.businessCostCentersNames;
            $scope.businessCostCenters = data.businessCostCenters;
            $scope.businessCostCenterMultiSelect= data.businessCostCenterMultiSelect;
            $scope.storeSelectedBCCMappings=data.storeSelectedBCCMappings;
            $scope.multiSelectSettings=data.multiSelectSettings;
            $scope.userDetails=StorageUtil.getUserMeta();
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.closeModal = function () {
            $uibModalInstance.close('success');
        }

        $scope.addWalletBccMapping = function () {
            console.log("Store selected BCC MPPINGS ::::::::::", $scope.storeSelectedBCCMappings);
            console.log("BCC Multi Select :::::::: ", $scope.businessCostCenterMultiSelect);
            $scope.storeSelectedBCCMappings= angular.copy(getBusinessCostCenterDataById());
            $scope.storeSelectedBCCMappings.forEach(function (value) {
                $scope.activeWalletBccMappings = $scope.addToActiveWalletBccMappings($scope.activeWalletBccMappings, value);
            });
            $scope.businessCostCenterMultiSelect=[];
        }
        function getBusinessCostCenterDataById (){
            var newArray =[];
            for(var mapping of $scope.businessCostCenterMultiSelect){
                console.log("Mapping:::::", mapping.split("_")[0]);
                console.log("Mapping:::::", mapping.split("_")[2]);
                newArray.push($scope.businessCostCenters.find(function(businessCostCenterMapping){
                    return businessCostCenterMapping.id === parseInt(mapping.split("_")[0]);
                }));
            }
            return newArray;
        }

        $scope.addToActiveWalletBccMappings = function (activeWalletBccMappings, bccmapping) {
            console.log("Inside add to active Wallet Bcc Mappings:::", bccmapping);
            if (!angular.isUndefined(activeWalletBccMappings) || activeWalletBccMappings != null) {
                var contains = false;
                var newActiveWalletBccMappings = [];
                activeWalletBccMappings.forEach(function (mapping) {
                    if (mapping.bccCode === bccmapping.code && mapping.mappingStatus === "IN_ACTIVE") {
                        mapping.mappingStatus = 'ACTIVE';
                        newActiveWalletBccMappings.push(mapping);
                        contains = true;
                    } else if (mapping.bccCode === bccmapping.code && mapping.mappingStatus === "ACTIVE") {
                        newActiveWalletBccMappings.push(mapping);
                        contains = true;
                    } else {
                        newActiveWalletBccMappings.push(mapping);
                    }
                });
                if (!contains) {
                    var walletBccMapping = {};
                    walletBccMapping.mappingStatus = 'ACTIVE';
                    walletBccMapping.walletId = null;
                    walletBccMapping.bccId = bccmapping.id;
                    walletBccMapping.bccName = bccmapping.name;
                    walletBccMapping.bccCode = bccmapping.code;
                    walletBccMapping.bccType = bccmapping.type;
                    walletBccMapping.walletType = $scope.selectedWalletType;
                    newActiveWalletBccMappings.push(walletBccMapping);
                }
            }
            return newActiveWalletBccMappings;
        }

        $scope.removeWalletBccMapping = function (walletBccMappingId) {
            if (walletBccMappingId!==null && $scope.activeWalletBccMappings!==null) {
                $scope.activeWalletBccMappings = removeMappingFromList($scope.activeWalletBccMappings, walletBccMappingId);
            }
            console.log("At line 228 modified active wallet bcc mappings :::::::", $scope.activeWalletBccMappings);
        }

        function removeMappingFromList(walletBccMappings, walletBccMappingId) {
            var newWalletBccMapping = [];
            if (!angular.isUndefined(walletBccMappings) || walletBccMappings != null) {
                var newList = [];
                walletBccMappings.forEach(function (mapping) {
                if (mapping.bccCode !== walletBccMappingId.bccCode ) {
                    console.log("Real Time mapping ::::::::", mapping);
                    newList.push(mapping);
                } else if (mapping.walletBccMappingId){
                    mapping.mappingStatus = 'IN_ACTIVE';
                    newList.push(mapping);
                }});
            newWalletBccMapping = angular.copy(newList);
            }
            return newWalletBccMapping;
        }

        $scope.saveUpdateWalletBccMapping = function (activeMappings) {
            if(!AppUtil.isEmptyObject(activeMappings)){
                console.log("Updating for mappings = ", activeMappings)
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.updateAllWalletBccMappings,
                    params: {
                        lastUpdatedBy: $scope.userDetails.id,
                        walletId: $scope.walletData.id
                    },
                    data: activeMappings
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200) {
                        alert("Mapping updated")
                        $scope.closeModal();
                    }
                }, function error(response) {
                    console.log("Error while updating wallet BCC mapping :::" + response);
                });
            }
        }
    }]).controller('UpdateWalletApprovalCtrl', ['$scope', 'toastr', '$uibModal', '$uibModalInstance','data','AppUtil','$rootScope','$http','APIJson','StorageUtil',
    function ($scope, toastr, $uibModal, $uibModalInstance,data,AppUtil,$rootScope,$http,APIJson,StorageUtil) {

        $scope.init = function () {
            $scope.allWalletApproverMappings = data.allWalletApproverMappings;
            $scope.activeWalletApproverMappings=data.activeWalletApproverMappings;
            $scope.walletData=data.walletData;
            $scope.action=data.action;
            $scope.approverNames= data.approverNames;
            $scope.approversMultiSelect= data.approversMultiSelect;
            $scope.multiSelectSettings=data.multiSelectSettings;
            $scope.userDetails=StorageUtil.getUserMeta();
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.closeModal = function () {
            $uibModalInstance.close('success');
        }

        $scope.addWalletApproverMapping = function () {
            $scope.storeSelectedApproverMappings= angular.copy(getApproverDataById());
            $scope.storeSelectedApproverMappings.forEach(function (value) {
                $scope.activeWalletApproverMappings = $scope.addToActiveWalletApproverMappings($scope.activeWalletApproverMappings, value);
            });
            $scope.approversMultiSelect=[];
        }
        function getApproverDataById (){
            var newArray = [];
            for(var mapping of $scope.approversMultiSelect){
                var id =parseInt(mapping.split("_")[0]);
                var name = mapping.split("_")[1];
                newArray.push({
                id:id,
                name:name
                });
            }
            return newArray;
        }

        $scope.addToActiveWalletApproverMappings = function (activeWalletApproverMappings, approvermapping) {
            if (!angular.isUndefined(activeWalletApproverMappings) || activeWalletApproverMappings != null) {
                var contains = false;
                var newActiveWalletApproverMappings = [];
                activeWalletApproverMappings.forEach(function (mapping) {
                    if (mapping.approverId === approvermapping.id && mapping.mappingStatus === "IN_ACTIVE") {
                        mapping.mappingStatus = 'ACTIVE';
                        newActiveWalletApproverMappings.push(mapping);
                        contains = true;
                    } else if (mapping.approverId === approvermapping.id && mapping.mappingStatus === "ACTIVE") {
                        newActiveWalletApproverMappings.push(mapping);
                        contains = true;
                    } else {
                        newActiveWalletApproverMappings.push(mapping);
                    }
                });
                if (!contains) {
                    var walletApproverMapping = {};
                    walletApproverMapping.mappingStatus = 'ACTIVE';
                    walletApproverMapping.walletId = null;
                    walletApproverMapping.approverId = approvermapping.id;
                    walletApproverMapping.approverName = approvermapping.name;
                    newActiveWalletApproverMappings.push(walletApproverMapping);
                }
            }
            return newActiveWalletApproverMappings;
        }

        $scope.removeWalletApproverMapping = function (walletApproverMappingId) {
            if (walletApproverMappingId!==null && $scope.activeWalletApproverMappings!==null) {
                $scope.activeWalletApproverMappings = removeMappingFromList($scope.activeWalletApproverMappings, walletApproverMappingId);
            }
        }

        function removeMappingFromList(walletApproverMappings,walletApproverMappingId ) {
            var newWalletApprroverMapping = [];
            if (!angular.isUndefined(walletApproverMappings) || walletApproverMappings != null) {
                var newList = [];
                walletApproverMappings.forEach(function (mapping) {
                if (mapping.approverId !== walletApproverMappingId.approverId ) {
                    newList.push(mapping);
                } else if (mapping.walletApproverMappingId){
                    mapping.mappingStatus = 'IN_ACTIVE';
                    newList.push(mapping);
                }});
            walletApproverMappings = angular.copy(newList);
            }
            return walletApproverMappings;
        }

        $scope.saveUpdateWalletApproverMapping = function (activeApproverMappings) {
            if(!AppUtil.isEmptyObject(activeApproverMappings)){
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.updateAllWalletApproverMappings,
                    params: {
                        lastUpdatedBy: $scope.userDetails.id,
                        walletId:$scope.walletData.id
                    },
                    data: activeApproverMappings
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200) {
                        alert("Mapping updated")
                        $scope.closeModal();
                    }
                }, function error(response) {
                    console.log("Error while updating wallet Approver mapping :::" + response);
                });
            }
        }
    }]);