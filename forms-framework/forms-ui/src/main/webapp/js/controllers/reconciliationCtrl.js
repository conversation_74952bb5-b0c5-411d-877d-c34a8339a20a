'use strict';
angular.module("formsApp")
        .controller("reconciliationCtrl",['$rootScope','$scope','APIJson','$http','StorageUtil',
        function($rootScope,$scope,APIJson,$http,StorageUtil){
            $scope.init = function(){
               
                $scope.tabList=["CHANNEL PARTNER","CASH"],
                $scope.selectedTab="CHANNEL PARTNER"
                $scope.isRunRecon = false;
                $scope.isReRunRecon = false;
                $scope.startDate=null;
                $scope.endDate=null;
                $scope.channelPartner=null;
                $scope.brand=null;
                $scope.aov=null;
                $scope.fileToUpload=null;
                $scope.pickupDate=null;
            }

            $scope.setPickupDate= function(pickupDate){
                $scope.pickupDate = pickupDate;
            }
            $scope.selectTab = function(tabName){
                $scope.selectedTab=tabName;
            }

            $scope.setRunRecon = function(){
                $scope.isReRunRecon=false;
                $scope.isRunRecon=true;
            }

            $scope.setReRunRecon = function(){
                $scope.isRunRecon=false;
                $scope.isReRunRecon=true;
            }

            $scope.setStartDate=function(startDate){
                $scope.startDate = startDate;
            }
            $scope.setEndDate = function(endDate){
                $scope.endDate = endDate;
            }
            $scope.setChannelPartner=function(channelPartner){
                $scope.channelPartner=channelPartner;
            }
            $scope.setBrand=function(brand){
                $scope.brand = brand;
            }
            $scope.setAov=function(aov){
                $scope.aov=aov;
            }
            $scope.startRecon = function(file){
                if($scope.channelPartner==null){
                    alert("Please select channel partner !");
                    return;
                }
                if($scope.startDate==null || $scope.endDate==null){
                    alert("Please select date !");
                    return;
                }
                if(file==null){
                    alert("Please upload file !");
                    return;
                }
                if(file.type!="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
                    alert("Please upload file in .xlsx format !");
                    return;
                }
                const body  = new FormData();
                
                body.append("file",file);
                body.append("channelPartner",$scope.channelPartner);
                body.append("startDate",`${$scope.startDate.getFullYear()}-${ ($scope.startDate.getMonth()+1) < 10 ? "0"+($scope.startDate.getMonth()+1) :($scope.startDate.getMonth()+1)}-${$scope.startDate.getDate() < 10 ? "0"+$scope.startDate.getDate() : $scope.startDate.getDate()}`);
                body.append("endDate",`${$scope.endDate.getFullYear()}-${ ($scope.endDate.getMonth()+1) < 10 ? "0"+($scope.endDate.getMonth()+1) :($scope.endDate.getMonth()+1)}-${$scope.endDate.getDate() < 10 ? "0"+$scope.endDate.getDate() : $scope.endDate.getDate()}`);
                body.append("userId",StorageUtil.getUserMeta().id);
                $rootScope.rootLoading = true;
                $http.post(APIJson.urls.reconciliation.channelPartnerStartRecon,body,{ headers: {
                    'Content-Type': undefined
                }}).then(function(response){
                    console.log(response);
                }).catch(err=>{
                    console.log(err);
                })
                $rootScope.rootLoading = false;
                alert("Channel Partner Recon Process Submitted, Output will be sent on mail.");
            }


            $scope.startCashRecon = function(file){
               
                if($scope.startDate==null || $scope.endDate==null){
                    alert("Please select date !");
                    return;
                }
                if(file==null){
                    alert("Please upload file !");
                    return;
                }
                if(file.type!="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"){
                    alert("Please upload file in .xlsx format !");
                    return;
                }
                const body  = new FormData();
                
                body.append("file",file);
                body.append("channelPartner",$scope.channelPartner);
                body.append("startDate",`${$scope.startDate.getFullYear()}-${ ($scope.startDate.getMonth()+1) < 10 ? "0"+($scope.startDate.getMonth()+1) :($scope.startDate.getMonth()+1)}-${$scope.startDate.getDate() < 10 ? "0"+$scope.startDate.getDate() : $scope.startDate.getDate()}`);
                body.append("endDate",`${$scope.endDate.getFullYear()}-${ ($scope.endDate.getMonth()+1) < 10 ? "0"+($scope.endDate.getMonth()+1) :($scope.endDate.getMonth()+1)}-${$scope.endDate.getDate() < 10 ? "0"+$scope.endDate.getDate() : $scope.endDate.getDate()}`);
                body.append("userId",StorageUtil.getUserMeta().id);
                body.append("pickupDate",`${$scope.pickupDate.getFullYear()}-${ ($scope.pickupDate.getMonth()+1) < 10 ? "0"+($scope.pickupDate.getMonth()+1) :($scope.pickupDate.getMonth()+1)}-${$scope.pickupDate.getDate() < 10 ? "0"+$scope.pickupDate.getDate() : $scope.pickupDate.getDate()}`);
                $rootScope.rootLoading = true;
                $http.post(APIJson.urls.reconciliation.cashStartRecon,body,{ headers: {
                    'Content-Type': undefined
                }}).then(function(response){
                    console.log(response);
                }).catch(err=>{
                    console.log(err);
                })
                $rootScope.rootLoading = false;
                alert("Cash Recon Process Submitted, Output will be sent on mail.");
            }



        }]);