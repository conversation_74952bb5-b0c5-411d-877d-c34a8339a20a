'use strict';

angular.module('formsApp')
.controller('WarningImagePreviewCtrl', ['$rootScope', '$scope', 'APIJson', '$http', 'warningImageDetails', 'StorageUtil', 'toastr', '$location', '$uibModal', '$uibModalInstance','$state',
	function ($rootScope, $scope, APIJson, $http, warningImageDetails, StorageUtil, toastr, $location, $uibModal, $uibModalInstance,$state) {

	$scope.warningImageUrl = warningImageDetails.imageUrl;

	$scope.downloadWarningImage = function () {
		$rootScope.rootLoading = true;
		saveAs(warningImageDetails.imageFile, warningImageDetails.imageName);
		$rootScope.rootLoading = false;
	};

	$scope.cancel = function () {
		$uibModalInstance.dismiss('cancel');
	};
}
]
);