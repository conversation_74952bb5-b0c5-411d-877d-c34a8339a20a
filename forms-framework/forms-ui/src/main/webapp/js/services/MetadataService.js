(function () {
    'use strict';

    angular.module('formsApp').factory('MetadataService', MetadataService);

    MetadataService.$inject = ['$http', 'APIJson','toastr','AppUtil'];

    function MetadataService($http, APIJson,toastr,AppUtil) {

        var service = {};
        service.userUnits = [];
        service.unitList = [];
        service.activeEmployeeList=[];
        service.subCategoriesList = [];
        service.companyList=[];
        service.familyList=[];
        service.businessCostCentersList=[];
        service.walletBusinessCostCenterMappings =[];
        service.getUnitList = getUnitList;
        service.checkIsSuccessFulAllocation= checkIsSuccessFulAllocation;
        service.getCompanyList= getCompanyList;
        service.getFamilyList=getFamilyList;
        service.getActiveEmployeeList = getActiveEmployeeList;
        service.getBusinessCostCenters=getBusinessCostCenters;
        service.saveVoucherCostCenterAllocation= saveVoucherCostCenterAllocation;
        service.totalAllocation = null;
        service.voucherCostCenterAllocation={};
        service.voucherCostCenterAllocations=[];
        service.getWalletBusinessCostCenterMappings= getWalletBusinessCostCenterMappings;
        service.auditUnitData = {};
        service.auditUnitEmployees = null;
        service.auditUnitProducts = null;
        service.isSuccessfulAllocation=false;
        service.resetTransactionalValues =resetTransactionalValues;
        service.getAuditUnitEmployees = getAuditUnitEmployees;
        service.getAuditUnitProducts = getAuditUnitProducts;
        service.getCodePrefix = getCodePrefix;
        service.getSKUForUnit = getSKUForUnit;
        service.getAreaManagerUnits = getAreaManagerUnits;
        service.getUserUnits = getUserUnits;
        service.setUserUnits = setUserUnits;
        service.getUnitSubCategories = getUnitSubCategories;
        service.getUnitListBySubCategory = getUnitListBySubCategory;

        function saveVoucherCostCenterAllocation(voucherCostCenterAllocationList,businessCostCenterlist,selectedBusinessCostCenter,isEdit,selectedVoucherCostCenterAllocationAmt,issuedAmount,callback ,error,selectedVoucherCostCenterAllocation){
            if(service.voucherCostCenterAllocations !=null && Array.isArray(service.voucherCostCenterAllocations)){
                var bccObj = getBusinessCostCenterData(selectedBusinessCostCenter,businessCostCenterlist);
                bccObj.allocatedAmt = parseInt(selectedVoucherCostCenterAllocationAmt);
                setVoucherCostCenterAllocationData(service.voucherCostCenterAllocation,bccObj);
                var newArray =angular.copy(voucherCostCenterAllocationList);
                var canSaveVoucherBCMappings = false ;
                if(isEdit){
                    var finalAllocationArray =[];
                    service.totalAllocation= 0;
                    canSaveVoucherBCMappings =editExistingVoucherCostCenterAllocation(service.voucherCostCenterAllocation, newArray,issuedAmount,selectedVoucherCostCenterAllocation,finalAllocationArray);
                    newArray= angular.copy(finalAllocationArray);
                }else{
                    //    ask how to set forceAllocation ?
                    newArray.push(service.voucherCostCenterAllocation);
                    canSaveVoucherBCMappings =runCommonValidations(service.voucherCostCenterAllocation,issuedAmount);
                }
                if(canSaveVoucherBCMappings){
                    service.voucherCostCenterAllocations= angular.copy(newArray);
                    if (typeof error == "function") {
                        callback(service.voucherCostCenterAllocations);
                    }
                }else{
                    if (typeof error == "function") {
                        error('Error updating voucher cost center mappings.')
                    }
                }
            }
        }
        function setVoucherCostCenterAllocationData(voucherCostCenterAllocationObj , bccObj){
            voucherCostCenterAllocationObj.businessCostCenterId=bccObj.bccId;
            voucherCostCenterAllocationObj.businessCostCenter=bccObj.bccName;
            voucherCostCenterAllocationObj.allocatedIssuedAmount=bccObj.allocatedAmt;
        }
        function getBusinessCostCenterData(selectedBusinessCostCenter,businessCostCenterList){
            var arr =selectedBusinessCostCenter.split('_');
            var id = arr[0];
            var obj =businessCostCenterList.find(function(businessCostCenter){
                return businessCostCenter.bccId===parseInt(id);
            });
            return obj ;
        }

        function editExistingVoucherCostCenterAllocation(voucherCostCenterAllocation, voucherCostCenterAllocations, issuedAmount,selectedVoucherCostCenterAllocation,finalAllocationArray) {
            for (var existingVoucherCostCenter of voucherCostCenterAllocations) {
                if (existingVoucherCostCenter.businessCostCenterId === selectedVoucherCostCenterAllocation.businessCostCenterId) {
                    existingVoucherCostCenter = angular.copy(voucherCostCenterAllocation);
                }
                finalAllocationArray.push(existingVoucherCostCenter);
            }
            var isSuccess = true ;
            if (!AppUtil.isEmptyObject(finalAllocationArray) && finalAllocationArray.length > 0) {
                for ( var allocation of finalAllocationArray){
                    var status = runCommonValidations(allocation,issuedAmount);
                    if(!status){
                        isSuccess=false;
                        break ;
                    }
                }
            }
            console.log("Final Voucher Cost Center Allocation List ::::::", finalAllocationArray);
            return isSuccess ;
        }

        function resetTransactionalValues(){
            service.isSuccessfulAllocation=false;
            service.walletBusinessCostCenterMappings =[];
            service.totalAllocation = null;
            service.voucherCostCenterAllocation={};
            service.voucherCostCenterAllocations=[];
        }

        /*function validateTotalAllocatedAmount(voucherCostCenterAllocations,issuedAmount, callback,error){
            if(voucherCostCenterAllocations !=null && Array.isArray(voucherCostCenterAllocations) && voucherCostCenterAllocations.length>0){
                if(runCommonValidations(voucherCostCenterAllocations,issuedAmount)){
                    return voucherCostCenterAllocations;
                } else {
                    return null;
                }
            }
        }*/

        function runCommonValidations(voucherCostCenterAllocation, issuedAmount) {
            if (AppUtil.isEmptyObject(voucherCostCenterAllocation.businessCostCenter) || AppUtil.isEmptyObject(voucherCostCenterAllocation.businessCostCenterId)) {
                toastr.error("Please select a business cost center !");
                return false;
            }
            if (voucherCostCenterAllocation.allocatedIssuedAmount == null) {
                toastr.error("Please assign allocation amount first !");
                return false;
            }
            if (voucherCostCenterAllocation.allocatedIssuedAmount !=undefined && voucherCostCenterAllocation.allocatedIssuedAmount !=null && voucherCostCenterAllocation.allocatedIssuedAmount<=0 ) {
                toastr.error("Allocation amount has to be greater than 0 !");
                return false;
            }

            if (voucherCostCenterAllocation.allocatedIssuedAmount !=undefined && voucherCostCenterAllocation.allocatedIssuedAmount !=null && voucherCostCenterAllocation.allocatedIssuedAmount>0 && (service.totalAllocation +parseInt(voucherCostCenterAllocation.allocatedIssuedAmount))> issuedAmount) {
                var remainingAmt = parseInt(issuedAmount)-parseInt(service.totalAllocation);
                toastr.error("Allocation amount is greater than the remaining amount to be allocated ,i.e::" + remainingAmt.toString());
                return false;
            }
            if (!AppUtil.isEmptyObject(voucherCostCenterAllocation.businessCostCenter) && !AppUtil.isEmptyObject(voucherCostCenterAllocation.businessCostCenter)) {
                var remainingAmt = issuedAmount - parseInt(voucherCostCenterAllocation.allocatedIssuedAmount);
                if (remainingAmt < 0) {
                    toastr.error("Wrong allocation .Current allocation amount is greater than the issuedAmount::"+issuedAmount.toString());
                    return false;
                } else {
                    service.totalAllocation = service.totalAllocation + parseInt(voucherCostCenterAllocation.allocatedIssuedAmount);
                }
                checkIsSuccessFulAllocation(service.totalAllocation, issuedAmount);
                return true;
            }
        }

        function checkIsSuccessFulAllocation(totalAllocation ,issuedAmount){
            if(!AppUtil.isEmptyObject(totalAllocation) && totalAllocation === issuedAmount){
                service.isSuccessfulAllocation =true;
            }else {
                service.isSuccessfulAllocation=false;
            }
        }

        function getUnitList(callback, error, selectedFamily) {
            var url ="";
            if (service.unitList != null && Array.isArray(service.unitList)) {
                if(selectedFamily!==undefined && selectedFamily !=null){
                    url = APIJson.urls.unitMetaData.activeUnits+"?category="+selectedFamily;
                }else {
                    url = APIJson.urls.unitMetaData.activeUnits+"?category=ALL"
                }
                $http({
                    method: 'GET',
                    url: url
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.unitList = response.data;
                        if (typeof error == "function") {
                            callback(service.unitList);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting unit list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            } else {
                callback(service.unitList);
            }
        }

        function getActiveEmployeeList(callback, error) {
            if (service.activeEmployeeList != null && Array.isArray(service.activeEmployeeList)) {
                $http({
                    method: 'GET',
                    url: APIJson.urls.employeeMetadata.activeEmployees
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.activeEmployeeList = response.data;
                        if (typeof error == "function") {
                            callback(service.activeEmployeeList);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting active employee list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            }
            /*else {
                callback(service.activeEmployeeList);
            }*/
        }

        function getCompanyList(callback,error){
            if(service.companyList !=null && Array.isArray(service.companyList)){
                $http({
                    method: 'GET',
                    url: APIJson.urls.unitMetaData.companies
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.companyList = response.data;
                        if (typeof error == "function") {
                            callback(service.companyList);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting company list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            }
        }

        function getFamilyList(callback,error){
            if (service.familyList != null) {
                $http({
                    method: 'GET',
                    url: APIJson.urls.unitMetaData.families
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.familyList = response.data;
                        if (typeof error == "function") {
                            callback(service.familyList);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting family list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            }
        }

        function getBusinessCostCenters(selectedAccountType,selectedCompany,selectedUnit,callback,error){
            if (service.businessCostCentersList!= null && Array.isArray(service.businessCostCentersList)) {
                var businessCostCentreCode=null;
                if(selectedAccountType=='UNIT'){
                    businessCostCentreCode=selectedUnit.id;
                }
                $http({
                    method: 'GET',
                    url: APIJson.urls.unitManagement.businessCostCenters,
                    params : {
                        companyId : selectedCompany.id,
                        accountType: selectedAccountType,
                        businessCostCentreCode: businessCostCentreCode,
                    }
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.unitList = response.data;
                        if (typeof error == "function") {
                            callback(service.unitList);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting unit list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            } else {
                callback(service.unitList);
            }
        }

        function getWalletBusinessCostCenterMappings(walletId , callback, error){
            if(service.walletBusinessCostCenterMappings !=null && Array.isArray(service.walletBusinessCostCenterMappings)){
                $http({
                    method: 'GET',
                    url: APIJson.urls.walletManagement.getBusinessCostCentersByWalletId,
                    params: {
                        walletId:walletId
                    }
                }).then(function success(response){
                    if(response.data!=null && Array.isArray(response.data)){
                        service.walletBusinessCostCenterMappings=response.data;
                        if (typeof error == "function") {
                            callback(service.walletBusinessCostCenterMappings);
                        }
                    }else{
                        if (typeof error == "function") {
                            error('Error getting wallet business cost center mapping list.');
                        }
                    }
                },function error(response) {
                    error(response.errorMessage);
                });
            }
        }

        function getAreaManagerUnits(amId) {
            var promise =  $http({
                method: 'GET',
                url: APIJson.urls.userManagement.activeUnitsForUser + "?amId="+amId
            }).then(function success(response) {
                if (response.data != null && Array.isArray(response.data)) {
                    return  response.data;
                }
            }, function error(response) {
                error(response.errorMessage);
            });
            return promise;
        }

        function getUserUnits() {
            return service.userUnits;
        }

        function setUserUnits(units) {
            service.userUnits = units;
        }

        function getAuditUnitEmployees(unitId, callback, error) {
            if(service.auditUnitData.id != null && service.auditUnitData.id == unitId &&
                service.auditUnitEmployees != null && Array.isArray(service.auditUnitEmployees)){
                callback(service.auditUnitEmployees);
            } else {
                $http({
                    method: 'POST',
                    url: APIJson.urls.userManagement.usersForUnit,
                    data: unitId
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.auditUnitData.id = unitId;
                        service.auditUnitEmployees = response.data;
                        if (typeof error == "function") {
                            callback(service.auditUnitEmployees);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting employees list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            }
        }

        function getAuditUnitProducts(unitId, callback, error) {
            if(service.auditUnitData.id != null && service.auditUnitData.id == unitId &&
                service.auditUnitProducts != null && Array.isArray(service.auditUnitProducts)){
                callback(service.auditUnitProducts);
            } else {
                $http({
                    method: 'POST',
                    url: APIJson.urls.unitMetaData.unitProductDataTrimmed+"?all=true",
                    data: unitId
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.auditUnitData.id = unitId;
                        service.auditUnitProducts = response.data;
                        if (typeof error == "function") {
                            callback(service.auditUnitProducts);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting products.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            }
        }

        function getSKUForUnit(unitId, callback, error) {
                $http({
                    method: 'POST',
                    url: APIJson.urls.sku.skuForUnit,
                    data: unitId
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        if (typeof error == "function") {
                            callback(response.data);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting products.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
        
        }
        
        function getCodePrefix(keyId, keyType, callback, error) {
            $http({
                method: 'GET',
                url: APIJson.urls.batchManagement.getPrefix,
                params: {keyId: keyId,
                    keyType: keyType}
            }).then(function success(response) {
                if (response.data != null) {
                    if (typeof error == "function") {
                        callback(response.data);
                    }
                } else {
                    if (typeof error == "function") {
                        error('Error getting products.');
                    }
                }
            }, function error(response) {
                error(response.errorMessage);
            });
    
    }
        
        function getUnitSubCategories(callback, error) {
            if (service.subCategoriesList != null && Array.isArray(service.subCategoriesList)) {
                $http({
                    method: 'GET',
                    url: APIJson.urls.unitMetaData.subCategories
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.subCategoriesList = response.data;
                        if (typeof error == "function") {
                            callback(service.subCategoriesList);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting unit list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            } else {
                callback(service.subCategoriesList);
            }
        }

        function getUnitListBySubCategory(category, subCategory, callback, error) {
            if (service.unitList != null && Array.isArray(service.unitList) && category!=="" && subCategory!=="") {
                $http({
                    method: 'GET',
                    url: APIJson.urls.unitMetaData.activeUnitsBySubCategory + "?category="+category+"&subCategory="+subCategory
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.unitList = response.data;
                        if (typeof error == "function") {
                            callback(service.unitList);
                        }
                    } else {
                        if (typeof error == "function") {
                            error('Error getting unit list.');
                        }
                    }
                }, function error(response) {
                    error(response.errorMessage);
                });
            } else {
                callback(service.unitList);
            }
        }

        return service;

    }

})();