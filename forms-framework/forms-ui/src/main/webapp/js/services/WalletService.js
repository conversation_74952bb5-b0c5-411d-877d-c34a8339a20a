(function () {
    'use strict';

    angular.module('formsApp').factory('WalletService', WalletService);

    WalletService.$inject = ['AppUtil', '$rootScope', '$http', '$uibModal', 'APIJson', 'toastr'];

    function WalletService(AppUtil, $rootScope, $http, $uibModal, APIJson, toastr) {

        var service = {};

        service.downlaodVoucherDetailSheet = function (voucherSearch, modifyAccountType) {
            var searchData = angular.copy(voucherSearch);
            if (modifyAccountType) {
                searchData.accountNo = (searchData.accountNo != undefined ? [searchData.accountNo] : null);
            }
            searchData.startDate = (searchData.startDate != null ? AppUtil.formatDate(searchData.startDate, 'yyyy-MM-dd') : null),
                searchData.endDate = (searchData.endDate != null ? AppUtil.formatDate(searchData.endDate, 'yyyy-MM-dd') : null),
                searchData.status = (searchData.status != undefined ? [searchData.status] : null);
            $http({
                method: 'POST',
                url: APIJson.urls.walletManagement.downloadVoucherDetail,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                data: searchData
            }).then(function success(response) {
                var fileName = "Voucher Details - " + Date.now() + ".xls";
                var blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }, fileName);
                saveAs(blob, fileName);
            }, function error(response) {
                console.log("error:", response);
                toastr.error("Unable to download Voucher Details Sheet");
            });
        };

        service.getStatusList = function () {
            return ["PENDING_SETTLEMENT", "AM_PENDING", "FINANCE_PENDING", "PENDING_REJECTION_AM", "PENDING_REJECTION_FINANCE", "REJECTED", "APPROVED", "SETTLED"];
        };

        service.showInvoice = function (voucher) {
            service.getVoucherDetails(voucher).then(function (d) {
                var fileDetail = null;
                if (d.fileDetails.length == 0) {
                    toastr.error("Invoice Not Found!");
                } else if (d.fileDetails.length == 1) {
                    fileDetail = d.fileDetails[0];
                    service.downloadInvoice(fileDetail.id, fileDetail.fileName, 'view');
                } else if (d.fileDetails.length > 1) {
                    toastr.error("Multiple Invoices Found.See details in view Mode!");
                }
            });
        };

        service.downloadVoucher = function (searchVoucher, modifyAccountType) {
            var diff = AppUtil.getDaysDifference(searchVoucher.startDate, searchVoucher.endDate);
            if (diff > 180) {
                toastr.info("Please select date range with in 6 months!");
                return true;
            } else if (diff > 60 && diff < 180) {
                toastr.warning("Date range is above 2 months.So we have downloaded sheet for you!");
                service.downlaodVoucherDetailSheet(searchVoucher, modifyAccountType);
                return true;
            }
            return false;
        };


        service.getVoucherDetails = function (voucher) {
            $rootScope.rootLoading = true;
            var promise = $http({
                method: 'GET',
                url: APIJson.urls.walletManagement.voucherDetail,
                params: {
                    voucherId: voucher.id
                }
            }).then(function success(response) {
                $rootScope.rootLoading = false;
                return response.data;
            }, function error(response) {
                $rootScope.rootLoading = false;
            });
            return promise;
        };

        service.downloadInvoice = function (imageId, imageName, actionType) {
            $rootScope.rootLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.walletManagement.downloadInvoice,
                data: imageId,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/pdf'
                }
            }).then(function success(response) {
                if (response && response.data != null) {
                    var blob = new Blob([response.data], {
                        type: 'image/*'
                    }, imageName);
                    $rootScope.rootLoading = false;
                    if (actionType == 'view') {
                        var imageUrl = URL.createObjectURL(blob);
                        previewInvoice(imageUrl, imageName, imageId, blob);
                    } else {
                        saveAs(blob, imageName);
                    }
                } else {
                    toastr.error("Could not fetch invoice.");
                }
            }, function error(response) {
                if (response && response.errorMsg) {
                    toastr.error(errorMsg);
                } else {
                    toastr.error('Error getting invoice.');
                }
                $rootScope.rootLoading = false;
            });
        };

        function previewInvoice(imageUrl, imageName, imageId, imageFile) {
            var modalInstance = $uibModal.open({
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'views/warningImagePreviewModal.html',
                controller: 'WarningImagePreviewCtrl',
                backdrop: 'static',
                keyboard: false,
                size: 'lg',
                resolve: {
                    warningImageDetails: function () {
                        return {
                            imageUrl: imageUrl,
                            imageName: imageName,
                            imageId: imageId,
                            imageFile: imageFile
                        }
                    }
                }
            });
            modalInstance.result.then(function () {

            });
        }

        return service;

    }

})();