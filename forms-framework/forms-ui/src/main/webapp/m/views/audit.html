<div data-ng-init="init()">
    <div data-ng-if="loading">
        <div class="pageHeader">
            <button class="btn btn-success" data-ng-click="backToForms()" style="background: transparent;">
                <i class="fas fa-arrow-left"></i>
            </button>
            Fill Audit
        </div>
        <div class="text-center" style="margin-top: 100px">
            <div class="loader xl"></div>
            <br/>
            Loading form...
        </div>
    </div>
    <div data-ng-if="!loading">
        <div data-ng-if="error">
            <div uib-alert class="alert alert-danger">{{errorMessage}}</div>
        </div>
        <div data-ng-if="auditForm!=null">
            <div class="auditFormPanel">
                <div class="pageHeader">
                    <button class="btn btn-success" data-ng-click="backToForms()" style="background: transparent;">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    {{auditForm.name}}
                </div>
                <div class="auditFormBody" style="margin-bottom: 50px;">
                    <div class="container-fluid">
                        <uib-tabset active="formDataActive">
                            <uib-tab index="0" heading="Form Metadata" disable="true">
                                <div class="row form-group" style="margin-top: 10px;">
                                    <div class="col-xs-12">
                                        <label>Select Unit</label>
                                        <div class="form-control"
                                             data-ng-click="showAutocomplete(units,'Select Audit Unit', 'AUDIT_UNIT')">
                                            {{selectedUnit.name}} - {{selectedUnit.id}}
                                        </div>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <label>Select Date</label>
                                        <p class="input-group">
                                            <input type="text" class="form-control"
                                                   uib-datepicker-popup="yyyy-MM-dd"
                                                   ng-model="auditData.auditDate" is-open="popupDate.opened"
                                                   datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                                   ng-required="true" close-text="Close"
                                                   data-ng-change="calculateProgressValue()"/>
                                            <span class="input-group-btn">
                                                    <button type="button" class="btn btn-default"
                                                            ng-click="popupDate.opened=true">
                                                        <i class="glyphicon glyphicon-calendar"></i>
                                                    </button>
                                                </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <label>Enter Time</label>
                                        <div uib-timepicker ng-model="auditData.auditTime" ng-change="changed()"
                                             show-meridian="true" min="minDate"
                                             data-ng-change="calculateProgressValue()"></div>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <label>Select Unit Manager</label>
                                        <div class="form-control"
                                             data-ng-click="showAutocomplete(unitEmployees,'Select Unit Manager', 'UNIT_MANAGER')">
                                            {{cafeManager.name}} - {{cafeManager.employeeCode}}
                                        </div>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <label>Select Manager on Duty</label>
                                        <div class="form-control"
                                             data-ng-click="showAutocomplete(unitEmployees,'Select Manager on Duty', 'MANAGER_ON_DUTY')">
                                            {{managerOnDuty.name}} - {{managerOnDuty.employeeCode}}
                                        </div>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <label>Select type</label>
                                        <select ng-model="auditData.auditType" class="form-control"
                                                data-ng-options="type.type as type.type for type in auditTypes track by type.id"></select>
                                    </div>
                                    <div class="col-xs-6">

                                    </div>
                                </div>
                            </uib-tab>
                            <uib-tab index="1" heading="Form Detail" disable="true">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="headSection" data-ng-if="currentFormTab.QGData != null">
                                            <p style="font-size: 23px;">
                                                {{currentFormTab.QGData.label}}
                                                <span style="cursor: pointer; font-size: 12px; color: #2380f9;"
                                                      data-ng-click="currentFormTab.QGData.showDesc = false"
                                                      data-ng-if="currentFormTab.QGData.showDesc == true">Hide Detail</span>
                                                <span style="cursor: pointer; font-size: 12px; color: #2380f9;"
                                                      data-ng-click="currentFormTab.QGData.showDesc = true"
                                                      data-ng-if="currentFormTab.QGData.showDesc != true">Show Detail</span>
                                                <label style="float: right;"
                                                       data-ng-if="currentFormTab.QGData.questionOptional == true">
                                                    <input type="checkbox" name="{{currentFormTab.QGData.id}}qoptional"
                                                           id="{{currentFormTab.QGData.id}}qoptional" value="Y"
                                                           data-ng-model="currentFormTab.QGData.na"
                                                           data-ng-change="setQuestionOpted('GROUP',currentFormTab.QGData.id,currentFormTab.QGData.na)">
                                                    NA
                                                </label>
                                            </p>
                                            <p data-ng-if="currentFormTab.QGData.description != null && currentFormTab.QGData.showDesc == true"
                                               style="font-size: 14px;">{{currentFormTab.QGData.description}}</p>
                                        </div>
                                        <div class="row form-group questionBlock"
                                             data-ng-repeat="question in currentFormTab.questions">
                                            <div class="col-xs-12">
                                                <label data-ng-if="question.entityLabel != null">{{question.entityLabel}}
                                                    <span data-ng-if="question.isMandatory!=true">(optional)</span></label>
                                                <span data-ng-if="question.showMaxScore == true && question.maxScore!=null">(Max-score: {{question.maxScore}})</span></label>
                                                <span style="cursor: pointer; font-size: 12px; color: #2380f9;"
                                                      data-ng-click="question.showDesc = false"
                                                      data-ng-if="question.showDesc == true">Hide Detail</span>
                                                <span style="cursor: pointer; font-size: 12px; color: #2380f9;"
                                                      data-ng-click="question.showDesc = true"
                                                      data-ng-if="question.showDesc != true">Show Detail</span>
                                                <p data-ng-if="question.entityDescription != null && question.showDesc == true">
                                                    {{question.entityDescription}}</p>
                                                <label style="float: right;"
                                                       data-ng-if="question.questionOptional == true">
                                                    <input type="checkbox" name="{{question.id}}qoptional"
                                                           id="{{question.id}}qoptional" value="Y"
                                                           data-ng-model="question.na"
                                                           data-ng-change="setQuestionOpted('ITEM',question.id,question.na)">
                                                    NA
                                                </label>

                                                <p data-ng-if="question.entityType=='SNIPPET'">
                                                    {{question.entityValues}}</p>

                                                <input data-ng-if="question.entityType=='NUMBER'" type="number"
                                                       class="form-control" data-ng-max="question.maxScore"
                                                       data-ng-model="answerMap[question.id].answer"
                                                       data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)"/>

                                                <div class="form-control" data-ng-init="loadProducts()"
                                                     data-ng-if="question.entityType=='PRODUCT'"
                                                     data-ng-click="showAutocomplete(unitProducts,'Select Product', 'PRODUCT',question.id,answerMap[question.id].answer,'product')">
                                                    {{answerMap[question.id].answer.product.name}}
                                                    {{answerMap[question.id].answer.product.id}}
                                                </div>

                                                <div class="form-control"
                                                     data-ng-if="question.entityType=='UNIT_EMPLOYEE'"
                                                     data-ng-click="showAutocomplete(unitEmployees,'Select Employee', 'EMPLOYEE',question.id,answerMap[question.id].answer,'employee')">
                                                    {{answerMap[question.id].answer.employee.name}}
                                                    {{answerMap[question.id].answer.employee.employeeCode}}
                                                </div>

                                                <textarea class="form-control"
                                                          data-ng-if="question.entityType=='TEXTAREA'"
                                                          ng-model="answerMap[question.id].answer"
                                                          data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)"></textarea>

                                                <div data-ng-if="question.entityType=='BOOLEAN'">
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" name="optionsRadio{{question.id}}"
                                                                   id="optionsRadio{{question.id}}yes" value="Y"
                                                                   data-ng-model="answerMap[question.id].answer"
                                                                   data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)">
                                                            Yes <span data-ng-if="question.maxScore!=null">(Marks:{{question.maxScore}})</span>
                                                        </label>
                                                    </div>
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" name="optionsRadio{{question.id}}"
                                                                   id="optionsRadio{{question.id}}no" value="N"
                                                                   data-ng-model="answerMap[question.id].answer"
                                                                   data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)">
                                                            No <span
                                                                data-ng-if="question.maxScore!=null">(Marks: 0)</span>
                                                        </label>
                                                    </div>
                                                </div>

                                                <div data-ng-if="question.entityType=='RADIO'">
                                                    <div class="radio" data-ng-repeat="item in question.entityValues">
                                                        <label>
                                                            <input type="radio" name="optionsRadio{{question.id}}"
                                                                   id="optionsRadio{{question.id}}{{item.id}}"
                                                                   data-ng-value="item"
                                                                   data-ng-model="answerMap[question.id].answer"
                                                                   data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)">
                                                            {{item.value}} <span data-ng-if="item.showAnswerScore && item.marks!=null">(Marks:{{item.marks}})</span>
                                                        </label>
                                                    </div>
                                                </div>

                                                <div data-ng-if="question.entityType=='SELECT_BOX'">
                                                    <select data-ng-options="item as item.value for item in question.entityValues"
                                                            data-ng-model="answerMap[question.id].answer"
                                                            class="form-control"
                                                            data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)"></select>
                                                </div>

                                                <div data-ng-if="question.entityType=='PRODUCT_EMPLOYEE_RADIO'"
                                                     style="background: #efefef; padding:10px;">
                                                    <label>Marks:</label>
                                                    <div class="radio" data-ng-repeat="item in question.entityValues">
                                                        <label>
                                                            <input type="radio" name="optionsRadio{{question.id}}"
                                                                   id="optionsPERadio{{question.id}}{{item.id}}"
                                                                   data-ng-value="item"
                                                                   data-ng-model="answerMap[question.id].answer.radio"
                                                                   data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)">
                                                            {{item.value}} <span data-ng-if="item.marks!=null">(Marks:{{item.marks}})</span>
                                                        </label>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>Product:</label>
                                                        <div class="form-control" data-ng-init="loadProducts()"
                                                             data-ng-click="showAutocomplete(unitProducts,'Select Product', 'PRODUCT',question.id,answerMap[question.id].answer,'product')">
                                                            {{answerMap[question.id].answer.product.name}}
                                                            {{answerMap[question.id].answer.product.id}}
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>Employee:</label>
                                                        <div class="form-control"
                                                             data-ng-click="showAutocomplete(unitEmployees,'Select Employee', 'EMPLOYEE',question.id,answerMap[question.id].answer,'employee')">
                                                            {{answerMap[question.id].answer.employee.name}}
                                                            {{answerMap[question.id].answer.employee.employeeCode}}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div data-ng-if="question.entityType=='TEXTAREA_EMPLOYEE_RADIO'"
                                                     style="background: #efefef; padding:10px;">
                                                    <label>Marks:</label>
                                                    <div class="form-group">
                                                        <label>Text:</label>
                                                        <textarea class="form-control"
                                                                  ng-model="answerMap[question.id].answer.textArea"
                                                                  data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)"></textarea>
                                                    </div>
                                                    <div class="radio" data-ng-repeat="item in question.entityValues">
                                                        <label>
                                                            <input type="radio" name="optionsRadio{{question.id}}"
                                                                   id="optionsTERadio{{question.id}}{{item.id}}"
                                                                   data-ng-value="item"
                                                                   data-ng-model="answerMap[question.id].answer.radio"
                                                                   data-ng-change="setEntityValue(question.id,answerMap[question.id].answer)">
                                                            {{item.value}} <span data-ng-if="item.marks!=null">(Marks:{{item.marks}})</span>
                                                        </label>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>Employee:</label>
                                                        <div class="form-control"
                                                             data-ng-click="showAutocomplete(unitEmployees,'Select Employee', 'EMPLOYEE',question.id,answerMap[question.id].answer,'employee')">
                                                            {{answerMap[question.id].answer.employee.name}}
                                                            {{answerMap[question.id].answer.employee.employeeCode}}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12" data-ng-if="question.additionalComment==true">
                                                <div class="form-group questionBlock">
                                                    <label>Comment/Observations/Remarks</label>
                                                    <textarea class="form-control"
                                                              ng-model="answerMap[question.id].comment"></textarea>
                                                </div>
                                                <div data-ng-show="question.attachDoc == true">
														<form>
															<div class="row">
																<div class="col-xs-12 form-group">
																	<label class="control-label"> Image upload : </label> <input
																		type="file" file-model="imageDetails[question.id].files"
																		accept="image/*" />
																</div>
															</div>
															<div class="row">
																<div class="col-xs-4 form-group">
																	<button class="btn btn-warning pull-right"
																		data-ng-click="uploadImageFile(answerMap[question.id], imageDetails[question.id].files)">Upload</button>
																</div>
																<div class="col-xs-5 form-group" data-ng-if ="answerMap[question.id].attachedDoc" ><label style="color: green;">Image Uploaded</label></div>
																<div class="col-xs-3 form-group"><button class="btn btn-danger"
																		data-ng-click="resetDetails()" data-ng-if="false">Delete</button></div>
															</div>
														</form>
													</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </uib-tab>
                        </uib-tabset>
                    </div>
                </div>
                <div class="auditFormFooter">
                    <div style="margin-right: 100px;padding: 10px;">
                        {{progressValue}} % completed
                        <uib-progressbar class="progress-striped" value="progressValue" type="{{progressType}}">
                        </uib-progressbar>
                    </div>
                    <div style="width: 90px;position: absolute;right: 0;top: 0;">
                        <button class="btn btn-success" data-ng-click="prev()"
                                style="width:45px;height:45px;margin-right:-4px;border:none;background-color:#5cb85c;outline: none;">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="btn btn-success" data-ng-click="next()" data-ng-if="showPreview == false"
                                style="width:45px;height: 45px;border: none;background-color:#5cb85c;outline: none;">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="btn btn-success" data-ng-click="previewModal()" data-ng-if="showPreview == true"
                                style="width:45px;height: 45px;border: none;background-color:#5cb85c;outline: none;">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="autocompletePanel" data-ng-if="autocompletePanel" data-ng-init="autocompleteInit()">
        <div class="aPanelHead">
            <div class="aPanelBack" data-ng-click="hideAutocomplete()"><i class="fas fa-arrow-left"></i></div>
            <div class="aPanelName">{{autocompleteName}}</div>
        </div>
        <input type="text" id="autocompleteInput" placeholder="Search here..." class="form-control"
               data-ng-change="filter()" data-ng-model="aPanelSearch"/>
        <ul class="searchList">
            <li class="listItem"
                data-ng-repeat="item in filtered = (autocompleteList | filter:aPanelSearch) track by item.id"
                data-ng-click="selectItem(item)">{{item.name}} - <span data-ng-if="model=='AUDIT_UNIT' || model=='PRODUCT'">{{item.id}}</span>
                <span data-ng-if="model=='EMPLOYEE' || model=='MANAGER_ON_DUTY' || model=='UNIT_MANAGER'">{{item.employeeCode}}</span>
            </li>
        </ul>
    </div>

</div>


<script type="text/ng-template" id="previewModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title">{{auditForm.name}}</h3>
    </div>
    <div class="modal-body" id="modal-body" data-ng-if="showErrors!=true">
        <div class="row">
            <div class="col-xs-12">
                <table class="table table-bordered stripe">
                    <tr>
                        <td>Cafe:</td>
                        <td>{{auditData.auditUnit.name}} - {{auditData.auditUnit.id}}</td>
                    </tr>
                    <tr>
                        <td>Cafe Manager Name:</td>
                        <td>{{auditData.cafeManager.name}}</td>
                    </tr>
                    <tr>
                        <td>Manager on Duty:</td>
                        <td>{{auditData.managerOnDuty.name}}</td>
                    </tr>
                    <tr>
                        <td>Auditor Name:</td>
                        <td>{{userData.name}}</td>
                    </tr>
                    <tr>
                        <td>Audit Type:</td>
                        <td>{{auditData.auditType}}</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="row" data-ng-if="scores.length>2">
            <div class="col-xs-12">
                <h3>Audit Scores</h3>
                <table class="table table-bordered stripe">
                    <tr>
                        <td>Category</td>
                        <td>Acquired Score</td>
                        <td>Max Score</td>
                        <td>Percentage</td>
                    </tr>
                    <tr data-ng-repeat="score in scores track by $index">
                        <td>{{score.name}}</td>
                        <td>{{score.acquiredScore}}</td>
                        <td>{{score.maxScore}}</td>
                        <td>{{score.percentage}}</td>
                    </tr>
                    <tr>
                        <td>Projected</td>
                        <td>{{projected.acquiredScore}}</td>
                        <td>{{projected.maxScore}}</td>
                        <td>{{projected.percentage}} %</td>
                    </tr>
                </table>
            </div>
        </div>
        <div data-ng-repeat="item in formRenderObj track by $index">
            <div data-ng-if="item.entityType == 'QUESTION_GROUP'">
                <div class="row headSection">
                    <div class="col-xs-12">
                        <p style="font-size: 23px;">{{item.entityLabel}}</p>
                        <!--<p data-ng-if="item.entityDescription != null"
                           style="font-size: 14px;">{{item.entityDescription}}</p>-->
                    </div>
                </div>
                <div class="row form-group questionBlock" data-ng-repeat="question in item.items">
                    <div class="col-xs-12">
                        <label data-ng-if="question.entityLabel != null">{{question.entityLabel}}
                            <span data-ng-if="question.isMandatory!=true">(optional)</span></label>
                        <!--<p data-ng-if="question.entityDescription != null">
                            {{question.entityDescription}}</p>-->

                        <p data-ng-if="question.entityType=='SNIPPET'">
                            {{question.entityValues}}</p>

                        <div data-ng-if="answerMap[question.id].na != true">
                            <div data-ng-if="question.entityType=='NUMBER'">
                                <b>Answer:</b> {{answerMap[question.id].answer}}
                                <div data-ng-if="question.showAnswerScore && question.maxScore != null"><strong>Marks: </strong>{{answerMap[question.id].answer}}/{{question.maxScore}}
                                </div>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>

                            <div data-ng-if="question.entityType=='PRODUCT'">
                                <b>Answer:</b>
                                <div>Product -- Id:{{answerMap[question.id].answer.product.id}},
                                    Name:{{answerMap[question.id].answer.product.name}}
                                </div>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>

                            <div data-ng-if="question.entityType=='UNIT_EMPLOYEE'">
                                <b>Answer:</b>{{answerMap[item.id]}}
                                <div>Employee -- Id:{{answerMap[question.id].answer.employee.employeeCode}},
                                    Name:{{answerMap[question.id].answer.employee.name}},
                                    Designation:{{answerMap[item.id].answer.employee.designation}}
                                </div>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>

                            <div data-ng-if="question.entityType=='TEXTAREA'">
                                <b>Answer:</b> <pre>{{answerMap[question.id].answer}}</pre>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>

                            <div data-ng-if="question.entityType=='BOOLEAN'">
                                <b>Answer:</b> {{answerMap[question.id].answer}}
                                <div data-ng-if="question.showAnswerScore && question.maxScore != null"><strong>Marks: </strong>{{answerMap[question.id].answer=='Y'?question.maxScore:0}}/{{question.maxScore}}
                                </div>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>

                            <div data-ng-if="question.entityType=='RADIO' || question.entityType=='SELECT_BOX'">
                                <b>Answer:</b> Option {{answerMap[question.id].answer.id}}:
                                {{answerMap[question.id].answer.value}}
                                <div data-ng-if="question.showAnswerScore && question.maxScore != null"><strong>Marks: </strong>{{answerMap[question.id].answer.marks}}/{{question.maxScore}}
                                </div>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>

                            <div data-ng-if="question.entityType=='PRODUCT_EMPLOYEE_RADIO'">
                                <b>Answer:</b>
                                <div>Option {{answerMap[question.id].answer.radio.id}}:
                                    {{answerMap[question.id].answer.radio.value}}
                                </div>
                                <div>Employee -- Id:{{answerMap[question.id].answer.employee.employeeCode}},
                                    Name:{{answerMap[question.id].answer.employee.name}},
                                    Designation:{{answerMap[question.id].answer.employee.designation}}
                                </div>
                                <div>Product -- Id:{{answerMap[question.id].answer.product.id}},
                                    Name:{{answerMap[question.id].answer.product.name}}
                                </div>
                                <div data-ng-if="question.showAnswerScore && question.maxScore != null"><strong>Marks: </strong>{{answerMap[question.id].answer.radio.marks}}/{{question.maxScore}}
                                </div>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>

                            <div data-ng-if="question.entityType=='TEXTAREA_EMPLOYEE_RADIO'">
                                <b>Answer:</b>
                                <div>Text: <pre>{{answerMap[question.id].answer.textArea}}</pre></div>
                                <div>Option: {{answerMap[question.id].answer.radio.id}}:
                                    {{answerMap[question.id].answer.radio.value}}
                                </div>
                                <div>Employee -- Id:{{answerMap[question.id].answer.employee.employeeCode}},
                                    Name:{{answerMap[question.id].answer.employee.name}},
                                    Designation:{{answerMap[question.id].answer.employee.designation}}
                                </div>
                                <div data-ng-if="question.showAnswerScore && question.maxScore != null"><strong>Marks: </strong>{{answerMap[question.id].answer.radio.marks}}/{{question.maxScore}}
                                </div>
                                <div data-ng-if="answerMap[question.id].comment!=null">
                                    <b>Comment:</b>{{answerMap[question.id].comment}}
                                </div>
                            </div>
                        </div>
                        <div data-ng-if="answerMap[question.id].na == true">NA</div>

                    </div>
                </div>
            </div>
            <div class="row form-group questionBlock" data-ng-if="item.entityType != 'QUESTION_GROUP'">
                <!--<div class="row form-group questionBlock" data-ng-repeat="question in item.items">-->
                <div class="col-xs-12">
                    <label data-ng-if="item.entityLabel != null">{{item.entityLabel}}
                        <span data-ng-if="item.isMandatory!=true">(optional)</span></label>
                    <!--<p data-ng-if="item.entityDescription != null">
                        {{item.entityDescription}}</p>-->

                    <p data-ng-if="item.entityType=='SNIPPET'">
                        {{item.entityValues}}</p>

                    <div data-ng-if="answerMap[item.id].na != true">
                        <div data-ng-if="item.entityType=='NUMBER'">
                            <b>Answer:</b> {{answerMap[item.id].answer}}
                            <div data-ng-if="item.showAnswerScore && item.maxScore != null"><strong>Marks: </strong>{{answerMap[item.id].answer}}/{{item.maxScore}}
                            </div>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                            <span data-ng-if="item.maxScore != null">Max Score: {{item.maxScore}}</span>
                        </div>

                        <div data-ng-if="item.entityType=='PRODUCT'">
                            <b>Answer:</b>
                            <div>Product -- Id:{{answerMap[item.id].answer.product.id}},
                                Name:{{answerMap[item.id].answer.product.name}}
                            </div>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                        </div>

                        <div data-ng-if="item.entityType=='UNIT_EMPLOYEE'">
                            <b>Answer:</b>
                            <div>Employee -- Id:{{answerMap[item.id].answer.employee.employeeCode}},
                                Name:{{answerMap[item.id].answer.employee.name}},
                                Designation:{{answerMap[item.id].answer.employee.designation}}
                            </div>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                        </div>

                        <div data-ng-if="item.entityType=='TEXTAREA'">
                            <b>Answer:</b> <pre>{{answerMap[item.id].answer}}</pre>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                        </div>

                        <div data-ng-if="item.entityType=='BOOLEAN'">
                            <b>Answer:</b> {{answerMap[item.id].answer}}
                            <div data-ng-if="item.showAnswerScore && item.maxScore != null"><strong>Marks: </strong>{{answerMap[item.id].answer=='Y'?item.maxScore:0}}/{{item.maxScore}}
                            </div>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                        </div>

                        <div data-ng-if="item.entityType=='RADIO' || item.entityType=='SELECT_BOX'">
                            <b>Answer:</b> Option {{answerMap[item.id].answer.id}}: {{answerMap[item.id].answer.value}}
                            <div data-ng-if="item.showAnswerScore && item.maxScore != null"><strong>Marks: </strong>{{answerMap[item.id].answer.marks}}/{{item.maxScore}}
                            </div>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                        </div>

                        <div data-ng-if="item.entityType=='PRODUCT_EMPLOYEE_RADIO'">
                            <b>Answer:</b>
                            <div>Option {{answerMap[item.id].answer.radio.id}}:
                                {{answerMap[item.id].answer.radio.value}}
                            </div>
                            <div>Employee -- Id:{{answerMap[item.id].answer.employee.employeeCode}},
                                Name:{{answerMap[item.id].answer.employee.name}},
                                Designation:{{answerMap[item.id].answer.employee.designation}}
                            </div>
                            <div>Product -- Id:{{answerMap[item.id].answer.product.id}},
                                Name:{{answerMap[item.id].answer.product.name}}
                            </div>
                            <div data-ng-if="item.showAnswerScore && item.maxScore != null"><strong>Marks: </strong>{{answerMap[item.id].answer.radio.marks}}/{{item.maxScore}}
                            </div>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                        </div>

                        <div data-ng-if="item.entityType=='TEXTAREA_EMPLOYEE_RADIO'">
                            <b>Answer:</b>
                            <div>Text: <pre>{{answerMap[item.id].answer.textArea}}</pre></div>
                            <div>Option: {{answerMap[item.id].answer.radio.id}}:
                                {{answerMap[item.id].answer.radio.value}}
                            </div>
                            <div>Employee -- Id:{{answerMap[item.id].answer.employee.employeeCode}},
                                Name:{{answerMap[item.id].answer.employee.name}},
                                Designation:{{answerMap[item.id].answer.employee.designation}}
                            </div>
                            <div data-ng-if="item.showAnswerScore && item.maxScore != null"><strong>Marks: </strong>{{answerMap[item.id].answer.radio.marks}}/{{item.maxScore}}
                            </div>
                            <div data-ng-if="answerMap[item.id].comment!=null">
                                <b>Comment:</b>{{answerMap[item.id].comment}}
                            </div>
                        </div>
                    </div>
                    <div data-ng-if="answerMap[question.id].na == true">NA</div>

                </div>
            </div>
        </div>
    </div>
    <div class="modal-body" id="modal-body" data-ng-if="showErrors == true">
        <div class="row">
            <div class="col s12">
                <h4 style="padding: 10px 25px;">Please fix following errors before submitting form:</h4>
                <ul>
                    <li data-ng-repeat="item in errorList">{{item}}</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-primary" type="button" ng-click="submitAudit()" data-ng-if="showErrors!=true">Submit</button>
        <button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
    </div>
</script>