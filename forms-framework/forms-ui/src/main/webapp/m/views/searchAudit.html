<div data-ng-init="init()">
    <div class="pageHeader">
        <div style="padding: 5px;">Search Audits</div>
    </div>
    <div style="padding: 0 15px;">
        <div class="row">
            <div class="col-xs-12">
                <div data-ng-show="search == true">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Unit</label>
                                <div class="form-control" data-ng-click="showAutocomplete(units,'Select Audit Unit', 'AUDIT_UNIT')">{{selectedUnit.name}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
	                    <div class="col-xs-12">
	                        <div class="form-group">
	                            <label>Select Form</label>
	                            <input type="text" data-ng-model="selectedAuditForm" class="form-control"
	                                   uib-typeahead="auditForm as auditForm.name for auditForm in auditFormList | filter:$viewValue"
	                                   typeahead-show-hint="true" typeahead-min-length="0"
	                                   typeahead-append-to-body="true"
	                                   typeahead-on-select="setAuditForm(selectedAuditForm)"/>
	                        </div>
	                    </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select Start Date</label>
                                <p class="input-group">
                                    <input type="text" class="form-control"
                                           uib-datepicker-popup="yyyy-MM-dd"
                                           ng-model="startDate" is-open="popupStartDate.opened"
                                           datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                           ng-required="true" close-text="Close"/>
                                    <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="popupStartDate.opened=true">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </button>
                                </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label>Select End Date</label>
                                <p class="input-group">
                                    <input type="text" class="form-control"
                                           uib-datepicker-popup="yyyy-MM-dd"
                                           ng-model="endDate" is-open="popupEndDate.opened"
                                           datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
                                           ng-required="true" close-text="Close"/>
                                    <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="popupEndDate.opened=true">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </button>
                                </span>
                                </p>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-xs-12"><button class="btn btn-primary" data-ng-click="searchAudit()">Search</button></div>
                    </div>
                </div>
                <div data-ng-show="search != true">
                    <div class="row" data-ng-if="auditList != null">
                        <div>
                            <button class="btn btn-success" data-ng-click="backToSearch()"><i class="fas fa-arrow-left"></i></button> Audits list
                        </div>
                        <div>
                            <div class="listItem rip" data-ng-repeat="item in auditList track by item.id">
                                <div style="font-size: 18px;">{{item.id}}) {{item.auditForm.name}}</div>
                                <div style="position: absolute;top:15px;right:15px;font-size: 14px;">{{item.acquiredScore}}/{{item.totalScore}}</div>
                                <div style="float:left;">
                                    <div style="font-size: 12px;color:#8a8a8a;">{{item.auditUnit.name}}</div>
                                    <div style="font-size: 12px;color:#8a8a8a;">{{item.auditSubmissionDate | date : 'dd/MM/yyyy hh:mm:ss a'}}</div>
                                    <div style="font-size: 12px;color:#8a8a8a;">{{item.auditor.name}}</div>
                                </div>
                                <div style="text-align: right;margin-top: 15px;">
                                    <button class="btn" data-ng-click="downloadAuditReport(item.id)"><i class="fas fa-download"></i></button>
									<button class="btn" data-ng-click="issueWarningLetter(item)" data-ng-if="item.cancel == true">
										<i style="color: red;" class="far fa-clipboard" ></i>
									</button>
									<button class="btn" data-ng-click="generateAuditReport(item.id)" style="display: none;">Generate</button>
                                    <button class="btn" data-ng-click="emailAuditReport(item.id)" style="display: none;">Email</button>
                                </div>
                                <div style="clear: both"></div>
                            </div>
                            <div class="alert alert-info" data-ng-if="auditList.length == 0">No audits found.</div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <div class="autocompletePanel" data-ng-if="autocompletePanel" data-ng-init="autocompleteInit()">
        <div class="aPanelHead">
            <div class="aPanelBack" data-ng-click="hideAutocomplete()"><i class="fas fa-arrow-left"></i></div>
            <div class="aPanelName">{{autocompleteName}}</div>
            <div class="aPanelClear" data-ng-click="clearAutoComplete()">Clear</div>
        </div>
        <input type="text" id="autocompleteInput" placeholder="Search here..." class="form-control" data-ng-change="filter()" data-ng-model="aPanelSearch" />
        <ul class="searchList">
            <li class="listItem" data-ng-repeat="item in filtered = (autocompleteList | filter:aPanelSearch) track by item.id"
                data-ng-click="selectItem(item)">{{item.name}} - {{item.id}}</li>
        </ul>
    </div>
</div>