<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title pull-left">Denomination Detail</h3>
	<h2 class="modal-title pull-right" style="color: #FF9933">
		Rs {{topupAmount}} <span style="color: #e0e8e0">|</span>
		<span style="color: #138808">Rs {{denominationSum}}</span>
	</h2>
</div>
<div class="modal-body">
	<div class="row" style="marin-bottom: 20px;">
		<div class="col-xs-12" data-ng-if="cashDenomination.length > 0">
			<div class="action-comment">Bundle Size :
				{{cashDenomination[0].bundleSize}}</div>

			<table class="table table-bordered table-striped">
				<tr>
					<th>In Words(Value)</th>
					<th>Packets</th>
					<th>Loose Currency</th>
					<th>Total</th>
				</tr>
				<tr
					data-ng-repeat="denom in cashDenomination | orderBy : 'denom.displayOrder'"
					data-ng-if="denom.bundleSize == 100 && denom.denominationValue < topupAmount">
					<td>{{denom.denominationText}}({{denom.denominationValue}})</td>
					<td><input type="number" class="form-control"
						data-ng-change="updateTotal(denom)"
						data-ng-model="denom.packetCount" /></td>
					<td><input type="number" class="form-control"
						data-ng-change="updateTotal(denom)"
						data-ng-model="denom.looseCurrencyCount" /></td>
					<td>{{denom.totalAmount}}</td>
				</tr>
			</table>
			<!-- <div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label">Comment</label>
						<textarea rows="5" class="form-control"
							data-ng-model="pullComment"></textarea>
					</div>
				</div>
			</div> -->
		</div>
	</div>
</div>
<div class="modal-footer">
	<div class=" col-xs-12" style="margin-bottom: 10px">
		<button class="btn btn-danger pull-left" type="button"
			data-ng-click="cancel()">Cancel</button>
		<button class="btn btn-info pull-right" type="button"
			data-ng-click="ok(false)">Submit</button>
		<button data-ng-if="false" class="btn btn-warning pull-right" type="button"
			data-ng-click="ok(true)">Force Submit</button>
	</div>

</div>