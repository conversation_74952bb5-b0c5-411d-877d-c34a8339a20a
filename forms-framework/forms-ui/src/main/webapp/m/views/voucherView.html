<div
        style="font-size: 18px; font-family: sans-serif; margin-bottom: 10px;"
        class="wallet-container">
    <h4
            style="color: maroon; text-align: center; text-decoration: underline;">Voucher
        Detail</h4>
    <div class="row">
        <div class="col-xs-12">
            <div class="row row-spacing">
                <div class="col-xs-12">A/c Detail :
                    {{selectedVoucher.wallet.name}}({{selectedVoucher.wallet.code}})
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-12">Voucher Id :
                    {{selectedVoucher.generatedVoucherId}}
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-12">Issued Amount :
                    {{selectedVoucher.issuedAmount}}
                </div>
            </div>
            <div class="row row-spacing"
                 data-ng-if="selectedVoucher.expenseAmount != null">
                <div class="col-xs-12">
                    Expense Amount : <span
                        data-ng-class="{'text-success' : selectedVoucher.expenseAmount <= selectedVoucher.issuedAmount ,
										'text-danger' : selectedVoucher.expenseAmount > selectedVoucher.issuedAmount}">{{selectedVoucher.expenseAmount}}</span>
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-12">Expense Type :
                    {{selectedVoucher.expenseType}}
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-12">Issue Date :
                    {{selectedVoucher.businessDate}}
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-12">Issued By :
                    {{selectedVoucher.issuedBy.name}} -
                    {{selectedVoucher.issuedBy.code}}
                </div>
            </div>
            <div class="row row-spacing">

                <div class="col-xs-12">Issued To :
                    {{selectedVoucher.issuedTo.name}} -
                    {{selectedVoucher.issuedTo.code}}
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-12">Status :
                    {{selectedVoucher.currentStatus}}
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-12">Issue Time :
                    {{selectedVoucher.issuedTime | date : 'dd/MM/yyyy hh:mm:ss a'}}
                </div>
            </div>
            <div class="row row-spacing">
                <div class="col-xs-6">Invoice Date:
                    {{selectedVoucher.voucherDate | date : 'dd/MM/yyyy'}}
                </div>
            </div>
            <div data-ng-if="selectedVoucher.fileDetails.length > 0">
                <h4 style="text-align: center; text-decoration: underline;">Invoice
                    Uploaded</h4>
                <div data-ng-repeat="img in selectedVoucher.fileDetails">
                    <div class="row row-spacing">
                        <div class="col-xs-12">
                            <button type="button" class="btn btn-sm"
                                    data-ng-click="downloadInvoice(img.id,img.fileName, 'download')">
                                Download {{img.fileType.toLowerCase()}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div data-ng-if="selectedVoucher.statusDetail.length > 1">
                <h4 style="text-align: center; text-decoration: underline;">Status
                    Detail</h4>
                <div data-ng-repeat="status in selectedVoucher.statusDetail">
                    <div class="row row-spacing"
                         data-ng-if="status.toStatus != 'PENDING_SETTLEMENT'">
                        <div class="col-xs-12">
                            <div data-ng-switch="status.toStatus" class="action-type">
                                <div data-ng-switch-when="CANCELLED">Cancellation Details</div>
                                <div data-ng-switch-when="AM_PENDING">Settlement Details</div>
                                <div data-ng-switch-when="PENDING_REJECTION_AM">Rejected - Area Manager</div>
                                <div data-ng-switch-when="FINANCE_PENDING">Approved - Area Manager
                                </div>
                                <div data-ng-switch-when="PENDING_REJECTION_FINANCE">
                                    Rejected - Finance
                                </div>
                                <div data-ng-switch-when="APPROVED">Approved - Finance</div>
                                <div data-ng-switch-when="REJECTED">Rejection - Acknowledgement</div>
                            </div>
                            <div class="action-comment">
                                <div class="col-xs-12">User :
                                    {{status.generatedBy.name}}-{{status.generatedBy.code}}
                                </div>
                                <div class="col-xs-12">Time : {{status.actionTime | date :
                                    'dd/MM/yyyy hh:mm:ss a'}}
                                </div>
                                <div class="col-xs-12">Comment : {{status.actionComment ==
                                    null ? '-' : status.actionComment}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" data-ng-if="selectedVoucher.rejections.length > 0">
                <div class="col-xs-12">
                    <h3>Rejection Reasons</h3>
                    <p data-ng-repeat="rejection in selectedVoucher.rejections track by rejection.id">{{rejection.name}}</p>
                </div>
            </div>
        </div>
    </div>
</div>