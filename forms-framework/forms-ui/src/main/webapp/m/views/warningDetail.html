<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.row-spacing {
	margin-top: 10px;
}

.delete-reason {
	color: red;
	font-size: 25px;
	float: right;
}

.warning-container {
	padding-right: 5px;
	padding-left: 5px;
	margin-right: 5%;
	margin-left: 5%;
}

.action-comment {
	color: blue;
	font-size: 15px;
}

.action-type {
	color: red;
	font-size: 16px;
}

.page-heading{
    font-size: 26px;
    color: red;
    margin-bottom: 13px;
    font-family: 'typewriter';
    float: left;
}
</style>

<div data-ng-init="init()" class="warning-container">
	<div class="row">
		<div class="col-xs-2">
			<button class="btn" data-ng-if="stateDetails.viewType != 'add'"
				ui-sref="dashboard.searchWarningDetail({searchParams: stateDetails.searchParams})">
				<i class="fas fa-arrow-left pull-left"
					style="color: red; font-size: 20px;"></i>
			</button>
		</div>
		<div class="col-xs-10">
			<h2 class="text-center page-heading">
				Warning Letter Details</h2>
		</div>

	</div>
	<!-- For Issuer/AM Use -->
	<div class="row">
		<fieldset data-ng-disabled="stateDetails.viewType != 'add'">
			<div data-ng-if="!uploadImage">
				<div class="col-xs-12">
					<form novalidate role="form" name="warningLetterForm"
						autocomplete="off">
						<div class="row row-spacing"
							data-ng-if="warningDetails.audit != null">
							<div class="col-xs-12 form-group">
								<label class="control-label"> Audit Id </label> <label
									class="action-comment control-label">{{warningDetails.audit.id}}</label>
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.audit == null && stateDetails.viewType == 'add'">
							<div class="col-xs-12 form-group">
								<label for="selectedUnit" class="control-label">Select
									Unit</label> <input id="selectedUnit" type="text"
									data-ng-model="selectedUnit" class="form-control"
									uib-typeahead="unit as unit.name for unit in units | filter:$viewValue"
									typeahead-show-hint="true" typeahead-min-length="0"
									typeahead-append-to-body="true"
									typeahead-on-select="setWarningUnit(selectedUnit)" />
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.viewType != 'add'">
							<div class="col-xs-12 form-group">
								<label class="control-label"> Cafe Name :</label> <label
									class="control-label action-comment">
									{{selectedUnit.name}}</label>
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.audit == null">
							<div class="col-xs-12 form-group">
								<label for="managerOnDuty" class="control-label">Manager
									On Duty</label> <select class="form-control" id="managerOnDuty"
									name="managerOnDuty" data-ng-model="empDetails.managerOnDuty"
									data-ng-options="employee as employee.name + ' - ' + employee.employeeCode for employee in unitEmployees"
									required="required">
								</select>
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.audit != null">
							<div class="col-xs-12 form-group">
								<label class="control-label">Manager On Duty</label> <label
									class="control-label">{{stateDetails.audit.mod.name}}</label>
							</div>
						</div>
						<div class="row row-spacing">
							<div class="col-xs-12 form-group">
								<label for="guiltyPerson" class="control-label">Guilty
									Person</label> <select class="form-control" id="guiltyPerson"
									name="guiltyPerson" data-ng-model="empDetails.guiltyPerson"
									data-ng-options="employee as employee.name + ' - ' + employee.employeeCode for employee in unitEmployees"
									required="required">
								</select>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-12 form-group">
								<label for="warningStartDate" class="control-label">
									Date Of Incidence</label>
								<p class="input-group">
									<input type="text" class="form-control" id="warningDateOfIncidence"
										uib-datepicker-popup="yyyy-MM-dd" data-ng-model="warningDetails.doi"
										is-open="startDate.opened" datepicker-options="dateOptions"
										placeholder="yyyy-MM-dd" data-ng-required="false"
										close-text="Close" /> <span class="input-group-btn">
										<button type="button" class="btn btn-default"
											data-ng-click="startDate.opened=true">
											<i class="glyphicon glyphicon-calendar"></i>
										</button>
									</span>
								</p>
							</div>
						</div>
						<div class="row row-spacing">
							<div class="col-xs-12 form-group">
								<label for="impactType" class="control-label">Impact
									Type</label> <select data-ng-if="stateDetails.viewType == 'add'"
									class="form-control" id="impactType" name="impactType"
									data-ng-model="warningDetails.impactType"
									data-ng-options="impact.name as impact.label for impact in impactTypeList"
									required="required"
									data-ng-change="updateImpactType(warningDetails.impactType)">
								</select> <span data-ng-if="stateDetails.viewType != 'add'"
									class="action-type">: {{warningDetails.impactType ==
									"BOTH" ? "BOTH (ZTZ + CRITICAL)" : warningDetails.impactType}}</span>
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.viewType == 'add'">
							<div class="col-xs-12 form-group">
								<label for="reasonType" class="control-label">Reason</label> <select
									class="form-control" id="reasonType" name="reasonType"
									data-ng-model="reasonDetail"
									data-ng-options="reason as reason.reasonName for reason in warningReasonList"
									required="required"
									data-ng-change="updateReasonlist(reasonDetail,'add')">
								</select>
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.viewType != 'add'">
							<div class="col-xs-12 form-group">
								<label for="reasonType" class="control-label">Reason
									List</label>
							</div>
						</div>
						<div class="row row-spacing" data-ng-if="reasonList.length > 0">

							<div class="card" data-ng-repeat="reason in reasonList">
								<div class="col-xs-12">
									<ul class="list-group list-group-flush">
										<li class="list-group-item">{{reason.reasonName}} <i
											class="far fa-trash-alt delete-reason" aria-hidden="true"
											data-ng-click="updateReasonlist(reason,'remove')"
											data-ng-if="stateDetails.viewType == 'add'"></i>
										</li>
									</ul>
								</div>
								<!-- 						<div class="col-xs-6" data-ng-if="$index % 2 != 0">
							<ul class="list-group list-group-flush">
								<li class="list-group-item">{{reason.reasonName}} <i
									class="fa fa-trash-o delete-reason" aria-hidden="true"
									data-ng-click="updateReasonlist(reason,'remove')"></i>
								</li>
							</ul>
						</div> -->
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.viewType == 'add'">
							<div class="col-xs-12 form-group">
								<label for="comment" class="control-label">Initiator
									Comment({{warningStatus.comment.length}}/{{commentLength}}) </label>

								<textarea style="width: 100%;" rows="5" name="comment"
									id="comment" data-ng-model="warningStatus.comment" required>
                        </textarea>
							</div>
						</div>
						<div class="row row-spacing"
							data-ng-if="stateDetails.viewType != 'add'">
							<div class="col-xs-12 form-group">
								<label class="control-label"> Initiator Name : </label> <label
									class="action-comment">{{warningStatus.authorisedPerson.name}}</label>
							</div>
							<div class="col-xs-12 form-group">
								<label class="control-label"> Initiated On : </label> <span
									class="action-comment">{{warningStatus.actionTakenOn |
									date : 'dd/MM/yyyy hh:mm:ss a'}}</span>
							</div>
							<div class="col-xs-12 form-group">
								<label class="control-label"> Initiator Comment : </label> <span
									class="action-comment">{{warningStatus.comment}}</span>
							</div>
							<div class="col-xs-12 form-group"
								data-ng-repeat="img in warningDetails.images">
								<a class="action-comment"
									data-ng-click="downloadWarningImage(img.id,img.imageName, 'view')">
									Image {{$index+1}} </a> --> <a class="action-type"
									data-ng-click="downloadWarningImage(img.id,img.imageName, 'download')">Download</a>
							</div>
						</div>
					</form>

					<div class="row" data-ng-if="stateDetails.viewType == 'add'">
						<div class="col-xs-12 form-group">
							<button class="btn btn-danger"
								data-ng-click="resetDetails(false)">Reset</button>
							<button class="btn btn-warning pull-right"
								data-ng-click="issueWarningLetter()"
								data-ng-disabled="warningLetterForm.$invalid">Issue
								Warning Letter</button>
							<!--  -->

						</div>
					</div>
				</div>
			</div>
			<div data-ng-if="uploadImage">
				<form>

					<div class="row row-spacing">
						<div class="col-xs-12 form-group">
							<label class="control-label"> Image upload (1): </label> <input
								type="file" file-model="imageDetails.files[0]" accept="image/*" />
						</div>
						<div class="col-xs-12 form-group">
							<label class="control-label"> Image upload (2): </label> <input
								type="file" file-model="imageDetails.files[1]" accept="image/*" />
						</div>
						<div class="col-xs-12 form-group">
							<label class="control-label"> Image upload (3): </label> <input
								type="file" file-model="imageDetails.files[2]" accept="image/*" />
						</div>
						<div class="col-xs-12 form-group">
							<label class="control-label"> Image upload (4): </label> <input
								type="file" file-model="imageDetails.files[3]" accept="image/*" />
						</div>
						<div class="col-xs-12 form-group">
							<label class="control-label"> Image upload (5): </label> <input
								type="file" file-model="imageDetails.files[4]" accept="image/*" />
						</div>
					</div>
					<div class="row">
						<div class="col-xs-12 form-group">
							<button class="btn btn-danger" data-ng-click="resetDetails(true)">Skip
								Uploading</button>
							<button class="btn btn-warning pull-right"
								data-ng-click="uploadImageFile()">Upload Images</button>

						</div>
					</div>
				</form>
			</div>
		</fieldset>
	</div>

	<div
		data-ng-if="stateDetails.viewType == 'edit' || (stateDetails.viewType == 'view' && warningDetails.cancelResponse == null )">
		<!-- For AM ONLY  Use -->
		<div
			data-ng-if="warningStatus.fromStatus != 'BY_AM' && (stateDetails.viewType == 'view' && warningDetails.amResponse != null) || (stateDetails.viewType == 'edit' && (warningDetails.amResponse != null || stateDetails.actionBy == 'amResponse'))">

			<div class="row row-spacing"
				data-ng-if="warningDetails.amResponse.id == null">
				<div class="col-xs-12 form-group">
					<label for="amComment" class="control-label">AM Comment
						({{warningDetails.amResponse.comment.length}}/{{commentLength}})</label>
					<textarea style="width: 100%;" rows="5" name="amComment"
						id="amComment" data-ng-model="warningDetails.amResponse.comment"
						required>
                        </textarea>
				</div>
			</div>

			<div class="row row-spacing"
				data-ng-if="warningDetails.amResponse.id != null">
				<div class="col-xs-12 form-group">
					<label class="control-label"> AM Name : </label> <label
						class="action-comment">{{warningDetails.amResponse.authorisedPerson.name}}</label>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> Action Taken On : </label> <span
						class="action-comment">{{warningDetails.amResponse.actionTakenOn
						| date : 'dd/MM/yyyy hh:mm:ss a'}}</span>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> AM Comment : </label> <span
						class="action-comment">{{warningDetails.amResponse.comment}}</span>
				</div>

			</div>

			<div class="row"
				data-ng-if="stateDetails.viewType == 'edit' && stateDetails.actionBy == 'amResponse'">
				<div class="col-xs-12 form-group">
					<button class="btn btn-primary"
						data-ng-click="warningActionDetail('ACCEPT')">Submit</button>
				</div>
			</div>
		</div>
		<div class="row row-spacing"
			data-ng-if="warningStatus.fromStatus != 'BY_AM' && stateDetails.viewType == 'view' && warningDetails.amResponse ==null">
			<div class="col-xs-12 form-group">
				<label class="control-label" style="color: red;">AM Response
					Awaited </label>
			</div>
		</div>
		<!-- For DGM Use -->
		<div
			data-ng-if="(stateDetails.viewType == 'view' && warningDetails.dgmResponse != null) || (stateDetails.viewType == 'edit' && (warningDetails.dgmResponse != null || stateDetails.actionBy == 'dgmResponse'))">

			<!-- <fieldset
			data-ng-disabled="!(stateDetails.viewType == 'edit' && stateDetails.actionBy == 'dgmResponse')"> -->
			<div class="row row-spacing"
				data-ng-if="warningDetails.dgmResponse.id == null">
				<div class="col-xs-12 form-group">
					<label for="dgmComment" class="control-label">DGM
						Comment({{warningDetails.dgmResponse.comment.length}}/{{commentLength}})
					</label>

					<textarea style="width: 100%;" rows="5" name="dgmComment"
						id="dgmComment" data-ng-model="warningDetails.dgmResponse.comment"
						required>
                        </textarea>
				</div>
			</div>
			<div class="row row-spacing"
				data-ng-if="warningDetails.dgmResponse.id != null">
				<div class="col-xs-12 form-group">
					<label class="control-label"> DGM Name : </label> <label
						class="action-comment">{{warningDetails.dgmResponse.authorisedPerson.name}}</label>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> Action Taken On : </label> <span
						class="action-comment">{{warningDetails.dgmResponse.actionTakenOn
						| date : 'dd/MM/yyyy hh:mm:ss a'}}</span>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> DGM Comment : </label> <span
						class="action-comment">{{warningDetails.dgmResponse.comment}}</span>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> DGM Action : </label> <span
						class="action-type">{{warningDetails.dgmResponse.toStatus}}</span>
				</div>
			</div>

			<div class="row"
				data-ng-if="stateDetails.viewType == 'edit' && stateDetails.actionBy == 'dgmResponse'">
				<div class="col-xs-12 form-group">
					<button class="btn btn-success pull-left"
						data-ng-click="warningActionDetail('ACCEPT')">Accept</button>
					<button class="btn btn-danger pull-right"
						data-ng-click="warningActionDetail('REJECT')">Reject</button>
				</div>
			</div>
			<!-- </fieldset> -->
		</div>
		<div class="row row-spacing"
			data-ng-if="stateDetails.viewType == 'view' && warningDetails.dgmResponse == null">
			<div class="col-xs-12 form-group">
				<label class="control-label" style="color: red;">DGM
					Response Awaited </label>
			</div>
		</div>
		<!-- For HR Use -->
		<div
			data-ng-if="(stateDetails.viewType == 'view' && warningDetails.hrResponse != null) || (stateDetails.viewType == 'edit' && (warningDetails.hrResponse != null || stateDetails.actionBy == 'hrResponse'))">

			<div class="row row-spacing"
				data-ng-if="warningDetails.hrResponse.id == null">
				<div class="col-xs-12 form-group">
					<label for="hrComment" class="control-label">HR
						Comment({{warningDetails.hrResponse.comment.length}}/{{commentLength}})
					</label>
					<textarea style="width: 100%;" rows="5" name="hrComment"
						id="hrComment" data-ng-model="warningDetails.hrResponse.comment"
						required>
                        </textarea>
				</div>
			</div>
			<div class="row row-spacing"
				data-ng-if="warningDetails.hrResponse.id != null">
				<div class="col-xs-12 form-group">
					<label class="control-label"> HR Name : </label> <label
						class="action-comment">{{warningDetails.hrResponse.authorisedPerson.name}}</label>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> Action Taken On : </label> <span
						class="action-comment">{{warningDetails.hrResponse.actionTakenOn
						| date : 'dd/MM/yyyy hh:mm:ss a'}}</span>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> HR Comment : </label> <span
						class="action-comment">{{warningDetails.hrResponse.comment}}</span>
				</div>
				<div class="col-xs-12 form-group">
					<label class="control-label"> HR Action : </label> <span
						class="action-type">{{warningDetails.hrResponse.toStatus}}</span>
				</div>
			</div>

			<div class="row" style="margin-bottom: 10px;"
				data-ng-if="stateDetails.viewType == 'edit' && stateDetails.actionBy == 'hrResponse'">
				<div class="col-xs-12 form-group">
					<button class="btn btn-success"
						data-ng-click="warningActionDetail('ACCEPT')">Accept</button>
					<button class="btn btn-danger pull-right"
						data-ng-click="warningActionDetail('REJECT')">Reject</button>
					<!-- <button class="btn btn-danger pull-right"
						data-ng-click="warningActionDetail('REOPEN')">Re Open</button> -->
				</div>
			</div>
		</div>

		<div class="row row-spacing"
			data-ng-if="stateDetails.viewType == 'view' && warningDetails.hrResponse == null">
			<div class="col-xs-12 form-group">
				<label class="control-label" style="color: red;">HR Response
					Awaited </label>
			</div>
		</div>
	</div>

	<div
		data-ng-if="stateDetails.viewType == 'cancel' || warningDetails.cancelResponse != null">
		<div class="row row-spacing"
			data-ng-if="warningDetails.cancelResponse.id == null">
			<div class="col-xs-12 form-group">
				<label for="amComment" class="control-label">Cancel Comment
					({{warningDetails.cancelResponse.comment.length}}/{{commentLength}})</label>
				<textarea style="width: 100%;" rows="5" name="cancelComment"
					id="cancelComment"
					data-ng-model="warningDetails.cancelResponse.comment" required>
                        </textarea>
			</div>
		</div>

		<div class="row row-spacing"
			data-ng-if="warningDetails.cancelResponse.id != null">
			<div class="col-xs-12 form-group">
				<label class="control-label"> Cancel Comment : </label> <span
					class="action-comment">{{warningDetails.cancelResponse.comment}}</span>
			</div>
			<div class="col-xs-12 form-group">
				<label class="control-label"> Final Status : </label> <span
					class="action-type">Warning Cancelled</span>
			</div>
		</div>

		<div class="row" data-ng-if="warningDetails.cancelResponse.id == null">
			<div class="col-xs-12 form-group">
				<button class="btn btn-primary"
					data-ng-click="warningActionDetail('ACCEPT')">Cancel</button>
			</div>
		</div>
	</div>

</div>
