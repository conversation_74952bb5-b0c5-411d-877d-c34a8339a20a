/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('adminapp').service('magicPinCatalogService', ['AppUtil', function (AppUtil) {

    var service = {};
    service.prepareCatalogs = prepareCatalogs;
    service.updateDynamicPricing = updateDynamicPricing;
    service.prepareAddonsMap = prepareAddonsMap;
    service.preparePaidAddonsMap = preparePaidAddonsMap;
    service.prepareFixedMealProductMap = prepareFixedMealProductMap;
    service.prepareTaxMap = prepareTaxMap;
    service.createProductMap = createProductMap;
    service.createDineInProductMap = createDineInProductMap;
    service.createProductNutritionMap = createProductNutritionMap;
    service.createProductMeatTypeMap = createProductMeatTypeMap;
    service.createProductAllergenTypeMap = createProductAllergenTypeMap;
    service.createProductServingInfoTypeMap=createProductServingInfoTypeMap;
    service.createProductServingSizeTypeMap=createProductServingSizeTypeMap;
    service.sortProductPrices = sortProductPrices;
    service.setMenuCategories = setMenuCategories;
    service.checkForDuplicateItemIds = checkForDuplicateItemIds;
    service.addNewCategory = addNewCategory;
    service.addNewSubCategory = addNewSubCategory;
    service.addCatalogCharges = addCatalogCharges;
    service.addCatalogTaxes = addCatalogTaxes ;
    service.addCatalogChargeIds = addCatalogChargeIds ;
    service.setMenuCatalogues = setMenuCatalogues;
    service.addNewCatalogue = addNewCatalogue;
    service.addNewCombo = addNewCombo;
    service.addHeroCombo = addHeroCombo;
    service.setSuperCombos = setSuperCombos;
    service.addSuperCombo = addSuperCombo;
    service.addSelections = addSelections;
    service.addSelectionEntities = addSelectionEntities;
    service.splitDesiChaiDimensions = splitDesiChaiDimensions;
    service.getCatalogueMapId = getCatalogueMapId;
    service.updateDesiChaiAsPerCustomProfiles = updateDesiChaiAsPerCustomProfiles;
    service.checkGstTags = checkGstTags;
    service.splitAllDesiChaiDimensions = false;
    service.errorList = [];
    service.addonsMap = {};
    service.fixedMealProductMap ={};
    service.paidAddonsMap = {};
    service.unitProductTaxCodeMap = {};
    service.taxMap = {};
    service.brand = null;
    service.metadata = null;
    service.addPackaging = null;
    service.unitId = null;
    service.unitData = null;
    service.menuSequence = null;
    service.productMap = null;
    service.productMeatTypeMap = null;
    service.productNutritionMap = null;
    service.productAllergenTypeMap=null;
    service.productServingInfoMap=null;
    service.productServingSizeMap=null;
    service.brand = null;
    service.menuRecommendationData = null;
    service.productImages = null;
    service.modifierGroups = [];
    service.modifierGroupIds = [];
    service.catalog = null;

    var magicPinMenu = {
        "outletId" : null,

        "partnerStoreId" : "",
        "menu": {
            "categories": []
        },
        "timings" : [],
        "charges" : [],
        "taxes" : [],
        "chargeIds" : [],
        "modifiers" : [],
        "callbackDetails" : {
            "url" : "",
            "data" : []
        }

    };

    var magicPinTaxes = {
        "title": null,
        "value" : null,
        "id" : null
    };

    var magicPinCharges = {
        "title" : null,
        "value" : null,
        "id" : null,
        "taxIds" : [],
        "type" : null

    };

    var magicPinCategory = {
        "id": null,
        "title": null,
        "rank": null,
        "timingIds" : [],
        "subCategories": [],
        "items" : null,
    };

    var magicPinSubcategory = {
        "id": null,
        "title": null,
        "rank": null,
        "items": []
    };

    var magicPinItem = {
        "id": null,
        "title": null,
        "imageUrl": "",
        "description" : "",
        "foodType" : null,
        "price" : null,
        "inStock" : true,
        "customizations" : [],
        "modifierIds" : [],
        "chargeIds" : [],
        "taxIds" : []

    };





    var magicPinCharge = {
        "title" : null,
        "value" : null,
        "id" : null,
        "taxIds" : [],
        "type" : null
    };

    var magicPinTiming = {
        "day" : null,
        "slots" : []
    };

    var magicPinTiming =  {
        "startTime" : null,
        "endTime" : null
    }



    var magicPinModifierGroup = {
        "id" : null,
        "title" : null,
        "minimum" : null,
        "maximum" : null,
        "addon" : null,
        "options" : [],
        "rank" : null
    };

    var magicPinMofifierOption = {
        "id" : null,
        "title" : null,
        "price" : null,
        "inStock" : null,
        "foodType" : null,
        "customizations" : null

    };

    var magicPinProperties = {
        "propertyId": null,
        "title": null,
        "rank": null,
        "propertyValues": [],
        "id": null
    };

    var magicPinOption = {
        id: null,
        title : null,
        price : null,
        inStock  : true,
        foodType : null,
        customizations : null
    };

    var magicPinCustomization =  {
        id : null,
        title : null,
        minimum : null,
        maximum : null,
        options : [],
        addon : null
    };

    var magicPinPropertyValue = { // of zomatoVariants
        "id": null,
    };



    var zomatoVariant = {
        "variantId": null,
        "rank": null,
        "id": null
    };

    var magicPinComboCatalogue = {
        "id": null,
        "title": null,
        "description": null,
        "reducedPrice": null,
        "taxGroups": [],
        "charges": [],
        "type": null, //BASIC/BOX
        "subtitle": null,
        "inStock": null,
        "media": [], //zomatoMediaObj
        "selections": [], //zomatoSelection
        "services": [], //service
    };

    var zomatoMediaObj = {
        "url": null,
        "usageType": "FOODSHOT" //FOODSHOT/COVER_IMAGE_URL
    };

    var zomatoSelections = {
        "id": null,
        "title": null,
        "maxSelections": null,
        "minSelections": null,
        "maxSelectionsPerItem": null,
        "discountValue": null,
        "selectionEntities": [] //zomatoSelectionEntities
    };

    var zomatoSelectionEntities = {
        "variantid": null,
        "catalogueid": null
    };

    var zomatoServices = {
        "service": null
    };

    var milkOption = ["Regular Milk", "Full Doodh (Full Milk)", "Doodh Kum (Less Milk)", "Paani Kum (More Milk)"];

    /*var taxGroupMap = {
        "00009963": "GST_D_P_5.00",
        "09021090": "GST_D_P_5.00",
        "19054000": "GST_D_P_5.00",
        "21069099": "GST_D_P_12.00",
        "21069040": "GST_D_P_5.00",
        "21012010": "GST_D_P_18.00",
        "09024090": "GST_D_P_5.00",
        "09109100": "GST_D_P_5.00"
    };*/

    /*var taxMap = new Map([["00009963", "D_P_2.50"],
        ["09021090", "D_P_2.50"],
        ["19054000", "D_P_2.50"],
        ["21069099", "D_P_6.00"],
        ["21069040", "D_P_2.50"],
        ["21012010", "D_P_9.00"],
        ["09024090", "D_P_2.50"],
        ["09109100", "D_P_2.50"]]);*/

    var cataloguesIds = [];
    var modifierGroupIds = [];

    function prepareAddonsMap() {
        service.addonsMap = {};
        service.unitData.products.forEach(function (prod) {
            if (prod.type === 5 && [501, 502, 503].indexOf(prod.subType) >= 0) {
                if (prod.prices.length > 0 && prod.prices[0].recipe != null && prod.prices[0].recipe.addons.length > 0) {
                    service.addonsMap[prod.id] = prod.prices[0].recipe.addons;
                }
            }
        });
    }

    function preparePaidAddonsMap() {
        service.paidAddonsMap = {};
        service.unitData.products.forEach(function (prod) {
            if (prod.prices != null && prod.prices.length > 0 && prod.prices[0].recipe != null &&
                prod.prices[0].recipe.options != null && prod.prices[0].recipe.options.length > 0) {
                prod.prices.map(function (price) {
                    var productTypePaidAddOns = [];
                    if (price.recipe != null && price.recipe.options != null) {
                        price.recipe.options.forEach(function (option) {
                            if (option.type === "PRODUCT" &&
                                productTypePaidAddOns.filter(function (paidAddon) {
                                    return paidAddon.id === option.id
                                }).length >= 0) {
                                productTypePaidAddOns.push(option);
                            }
                        });
                    }
                    if (productTypePaidAddOns.length > 0) {
                        if (service.paidAddonsMap[prod.id] == null) {
                            service.paidAddonsMap[prod.id] = {}
                        }
                        service.paidAddonsMap[prod.id][price.dimension] = productTypePaidAddOns;
                    }
                });
            }
        });
    }

    function prepareFixedMealProductMap() {
        service.fixedMealProductMap = {};
        service.unitData.products.forEach(function (prod) {
            if (prod.classification === "MENU" && prod.type === 8 && prod.subType === 3891 && prod.prices != null
                && prod.prices.length > 0 && prod.prices[0].recipe != null) {
                prod.prices.map(function (price) {
                    var key = prod.id+"_"+price.dimension;
                    var fixedMealCompositeProducts = {};
                    if (price.recipe != null && price.recipe.ingredient != null && price.recipe.ingredient.compositeProduct != null && price.recipe.ingredient.compositeProduct.details != null && price.recipe.ingredient.compositeProduct.details.length > 0) {
                        price.recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail) {
                            var fixedMealOptions = {};
                            var fixedMealCompositeProductName = compositeProductDetail.name !== null ? compositeProductDetail.name : "FixedMeal_Option";
                            compositeProductDetail.menuProducts.forEach(function (menuProductDetail) {
                                var fixedMealOption = angular.copy(menuProductDetail);
                                var fixedMealOptionKey = menuProductDetail.product.productId + "_" + menuProductDetail.dimension.name;
                                fixedMealOptions[fixedMealOptionKey] = fixedMealOption;
                            });
                            fixedMealCompositeProducts[fixedMealCompositeProductName] = angular.copy(fixedMealOptions);
                        });
                    }
                    service.fixedMealProductMap[key] = angular.copy(fixedMealCompositeProducts)
                });
            }
        });
        console.log("Fixed Meal Product Map ::::::::::::::::::::", service.fixedMealProductMap);
    }

    function prepareTaxMap() {
        service.taxMap = {};
        service.unitData.taxes.forEach(function (tax) {
            service.taxMap[tax.taxCode] = tax;
        })
    }

    function getTaxGroupId(key,value) {
        return key+"_D_P_" + parseFloat(value).toFixed(2);
    }

    function prepareCatalogs(selectedUnit, unitData, metadata, menuSequence, brand, menuRecommendationData, productImages,nonMappedProducts) {
        try {
            service.errorList = [];
            var unitId = selectedUnit.id;
            var addPackaging = selectedUnit.addPackaging;
            var splitAllDesiChaiDimensions = selectedUnit.splitAllDesiChaiDimensions;
            var miniKetliDefault = selectedUnit.miniKetliDefault;
            if (unitId == null || unitData == null || metadata == null || brand == null) {
                service.errorList.push("unit details should not be null");
                return;
            }
            if (addPackaging == null) {
                addPackaging = true;
            }
            if (splitAllDesiChaiDimensions == null || selectedUnit.clubAllDesiChaiDimensions == true) {
                splitAllDesiChaiDimensions = false;
            }
            cataloguesIds = [];
            modifierGroupIds = [];
            service.unitId = unitId;
            service.unitData = unitData;
            service.menuSequence = menuSequence;
            service.brand = brand;
            service.metadata = metadata;
            service.addPackaging = addPackaging;
            service.menuRecommendationData = menuRecommendationData;
            service.productImages = productImages;
            service.splitAllDesiChaiDimensions = splitAllDesiChaiDimensions;
            service.clubAllDesiChaiDimensions = selectedUnit.clubAllDesiChaiDimensions
            service.brand = brand;
            service.zomatoTaxMap = {};
            service.updateDynamicPricing();
            service.prepareAddonsMap();
            service.preparePaidAddonsMap();
            service.prepareFixedMealProductMap();
            service.prepareTaxMap();
            service.sortProductPrices();
            service.createProductMap();
            service.createDineInProductMap();
            service.createProductMeatTypeMap();
            service.createProductAllergenTypeMap();
            service.createProductServingInfoTypeMap();
            service.createProductServingSizeTypeMap();
            service.createProductNutritionMap();
            service.catalog = angular.copy(magicPinMenu);
            service.catalog.outletId = (unitId).toString();
            service.catalog.partnerStoreId = (unitId).toString();
            service.catalog.taxes =[];
            service.catalog.taxes=service.addCatalogTaxes(service.catalog.taxes);
            service.nonMappedProducts = nonMappedProducts;
            service.setMenuCategories(nonMappedProducts);
            service.checkForDuplicateItemIds();

            //  service.setMenuCatalogues();
            //service.setSuperCombos();
            var catHighestIndex = 0;
            var otherCat = null;
            service.menuSequence.productGroupSequences.forEach(function (cat) {
                if (catHighestIndex < cat.groupIndex) {
                    catHighestIndex = cat.groupIndex;
                }
            });
            Object.keys(service.productMap).forEach(function (key) {
                var prod = service.productMap[key];
                if (prod.selected !== true) {
                    if (prod.classification === "MENU" && prod.billType !== "ZERO_TAX" && prod.type !== 12 &&
                        [11, 12, 50, 1292, 1293, 1294].indexOf(prod.id) < 0) {
                    }
                }
            });
            if (otherCat != null && otherCat.items != null && otherCat.items.length > 0) {
                service.catalog.menu.categories.push(otherCat);
            }

            service.catalog.charges = [];
            // service.catalog.taxes=[];
            service.catalog.charges = service.addCatalogCharges(service.catalog.charges);
            // service.catalog.taxes= service.addCatalogTaxes(service.catalog.taxes);
            service.chargeIds = service.addCatalogChargeIds(service.catalog.charges);
            service.catalog.modifiers = service.modifierGroups;

            service.catalog.menu.categories.sort(customFunction);
            //service.checkGstTags();
             if (!showErrorList()) {
                 return service.catalog;
             } else {
                 console.log(service.catalog);
                 return magicPinMenu;
             }
            return service.catalog;
        } catch (e) {
            console.log(e);
            if (!showErrorList()) {
                bootbox.alert(e);
            }
            console.log(service.catalog);
            return magicPinMenu;
        }
    }

    function showErrorList() {
        if (service.errorList.length > 0) {
            var errors = "<ul>";
            service.errorList.forEach(function (err) {
                errors += "<li>" + err + "</li>"
            });
            errors += "</ul>";
            bootbox.alert(errors);
            return true;
        } else {
            return false;
        }
    }

    function customFunction(a, b) {
        return a.rank - b.rank;
    }

    function createProductMap() {
        var productMap = {};
        service.unitData.products.forEach(function (product) {
            productMap[product.id] = product;
        });
        service.productMap = productMap;
    }

    function createDineInProductMap() {
        var productMap = {};
        service.metadata.dineInMenuProfile.products.forEach(function (product) {
            productMap[product.id] = product;
        });
        service.dineInProductMap = productMap;
    }

    function createProductNutritionMap(){
        var productNutritionMap = {};
        service.metadata.productNutrition.forEach(function (nutritionData) {
            productNutritionMap[nutritionData.productId] = {
                "calorieCount": nutritionData.calorieCount,
                "proteinCount": nutritionData.proteinCount,
                "fatCount" : nutritionData.fatCount,
                "carbohydrateCount": nutritionData.carbohydrateCount,
                "fiberCount": nutritionData.fiberCount
            }
        });
        service.productNutritionMap = productNutritionMap;
    }

    function createProductMeatTypeMap() {
        var productMeatTypeMap = {};
        if (service.metadata.productMeatTags != null && service.metadata.productMeatTags.length > 0) {
            service.metadata.productMeatTags[0].mappings.forEach(function (product) {
                productMeatTypeMap[product.productId] = product.tags;
            });
        }
        service.productMeatTypeMap = productMeatTypeMap;
    }

    function createProductAllergenTypeMap() {
        var productAllergenTypeMap = {};
        if (service.metadata.allergenInfoSizeTags.partnerProductsAllergenTags != null && service.metadata.allergenInfoSizeTags.partnerProductsAllergenTags.length > 0) {
            service.metadata.allergenInfoSizeTags.partnerProductsAllergenTags[0].mappings.forEach(function (product) {
                productAllergenTypeMap[product.productId] = product.tags;
            });
        }
        service.productAllergenTypeMap = productAllergenTypeMap;
    }

    function createProductServingInfoTypeMap() {
        var productServingInfoMap = {};
        if (service.metadata.allergenInfoSizeTags.partnerProductsServingInfoTags != null && service.metadata.allergenInfoSizeTags.partnerProductsServingInfoTags.length > 0) {
            service.metadata.allergenInfoSizeTags.partnerProductsServingInfoTags[0].mappings.forEach(function (product) {
                productServingInfoMap[product.productId] = product.tags;
            });
        }
        service.productServingInfoMap = productServingInfoMap;
    }

    function createProductServingSizeTypeMap() {
        var productServingSizeMap = {};
        if (service.metadata.allergenInfoSizeTags.partnerProductsServingSizeTags != null && service.metadata.allergenInfoSizeTags.partnerProductsServingSizeTags.length > 0) {
            service.metadata.allergenInfoSizeTags.partnerProductsServingSizeTags[0].mappingsVU.forEach(function (product) {
                productServingSizeMap[product.productId] = product.tags;
            });
        }
        service.productServingSizeMap = productServingSizeMap;
    }


    function sortProductPrices() {
        service.unitData.products.forEach(function (product) {
            product.prices.sort(function (a, b) {
                return a.price - b.price;
            });
        })
    }

    function updateDynamicPricing() {
        if (service.metadata.dynamicPriceProfile != null) {
            var profile = service.metadata.dynamicPriceProfile;
            var strategy = profile.profileType;
            switch (strategy) {
                case "FLAT_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var threshold = (profile.thresholdPercentage / 100) * price.price;
                                        if(rangeVal.deltaPrice<0){
                                            threshold = -threshold;
                                        }
                                        if(Math.abs(threshold) < Math.abs(rangeVal.deltaPrice)) {
                                            price.price += threshold;
                                        } else {
                                            price.price += rangeVal.deltaPrice;
                                        }
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "RANGE_FLAT_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var threshold = (profile.thresholdPercentage / 100) * price.price;
                                        if(rangeVal.deltaPrice<0){
                                            threshold = -threshold;
                                        }
                                        if (Math.abs(threshold) < Math.abs(rangeVal.deltaPrice)) {
                                            price.price += threshold;
                                        } else {
                                            price.price += rangeVal.deltaPrice;
                                        }
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "PERCENTAGE_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var deltaValue = (rangeVal.deltaPrice / 100) * price.price;
                                        price.price += deltaValue;
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "RANGE_PERCENTAGE_AMOUNT":
                    profile.profileRangeValueDetails.forEach(function (rangeVal) {
                        service.unitData.products.forEach(function (product) {
                            if(product.taxCode !== "COMBO" && product.taxCode !== "GIFT_CARD" &&
                                product.classification === "MENU" && product.billType === "NET_PRICE") {
                                product.prices.forEach(function (price) {
                                    price.originalPrice = price.originalPrice==null ? price.price : price.originalPrice;
                                    if (price.originalPrice >= rangeVal.startPrice && price.originalPrice <= rangeVal.endPrice) {
                                        var deltaValue = (rangeVal.deltaPrice / 100) * price.price;
                                        price.price += deltaValue;
                                    }
                                });
                            }
                        });
                    });
                    break;
                case "SLASH_PRICING":
                    break;
                case "RANGE_SLASH_PRICING":
                    break;
            }

        }
    }

    function setMenuCategories() {
        service.menuSequence.productGroupSequences.sort(customFunction);
        service.menuSequence.productGroupSequences.forEach(function (cat) {
            cat.timingsApplicable = service.menuSequence.timingsApplicable;
            var category = service.addNewCategory(cat);
            if (category != null) {
                service.catalog.menu.categories.push(category);
            }
        });
        service.catalog.menu.categories.sort(customFunction);
        console.log("printing menu catalogue ids ::::::::::::::::::::::::::", cataloguesIds);
    }

    function checkForDuplicateItemIds() {
        var catalogueIdCountMap = {};
        if(cataloguesIds !== undefined && cataloguesIds !== null && cataloguesIds.length> 0)
        {
            for(var catalogueId of cataloguesIds){
                if(!catalogueIdCountMap[catalogueId]){
                    catalogueIdCountMap[catalogueId]= 1 ;
                }else{
                    catalogueIdCountMap[catalogueId]+=1;
                }
            }
        }

        var duplicateCatlogueIds =[];
        Object.entries(catalogueIdCountMap).forEach(([catalogueId, count]) => {
            if (count > 1) {
                duplicateCatlogueIds.push(catalogueId);
            }
        });
        if(duplicateCatlogueIds.length>0){
            service.errorList.push("Duplicate Item ids mapped to Menu ::::::::::::::::::::", JSON.stringify(duplicateCatlogueIds));
        }
    }

    function addNewCategory(cat) {
        var duplicateCatal = [];
        var category = angular.copy(magicPinCategory);
        category.id = (cat.groupId).toString();
        category.title = cat.groupName;
        category.rank = cat.groupIndex;
        //timing logic to change
        //  if(cat.timings !== null && cat.timings !== undefined && cat.timingsApplicable ) {
        //     cat.timings.startDate  = "";
        //     cat.timings.endDate = "";
        //     category.timings = cat.timings;
        // }
        cat.subGroups.forEach(function (subCat) {
            var subCategory = service.addNewSubCategory(subCat, duplicateCatal);
            if (subCategory != null) {
                category.subCategories.push(subCategory);
            }
        });
        category.subCategories.sort(customFunction);
        if (category.subCategories.length !== 0) {
            return category;
        } else {
            return null;
        }
    }

    function isNonMappedProduct(productId){
        if(service.nonMappedProducts!=null) {
            for(var nonMappedProduct in service.nonMappedProducts){
                if(service.nonMappedProducts[nonMappedProduct].id == productId){
                    return true;
                }
            }
        }
        return false;
    }

    function addNewSubCategory(subCat, duplicateCatal) {
        var subCategory = angular.copy(magicPinSubcategory);
        subCategory.id = (subCat.groupId).toString();
        subCategory.title = subCat.groupName;
        subCategory.rank = subCat.groupIndex;
        subCat.productSequenceList.forEach(function (prodSequence) {
            var productObj = service.productMap[prodSequence.product.id];
            if(isNonMappedProduct(prodSequence.product.id)){
                return;
            }
            if (productObj != null) {
                productObj.selected = true;
                productObj.productIndex = prodSequence.productIndex;
                service.productMap[prodSequence.product.id] = productObj;
                if (!duplicateCatal.includes(productObj.id)) {
                    if ([10, 1282].indexOf(productObj.id) >= 0 && !service.clubAllDesiChaiDimensions) {
                        splitDesiChaiProductEntry(productObj, subCategory, duplicateCatal);
                    } else {
                        // var productEntry = addProduct(productObj);
                        var productEntry = service.addNewCatalogue(productObj);
                        addCustomizationToModifierGroups(productEntry,productObj);
                        subCategory.items.push(productEntry);
                        duplicateCatal.push(productObj.id);
                        if (!cataloguesIds.includes(productObj.id)) {
                            cataloguesIds.push(productObj.id);
                        }

                        var customChaiEntries = [];
                        customChaiEntries.push(productEntry);
                        if (!cataloguesIds.includes(productObj.id)) {
                            cataloguesIds.push(productObj.id);
                        }
                        customChaiEntries.forEach(function (productEntry) {
                            updateDesiChaiAsPerCustomProfiles(productEntry,subCategory.items);
                            // addDesiChaiCustomizationProduct(productEntry, subCategory.items);
                        })
                    }
                }
            }
        });
        subCategory.items.sort(customFunction);
        if (subCategory.items.length !== 0) {
            return subCategory;
        } else {
            return null;
        }
    }

    function addCustomizationToModifierGroups(catalogueEntry,productObj){
        if(productObj.prices.length > 1){
            addModifierGroupForMultiDimensionProduct(catalogueEntry,productObj);
        }else{
            addModifierGroupsForSingleDimensionProduct(catalogueEntry,productObj);
        }
    }

    function addModifierGroupForMultiDimensionProduct(catalogueEntry,productObj){
        var isCombo = productObj.taxCode == "COMBO";
        var modifiersList = [];
        var modifierIds = [];
        var finalCustomization = [];
        catalogueEntry.customizations.forEach(function (customization){
            if(customization.title != "Size"){
                var id = isCombo ? customization.id + "_" + productObj.id : customization.id;
                customization.id = id;
                if(service.modifierGroupIds.indexOf(id) == -1){
                    service.modifierGroups.push(customization);
                    service.modifierGroupIds.push(id);
                }
                modifierIds.push(id);
                modifiersList.push(customization);
            }
        });

        catalogueEntry.customizations.forEach(function (customization){
            if(customization.title == "Size"){
                customization.options.forEach(function (option){
                    var optionModifierGroups = [];
                    option.customizations.forEach(function(customization){
                        var id = isCombo ? customization.id + "_" + productObj.id : customization.id;
                        customization.id = id;
                        if(service.modifierGroupIds.indexOf(id) == -1) {
                            service.modifierGroups.push(customization);
                            service.modifierGroupIds.push(id);
                        }
                        optionModifierGroups.push(id);
                    });
                    option.modifierIds = modifierIds;
                    option.modifierIds = option.modifierIds.concat(optionModifierGroups);
                    option.customizations = [];
                });
                finalCustomization.push(customization);
                /*    service.modifierGroups.push(customization);
                    service.modifierGroupIds.push(customization.id);*/
            }
            //modifierIds.push(customization.id);
            //modifiersList.push(customization);
        });
        catalogueEntry.customizations = finalCustomization;





    }

    function addModifierGroupsForSingleDimensionProduct(catalogueEntry,productObj){
        var isCombo = productObj.taxCode == "COMBO";
        var modifiersList = [];
        var modifierIds = [];
        var finalCustomization = [];
        catalogueEntry.customizations.forEach(function (customization){
            var id = isCombo ? customization.id + "_" + productObj.id : customization.id;
            customization.id = id;
            if(service.modifierGroupIds.indexOf(id) == -1){
                service.modifierGroups.push(customization);
                service.modifierGroupIds.push(id);
            }
            modifierIds.push(id);
            modifiersList.push(customization);
        });
        catalogueEntry.customizations = [];
        catalogueEntry.modifierIds = modifierIds;
    }

    function splitDesiChaiProductEntry(product, subCategory, duplicateCatal) {
        var customChaiEntries = [];
        if (service.splitAllDesiChaiDimensions) {
            product.prices.forEach(function (price) {
                var prod = angular.copy(product);
                prod.id = prod.id + "_" + price.dimension;
               // var productEntry = addProduct(prod);
                prod.prices = [];
                prod.prices.push(price);
                var productEntry = addNewCatalogue(prod);
                productEntry.prod = prod;
                addCustomizationToModifierGroups(productEntry,prod);

                subCategory.items.push(productEntry);
                customChaiEntries.push(productEntry);
                duplicateCatal.push(product.id);
                if (!cataloguesIds.includes(product.id)) {
                    cataloguesIds.push(product.id);
                }
            });
        } else {
            var noBkPrice = product.prices.filter(function (price) {
                return price.dimension !== "BadiKetli";
            });
            var bkPrice = product.prices.filter(function (price) {
                return price.dimension === "BadiKetli";
            });
            if (noBkPrice.length > 0) {
                var productEntry = addNewCatalogue(product);
                addCustomizationToModifierGroups(productEntry,product);
                subCategory.items.push(productEntry);
                customChaiEntries.push(productEntry);
                duplicateCatal.push(product.id);
                if (!cataloguesIds.includes(product.id)) {
                    cataloguesIds.push(product.id);
                }
            }
            if (bkPrice.length > 0) {
                var prod = angular.copy(product);
                prod.id = prod.id + "_" + bkPrice[0].dimension;
                var productEntry = addNewCatalogue(prod);
                addCustomizationToModifierGroups(productEntry,product);
                subCategory.items.push(productEntry);
                customChaiEntries.push(productEntry);
                duplicateCatal.push(product.id);
                if (!cataloguesIds.includes(product.id)) {
                    cataloguesIds.push(product.id);
                }
            }
        }
        customChaiEntries.forEach(function (productEntry) {
             addDesiChaiCustomizationProduct(productEntry, subCategory.items,productEntry.prod);
        })
    }

    function addDesiChaiCustomizationProduct(productEntry, entities,product) {
        if (productEntry.id === "10" || productEntry.id.includes("10_")) {
            updateDesiChaiAsPerCustomProfiles(productEntry,entities,product);

            /* service.metadata.desiChaiCustomProfiles.forEach(function (profile) {
                 if (profile.profileType === "PRODUCT") {
                     var newProduct = angular.copy(product);
                     var profileName = profile.profileName.replaceAll(" ", "");
                     newProduct.Id = newProduct.id + "_" + profileName;
                     entities.push(newProduct);
                 }
             });*/
        }
    }

    function addProduct(product) {
        var item = angular.copy(magicPinItem);
        item.id = product.id + "";
        item.title = product.name;
        //item.entityType = product.subType.toString() === "3676" ? "combo" : "catalogue";
        item.order = (product != null ? product.productIndex : 1);
        return item;
    }


    function addCatalogCharges(charges) {
        service.unitData.products.forEach(function (prod) {
            var chargeIds = [];
            if (service.addPackaging === true) {
                chargeIds.push(1043);
            }
            if (chargeIds.indexOf(prod.id) >= 0) {
                service.unitData.taxes.forEach(function (tax) {
                    if (["COMBO", "ZERO_TAX", "GIFT_CARD"].indexOf(tax.taxCode) < 0) {
                        var magicPinCharges = angular.copy(magicPinCharge);
                        magicPinCharges.title = 'Packaging Charges';
                        magicPinCharges.value=service.metadata.packagingValue;
                        magicPinCharges.type = service.metadata.packagingType;
                        magicPinCharges.taxIds = getTaxIds(prod);
                        magicPinCharges.id = prod.id + "_" + tax.taxCode;
                        charges.push(magicPinCharges);
                    }
                });
            }
        });
        if (service.metadata.packagingType === "FIXED") {
            var x = [];
            x.push(charges[0]);
            charges = x;
        }
        return charges;
    }

    function addCatalogTaxes(taxes) {
        var taxIds = [];
        service.unitData.products.forEach(function (prod) {
            var taxesApplicableOnProd =service.unitData.taxes.filter(function (tax) {
                return tax.taxCode === prod.taxCode;
            });
            var magicPinTaxArr =[];
            taxesApplicableOnProd.forEach(function (tax) {
                if (["COMBO", "ZERO_TAX", "GIFT_CARD"].indexOf(tax.taxCode) < 0) {
                    setMagicPinTaxDetailForProduct(tax,magicPinTaxArr,taxes, taxIds);
                }else if (tax.taxCode === "COMBO"){
                    if( prod !==null && prod!==undefined && prod.prices !=null && prod.prices != undefined && prod.prices.length > 0 &&  prod.prices[0].recipe !== null && prod.prices[0].recipe !== undefined && prod.prices[0].recipe.ingredient.compositeProduct !== null && prod.prices[0].recipe.ingredient.compositeProduct !==undefined){
                        var constituentProductId = prod.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId;
                        if(service.unitProductTaxCodeMap?.[constituentProductId] != null){
                            magicPinTaxArr=service.unitProductTaxCodeMap[constituentProductId];
                        }else{
                            setMagicPinTaxDetailForProduct(tax,magicPinTaxArr,taxes,taxIds);
                        }
                    }
                }
                if (service.unitProductTaxCodeMap !== undefined && service.unitProductTaxCodeMap !== null && !service.unitProductTaxCodeMap[prod.id]) {
                    service.unitProductTaxCodeMap[prod.id] = magicPinTaxArr;
                }
            })
        });

        console.log("printing unit product tax code map :::::;;::::::::::::::::::::::", service.unitProductTaxCodeMap);
        return taxes;
    }

    function setMagicPinTaxDetailForProduct(tax,magicPinTaxArr,taxes,taxIds){
        const taxDetailArr = Object.entries(tax.state);
        for (const [key, value] of taxDetailArr) {
            if(key !=="igst"){
                var magicPinTaxGroup = angular.copy(magicPinTaxes);
                magicPinTaxGroup.id = getTaxGroupId(key, value);
                magicPinTaxGroup.title = key;
                magicPinTaxGroup.value = value;
                if (!taxIds.includes(magicPinTaxGroup.id)) {
                    taxes.push(magicPinTaxGroup);
                    taxIds.push(magicPinTaxGroup.id);
                }
                magicPinTaxArr.push(magicPinTaxGroup);
            }
        }
    }

    function getTaxIds(product) {
        var pId = product.id != null ? product.id : product.productId;
        var taxIds = [];
        if (service.unitProductTaxCodeMap && service.unitProductTaxCodeMap[pId]) {
            taxIds = service.unitProductTaxCodeMap[pId].map(function (ele) {
                return ele.id;
            });
        }


        
        return taxIds;
    }

    function addCatalogChargeIds(charges) {
        var chargeIds = [];
        for ( var charge of charges){
            if (charge.type === 'FIXED'){
                chargeIds.push(charge.id);
            }
        }
        return chargeIds ;
    }


    function setMenuCatalogues() {
        var catalogueMap = {};
        service.unitData.products.forEach(function (product) {
            if (cataloguesIds.includes(product.id)) {
                if (product.taxCode !== "COMBO" && product.classification === "MENU") {
                    if ([10, 1282].indexOf(product.id) >= 0 && !service.clubAllDesiChaiDimensions) {
                        service.splitDesiChaiDimensions(catalogueMap, product)
                    } else {
                        var catalogEntry = service.addNewCatalogue(product);
                        service.catalog.menu.catalogues.push(catalogEntry);
                        catalogueMap[catalogEntry.vendorEntityId] = catalogEntry;
                        service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap);
                    }
                }
            }
        });
        //for normal combo items
        service.unitData.products.forEach(function (product) {
            if (cataloguesIds.includes(product.id)) {
                if (product.taxCode === "COMBO" && product.subType !== 3676 && product.subType !== 3675) {
                    service.catalog.menu.catalogues.push(service.addNewCombo(product, catalogueMap));
                }
            }
        });
        //for hero combo items
        service.unitData.products.forEach(function (product) {
            if (cataloguesIds.includes(product.id)) {
                if (product.taxCode === "COMBO" && product.subType === 3675) {
                    service.catalog.menu.catalogues.push(service.addHeroCombo(product, catalogueMap));
                }
            }
        });
    }

    function splitDesiChaiDimensions(catalogueMap, product) {
        var catalogEntry;
        if (service.splitAllDesiChaiDimensions) {
            product.prices.forEach(function (price) {
                var prod = angular.copy(product);
                prod.id = prod.id + "_" + price.dimension;
                prod.prices = [price];
                catalogEntry = service.addNewCatalogue(prod);
                service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap,prod);
                service.catalog.menu.catalogues.push(catalogEntry);
                catalogueMap[catalogEntry.id] = catalogEntry;
            });
        } else {
            var prodBkPrices = [];
            var prodPrices = [];
            product.prices.forEach(function (price) {
                if (price.dimension === "BadiKetli") {
                    prodBkPrices.push(angular.copy(price));
                } else {
                    prodPrices.push(angular.copy(price));
                }
            });
            if (prodPrices.length > 0) {
                var prod = angular.copy(product);
                prod.prices = prodPrices;
                catalogEntry = service.addNewCatalogue(prod);
                service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap);
                service.catalog.menu.catalogues.push(catalogEntry);
                catalogueMap[catalogEntry.id] = catalogEntry;
            }
            if (prodBkPrices.length > 0) {
                var prodBk = angular.copy(product);
                prodBk.id = prodBk.id + "_BadiKetli";
                prodBk.prices = prodBkPrices;
                catalogEntry = service.addNewCatalogue(prodBk);
                service.updateDesiChaiAsPerCustomProfiles(catalogEntry, catalogueMap);
                service.catalog.menu.catalogues.push(catalogEntry);
                catalogueMap[catalogEntry.id] = catalogEntry;
            }
        }
    }

    //please note that this function is called from multiple places and
    // creates catalog for items where classification != "MENU" as well
    // second param price is specifically added to take care of paid addons to get dimension
    function addNewCatalogue(product, price) {
        console.log(product.name);
        var catalogue = angular.copy(magicPinItem);
        catalogue.id = product.id + "";
        catalogue.description = getProductDescription(product.description, product.name);
        var productAlias = getProductAlias(product);
        var name = productAlias != null ? productAlias : getProductName(product.name);
        catalogue.title = name;
        if (product.id.toString().indexOf("_") >= 0) {
            catalogue.title = name + getDimensionName(product.prices[0].dimension,product.subType) +
                getDimensionDescription(product.prices[0].dimension, product.prices[0].dimensionDescriptor);
        }
        catalogue.inStock = true;
        catalogue.tags = [];
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        if(product.attribute === 'NON_VEG'){
            catalogue.foodType = 2;
        }else{
            catalogue.foodType = 1;
        }
        if (product.classification === "MENU" || product.classification === "PAID_ADDON") {
            if (service.productImages[productId] == null || service.productImages[productId].gridLow == null
                || service.productImages[productId].gridLow.url == null) {
                if (AppUtil.getEnvType() === "PROD" && product.classification === "MENU") {
                    service.errorList.push("Product image missing for: " + product.name);
                }
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[productId].gridLow.url;
            }
            if (product.billType !== "ZERO_TAX" && product.type !== 12 &&
                [11, 12, 50, 1292, 1293, 1294].indexOf(product.id) < 0 && product.taxCode !== "GIFT_CARD") {
                if(product.taxCode !== "COMBO" && product.classification === "MENU"){
                    catalogue.customizations = addNewCustomizations(product);
                }else if (product.taxCode === "COMBO" && product.subType !== 3676 && product.subType !== 3675){
                    catalogue.customizations = addComboCustomization(product);
                }
                catalogue.price = getCataloguePrice(product);
                var taxCode = product.taxCode;
                if(product.taxCode != "COMBO"){
                    catalogue.taxIds = getTaxIds(product);
                }else{
                    if(product.prices[0].recipe != null && product.prices[0].recipe.ingredient.compositeProduct !=  null ){
                        taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
                        catalogue.taxIds = getTaxIds(product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product);
                    }else{
                        taxCode = "996331";
                    }
                }
                if (service.addPackaging === true && service.metadata.packagingType !== "FIXED" &&  product.billType !=="MRP") {
                    catalogue.chargeIds.push(addCharge(taxCode));
                }
            }
        }

        return catalogue;
    }

    function  getCataloguePrice(product){
        if(product.prices.length == 1){
            return product.prices[0].price;
        }else {
            return 0;
        }
    }

    function addNewCombo(product, catalogueMap) {
        if (product.prices == null || product.prices.length === 0) {
            alert("Combo " + product.name + " price is missing.");
        } else if (product.prices[0].recipe == null) {
            alert("Combo " + product.name + " recipe is missing.");
        } else {
            var catalogue = angular.copy(zomatoCatalogues);
            catalogue.id = product.id + "";
            catalogue.name = (product.prices[0].aliasProductName ? product.prices[0].aliasProductName : getProductName(product.name));//getProductName(product.name);
            catalogue.description = getProductDescription(product.description, product.name);
            catalogue.inStock = true;
            if (service.productImages[product.id] == null || service.productImages[product.id].gridLow == null
                || service.productImages[product.id].gridLow.url == null) {
                if (AppUtil.getEnvType() == "PROD") {
                    service.errorList.push("Product image missing for: " + product.name);
                }
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[product.id].gridLow.url;
            }
            catalogue.properties = [];
            catalogue.variants = [];
            var found = false;
            //Below code does the following:
            //Finds any hot beverage if available in the combo sub items and for that item, try to find the
            //zomato catalog entry because catalog for all normal items are already prepared. From sub items catalog entry
            //pick the properties and variants matching the dimension of sub item
            product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail) {
                compositeProductDetail.menuProducts.forEach(function (menuProductDetail) {
                    if (service.productMap[menuProductDetail.product.productId] == null) {
                        service.errorList.push("combo: " + product.name + " item: " + menuProductDetail.product.name + " not found in catalog.");
                    }
                    if (service.productMap[menuProductDetail.product.productId] === undefined) {
                        console.log('Cannot find entry for product id ' + menuProductDetail.product.productId);
                    }
                    if (service.productMap[menuProductDetail.product.productId].type === 5 && !found) { //if item is hot beverage
                        found = true;
                        var sizePropid = null;
                        var exactMatch = true;
                        var catalogueMapId = service.getCatalogueMapId(product, menuProductDetail, catalogueMap);
                        catalogueMap[catalogueMapId].properties.forEach(function (prop) {
                            var property = angular.copy(prop);
                            if (prop.name === "Size") {
                                property.propertyValues = [];
                                var found = false;
                                prop.propertyValues.forEach(function (propValue) {
                                    if (propValue.id.indexOf(menuProductDetail.dimension.code) >= 0) {
                                        var val = angular.copy(propValue);
                                        property.propertyValues.push(val);
                                        sizePropid = val.id;
                                        found = true;
                                    }
                                });
                                if (!found) {
                                    service.errorList.push("Combo: " + product.name + " item: " + menuProductDetail.product.name +
                                        " dimension: " + menuProductDetail.dimension.name + " not found in catalog.")
                                }
                                property.id = property.id + "_" + product.id;
                            } else {
                                property.id = property.id + "_" + product.id;
                            }
                            property.propertyValues.forEach(function (propVal) {
                                propVal.id = propVal.id + "_" + product.id;
                            });
                            catalogue.properties.push(property);
                        });
                        if (sizePropid == null) {
                            sizePropid = menuProductDetail.dimension.code;
                            exactMatch = false;
                        }

                        catalogueMap[catalogueMapId].variants.forEach(function (variant) {
                            var propMatched = false;
                            variant.propertyValues.forEach(function (propValue) {
                                if (!propMatched && exactMatch && propValue.id === sizePropid) {
                                    propMatched = true;
                                }
                                if (!propMatched && !exactMatch && propValue.id.indexOf(sizePropid) >= 0) {
                                    propMatched = true;
                                }
                            });
                            if (!propMatched && !exactMatch && variant.id.indexOf(sizePropid) >= 0) {
                                propMatched = true;
                            }
                            if (propMatched) {
                                var variant1 = angular.copy(variant);
                                variant1.prices.forEach(function (price) {
                                    price.price = product.prices[0].price;
                                });
                                variant1.id = variant1.id + "_" + product.id;
                                var propValList = [];
                                variant1.propertyValues.forEach(function (propVal) {
                                    var val = angular.copy(propVal);
                                    val.id = val.id + "_" + product.id;
                                    propValList.push(val);
                                });
                                variant1.propertyValues = propValList;
                                dropUpsellingFromVariants(variant1);
                                catalogue.variants.push(variant1);
                            }
                        });
                    }
                });
            });
            if (catalogue.variants.length === 0) {
                catalogue.variants.push({
                    id: product.id + "_" + product.prices[0].dimension,
                    propertyValues: [],
                    prices: [{service: "delivery", price: product.prices[0].price}],
                    modifierGroups: []
                })
            }
            var taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
            // catalogue.taxGroups = [{slug: getTaxGroupId(service.taxMap[taxCode])}];
            catalogue.taxIds = getTaxIds(product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product);
            var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
            var tag = str.toString().replace("_", "-");
            catalogue.tags = [tag];
            catalogue.tags.push((product.billType === "NET_PRICE" && service.taxMap[taxCode].state.cgst == 2.5) ? "services" : "goods");
            if (service.addPackaging === true && service.metadata.packagingType !== "FIXED" && product.billType !== 'MRP') {
                catalogue.charges.push(addCharge(taxCode));
            }
            return catalogue;
        }
    }

    function getCatalogueMapId(product, menuProductDetail, catalogueMap) {
        var catalogueMapId = menuProductDetail.product.productId;
        if ([10, 1282].indexOf(menuProductDetail.product.productId) >= 0) {
            if (service.splitAllDesiChaiDimensions) {
                catalogueMapId = menuProductDetail.product.productId + "_" + menuProductDetail.dimension.code;
            } else if (menuProductDetail.dimension.code === "BadiKetli") {
                catalogueMapId = menuProductDetail.product.productId + "_" + menuProductDetail.dimension.code;
            }
            if(service.clubAllDesiChaiDimensions) {
                catalogueMapId = menuProductDetail.product.productId;
            }
        }
        if (catalogueMap[catalogueMapId] == null) {
            service.errorList.push("combo: " + product.name + " item: " + menuProductDetail.product.name +
                menuProductDetail.dimension.code + " not found in catalog.");
        }
        return catalogueMapId;
    }

    function addCharge(taxCode) {
        /*var catalogCharges = [];
        catalogCharges.push(1043 + "_" + taxCode);*/
        return 1043 + "_" + taxCode;
    }


    function addComboCustomization(product){
        var propertyGroups = [];
        var comboGroupId = 1;
        if(product.prices[0].recipe.ingredient.compositeProduct != null){
            product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail) {
                var menuProductDetail = compositeProductDetail.menuProducts[0];
                if (service.productMap[menuProductDetail.product.productId] == null) {
                    service.errorList.push("combo: " + product.name + " item: " + menuProductDetail.product.name + " not found in catalog.");
                }
                if (service.productMap[menuProductDetail.product.productId] === undefined) {
                    console.log('Cannot find entry for product id ' + menuProductDetail.product.productId);
                }
                comboGroupId = propertyGroups.length + 1;
                propertyGroups = propertyGroups.concat(addNewCustomizations(service.productMap[menuProductDetail.product.productId],
                    menuProductDetail.dimension.code,comboGroupId));

            });
        }
        if (service.menuRecommendationData != null && service.menuRecommendationData[product.id] != null) {
            // returns list of modifier groups
            propertyGroups.push(addModifierGroupForRecommendations(product,comboGroupId));
        }
        propertyGroups = removePriceFromDimensionPropertyForCombo(propertyGroups);
        return propertyGroups;
    }

    function removePriceFromDimensionPropertyForCombo(propertyGroups){
        var groups = [];
        var uniqueAddons = {};
        propertyGroups.forEach(function (group){
            if(group.title === "Size" && group.options.length > 0){
                group.options[0].price = 0;
                removePaidAddonForCombo(group.options[0]);
            }
            if(!group.title.includes("Top it up with") && uniqueAddons[group.id] == null){
                uniqueAddons[group.id] = true;
                groups.push(group);
            }
        });
        return groups;
    }

    function removePaidAddonForCombo(option){
        option.customizations = [];
    }

    function addNewCustomizations(product,dimesnionFilter,groupId) {
        var productId = product.id;
        var propertyGroups = [];
        if (product.prices == null || product.prices.length === 0) {
            alert("Price not found for " + product.name + ". Please add price first.");
        }
        if(groupId==null){
            var groupId = 1;
        }

        if(product.prices == null || product.prices.length == 0){
            return [];
        }
        if(product.prices[0].recipe == null){
            return [];
        }
        var dimensionProperties = addDimensionProperties(product,groupId,dimesnionFilter);
        if (Array.isArray(dimensionProperties) && dimensionProperties.length) {
            dimensionProperties.forEach(function (dimensionProperty) {
                propertyGroups.push(dimensionProperty);
            });
            groupId = propertyGroups.length + 1;
        }

        if ((product.classification === "MENU") && product.prices.length !== 0 && dimesnionFilter == null) {
            if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                // returns list of modifier groups
                propertyGroups.push(addModifierGroupForRecommendations(product,groupId));
            }
        }





        return propertyGroups;
    }


    function addDimensionProperties(product,groupId,dimensionFilter) {
        var groups = [];
        var property = angular.copy(magicPinCustomization);
        var productId = product.id;
        if (product.prices.length > 1) {
            property.title = "Size";
            property.rank = groupId;
            property.id = product.id + "_size";
            property.minimum = 1;
            property.maximum = 1;
            product.prices.forEach(function (price) {
                if(dimensionFilter!=null){
                    if(dimensionFilter === price.dimension){
                        property.options.push(addDimensionPropertyValues(price, product));
                    }
                }else{
                    property.options.push(addDimensionPropertyValues(price, product));
                }
            });
            property.addon  = false;
            groupId++;

            groups.push(property);
        }else{
            if ((product.classification === "MENU")&& product.prices.length !== 0) {
                /* if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                     // returns list of modifier groups
                     groups.push(addModifierGroupForRecommendations(product));
                 }*/

                if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][product.prices[0].dimension] != null) {
                    var modGroup = addPaidModifierGroupVariants(product.prices[0], product,groupId);
                    if (modGroup != null) {
                        groups.push(modGroup);
                    }
                }
            }
        }
        //for desi chai and baarish wali chai milk options merging
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        productId = productId + "";
        if (productId === "10" || productId === "1282") {
            var property = angular.copy(magicPinCustomization);
            property.title = "Milk Option";
            property.rank = groupId;
            property.id = product.id + "_milk_option";
            var index;
            property.minimum = 1;
            property.maximum = 1;
            for (index = 0; index < milkOption.length; index++) {
                property.options.push(addMilkPropertyValues(index, product));
            }
            groupId++;
            groups.push(property);
        }

        var ingredientProductProperties = addIngredientProducts(product,groupId);
        if (Array.isArray(ingredientProductProperties) && ingredientProductProperties.length) {
            ingredientProductProperties.forEach(function (ingredientProductProp) {
                groups.push(ingredientProductProp);
            });
            groupId = groups.length + 1;
        }



        groups.push(addNewModifierGroups(product.prices[0].recipe,product,groupId));






        return groups;
    }

    function addDimensionPropertyValues(price, product) {
        var propertyValues = angular.copy(magicPinOption);
        var productId = product.id;
        if (price.dimension === "MiniKetli" || price.dimension === "ChotiKetli" || price.dimension === "BadiKetli") {
            var name = price.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(price.dimension, price.dimensionDescriptor) + ")".trim();
            propertyValues.title = name;
        } else {
            propertyValues.title = (price.dimensionDescriptor ? price.dimensionDescriptor : price.dimension);
        }
        propertyValues.id = price.dimension + "_" + product.id;
        propertyValues.price = price.price;
        propertyValues.foodType = product.attribute == "NON_VEG" ? 2 : 1;

        if ((product.classification === "MENU")) {
            var variantMofifierGroups = [];
            /* if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                 // returns list of modifier groups
                 variantMofifierGroups.push(addModifierGroupForRecommendations(product));
             }*/

            if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][price.dimension] != null) {
                var modGroup = addPaidModifierGroupVariants(price, product);
                if (modGroup != null) {
                    variantMofifierGroups.push(modGroup);
                }
            }
            propertyValues.customizations = variantMofifierGroups;
        }

        //propertyValues = addOptionsDesiChai(price,product,propertyValues);
        /*if (price.recipe.dimension.desc != null && price.recipe.dimension.desc.trim().length > 0) {
            propertyValues.value += " (" + price.recipe.dimension.desc + ")";
        }*/
        //  propertyValues.includes = getDimensionId(product, price.dimension);
        return propertyValues;
    }

    function addOptionsDesiChai(recipe, product) {
        var desiChaiAddonOrderMap = AppUtil.getDesiChaiAddonsOrderForCOD();
        sortDesiChaiAddons(recipe.addons,desiChaiAddonOrderMap);
        var customizationGroup = Angular.copy(magicPinCustomization);
        customizationGroup.id = getModifierGroupId(product);
        customizationGroup.name = "Add Ons";//getModifierGroupId(product);
        customizationGroup.displayName = "Add Ons";
        customizationGroup.minimum = 0;
        var data = 0;
        for (var i = 0; i < recipe.addons.length; i++) {
            if (recipe.addons[i].product.productId == 969) {
                customizationGroup.options.push(addAddOns(recipe.addons[i], data));
                data++;
                break;
            }
        }

        for (var i = 0; i < recipe.addons.length; i++) {
            if (recipe.addons[i].product.productId != 969) {
                customizationGroup.options.push(addAddOns(recipe.addons[i], data));
                data++;
            }
        }

        customizationGroup.maximum = data;
   /*     var addOn = angular.copy(zomatoVariant);
        addOn.vendorEntityId = "999999";
        if (!cataloguesIds.includes(addOn.vendorEntityId)) {
            cataloguesIds.push(addOn.vendorEntityId);
            service.catalog.menu.catalogues.push(addNewCatalogue(createNoAddonProduct('No Addon', addOn.vendorEntityId)));
        }
        addOn.rank = data;
        customizationGroup.options.push(addOn);*/
        return customizationGroup;
    }


    function addMilkPropertyValues(index, product) {
        var option = angular.copy(magicPinOption);
        option.title = milkOption[index];
        option.id = getMilkVariantDetailId(product, milkOption[index]);
        option.price = 0;
        option.foodType = 1;
        return option;
    }

    function addIngredientProducts(product,groupId) {
        var groups = [];
        //var propertyIndex = 3;
        if (product.prices == null || product.prices.length === 0) {
            service.errorList.push("Price not found for " + product.name + ". Please add price first.")
        } else {
            if (product.prices[0].recipe != null && product.prices[0].recipe.ingredient.variants != null) {
                product.prices[0].recipe.ingredient.variants.forEach(function (ingredientVariant, index) {
                    var customization = angular.copy(magicPinCustomization);
                    customization.title = ingredientVariant.product.name;
                    if (ingredientVariant.product.displayName != null && ingredientVariant.product.displayName.trim().length > 0) {
                        customization.title = ingredientVariant.product.displayName;
                    }
                    customization.rank = groupId + index;
                    customization.addon = false;
                    groupId = groupId + index;
                    customization.id = ingredientVariant.product.name + "_" + (product.id).toString();

                    ingredientVariant.details.sort(function (a, b) {
                        return (a.defaultSetting === b.defaultSetting) ? 0 : a.defaultSetting ? -1 : 1;
                    });
                    ingredientVariant.details.forEach(function (ingredientVariantDetail, indexx) {
                        customization.options.push(addIngredientPropertyValues(ingredientVariantDetail, indexx, product));
                    });
                    customization.minimum = 1;
                    customization.maximum = 1;
                    groups.push(customization);
                });
            }
            if (product.prices[0].dimension === "None" && product.prices[0].recipe != null  &&  product.prices[0].recipe.ingredient.products.length !== 0) {
                product.prices[0].recipe.ingredient.products.forEach(function (ingredientProduct, index) {
                    var property = angular.copy(magicPinCustomization);
                    property.title= ingredientProduct.display;
                    property.rank = groupId + index;
                    groupId = groupId  + index;
                    property.id = product.id + "_" + ingredientProduct.display;
                    ingredientProduct.details.forEach(function (ingredientProductDetail) {
                        property.propertyValues.push(addIngredientProductPropertyValues(ingredientProductDetail, product));
                    });
                    groups.push(property);
                });
            }
        }
        return groups;
    }

    function addModifierGroups(product) {
        var groups = [];
        if (product.prices == null || product.prices.length === 0) {
            service.errorList.push("Price not found for " + product.name + ". Please add price first.");
        }
        if ((product.classification === "MENU") && product.prices.length !== 0) {
            if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                // returns list of modifier groups
                variantsObj.modifierGroups.push.apply(variantsObj.modifierGroups, addModifierGroupForRecommendations(product));
            }

            if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][price.dimension] != null) {
                var modGroup = addPaidModifierGroupVariants(price, product);
                if (modGroup != null) {
                    variantsObj.modifierGroups.push(modGroup);
                }
            }

            groups.push(variantsObj);

        }
        return groups;
    }

    function addIngredientProductPropertyValues(ingredientProductDetail, product) {
        var option = angular.copy(magicPinOption);
        option.title = ingredientProductDetail.product.name;
        option.id = getIngredientProductDetailId(product, ingredientProductDetail);
        option.price = 0;
        return option;
    }

    function addIngredientPropertyValues(ingredientVariantDetail, index, product) {
        var option = angular.copy(magicPinOption);
        option.title = ingredientVariantDetail.alias;
        option.id = getIngredientVariantDetailId(product, ingredientVariantDetail);
        option.price = 0;

        return option;
    }

    function addVariants(product) {
        var groups = [];
        if (product.prices == null || product.prices.length === 0) {
            service.errorList.push("Price not found for " + product.name + ". Please add price first.");
        }
        if ((product.classification === "MENU")&& product.prices.length !== 0) {
            //var propertyIdArrays = [];
            product.prices.forEach(function (price, index) {
                var variantArray = [];
                //for dimension split products like desi chai we need to check _ in product id
                var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
                if ([10, 1282].indexOf(parseInt(productId)) >= 0) {
                    var detailArray = [];
                    for (var i = 0; i < milkOption.length; i++) {
                        var id = getMilkVariantDetailId(product, milkOption[i]);
                        detailArray.push(id);
                    }
                    variantArray.push(detailArray);
                }
                if (price.recipe.ingredient.variants.length > 0) {
                    price.recipe.ingredient.variants.forEach(function (ingredientVariant) {
                        var detailArray = [];
                        ingredientVariant.details.forEach(function (variantDetail) {
                            var id = getIngredientVariantDetailId(product, variantDetail);
                            detailArray.push(id);
                        });
                        variantArray.push(detailArray);
                    });
                }
                if (price.recipe.ingredient.products.length > 0) {
                    price.recipe.ingredient.products.forEach(function (ingredientProduct) {
                        var detailArray = [];
                        ingredientProduct.details.forEach(function (productDetail) {
                            var id = getIngredientProductDetailId(product, productDetail);
                            detailArray.push(id)
                        });
                        variantArray.push(detailArray);
                    })
                }
                if (variantArray.length > 0) {
                    var result = [];
                    getCombinations(variantArray, result);
                    for (var i = 0; i < result.length; i++) {
                        var variantsObj = angular.copy(zomatoVariants);
                        variantsObj.id = product.id + "_" + price.dimension + "_" + i;
                        variantsObj.prices.push(addPrice(price.price));

                        var propValues = result[i].trim().split(" ");
                        if (product.prices.length > 1) {
                            //adding dimension variant object
                            var propertyValue = angular.copy(magicPinPropertyValue);
                            propertyValue.id = getDimensionId(product, price.dimension);
                            variantsObj.propertyValues.push(propertyValue);
                        }
                        propValues.forEach(function (val) {
                            var propertyValue = angular.copy(magicPinPropertyValue);
                            propertyValue.id = val;
                            variantsObj.propertyValues.push(propertyValue);
                        });
                        if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                            // returns list of modifier groups
                            variantsObj.modifierGroups.push.apply(variantsObj.modifierGroups, addModifierGroupForRecommendations(product));
                        }
                        if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][price.dimension] != null) {
                            var modGroup = addPaidModifierGroupVariants(price, product);
                            if (modGroup != null) {
                                variantsObj.modifierGroups.push(modGroup);
                            }
                        }
                        //adding modifier group
                        if (service.addonsMap[productId] != null) {
                            variantsObj.modifierGroups.push(addModifierGroupVariants(price.recipe, product));
                        }
                        groups.push(variantsObj);
                    }
                } else {
                    var variantsObj = angular.copy(zomatoVariants);
                    variantsObj.id = product.id + "_" + price.dimension + "_" + index;
                    variantsObj.prices.push(addPrice(price.price));
                    var fixedMealProductKey = productId+"_"+price.dimension;
                    if (product.prices.length > 1) {
                        var propertyValue = angular.copy(zomatoPropertyValue);
                        propertyValue.id = getDimensionId(product, price.dimension);
                        variantsObj.propertyValues.push(propertyValue);
                    }
                    if (service.paidAddonsMap[productId] != null && service.paidAddonsMap[productId][price.dimension] != null) {
                        var modGroup = addPaidModifierGroupVariants(price, product);
                        if (modGroup != null) {
                            variantsObj.modifierGroups.push(modGroup);
                        }
                    }
                    //adding modifier group
                    if (service.addonsMap[productId] != null) {
                        variantsObj.modifierGroups.push(addModifierGroupVariants(price.recipe, product));
                    }
                    if (service.menuRecommendationData != null && service.menuRecommendationData[productId] != null) {
                        variantsObj.modifierGroups.push.apply(variantsObj.modifierGroups, addModifierGroupForRecommendations(product));
                    }
                    if (service.fixedMealProductMap != null && service.fixedMealProductMap[fixedMealProductKey] != null) {
                        variantsObj.modifierGroups.push.apply(variantsObj.modifierGroups, addModifierGroupForFixedMeal(product));
                    }
                    groups.push(variantsObj);
                }
            });
            return groups;
        }
    }

    function addAddonVariants(product) {
        var groups = [];
        var variantsObj = angular.copy(zomatoVariants);
        variantsObj.id = product.id + "";
        variantsObj.prices.push(addPrice(0));
        groups.push(variantsObj);
        return groups;

    }

    function addPaidAddonVariants(product, priceDetail) {
        var groups = [];
        if (service.productMap[product.id] != null && service.productMap[product.id].prices.length > 0
            && service.dineInProductMap[product.id] != null && service.dineInProductMap[product.id].prices.length > 0) {
            var variantsObj = angular.copy(zomatoVariants);
            variantsObj.id = product.id + "";
            var price = 0;
            if (service.productMap[product.id].prices.length === 1) {
                price = service.productMap[product.id].prices[0].price;
            } else {
                price = service.productMap[product.id].prices.filter(function (data) {
                    return data.dimension === priceDetail.dimension
                })[0].price;
            }
            variantsObj.prices.push(addPrice(price));
            groups.push(variantsObj);
        }
        return groups;
    }


    function addModifierGroupVariants(recipe, product) {
        var modifierGroupVariants = angular.copy(magicPinModifierGroup);
        modifierGroupVariants.id = getModifierGroupId(product);
        modifierGroupVariants.rank = 3;
        if (!modifierGroupIds.includes(getModifierGroupId(product))) {
            service.catalog.menu.modifierGroups.push(addNewModifierGroups(recipe, product));
            modifierGroupIds.push(getModifierGroupId(product));
        }
        return modifierGroupVariants;
    }

    function addPaidModifierGroupVariants(price, product,groupId) {

        /* var modifierGroupVariants = angular.copy(magicPinCustomization);
         modifierGroupVariants.id = getPaidModifierGroupId(product, price.dimension);
         modifierGroupVariants.rank = 5;
         modifierGroupVariants.title = "Top it up with";
         //var vid = modifierGroupVariants.id;
         var modGroup = addNewPaidModifierGroups(price, product);
         modifierGroupVariants.options.push(modGroup);*/
        return  addNewPaidModifierGroups(price,product,groupId);
        /* if (!modifierGroupIds.includes(vid)) {
             var modGroup = addNewPaidModifierGroups(price, product);
             if (modGroup == null) {
                 return null;
             }
             service.catalog.menu.modifierGroups.push(modGroup);
             modifierGroupIds.push(vid);
             modifierGroupVariants.options.push(modGroup);
         }*/
        //return modifierGroupVariants;
    }

    function addNewModifierGroups(recipe, product,groupId) {
        var compareId = product.id + "";
        var isDesiChaiGroup = ([10, 1282].indexOf(product.id) >= 0) || compareId.includes("10_")
            || compareId.includes("1282_");
        if (isDesiChaiGroup) {
            return addNewModifierGroupsDesiChai(recipe, product,groupId);
        } else {
            return addNewModifierGroupsNonDesiChai(recipe, product,groupId)
        }
    }

    function addNewModifierGroupsNonDesiChai(recipe, product,groupId) {
        var modGroup = angular.copy(magicPinModifierGroup);
        modGroup.id = getModifierGroupId(product);
        modGroup.title = "Add Ons"; //getModifierGroupId(product);
        //modGroup.displayName = "Add Ons";
        modGroup.addon = false;
        modGroup.minimum = 0;
        var data = 0;
        recipe.addons.forEach(function (addOn, index) {
            modGroup.options.push(addAddOns(addOn, index));
            data++;
        });
        modGroup.maximum = data;
        modGroup.rank = groupId;
        groupId++;
        return modGroup;
    }


    function sortDesiChaiAddons(addons, desiChaiAddonOrderMap){
        addons.sort(function (a, b) {
            return desiChaiAddonOrderMap[a.product.productId] != null ? (
                desiChaiAddonOrderMap[b.product.productId] == null || desiChaiAddonOrderMap[a.product.productId] < desiChaiAddonOrderMap[b.product.productId] ? -1 : 1
            ) : 1
        });
    }

    function addNewModifierGroupsDesiChai(recipe, product,groupId) {
        var modGroup = angular.copy(magicPinCustomization);
        var desiChaiAddonOrderMap = AppUtil.getDesiChaiAddonsOrderForCOD();
        sortDesiChaiAddons(recipe.addons,desiChaiAddonOrderMap);
        modGroup.id = getModifierGroupId(product);
        modGroup.title = "Add Ons";//getModifierGroupId(product);
        //modGroup.displayName = "Add Ons";
        modGroup.addon = false;
        modGroup.minimum = 0;
        var data = 0;
        for (var i = 0; i < recipe.addons.length; i++) {
            if (recipe.addons[i].product.productId == 969) {
                modGroup.options.push(addAddOns(recipe.addons[i], data));
                data++;
                break;
            }
        }

        for (var i = 0; i < recipe.addons.length; i++) {
            if (recipe.addons[i].product.productId != 969) {
                modGroup.options.push(addAddOns(recipe.addons[i], data));
                data++;
            }
        }

        modGroup.maximum = data;
        /*var addOn = angular.copy(magicPinOption);
        addOn.id = "999999";
        var noAddonProduct = createNoAddonProduct('No Addon', addOn.id)
        modGroup.options.push(addAddOns(noAddonProduct, data+1));*/
//        addOn.order = data;
        modGroup.rank = groupId;
        groupId++;
        return modGroup;
    }

    function addAddOns(addOns, index) {
        var addOn = angular.copy(magicPinOption);
        addOn.id = addOns.product.productId + "";
        addOn.title = addOns.product.name;
        addOn.price = 0;
        /*  if (!cataloguesIds.includes(addOn.id)) {
              cataloguesIds.push(addOn.id);
              //  service.catalog.menu.catalogues.push(addNewCatalogue(createPaidAddonProduct(paidAddOns), price));
          }*/
        addOn.rank = index + 1;
        return addOn;
    }

    function addNewPaidModifierGroups(price, product,groupId) {
        var recipe = price.recipe;
        var modGroup = angular.copy(magicPinCustomization);
        modGroup.id = getPaidModifierGroupId(product, price.dimension);
        modGroup.title = "Top it up with";
        modGroup.displayName = "Top it up with";
        modGroup.minimum = 0;
        modGroup.addon = true;
        var data = 0;
        if (recipe.options != null) {
            recipe.options.forEach(function (paidAddOn, index) {
                if (paidAddOn.type === "PRODUCT" && service.productMap[paidAddOn.productId] != null &&
                    service.productMap[paidAddOn.productId].prices != null &&
                    service.productMap[paidAddOn.productId].prices.length > 0 &&
                    service.dineInProductMap[paidAddOn.productId] != null &&
                    service.dineInProductMap[paidAddOn.productId].prices != null &&
                    service.dineInProductMap[paidAddOn.productId].prices.length > 0 &&
                    service.productMap[paidAddOn.productId].classification === "PAID_ADDON" && !isNonMappedProduct(paidAddOn.productId)) {
                    modGroup.options.push(addPaidAddOns(paidAddOn, index, price));
                    data++;
                }
            });
        }
        modGroup.maximum = data;
        modGroup.rank = groupId;
        groupId++;
        return data > 0 ? modGroup : null;
    }

    function  getPaidAddonPrice(paidAddon , priceDetail){
        var price = 0;
        if (service.productMap[paidAddon.id].prices.length === 1) {
            price = service.productMap[paidAddon.id].prices[0].price;
        } else {
            price = service.productMap[paidAddon.id].prices.filter(function (data) {
                return data.dimension === priceDetail.dimension
            })[0].price;
        }
        return price;
    }

    function addPaidAddOns(paidAddOns, index, price) {
        var addOn = angular.copy(magicPinOption);
        addOn.id = paidAddOns.productId + "";
        addOn.title = paidAddOns.name;
        addOn.price = getPaidAddonPrice(addOn,price);
        addOn.foodType = service.productMap[addOn.id].attribute == "NON_VEG" ? 2 : 1;
        if (!cataloguesIds.includes(addOn.id)) {
            cataloguesIds.push(addOn.id);
            //  service.catalog.menu.catalogues.push(addNewCatalogue(createPaidAddonProduct(paidAddOns), price));
        }
        addOn.rank = index;
        return addOn;
    }

    function addPrice(variantPrice) {
        var prices = angular.copy(zomatoPrices);
        prices.service = "delivery";
        prices.price = variantPrice;
        return prices;
    }

    function addModifierGroupForRecommendations(product,groupId) {
        var recomModGroup = addNewRecommendationModifierGroups(product,groupId);
        return recomModGroup;
    }

    function addModifierGroupForFixedMeal(product) {

        var modifierGroupsInVariantList = [];
        //multiple modifierGroups for single product Id/id
        var recomModGroupsList = addNewFixedMealModifierGroup(product);
        recomModGroupsList.forEach(function (recomGroupForProduct) {
            var modifierGroupVariants = angular.copy(zomatoModifierGroup);
            //refers id as that of recom group
            modifierGroupVariants.id = recomGroupForProduct.id;
            modifierGroupVariants.rank = 1;

            if (!modifierGroupIds.includes(recomGroupForProduct.id)) {
                modifierGroupIds.push(recomGroupForProduct.id);
                service.catalog.menu.modifierGroups.push(recomGroupForProduct);
            }

            modifierGroupsInVariantList.push(modifierGroupVariants);
        });
        return modifierGroupsInVariantList;
    }

    function addNewFixedMealModifierGroup(product) {
        //multiple recommendation groups for a product
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        var productKey = productId + "_" + product.prices[0].dimension;
        console.log("Product id in Fixed Meal Modifer Group ::::", productKey);
        var fixedMealModifierGroupList = [];
        var fixedMeal = service.fixedMealProductMap[productKey];
        var fixedMealPrice = 0;
        var fixedMealPriceAvailable = false ;
        if(product.prices!=null && product.prices.length>0 && product.prices[0].price >0){
            fixedMealPriceAvailable= true;
            fixedMealPrice=product.prices[0].price;
        }
        Object.entries(fixedMeal).forEach(function([optionName,optionDetail], index) {
            var modGroup = angular.copy(zomatoModifierGroups);
            modGroup.id = productId + "_" + optionName;
            modGroup.title = optionName;
            modGroup.displayName = optionName;
            modGroup.minimum = 1;
            var data = 0;
            if(fixedMealPrice ===0){
                Object.entries(optionDetail).forEach(function ([item,detail] , index){
                    var product = service.productMap[detail.product.productId];
                    fixedMealPrice+=product.prices[0].price;
                    fixedMealPriceAvailable =true;
                });
            }
            Object.entries(optionDetail).forEach(function([item,detail] , index){
                var fixedMealOption = addFixedMealOptions(item, detail, product, index, fixedMealPrice, fixedMealPriceAvailable);
                if (!AppUtil.isEmptyObject(fixedMealOption)) {
                    modGroup.variants.push(fixedMealOption);
                    data++;
                }
            });
            modGroup.maximum = 1;
            if (modGroup.variants.length !== 0) {
                fixedMealModifierGroupList.push(modGroup);
            }
        });
        return fixedMealModifierGroupList;
    }

    function addNewRecommendationModifierGroups(product,groupId) {
        //multiple recommendation groups for a product
        var productId = product.id.toString().indexOf("_") >= 0 ? product.id.split("_")[0] : product.id;
        var modGroup = angular.copy(magicPinCustomization);
        modGroup.rank=groupId;
        groupId++;
        modGroup.minimum = 0;
        modGroup.addon  = true;
        var data = 0;
        service.menuRecommendationData[productId].forEach(function (recomGroup, groupIndex) {
            modGroup.id = getRecommendationModifierGroupId(product) + "_" + groupIndex;
            modGroup.title = recomGroup.recommendationTitle;

            //modGroup.displayName = recomGroup.recommendationTitle;
            recomGroup.recommendedItemsList.forEach(function (item, index) {
                if(!isNonMappedProduct(item.productId)) {
                    var upsellItem = addRecommendationItem(item, product, index);
                    if (!AppUtil.isEmptyObject(upsellItem)) {
                        modGroup.options.push(upsellItem);
                        data++;
                    }
                }
            });
        });
        modGroup.maximum = data;

        return modGroup;
    }

    function addRecommendationItem(item, product, index) {
        if (service.productMap[item.productId] == null) {
            service.errorList.push("Upselling product id: " + item.productId + " for product: " + product.name + " not found in catalog");
        } else {
            var upsellingItem = angular.copy(magicPinOption);
            var recommendationProduct = service.productMap[item.productId];
            var dimension = item.dimension != null ? item.dimension : "None";
            upsellingItem.id = item.productId + "_" + dimension + "_" + product.id + "_RECOM"; //Recommendation item
            cataloguesIds.push(upsellingItem.id);
            var createdProduct = createComboAddonProduct(recommendationProduct, item.discountAmount, upsellingItem.id, item.dimension, 1);
            if(item.discountAmount > 0) {
                createdProduct.name = createdProduct.name + " - save Rs. " + item.discountAmount;
            }
            var createdCatalog = addNewCatalogue(createdProduct);
            createdCatalog.variants = [{
                id: upsellingItem.id,
                propertyValues: [],
                prices: [{service: "delivery", price: recommendationProduct.prices[0].price - item.discountAmount}],
                modifierGroups: []
            }];
            //  service.catalog.menu.catalogues.push(createdCatalog);

            upsellingItem.title = createdProduct.name;
            upsellingItem.price = createdProduct.prices[0].price;
            upsellingItem.foodType = createdProduct.attribute == "NON_VEG" ? 2 : 1;
            upsellingItem.rank = index;
            return upsellingItem;
        }
    }

    function addFixedMealItems(item, product, index) {
        if (service.productMap[item.productId] == null) {
            service.errorList.push("Upselling product id: " + item.productId + " for product: " + product.name + " not found in catalog");
        } else {
            var upsellingItem = angular.copy(zomatoVariant);
            var recommendationProduct = service.productMap[item.productId];
            var dimension = item.dimension != null ? item.dimension : "None";
            upsellingItem.id = item.productId + "_" + dimension + "_" + product.id + "_FIXED_MEAL"; //Recommendation item
            if (!cataloguesIds.includes(upsellingItem.id)) {
                cataloguesIds.push(upsellingItem.id);
                var createdProduct = createComboAddonProduct(recommendationProduct, item.discountAmount, upsellingItem.id, item.dimension, 1);
                if(item.discountAmount > 0) {
                    createdProduct.name = createdProduct.name + " - save Rs. " + item.discountAmount;
                }
                var createdCatalog = addNewCatalogue(createdProduct);
                createdCatalog.variants = [{
                    id: upsellingItem.id,
                    propertyValues: [],
                    prices: [{service: "delivery", price: recommendationProduct.prices[0].price - item.discountAmount}],
                    modifierGroups: []
                }];
                service.catalog.menu.catalogues.push(createdCatalog);
            }
            upsellingItem.order = index;
            return upsellingItem;
        }
    }

    function addFixedMealOptions(item , detail , product, index, fixedMealPrice, fixedMealPriceAvailable) {
        var fixedMealOptionProductId = item.toString().indexOf("_") >= 0 ? item.split("_")[0] : [];
        var fixedMealOptionDimension = item.toString().indexOf("_") >= 0 ? item.split("_")[1] : "None";
        var discountAmount = product.discountAmount != null && product.discountAmount>0 ? product.discountAmount : 0 ;
        if (service.productMap[fixedMealOptionProductId] == null) {
            service.errorList.push("Fixed Meal product id: " + item.productId + " for product: " + product.name + " not found in catalog");
        } else {
            var fixedMealOption = angular.copy(zomatoVariant);
            var fixedMealProduct = service.productMap[fixedMealOptionProductId];
            fixedMealOption.id = fixedMealOptionProductId; //Recommendation item
            if (!cataloguesIds.includes(fixedMealOption.id)) {
                cataloguesIds.push(fixedMealOption.id);
                var createdProduct = createComboAddonProduct(fixedMealProduct, discountAmount, fixedMealOption.id, fixedMealOptionDimension, 1, fixedMealPriceAvailable);
                if(discountAmount > 0) {
                    createdProduct.name = createdProduct.name + " - save Rs. " + discountAmount;
                }
                var createdCatalog = addNewCatalogue(createdProduct);
                createdCatalog.variants = [{
                    id: fixedMealOption.id,
                    propertyValues: [],
                    prices: [{service: "delivery", price: 0}],
                    modifierGroups: []
                }];
                service.catalog.menu.catalogues.push(createdCatalog);
            }
            fixedMealOption.order = index;
            return fixedMealOption;
        }
    }

    function addHeroCombo(product, catalogueMap) {
        if (product.prices == null || product.prices.length === 0) {
            alert("Hero Combo " + product.name + " price is missing.");
        } else if (product.prices[0].recipe == null) {
            alert("Hero Combo " + product.name + " recipe is missing.");
        } else {
            //first sub item of combo is supposed to be the hero item and should have only 1 option
            var heroProduct = product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0];
            var heroProductId = heroProduct.product.productId;
            var heroProdDim = heroProduct.dimension.code;
            var catalogue = angular.copy(magicPinItem);
            catalogue.id = product.id + "";
            catalogue.title = (product.prices[0].aliasProductName ? product.prices[0].aliasProductName : getProductName(product.name, product.id));
            catalogue.description = getProductDescription(product.description, product.name);
            catalogue.inStock = true;
            if (service.productImages[product.id] == null || service.productImages[product.id].gridLow == null ||
                service.productImages[product.id].gridLow.url == null) {
                if (AppUtil.getEnvType() == "PROD") {
                    service.errorList.push("Product image missing for: " + product.name);
                }
            } else {
                catalogue.imageUrl = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[product.id].gridLow.url;
            }
            catalogue.properties = [];
            catalogue.variants = [];
            if (service.productMap[heroProductId] == null) {
                service.errorList.push("Hero combo: " + product.name + " item: " + heroProduct.product.name + " not found in catalog.");
            }
            var sizePropid = null;
            var exactMatch = true;
            var catalogueMapId = service.getCatalogueMapId(product, heroProduct, catalogueMap);
            catalogueMap[catalogueMapId].properties.forEach(function (prop) {
                var property = angular.copy(prop);
                if (prop.name === "Size") {
                    property.propertyValues = [];
                    var found = false;
                    prop.propertyValues.forEach(function (propValue) {
                        if (propValue.id.indexOf(heroProdDim) >= 0) {
                            var val = angular.copy(propValue);
                            property.propertyValues.push(val);
                            sizePropid = val.id;
                            found = true;
                        }
                    });
                    if (!found) {
                        service.errorList.push("Combo: " + product.name + " item: " + heroProduct.product.name + " dimension: "
                            + heroProduct.dimension.name + " not found in catalog.")
                    }
                    property.id = property.id + "_" + product.id;
                } else {
                    property.id = property.id + "_" + product.id;
                }
                property.propertyValues.forEach(function (propVal) {
                    propVal.id = propVal.id + "_" + product.id;
                });
                catalogue.properties.push(property);
            });
            if (sizePropid == null) {
                sizePropid = heroProdDim;
                exactMatch = false;
            }
            catalogueMap[catalogueMapId].variants.forEach(function (variant) {
                var propMatched = false;
                variant.propertyValues.forEach(function (propValue) {
                    if (!propMatched && exactMatch && propValue.id === sizePropid) {
                        propMatched = true;
                    }
                    if (!propMatched && !exactMatch && propValue.id.indexOf(sizePropid) >= 0) {
                        propMatched = true;
                    }
                });
                if (propMatched) {
                    var variant1 = angular.copy(variant);
                    variant1.prices.forEach(function (price) {
                        price.price = product.prices[0].price;
                    });
                    variant1.id = variant1.id + "_" + product.id;
                    var propValList = [];
                    variant1.propertyValues.forEach(function (propVal) {
                        var val = angular.copy(propVal);
                        val.id = val.id + "_" + product.id;
                        propValList.push(val);
                    });
                    variant1.propertyValues = propValList;
                    dropUpsellingFromVariants(variant1);
                    catalogue.variants.push(variant1);
                }
            });
            if (catalogue.variants.length === 0) {
                catalogue.variants.push({
                    id: product.id + "_" + product.prices[0].dimension,
                    propertyValues: [],
                    prices: [{service: "delivery", price: product.prices[0].price}],
                    modifierGroups: []
                })
            }
            //adding food and dessert addons for hero combo
            product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (compositeProductDetail, index) {
                //preparing modifier group for combo options
                if (index > 0 && index < 3) {
                    var id = index+1 ;
                    var modGroup = addComboModifierGroups(product, compositeProductDetail, "Option " + id);
                    catalogue.variants.forEach(function (variant) {
                        variant.modifierGroups.push({
                            id: modGroup.id,
                            rank: variant.modifierGroups.length + 1
                        })
                    })
                }
            });
            var taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
            // catalogue.taxGroups = [{slug: getTaxGroupId(service.taxMap[taxCode])}];
            //Todo - Archit to verify this
            catalogue.taxIds= getTaxIds(product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product);
            var str = [(product.attribute != null ? product.attribute.toLowerCase() : "veg")];
            var tag = str.toString().replace("_", "-");
            catalogue.tags = [tag];
            catalogue.tags.push((product.billType === "NET_PRICE" && service.taxMap[taxCode].state.cgst == 2.5) ? "services" : "goods");
            if (service.addPackaging === true && product.billType !== 'MRP') {
                catalogue.charges.push(addCharge(taxCode));
            }
            return catalogue;
        }
    }

    function addComboModifierGroups(product, compositeProductDetail, addonTag) {
        var modGroup = angular.copy(zomatoModifierGroups);
        modGroup.id = getHeroComboModifierGroupId(product, addonTag);
        modGroup.name = addonTag;//getHeroComboModifierGroupId(product, addonTag);
        modGroup.displayName = addonTag;
        modGroup.minimum = 1;
        modGroup.maximum = 1;
        compositeProductDetail.menuProducts.forEach(function (menuProduct, index) {
            modGroup.variants.push(addComboAddOns(menuProduct, index, product, addonTag, compositeProductDetail.discount));
        });
        if (!modifierGroupIds.includes(modGroup.id)) {
            service.catalog.menu.modifierGroups.push(modGroup);
            modifierGroupIds.push(modGroup.id);
        }
        return modGroup;
    }

    function addComboAddOns(menuProduct, index, product, addonTag, discount) {
        var addOn = angular.copy(zomatoVariant);
        addOn.id = menuProduct.product.productId + "_" + menuProduct.dimension.code + "_" +
            product.id + "_" + addonTag + "_HERO"; //Hero Combo Item
        if (!cataloguesIds.includes(addOn.id)) {
            cataloguesIds.push(addOn.id);
            if (service.productMap[menuProduct.product.productId] == null) {
                service.errorList.push("Hero Combo: " + product.name + " sub item: " + menuProduct.product.name + " is missing");
            }
            var addonProduct = createComboAddonProduct(service.productMap[menuProduct.product.productId], discount, addOn.id,
                menuProduct.dimension.code, menuProduct.quantity);
            var comboSubItemCatalog = addNewCatalogue(addonProduct);
            comboSubItemCatalog.variants = [{
                id: addOn.id,
                propertyValues: [],
                prices: [{service: "delivery", price: addonProduct.prices[0].price}],
                modifierGroups: []
            }];
            service.catalog.menu.catalogues.push(comboSubItemCatalog);
        }
        addOn.rank = index;
        return addOn;
    }


    //TODO to FIx LAter
    function setSuperCombos() {
        service.unitData.products.forEach(function (product) {
            if (cataloguesIds.includes(product.id) && product.subType === 3676) {
                service.catalog.menu.combos.push(service.addSuperCombo(product));
            }
        });
    }

    function addSuperCombo(product) {
        var comboCatalogue = angular.copy(zomatoComboCatalogue);
        comboCatalogue.id = (product.id).toString();
        comboCatalogue.name = (product.prices[0].aliasProductName ? product.prices[0].aliasProductName : getProductName(product.name, product.id));
        comboCatalogue.description = getProductDescription(product.description, product.name);
        comboCatalogue.type = "BASIC";
        comboCatalogue.subtitle = comboCatalogue.description.substring(0, 49);
        comboCatalogue.inStock = true;
        var taxCode = service.productMap[product.prices[0].recipe.ingredient.compositeProduct.details[0].menuProducts[0].product.productId].taxCode;
        if (service.addPackaging === true && product.billType !== 'MRP') {
            comboCatalogue.charges.push(addCharge(taxCode));
        }
        // comboCatalogue.taxGroups = [{slug: getTaxGroupId(service.taxMap[taxCode])}];
        comboCatalogue.taxIds = getTaxIds(product);
        comboCatalogue.selections = addSelections(product);
        comboCatalogue.services.push(addServices());
        var media = angular.copy(zomatoMediaObj);
        if (service.productImages[product.id] == null || service.productImages[product.id].gridLow == null ||
            service.productImages[product.id].gridLow.url == null) {
            if (AppUtil.getEnvType() == "PROD") {
                service.errorList.push("Product image missing for: " + product.name);
            }
        } else {
            media.url = "https://d3pjt1af33nqn0.cloudfront.net/product_image/" + service.productImages[product.id].gridLow.url;
        }
        comboCatalogue.media.push(media);
        return comboCatalogue;
    }

    function addServices() {
        var catalogService = angular.copy(zomatoServices);
        catalogService.service = "delivery";
        return catalogService;
    }

    function addSelections(product) {
        var groups = [];
        product.prices[0].recipe.ingredient.compositeProduct.details.forEach(function (detail, index) {
            var selections = angular.copy(zomatoSelections);
            selections.id = product.id + "_item_" + index;
            selections.title = detail.name;
            selections.maxSelections = 1;
            selections.minSelections = 1;
            selections.maxSelectionsPerItem = 1;
            if (detail.discount == null) {
                console.log("Super combo: " + product.name + ", discount Value is null");
                service.errorList.push("Super combo: " + product.name + ", discount Value is null");
                return;
            } else {
                selections.discountValue = detail.discount;
            }
            selections.selectionEntities = addSelectionEntities(detail, product);
            groups.push(selections);
        });
        return groups;
    }

    function addSelectionEntities(detail, product) {
        var groups = [];
        detail.menuProducts.forEach(function (item) {
            var itemId = item.product.productId;
            if (item.quantity > 1) {
                service.errorList.push("super combo " + product.name + " item: " + detail.name + "quantity is " +
                    item.quantity + " allowed quantity is one");
            }
            service.catalog.menu.catalogues.forEach(function (catalogue) {
                if (catalogue.id === itemId.toString()) {
                    catalogue.variants.forEach(function (variant) {
                        if (variant.id.indexOf(item.dimension.name)) {
                            var selectionEntity = angular.copy(zomatoSelectionEntities);
                            selectionEntity.variantid = variant.id;
                            selectionEntity.catalogueid = catalogue.id;
                            groups.push(selectionEntity);
                        }
                    });
                }
            });
        });
        return groups;
    }

    function getProductAlias(product) {
        var alias = null;
        product.prices.forEach(function (price) {
            if (price.aliasProductName != null) {
                alias = price.aliasProductName;
            }
        });
        return alias;
    }

    function getProductName(productName) {
        if (productName.indexOf("Kulhad Wali") >= 0) {
            return productName.trim().replace("Kulhad Wali", "");
        } else if (productName.indexOf("Kulhad") >= 0 && productName.indexOf("Kulhad Chai") < 0) {
            return productName.trim().replace("Kulhad", "");
        } else {
            return productName.trim();
        }
    }

    function getProductDescription(productDescription, productName) {
        // console.log(productName, productDescription);
        if (productDescription == null) {
            return productDescription;
        }
        if (productName.indexOf("Kulhad Chai") < 0 && productDescription.indexOf("served in a Kulhad") >= 0) {
            return productDescription.trim().replace("served in a Kulhad", "");
        } else {
            return productDescription.trim();
        }
    }

    function dropUpsellingFromVariants(variant) {
        var modGroups = [];
        variant.modifierGroups.forEach(function (modGroup) {
            if (modGroup.id.indexOf("_Recommendations") < 0) {
                modGroups.push(modGroup);
            }
        });
        variant.modifierGroups = modGroups;
    }

    function updateDesiChaiAsPerCustomProfiles(catalog, entries ,product) {
        if (catalog.id === "10" || catalog.id.includes("10_")) {
            service.metadata.desiChaiCustomProfiles.forEach(function (profile) {
                if (profile.profileType === "CUSTOMIZATION") {
                    setCustomizationTypeProfile(catalog, profile, service.catalog.menu);
                }
                if (profile.profileType === "PRODUCT") {
                    var catalogEntry = angular.copy(catalog);
                    //setCustomizationTypeProfile(catalogEntry, profile, service.catalog.menu);
                    catalogEntry.title = profile.productName;
                    //  if (dimensionName != null) {
                    //     catalogEntry.title = catalogEntry.title + " (" + dimensionName + ") ";
                    //  }
                    if (profile.productDescription != null) {
                        catalogEntry.description = profile.productDescription;
                    }
                    var profileName = profile.profileName.replaceAll(" ", "");
                    if (product != null && product.id.toString().indexOf("_") >= 0) {
                            catalogEntry.title = catalogEntry.title + getDimensionName(product.prices[0].dimension,product.subType) +
                                     getDimensionDescription(product.prices[0].dimension, product.prices[0].dimensionDescriptor);
                    }
                    catalogEntry.id = catalogEntry.id + "_" + profileName;
                    catalogEntry.customizations.forEach(function (prop) {
                        prop.id = prop.id + "_" + profileName;
                        prop.options.forEach(function (propVal) {
                            propVal.id = propVal.id + "_" + profileName;
                        });
                    });
                    var addonIds = [];
                    profile.addons.forEach(function (addon) {
                        addonIds.push(addon.id + "");
                    });
                    if(addonIds.length > 0) {
                       if(service.splitAllDesiChaiDimensions){
                            var updateModifiers = [];
                            catalogEntry.modifierIds.forEach(function (modifierId) {
                                if (modifierId.toLowerCase().includes("addons") && !modifierId.toLowerCase().includes("paid_addon")) {
                                    var existingModGroup = getModifierGroup(modifierId);
                                    var newModifierId = modifierId + "_" + profile.profileName;
                                    var newModGroup = angular.copy(existingModGroup);
                                    newModGroup.id = newModifierId;
                                    newModGroup.options = existingModGroup.options.filter(function (exisMod) {
                                        return addonIds.indexOf(exisMod.id) > -1;
                                    });
                                    newModGroup.minimum = newModGroup.options.length;
                                    newModGroup.maximum = newModGroup.options.length;
                                    if (service.modifierGroupIds.indexOf(newModifierId) == -1) {
                                        service.modifierGroups.push(newModGroup);
                                        service.modifierGroupIds.push(newModifierId);
                                    }
                                    updateModifiers.push(newModifierId);
                                } else {
                                    updateModifiers.push(modifierId);
                                }
                            });
                            catalogEntry.modifierIds = updateModifiers;
                        }else{
                            catalogEntry.customizations.forEach(function (prop) {
                                prop.options.forEach(function (option) {
                                    var updateModifiers = [];
                                    option.modifierIds.forEach(function (modifierId){
                                        if(modifierId.toLowerCase().includes("addons") && !modifierId.toLowerCase().includes("paid_addon")){
                                            var existingModGroup = getModifierGroup(modifierId);
                                            var newModifierId = modifierId + "_" + profile.profileName;
                                            var newModGroup = angular.copy(existingModGroup);
                                            newModGroup.id = newModifierId;
                                            newModGroup.options = existingModGroup.options.filter(function (exisMod) {
                                                return addonIds.indexOf(exisMod.id) >-1;
                                            });
                                            newModGroup.minimum = newModGroup.options.length;
                                            newModGroup.maximum = newModGroup.options.length;
                                            if(service.modifierGroupIds.indexOf(newModifierId) == -1){
                                                service.modifierGroups.push(newModGroup);
                                                service.modifierGroupIds.push(newModifierId);
                                            }
                                            updateModifiers.push(newModifierId);
                                        }else{
                                            updateModifiers.push(modifierId);
                                        }
                                    });
                                    option.modifierIds = updateModifiers;
                                });
                                if (prop.id.toLowerCase().includes("addons") && !prop.id.toLowerCase().includes("paid_addon")) {
                                    prop.options = prop.options.filter(function (addon){
                                        return addonIds.indexOf(addon.id.split("_")[0]) >=0;
                                    });
                                }
                            });
                        }

                    }



                    /*  catalogEntry.variants.forEach(function (variant) {
                          variant.id = variant.id + "_" + profileName;
                          variant.propertyValues.forEach(function (propVal) {
                              propVal.id = propVal.id + "_" + profileName;
                          });
                          if(addonIds.length > 0) {
                              variant.modifierGroups.forEach(function (modGroup) {
                                  if (modGroup.id.toLowerCase().includes("addon") && !modGroup.id.toLowerCase().includes("paid_addon")) {
                                      modGroup.id = modGroup.id + "_" + profileName;
                                  }
                              });
                          }
                      });*/
                    entries.push(catalogEntry);
                    //   service.catalog.menu.catalogues.push(catalogEntry);
                    //  catalogueMap[catalogEntry.id] = catalogEntry;
                }
            });
        }
    }

    function getModifierGroup(groupId) {
        var searchedModifierGroup = null;
        service.modifierGroups.forEach(function(mod){
            if(mod.id == groupId){
                searchedModifierGroup = mod;
                return;
            }
        });
        return searchedModifierGroup;
    }

    function setCustomizationTypeProfile(catalog, profile, menu) {
        catalog.properties.forEach(function (property) {
            var newValues;
            if (profile.dimensionType != null && property.name.toLowerCase().includes("size")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.id.toLowerCase().includes(profile.dimensionType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.id.toLowerCase().includes(profile.dimensionType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
            if (profile.milkType != null && property.name.toLowerCase().includes("milk")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.value.toLowerCase().includes(profile.milkType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.value.toLowerCase().includes(profile.milkType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
            if (profile.sugarType != null && property.name.toLowerCase().includes("sugar")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.value.toLowerCase().includes(profile.sugarType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.value.toLowerCase().includes(profile.sugarType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
            if (profile.pattiType != null && property.name.toLowerCase().includes("patti")) {
                newValues = [];
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return propValue.value.toLowerCase().includes(profile.pattiType.toLowerCase())
                }));
                newValues = newValues.concat(property.propertyValues.filter(function (propValue) {
                    return !propValue.value.toLowerCase().includes(profile.pattiType.toLowerCase())
                }));
                property.propertyValues = newValues;
            }
        });
        var addonIds = [];
        profile.addons.forEach(function (addon) {
            addonIds.push(addon.id + "");
        });
        if(profile.addons.length > 0) {
            if (profile.profileType === "CUSTOMIZATION") {
                menu.modifierGroups.forEach(function (modGroup) {
                    catalog.variants[0].modifierGroups.forEach(function (modG) {
                        if (modG.id.toLowerCase().includes("addon") && !modG.id.toLowerCase().includes("paid_addon")) {
                            if (modGroup.id === modG.id) {
                                var newAddons = [];
                                newAddons = newAddons.concat(modGroup.variants.filter(function (modVar) {
                                    return addonIds.includes(modVar.id);
                                }));
                                newAddons = newAddons.concat(modGroup.variants.filter(function (modVar) {
                                    return !addonIds.includes(modVar.id);
                                }));
                                modGroup.variants = newAddons;
                            }
                        }
                    });
                });
            }
            if (profile.profileType === "PRODUCT") {
                menu.modifierGroups.forEach(function (modGroup) {
                    catalog.variants[0].modifierGroups.forEach(function (modG) {
                        if (modG.id.toLowerCase().includes("addon") && !modG.id.toLowerCase().includes("paid_addon")) {
                            if (modGroup.id === modG.id) {
                                var newModGroup = angular.copy(modGroup);
                                newModGroup.id = newModGroup.id + "_" + profile.profileName.replaceAll(" ", "");
                                var newAddons = [];
                                newAddons = newAddons.concat(newModGroup.variants.filter(function (modVar) {
                                    return addonIds.includes(modVar.id);
                                }));
                                //  newAddons = newAddons.concat(newModGroup.variants.filter(function (modVar) {
                                //    return !addonIds.includes(modVar.id);
                                //}));
                                newModGroup.min = addonIds.length;
                                newModGroup.max = addonIds.length;
                                newModGroup.variants = newAddons;
                                menu.modifierGroups.push(newModGroup);
                            }
                        }
                    });
                });
            }
        }
    }

    function checkGstTags() {
        console.log("%cCatalogs with missing GST tags:::::\n::::::::::::::::::::::::::::::", "color:red; font-size:30px");
        service.catalog.menu.catalogues.forEach(function (catalog) {
            if(service.productMap[parseInt(catalog.id)].classification == "MENU") {
                if (catalog.tags == null || catalog.tags.length == 0) {
                    console.log(catalog.id, catalog.name);
                } else {
                    var found = false;
                    catalog.tags.forEach(function (tag) {
                        if(!found && (tag == "goods" || tag == "services")) {
                            found = true;
                        }
                    });
                    if(!found) {
                        console.log(catalog.id, catalog.name);
                    }
                }
            }
        });
        console.log("%cCatalogs with missing GST tags END:::::\n::::::::::::::::::::::::::::::", "color:red; font-size:30px");
    }

    /////////////////////////////////////////////////////////
    ///                  JARVIS                           ///
    /////////////////////////////////////////////////////////

    function getIngredientVariantDetailId(product, ingredientVariantDetail) {
        return product.id + "_" + ingredientVariantDetail.alias.replace(/[ ]/g, "_")
    }

    function getIngredientProductDetailId(product, ingredientProductDetail) {
        return product.id + "_" + ingredientProductDetail.product.name.replace(/[ ]/g, "_");
    }

    function getMilkVariantDetailId(product, milkValue) {
        return product.id + "_" + milkValue.replace(/[ ]/g, "");
    }

    function getDimensionId(product, dimension) {
        return product.id + "_" + dimension.replace(/[ ]/g, "");
    }

    function getModifierGroupId(product) {
        return product.id + "_Addons";
    }

    function getPaidModifierGroupId(product, dimension) {
        return product.id + "_" + dimension + "_Paid_Addons";
    }

    function getHeroComboModifierGroupId(product, addonTag) {
        return product.id + "_Addons_" + addonTag;
    }

    function getRecommendationModifierGroupId(product) {
        return product.id + "_Recommendations";
    }

    function getCombinations(arr, result) {
        var n = arr.length;
        var indices = [];
        for (var i = 0; i < n; i++) {
            indices[i] = 0;
        }
        while (true) {
            var resItem = "";
            for (var i = 0; i < n; i++) {
                resItem = resItem + arr[i][indices[i]] + " ";
            }
            result.push(resItem);
            var next = n - 1;
            while (next >= 0 && (indices[next] + 1 >= arr[next].length)) {
                next -= 1;
            }
            if (next < 0) {
                return;
            }
            indices[next] += 1;
            for (var i = next + 1; i < n; i++) {
                indices[i] = 0;
            }
        }
    }

    function createAddonProduct(addon) {
        var name = null;
        if (service.productMap[addon.product.productId] == null || service.productMap[addon.product.productId] == undefined) {
            name = addon.product.name;
        } else {
            name = getProductName(service.productMap[addon.product.productId].name);
        }
        return {
            id: addon.product.productId,
            name: name,
            type: addon.product.type,
            subType: addon.product.subType,
            classification: addon.product.classification,
            taxCode: "00009963",
            billType: "NET_PRICE",
            prices: [
                {
                    dimension: addon.dimension.code,
                    price: 0,
                    recipe: null
                }
            ]
        }
    }

    function createPaidAddonProduct(paidAddon) {
        var name = null;
        if (service.productMap[paidAddon.productId] == null || service.productMap[paidAddon.productId] == undefined) {
            name = paidAddon.name;
        } else {
            name = getProductName(service.productMap[paidAddon.productId].name);
        }
        var product = service.productMap[paidAddon.productId];
        product.name = name;
        product.billType = "NET_PRICE";
        return product;
    }

    function createNoAddonProduct(productName, productId) {
        return { product : {
                id: productId,
                productId : productId,
                name: productName,
                type: 12,
                subType: 1202,
                attribute:"VEG",
                classification: 'FREE_ADDON',
                taxCode: "00009963",
                billType: "NET_PRICE",
                prices: [
                    {
                        dimension: 'Regular',
                        price: 0,
                        recipe: null
                    }
                ]
            }};
    }

    function createComboAddonProduct(product, discount, id, dimension, quantity,fixedMealPriceAvailable) {
        var price = product.prices[0].price;
        if (dimension != null) {
            product.prices.forEach(function (priceObj) {
                if (priceObj.dimension === dimension) {
                    price = priceObj.price;
                }
            });
        }
        if(fixedMealPriceAvailable !==undefined && fixedMealPriceAvailable){
            price = 0 ;
        }
        if (quantity != null && quantity > 1) {
            price = price * quantity;
        }
        if (discount != null && discount > 0) {
            price = price - ((price * discount) / 100).toFixed(2);
        }
        var alias = getProductAlias(product);
        var name = alias != null ? alias : product.name;
        return {
            id: id,
            name: name + ((quantity != null && quantity > 1) ? (" x " + quantity) : ""),
            type: product.type,
            subType: product.subType,
            attribute: product.attribute,
            //classification: "PAID_ADDON",
            classification: "COMBO_ADDON",
            taxCode: product.taxCode,
            billType: product.billType,
            prices: [
                {
                    dimension: dimension != null ? dimension : product.prices[0].dimension,
                    price: price,
                    recipe: null,
                }
            ]
        }
    }
    function getDimensionName(dimension,productSubType) {
        if (dimension == null || dimension == "None" || dimension == "none" || productSubType == 901) {
            return "";
        } else {
            return " (" + dimension.replace(/([A-Z])/g, ' $1').substring(1) + ") ";
        }
    }

    function getDimensionDescription(dimension, dimensionDescriptor) {
        if (dimensionDescriptor) {
            return dimensionDescriptor;
        }
        var desc = "";
        switch (dimension) {
            case "MiniKetli":
                desc = "Serves 2, 250ml";
                break;
            case "ChotiKetli":
                desc = "Serves 4, 400ml";
                break;
            case "BadiKetli":
                desc = "Serves 10, 1000ml";
                break;
        }
        return desc;
    }

    function getPrepTime(minutes) {
        if (minutes > 0 && minutes < 5) {
            return "0to5min";
        } else if (minutes >= 5 && minutes < 10) {
            return "5to10min";
        } else if (minutes >= 10 && minutes < 15) {
            return "10to15min";
        } else if (minutes >= 15 && minutes < 20) {
            return "15to20min";
        } else if (minutes >= 20 && minutes < 25) {
            return "20to25min";
        } else if (minutes >= 25 && minutes < 30) {
            return "25to30min";
        } else if (minutes >= 30 && minutes < 35) {
            return "30to35min";
        } else if (minutes >= 35 && minutes < 40) {
            return "35to40min";
        } else if (minutes >= 40 && minutes < 45) {
            return "40to45min";
        } else if (minutes >= 45 && minutes < 50) {
            return "45to50min";
        } else if (minutes >= 50 && minutes < 55) {
            return "50to55min";
        } else if (minutes >= 55 && minutes < 60) {
            return "55to60min";
        } else {
            return "0to5min"
        }
    }

    return service;
}]);
