package com.stpl.tech.kettle.channelpartner.core.exceptions;

import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrder;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by chaayos on 17-05-2017.
 */
@ResponseStatus(value = HttpStatus.OK, reason = "Generic Channel Partner Exception")
public class ChannelPartnerException extends Exception{

    private static final long serialVersionUID = 6163156256372353453L;

    private ChannelPartnerError code;

    public ChannelPartnerException() {
    }

    public ChannelPartnerException(String message) {
        super(message);
        this.code = new ChannelPartnerError("Error in transaction", message, 702);
    }

    public ChannelPartnerException(String title, String message) {
        super(message);
        this.code = new ChannelPartnerError(title, message, 702);
    }

    public ChannelPartnerException(ChannelPartnerError code) {
        super(code.getErrorMsg());
        this.code = code;
    }

    public ChannelPartnerException(ZomatoError error) {
        super(error.getMessage());
        this.code = new ChannelPartnerError(null,"Zomato error:" + error.getMessage(), error.getCode());
    }

    public ChannelPartnerException(SwiggyError error) {
        super(error.getStatusMessage());
        this.code = new ChannelPartnerError(null,"Swiggy error:" + error.getStatusMessage(), error.getStatusCode());
    }

    public ChannelPartnerError getCode() {
        return code;
    }

    public ChannelPartnerException(Throwable cause) {
        super(cause);
    }

    public ChannelPartnerException(String message, Throwable cause) {
        super(message, cause);
    }

    public ChannelPartnerException(String message, Throwable cause, boolean enableSuppression,
                                   boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
