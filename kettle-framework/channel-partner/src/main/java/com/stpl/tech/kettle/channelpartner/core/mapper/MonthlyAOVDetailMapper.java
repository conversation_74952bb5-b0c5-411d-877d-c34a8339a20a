package com.stpl.tech.kettle.channelpartner.core.mapper;

import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MonthlyAOVDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.MonthlyAOVMetadata;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)

public interface MonthlyAOVDetailMapper {
    MonthlyAOVDetailMapper INSTANCE = Mappers.getMapper(MonthlyAOVDetailMapper.class);

    MonthlyAOVMetadata toDto(MonthlyAOVDetail monthlyAOVDetail);
    List<MonthlyAOVMetadata> toDtoList(List<MonthlyAOVDetail> monthlyAOVDetailList);
    MonthlyAOVDetail toDomain(MonthlyAOVMetadata monthlyAOVMetadata);


}
