package com.stpl.tech.kettle.channelpartner.core.notification.templates;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class OrderMismatchNotificationTemplate extends AbstractVelocityTemplate {

    private PartnerOrderDetail order;
    private String partnerOrderId;
    private String basePath;

    public OrderMismatchNotificationTemplate() {

    }

    public OrderMismatchNotificationTemplate(PartnerOrderDetail order, String partnerOrderId, String basePath) {
        this.order = order;
        this.partnerOrderId = partnerOrderId;
        this.basePath = basePath;
    }

    public PartnerOrderDetail getOrder() {
        return order;
    }

    public String getPartnerOrderId() {
        return partnerOrderId;
    }

    @Override
    public String getTemplatePath() {
        return "templates/OrderMismatchTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + order.getKettleOrderId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("order", order);
        return stringObjectMap;
    }

}
