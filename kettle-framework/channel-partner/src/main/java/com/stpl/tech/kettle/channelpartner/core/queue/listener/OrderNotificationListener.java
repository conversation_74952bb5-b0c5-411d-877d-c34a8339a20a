package com.stpl.tech.kettle.channelpartner.core.queue.listener;

import com.stpl.tech.kettle.channelpartner.core.queue.OrderNotificationQueue;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderNotification;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyPartnerSupportRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.BlockingQueue;

@Component
public class OrderNotificationListener {

    private static final Logger LOG = LoggerFactory.getLogger(OrderNotificationListener.class);

    @Autowired
    private SwiggyService swiggyService;

    @Autowired
    private ZomatoService zomatoService;

    @Scheduled(fixedRate = 1000)
    public void consumeNotificationDelayQueue() {
        BlockingQueue<OrderNotification> queue = OrderNotificationQueue.getInstance();
        while (queue.peek() != null) {
            try {
                OrderNotification object = queue.take();
                LOG.info("Consumption Time::::::" + ChannelPartnerUtils.getFormattedTime(new Date(object.getStartTime()), "dd-MM-yyyy HH:mm:ss"));
                notifyPartner(object.getPartnerName(), object);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private void notifyPartner(String partnerName, OrderNotification object) {
        Object request = object.getNotificationData();
        switch (partnerName) {
            case "SWIGGY":
                SwiggyPartnerSupportRequest swiggyPartnerSupportRequest = (SwiggyPartnerSupportRequest) request;
                LOG.info("Confirmation Swiggy order id: " + swiggyPartnerSupportRequest.getSwiggyOrderId());
                try {
                    swiggyService.confirmSwiggyOrder(swiggyPartnerSupportRequest, false);
                } catch (Exception e) {
                    LOG.error("Error confirming Swiggy order " + swiggyPartnerSupportRequest.getSwiggyOrderId() +
                            " :::::::::::::::", e);
                }
                break;
            case "ZOMATO":
                ZomatoNotificationRequest zomatoNotificationRequest = (ZomatoNotificationRequest) request;
                LOG.info("Confirmation Zomato order id: " + zomatoNotificationRequest.getOrderId());
                try {
                    zomatoService.confirmZomatoOrder((ZomatoNotificationRequest) request, false);
                } catch (Exception e) {
                    LOG.error("Error confirming Zomato order " + zomatoNotificationRequest.getOrderId() +
                            " :::::::::::::::", e);
                }
                break;
            default:
        }
    }
}
