package com.stpl.tech.kettle.channelpartner.core.queue.listener;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.amazonaws.util.Base64;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageFormatException;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.Map;

public class UnitProductStockListener implements MessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(UnitProductStockListener.class);

    private MessageProducer errorQueue;

    private RedisPublisher redisPublisher;

    private Map<Integer, PartnerDetail> partnerMap;

    public UnitProductStockListener(MessageProducer errorQueue, RedisPublisher publisher, Map<Integer, PartnerDetail> partnerMap) {
        this.errorQueue = errorQueue;
        this.redisPublisher = publisher;
        this.partnerMap = partnerMap;
    }

    @Override
    public void onMessage(Message message) {
        try {
            LOG.info("On Message " + message.getJMSMessageID());
            if (message instanceof SQSObjectMessage) {
                SQSObjectMessage object = (SQSObjectMessage) message;
                if (object.getObject() instanceof UnitProductsStockEvent) {
                    //message.acknowledge();
                    UnitProductsStockEvent event = (UnitProductsStockEvent)object.getObject();
                    LOG.info("STOCK EVENT RECEIVED OBJECT::::", new Gson().toJson(event));
                    if (processMessage(event)) {
                        message.acknowledge();
                    }
                }
            }
            if (message instanceof SQSTextMessage) {
                SQSTextMessage object = (SQSTextMessage) message;
                if (object.getText() != null) {
                    //message.acknowledge();
                    String response = object.getText();
                    UnitProductsStockEvent event = deserialize(response);
                    LOG.info("STOCK EVENT RECEIVED TEXT::::"+ new Gson().toJson(event));
                    if (processMessage(event)) {
                        message.acknowledge();
                    }
                }
            }
        } catch (JMSException e) {
            LOG.error("Error while saving the message", e);
            try {
                LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
                errorQueue.send(message);
            } catch (JMSException e1) {
                LOG.error("Error while saving the message to error queue", e);
            }
        }
    }

    private boolean processMessage(UnitProductsStockEvent event) {
        LOG.info("Got Message " + new Gson().toJson(event));
        PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
        partnerActionEvent.setEventData(event);
        partnerActionEvent.setEventType(PartnerActionEventType.UNIT_PRODUCT_STOCK);
        if(event.getPartnerId() != null){
            partnerActionEvent.getPartnerIds().add(event.getPartnerId());
        }
        if(event.getPartnerId() != null) {
            if(partnerMap.containsKey(event.getPartnerId())) {
                redisPublisher.publish(partnerMap.get(event.getPartnerId()).getPartnerName(), new Gson().toJson(partnerActionEvent));
            } else {
                LOG.error("Error in stock event listener: Partner id " + event.getPartnerId() + " does not exist.");
            }
        } else {
            partnerMap.values().forEach(partnerDetail -> {
                redisPublisher.publish(partnerDetail.getPartnerName(), new Gson().toJson(partnerActionEvent));
            });
        }
        return true;
    }

    private static UnitProductsStockEvent deserialize(String data) throws JMSException {
        if (data == null) {
            return null;
        } else {
            UnitProductsStockEvent obj;
            try {
                byte[] b = Base64.decode(data.getBytes());
                ByteArrayInputStream bi = new ByteArrayInputStream(b);
                ObjectInputStream si = new ObjectInputStream(bi);
                obj = (UnitProductsStockEvent) si.readObject();
                return obj;
            } catch (IOException ex) {
                LOG.error("IOException: cannot serialize objectMessage", ex);
                throw convertExceptionToMessageFormatException(ex);
            } catch (Exception ex) {
                LOG.error("IOException: cannot serialize objectMessage", ex);
            }
        }
        return null;
    }

    private static MessageFormatException convertExceptionToMessageFormatException(Exception e) {
        MessageFormatException ex = new MessageFormatException(e.getMessage());
        ex.initCause(e);
        return ex;
    }
}
