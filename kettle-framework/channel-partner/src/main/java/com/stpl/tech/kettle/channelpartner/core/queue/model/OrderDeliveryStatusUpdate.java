package com.stpl.tech.kettle.channelpartner.core.queue.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDeliveryStatusUpdate implements Serializable {
    Integer unitId;
    String status;
    String orderId;

    String partnerOrderId;

    Date addTime;

    String riderName;

    String riderContact;
    String partnerName;

    Date billingServerTime;

}
