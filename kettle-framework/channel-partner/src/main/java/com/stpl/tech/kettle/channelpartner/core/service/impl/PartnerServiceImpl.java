package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerService;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerCafeStatusHistoryDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerCafeStatusHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;

@Service
public class PartnerServiceImpl implements PartnerService {

    @Autowired
    private PartnerDao partnerDao;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private PartnerCafeStatusHistoryDao partnerCafeStatusHistoryDao;

    @Override
    public List<PartnerDetail> getPartners() {
        return partnerDao.findAll();
    }

    @Override
    public List<PartnerDetail> getAllPartners() {
        return partnerDao.findAll();
    }

    @Override
    public List<PartnerDetail> getActivePartners() {
        return new ArrayList<>(channelPartnerDataCache.getPartnerCache().values());
    }

    @Override
    public PartnerDetail addPartner(PartnerDetail partnerDetail) {
        if (partnerDetail.getPartnerName() != null && !partnerDetail.getPartnerName().trim().isEmpty()
            && partnerDetail.getPartnerAuthKey() != null) {
            partnerDetail = partnerDao.insert(partnerDetail);
            if (partnerDetail != null) {
                channelPartnerDataCache.clearPartnerCache();
                channelPartnerDataCache.initPartnerCache();
                return partnerDetail;
            }
        }
        return null;
    }

    @Override
    public PartnerDetail activatePartner(String partnerId) {
        if (partnerId != null) {
            Optional<PartnerDetail> partnerDetailData = partnerDao.findById(partnerId);
            if (partnerDetailData.isPresent()) {
            	PartnerDetail partnerDetail = partnerDetailData.get();
                partnerDetail .setPartnerStatus(PartnerStatus.ACTIVE);
                partnerDetail = partnerDao.save(partnerDetail);
                if (partnerDetail != null) {
                    return partnerDetail;
                }
            }
        }
        return null;
    }

    @Override
    public PartnerDetail deactivatePartner(String partnerId) {
        if (partnerId != null) {
            Optional<PartnerDetail> partnerDetailData = partnerDao.findById(partnerId);
            if (partnerDetailData.isPresent()) {
            	PartnerDetail partnerDetail =  partnerDetailData.get();
                partnerDetail.setPartnerStatus(PartnerStatus.IN_ACTIVE);
                partnerDetail = partnerDao.save(partnerDetail);
                if (partnerDetail != null) {
                    return partnerDetail;
                }
            }
        }
        return null;
    }

    @Override
    public List<PartnerCafeStatusHistory> getPartnerCafeStatusHistory(Integer brandId, Integer unitId, String partnerName) {
        return partnerCafeStatusHistoryDao.findTop15ByBrandIdAndUnitIdAndPartnerNameOrderByLastUpdatedTimeDesc(brandId,
            unitId, partnerName);
    }
}
