package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderActions;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderCacheDetail;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatusLog;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerMenuStatusDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TrackServiceImpl implements TrackService {

    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private PartnerMenuStatusDao partnerMenuStatusDao;

    @Override
    public PartnerOrderDetail trackPartnerOrder(String orderId, String partnerId, String partnerName, String partnerCode, Object order,
                                                String linkedOrderId, boolean isManual, String partnerOrderVersion) {
        Date time = ChannelPartnerUtils.getCurrentTimestamp();
        PartnerOrderDetail partnerOrderDetail = new PartnerOrderDetail();
        partnerOrderDetail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        partnerOrderDetail.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        partnerOrderDetail.setPartnerOrderId(orderId);
        partnerOrderDetail.setPartnerId(partnerId);
        partnerOrderDetail.setPartnerName(partnerName);
        partnerOrderDetail.setPartnerOrder(order);
        partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.RECEIVED);
        PartnerOrderActions partnerOrderActions = generatePartnerOrderAction(PartnerActionCode.RECEIVED, "Order received from partner.", time);
        partnerOrderDetail.getOrderActions().add(partnerOrderActions);
        partnerOrderDetail.getOrderStateLogs().add(generatePartnerOrderStatusLog(null, PartnerOrderStatus.RECEIVED, isManual, time));
        if (linkedOrderId != null) {
            partnerOrderDetail.setLinkedOrderId(linkedOrderId);
        }
        partnerOrderDetail.setExternalOrderId(ChannelPartnerUtils.generatePartnerOrderId(partnerCode));
        partnerOrderDetail.setPartnerOrderVersion(partnerOrderVersion);
        partnerOrderDetail.setBeingProcessed(true);
        partnerOrderDetail.setToBeProcessed(true);
        partnerOrderDetail.setToBeRejected(false);
        partnerOrderDetail = partnerOrderDao.save(partnerOrderDetail);
        return partnerOrderDetail;
    }

    @Override
    public void addPartnerOrderToCache(String partnerOrderId, String partnerName, Integer unitId) {
        PartnerOrderCacheDetail partnerOrderCacheDetail = new PartnerOrderCacheDetail();
        partnerOrderCacheDetail.setPartnerName(partnerName);
        partnerOrderCacheDetail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        partnerOrderCacheDetail.setPartnerOrderId(partnerOrderId);
        partnerOrderCacheDetail.setUnitId(unitId);
        channelPartnerDataCache.getPartnerOrderCache().put(partnerOrderId, partnerOrderCacheDetail);
    }

    @Override
    public List<PartnerOrderDetail> getPartnerOrderByPartnerOrderId(String orderId) {
        List<PartnerOrderDetail> details = partnerOrderDao.searchByPartnerOrderId(orderId);
        if (details == null) {
            details = new ArrayList<>();
        }
        return details;
    }

    @Override
    public PartnerOrderDetail updatePartnerOrder(PartnerOrderDetail partnerOrderDetail) {
        if (partnerOrderDetail != null) {
            partnerOrderDetail = partnerOrderDao.save(partnerOrderDetail);
            if (partnerOrderDetail != null) {
                return partnerOrderDetail;
            }
        }
        return null;
    }

    @Override
    public PartnerOrderStatusLog generatePartnerOrderStatusLog(PartnerOrderStatus from, PartnerOrderStatus to, boolean isManual, Date time) {
        if (from != null || to != null) {
            PartnerOrderStatusLog partnerOrderStatusLog = new PartnerOrderStatusLog();
            partnerOrderStatusLog.setFromStatus(from);
            partnerOrderStatusLog.setToStatus(to);
            partnerOrderStatusLog.setManual(isManual);
            if (time == null) {
                time = ChannelPartnerUtils.getCurrentTimestamp();
            }
            partnerOrderStatusLog.setUpdateTime(time);
            partnerOrderStatusLog.setUpdateTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
            return partnerOrderStatusLog;
        } else {
            //TODO throw error
            return null;
        }
    }

    @Override
    public PartnerOrderActions generatePartnerOrderAction(PartnerActionCode partnerActionCode, String actionDetail, Date time) {
        PartnerOrderActions partnerOrderActions = new PartnerOrderActions();
        partnerOrderActions.setPartnerActionCode(partnerActionCode);
        partnerOrderActions.setActionDetail(actionDetail);
        if (time == null) {
            time = ChannelPartnerUtils.getCurrentTimestamp();
        }
        partnerOrderActions.setActionTime(time);
        partnerOrderActions.setActionTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        return partnerOrderActions;
    }

    @Override
    public PartnerMenuStatus updatePartnerMenuStatus(PartnerMenuStatus partnerMenuStatus) {
        if (partnerMenuStatus != null) {
            partnerMenuStatus = partnerMenuStatusDao.insert(partnerMenuStatus);
            if (partnerMenuStatus != null) {
                return partnerMenuStatus;
            }
        }
        //TODO throw exception
        return null;


    }

    @Override
    public PartnerOrderDetail getPartnerData(String orderId) {
        return partnerOrderDao.searchEntityByPartnerOrderId(orderId);
    }
}
