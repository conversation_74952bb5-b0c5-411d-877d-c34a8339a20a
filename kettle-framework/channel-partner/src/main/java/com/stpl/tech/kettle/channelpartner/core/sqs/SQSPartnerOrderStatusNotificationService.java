package com.stpl.tech.kettle.channelpartner.core.sqs;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.channelpartner.core.queue.listener.PartnerOrderStateListener;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;

@Service
public class SQSPartnerOrderStatusNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(SQSPartnerOrderStatusNotificationService.class);

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private RedisPublisher redisPublisher;

    @PostConstruct
    public void init() throws JMSException {
        Regions region = Regions.valueOf(props.getAwsQueueRegion());
        SQSSession session = SQSNotification.getInstance().getSession(region);
        LOG.info("SUBSCRIBING QUEUE ::::::::::::::: " + props.getEnvType() + "_PARTNER_ORDER_STATUS in region " + Regions.valueOf(props.getAwsQueueRegion()).getName() );
        MessageProducer producer = SQSNotification.getInstance().getProducer(session, props.getEnvType(), "_PARTNER_ORDER_STATUS");
        PartnerOrderStateListener listener = new PartnerOrderStateListener(producer, redisPublisher);
        MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvType(), "_PARTNER_ORDER_STATUS");
        consumer.setMessageListener(listener);
        SQSNotification.getInstance().getSqsConnection(region).start();
    }

}
