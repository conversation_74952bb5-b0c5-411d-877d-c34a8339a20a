package com.stpl.tech.kettle.channelpartner.core.sqs;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.queue.listener.UnitProductStockListener;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;

@Service
public class SQSUnitProductStockNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(SQSUnitProductStockNotificationService.class);

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @PostConstruct
    public void init() throws JMSException {
        Regions region = Regions.EU_WEST_1;
        SQSSession session = SQSNotification.getInstance().getSession(region);
        LOG.info("SUBSCRIBING QUEUE ::::::::::::::: " + props.getEnvType() + "_STOCK_EVENTS.fifo in region ");
        MessageProducer producer = SQSNotification.getInstance().getProducer(session, props.getEnvType(), "_STOCK_EVENTS.fifo");
        UnitProductStockListener listener = new UnitProductStockListener(producer, redisPublisher, channelPartnerDataCache.getPartnerCacheById());
        MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvType(), "_STOCK_EVENTS.fifo");
        consumer.setMessageListener(listener);
        SQSNotification.getInstance().getSqsConnection(region).start();
    }

}
