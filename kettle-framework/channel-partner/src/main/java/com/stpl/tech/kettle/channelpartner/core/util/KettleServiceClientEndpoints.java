package com.stpl.tech.kettle.channelpartner.core.util;

/**
 * Created by <PERSON><PERSON><PERSON> on 06-10-2016.
 */
public class KettleServiceClientEndpoints {

    public static final String KETTLE_SERVICE_BASE_URL = "kettle-service/rest/v1/";

    public static final String CREATE_ORDER = KETTLE_SERVICE_BASE_URL + "order-management/order/create/web";

    public static final String CANCEL_ORDER = KETTLE_SERVICE_BASE_URL + "order-management/order/cancel";

    public static final String UNIT_PRODUCT_INVENTORY_STATUS = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit/inventory/products/web";

    public static final String UNIT_PRODUCT_INVENTORY_LIVE_STATUS = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit/inventory/products/live/web";

    public static final String PARTNER_ORDER_STATUS = KETTLE_SERVICE_BASE_URL + "order-management/partner-order-status";

    public static final String UNIT_CHANNEL_PARTNER_MAPPING = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit-channel-partner";

    public static final String UNIT_INVENTORY_WEB = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit/inventory/web";

    public static final String UNIT_INVENTORY_LIVE_WEB = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit/inventory/live/web";

    public static final String UNIT_PARTNER_MENU_MAPPING = KETTLE_SERVICE_BASE_URL + "pos-metadata/unit-partner-menu/get";

    public static final String CALCULATE_AOV = KETTLE_SERVICE_BASE_URL + "order-management/order/monthly/aov";

}
