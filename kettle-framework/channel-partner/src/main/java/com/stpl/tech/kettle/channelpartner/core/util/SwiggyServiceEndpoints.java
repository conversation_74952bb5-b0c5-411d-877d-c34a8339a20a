package com.stpl.tech.kettle.channelpartner.core.util;

public enum SwiggyServiceEndpoints {
    CALL_PARTNER_SUPPORT("https://rms.swiggy.com/external/order/contact-swiggy-support", "https://rms.swiggy.com/external/order/contact-swiggy-support"),
    CONFIRM_ORDER("https://rms.swiggy.com/external/order/confirm", "https://rms.swiggy.com/external/order/confirm"),
    ITEM_AVAILABLE("https://rms.swiggy.com/partner/itemtoggle", "https://rms.swiggy.com/v1/partner/itemtoggle"),
    UNIT_AVAILABLE("https://rms.swiggy.com/partner/toggle", "https://rms.swiggy.com/partner/toggle"),
    MARK_FOOD_READY("https://rms.swiggy.com/external/order/mark-food-ready", "https://rms.swiggy.com/external/order/mark-food-ready"),
    RIDER_TIME_TO_ARRIVE("https://rms.swiggy.com/external/order/delivery/time-to-arrive", "https://rms.swiggy.com/external/order/delivery/time-to-arrive"),
    MENU_ADD_V1("https://cloud-menu-cms.swiggy.com/v1/restaurant/{id}/full-menu", "https://cloud-menu-cms.swiggy.com/v1/restaurant/{id}/full-menu"),
    RESTAURANT_LOOKUP("https://rms.swiggy.com/external/restaurant/{id}/availability", "https://rms.swiggy.com/external/restaurant/{id}/availability"),
	ADDON_AVAILABLE("https://rms.swiggy.com/v1/partner/addon-toggle", "https://rms.swiggy.com/v1/partner/addon-toggle"),
	VARIANT_AVAILABLE("https://rms.swiggy.com/v1/partner/variant-toggle", "https://rms.swiggy.com/v1/partner/variant-toggle"),
    TRACK_MENU("https://cloud-menu-cms.swiggy.com/v1/request-tracker/{request_id}", "https://cloud-menu-cms.swiggy.com/v1/request-tracker/{request_id}"),
    MARK_OUT_OF_STOCK("https://rms.swiggy.com/external/order/v1/items/oos/","https://rms.swiggy.com/external/order/v1/items/oos/"),

    DELIVERY_TIMING_UPDATE("https://rms.swiggy.com/partner/bulk/timings","https://rms.swiggy.com/partner/bulk/timings"),
    ORDER_REJECT("https://rms.swiggy.com/external/api/v1/action","https://rms.swiggy.com/external/api/v1/action"),
    CUSTOMER_CONNECT("https://rms.swiggy.com/external/order/{order_id}/customer-masked-number","https://rms.swiggy.com/external/order/{order_id}/customer-masked-number");

    final String dev;
    final String prod;

    SwiggyServiceEndpoints(String dev, String prod){
        this.dev = dev;
        this.prod = prod;
    }

    public String getUrl(boolean isDev) {
        if(isDev){
            return this.dev;
        } else {
            return this.prod;
        }
    }
}
