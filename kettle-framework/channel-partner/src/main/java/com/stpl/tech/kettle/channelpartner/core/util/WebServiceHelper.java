package com.stpl.tech.kettle.channelpartner.core.util;

import com.google.gson.Gson;
import com.hazelcast.core.LifecycleService;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.RateLimitApi;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu.SwiggyMenuRequest;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class WebServiceHelper {

    private static final Logger LOG = LoggerFactory.getLogger(WebServiceHelper.class);
    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private EnvironmentProperties environmentProperties;

    private final Map<String,RateLimiter> apiRateLimiterMap = new HashMap<>();

    //private final RateLimiter rateLimiter = new RateLimiter(1700);

    @PostConstruct
    void postConstruct(){
        List<RateLimitApi> rateLimitApis = List.of(RateLimitApi.values());
        for(RateLimitApi api : rateLimitApis){
            RateLimiter rateLimiter = new RateLimiter(api.getRateLimit());
            apiRateLimiterMap.put(api.value(),rateLimiter);
        }
    }

    public RateLimiter getRateLimiter() {
        return apiRateLimiterMap.get(RateLimitApi.SWIGGY_ITEM_TOGGLE.value());
    }

    public <T> T postWithAuth(String endpoint, String token, Object body, Class<T> clazz) {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.set("auth-internal", token);
        requestHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return callApi(endpoint, HttpMethod.POST, clazz, body, null, requestHeaders);
    }

    public <T> T postWithAuthentication(String endpoint, String token,  Map<String, String> uriVariables, Object body, Class<T> clazz) {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.set("auth", token);
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        return callApi_(endpoint, HttpMethod.POST, clazz, body, uriVariables, requestHeaders);
    }

    public <T> T callInternalApi(String endpoint, String token, HttpMethod method, Class<T> clazz, Object body,
                                 Map<String, ?> uriVariables) {
        return exchangeWithAuth(endpoint, token, method, clazz, body, uriVariables, MediaType.APPLICATION_JSON_UTF8);
    }

    public <T> T callSwiggyApi(EnvironmentProperties props, SwiggyServiceEndpoints endpoint, HttpMethod method,
                               Object request, Class<T> clazz) {
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("api-key", props.getSwiggyAPIKey());
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());
        return exchange(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), method, clazz,
                request, null, headerVariables);

    }

    public <T> T callSwiggyApiStockAPI(EnvironmentProperties props, SwiggyServiceEndpoints endpoint, HttpMethod method,
                                       Object request, Class<T> clazz,String apiType) throws ChannelPartnerException {
        boolean enablePartnerCallBlocking = Boolean.parseBoolean(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.PARTNER_CALL_BLOCK));
        if (enablePartnerCallBlocking && apiRateLimiterMap.containsKey(apiType)) {
            if (apiRateLimiterMap.get(apiType).allowRequest()) {
                return callSwiggyApi(props,endpoint,method,request,clazz);
            } else {
                LOG.error("Swiggy Stock IN/OUT API Call Limit Exceeded");
                throw new ChannelPartnerException("RATE_LIMIT_EXCEEDED");
            }
        } else {
            return callSwiggyApi(props,endpoint,method,request,clazz);
        }
    }

    public <T> T callSwiggyApi(EnvironmentProperties props, SwiggyServiceEndpoints endpoint, HttpMethod method,
                               Object request, Class<T> clazz, Map<String, ?> uriVariables) {
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("api-key", props.getSwiggyAPIKey());
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());
        return exchange(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), method, clazz,
            request, uriVariables, headerVariables);
    }

    public <T> T callSwiggyApi(EnvironmentProperties props, SwiggyServiceEndpoints endpoint, HttpMethod method,
                               Object request, Class<T> clazz, Map<String, ?> uriVariables,
                               Map<String, String> pathVariables, Map<String, String> headerVariable) {
//        try {
        String serviceURL = resolveParamVariables(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), pathVariables);
        LOG.info("callSwiggyApi where service URL is " + serviceURL);
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("api-key", props.getSwiggyAPIKey());
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());
        if (!uriVariables.isEmpty()) {
            return exchange(serviceURL, method, clazz, request, uriVariables, headerVariables);
        }
        return exchange(serviceURL, method, clazz, request, null, headerVariables);
//        } catch (Exception ex) {
//            LOG.error("Exception Occurred while in callSwiggyApi ", ex);
//        }
//        return null;
    }

    public <T> T callSwiggyApi(EnvironmentProperties props, SwiggyServiceEndpoints endpoint, HttpMethod method,
                               Class<T> clazz, Map<String, String> pathVariables) {
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("api-key", props.getSwiggyAPIKey());
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());
        return exchange(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), method, clazz,
            pathVariables, headerVariables);
    }


    public <T> T swiggyFullMenuCreateCall(EnvironmentProperties props, SwiggyServiceEndpoints endpoint, HttpMethod method,
                                                       Class<T> clazz, SwiggyMenuRequest request, Map<String, String> pathVariables) throws IOException {


        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("api-key", props.getSwiggyAPIKey());
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());
        headerVariables.put("Authorization", "Basic "+props.getSwiggyMenuAuthorizationKey());
        headerVariables.put("tokenid", props.getSwiggyMenuTokenId());
        return exchange(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), method, clazz, request,
                pathVariables, headerVariables);



        /*SwiggyMenuResponse swiggyMenuResponse = new SwiggyMenuResponse();
        String serviceURL = resolvePathVariables(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), pathVariables);

        URL url = new URL(serviceURL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Authorization", "Basic dXNlcjpjaGVjaw==");
        connection.setRequestProperty("tokenid", "1002651b75492a31");
        connection.setDoOutput(true);

        String requestBody = new ObjectMapper().writeValueAsString(request);
        //LOG.info("Service  URL :"+ serviceURL);
        LOG.info("Request Body :" + requestBody);
        OutputStream os = null;
        InputStream inputStream = null;
        try {
            os = connection.getOutputStream();
            byte[] input = requestBody.getBytes();
            os.write(input, 0, input.length);
            int code = connection.getResponseCode();
            LOG.info("Response Code :" + code);
            LOG.info("input :" + new String(input));

            inputStream = connection.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "utf-8"));
            StringBuilder response = new StringBuilder();
            String responseLine = null;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            LOG.info("Response :" + response.toString());

            SwiggyMenuResponse sMResponse = null;
            ObjectMapper mapper = new ObjectMapper();
            sMResponse = mapper.readValue(response.toString(), SwiggyMenuResponse.class);
            if (sMResponse != null) {
                return sMResponse;
            }
        } catch (Exception ex) {
            LOG.error("Exception Occured while in swiggyFullMenuCreateCall ", ex);
            return null;
        } finally {
            if (os != null) {
                os.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
        return swiggyMenuResponse;*/
    }

    private String resolvePathVariables(String serviceURL, Map<String, String> pathVariables) {
        if (pathVariables != null && !pathVariables.isEmpty()) {
            for (Map.Entry<String, String> entry : pathVariables.entrySet()) {
                LOG.info("Parameter : " + entry.getKey() + ", Value : " + entry.getValue());
                String key = "{" + entry.getKey() + "}";
                if (serviceURL.toLowerCase().contains(key.toLowerCase())) {
                    serviceURL = serviceURL.replace(key, entry.getValue());
                }
            }
        }
        return serviceURL;
    }


    private String resolveParamVariables(String serviceURL, Map<String, String> pathVariables){
        if(pathVariables!=null && !pathVariables.isEmpty()){
            boolean flag = true;
            for (Map.Entry<String, String> entry : pathVariables.entrySet()) {
                LOG.info("Parameter : " + entry.getKey() + ", Value : " + entry.getValue());
                if (!serviceURL.toLowerCase().contains(entry.getKey()) && flag) {
                    serviceURL = serviceURL.concat("?"+entry.getKey()+"="+entry.getValue());
                    flag = false;
                }
                else if (!serviceURL.toLowerCase().contains(entry.getKey())) {
                    serviceURL = serviceURL.concat("&"+entry.getKey()+"="+entry.getValue());
                }
            }
        }

        return serviceURL;
    }


    public <T> T callZomatoApi(EnvironmentProperties props, ZomatoServiceEndpoints endpoint, HttpMethod method,
                               Object request, Class<T> clazz, Integer brandId) {
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("X-Zomato-Api-Key", props.getZomatoAPIKey(brandId));
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());
        return exchange(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), method, clazz,
            request, null, headerVariables);
    }

    public <T> T callZomatoApi(EnvironmentProperties props, ZomatoServiceEndpoints endpoint, HttpMethod method,
                               Map<String, ?> uriVariables, Class<T> clazz, Integer brandId) {
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("X-Zomato-Api-Key", props.getZomatoAPIKey(brandId));
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());
        return exchange(endpoint.getUrl(ChannelPartnerUtils.isDev(props.getEnvType())), method, clazz,
            null, uriVariables, headerVariables);
    }

    private <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, Object body,
                                   Map<String, ?> uriVariables, MediaType type) {
        if (type == null) {
            type = MediaType.APPLICATION_JSON_UTF8;
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(type));
        headers.setContentType(type);
        headers.set("auth-internal", token);
        return callApi(endpoint, method, clazz, body, uriVariables, headers);
    }

    private <T> T exchange(String endpoint, HttpMethod method, Class<T> clazz, Object body,
                           Map<String, ?> uriVariables, Map<String, ?> headerVariables) {

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headerVariables.keySet().forEach(s -> headers.set(s, headerVariables.get(s).toString()));
        return callApi(endpoint, method, clazz, body, uriVariables, headers);
    }

    private <T> T exchange(String endpoint, HttpMethod method, Class<T> clazz,
                           Map<String, String> pathVariable, Map<String, ?> headerVariables) {

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headerVariables.keySet().forEach(s -> {
            headers.set(s, headerVariables.get(s).toString());
        });
        return callApi(endpoint, method, clazz, pathVariable, headers);
    }

    private <T>T callApi_(String endpoint, HttpMethod method, Class<T> clazz, Object body,
                          Map<String, String> uriVariables, HttpHeaders headers) {
        if(method.equals(HttpMethod.PATCH)) {
            return callPatchApi(endpoint, method, clazz, body, uriVariables, headers);
        }
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity entity;
        if (body != null) {
            entity = new HttpEntity(body, headers);

        } else {
            entity = new HttpEntity<String>(headers);
        }
        endpoint = resolveParamVariables(endpoint, uriVariables);
        endpoint  =  endpoint.replaceAll("\\s+","+");
        URI uri = null;
        try {
            uri = new URI(endpoint);
        } catch (URISyntaxException e) {
            LOG.error("Error in URL::::",e);
            e.printStackTrace();
            return null;
        }
        LOG.info("URL::::::" + new Gson().toJson(uri));
        LOG.info("METHOD::::::" + method);
        LOG.info("HEADERS::::::" + new Gson().toJson(headers));
        LOG.info("BODY::::::" + new Gson().toJson(body));
        return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
    }

    private <T> T callApi(String endpoint, HttpMethod method, Class<T> clazz, Object body,
                          Map<String, ?> uriVariables, HttpHeaders headers) {
        if(method.equals(HttpMethod.PATCH)) {
            return callPatchApi(endpoint, method, clazz, body, uriVariables, headers);
        }
        HttpEntity entity;
        if (body != null) {
            entity = new HttpEntity(body, headers);

        } else {
            entity = new HttpEntity<String>(headers);
        }
        endpoint = setUriVariables(endpoint, uriVariables);
        URI uri = null;
        try {
            uri = new URI(endpoint);
        } catch (URISyntaxException e) {
            LOG.error("Error in URL::::",e);
            e.printStackTrace();
            return null;
        }
        LOG.info("URL::::::" + new Gson().toJson(uri));
        LOG.info("METHOD::::::" + method);
        LOG.info("HEADERS::::::" + new Gson().toJson(headers));
        LOG.info("BODY::::::" + new Gson().toJson(body));
        return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
    }

    private <T> T callPatchApi(String endpoint, HttpMethod method, Class<T> clazz, Object body,
                          Map<String, ?> uriVariables, HttpHeaders headers) {
        RestTemplate restTemplate = new RestTemplate();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(1000);
        requestFactory.setReadTimeout(1000);
        restTemplate.setRequestFactory(requestFactory);

        HttpEntity entity;
        if (body != null) {
            entity = new HttpEntity(body, headers);
        } else {
            entity = new HttpEntity<String>(headers);
        }
        endpoint = setUriVariables(endpoint, uriVariables);
        URI uri = null;
        try {
            uri = new URI(endpoint);
        } catch (URISyntaxException e) {
            LOG.error("Error in URL::::",e);
            e.printStackTrace();
            return null;
        }
        LOG.info("URL::::::" + new Gson().toJson(uri));
        LOG.info("METHOD::::::" + method);
        LOG.info("HEADERS::::::" + new Gson().toJson(headers));
        LOG.info("BODY::::::" + new Gson().toJson(body));
        return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
    }

    private String setUriVariables(String endpoint, Map<String, ?> uriVariables) {
        if (uriVariables != null) {
            for (Map.Entry<String, ?> entry : uriVariables.entrySet()) {
                LOG.info("Parameter : " + entry.getKey() + ", Value : " + entry.getValue());
                String key = "{" + entry.getKey() + "}";
                if (endpoint.toLowerCase().contains(key.toLowerCase())) {
                	endpoint = endpoint.replace(key, (CharSequence) entry.getValue());
                }
            }
        }
        return endpoint;
    }

    private <T> T callApi(String endpoint, HttpMethod method, Class<T> clazz,
                          Map<String, String> pathVariables, HttpHeaders headers) {
        if(method.equals(HttpMethod.PATCH)) {
            return callPatchApi(endpoint, method, clazz, pathVariables, headers);
        }
        HttpEntity<String> entity;
        entity = new HttpEntity<String>(headers);
        endpoint = resolvePathVariables(endpoint, pathVariables);
        URI uri = null;
        try {
            uri = new URI(endpoint);
        } catch (URISyntaxException e) {
            LOG.error("Error in URL::::");
            e.printStackTrace();
            return null;
        }
        LOG.info("URL::::::" + new Gson().toJson(uri));
        LOG.info("METHOD::::::" + method);
        LOG.info("HEADERS::::::" + new Gson().toJson(headers));
        return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
    }

    private <T> T callPatchApi(String endpoint, HttpMethod method, Class<T> clazz,
                               Map<String, String> pathVariables, HttpHeaders headers) {
        RestTemplate restTemplate = new RestTemplate();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(1000);
        requestFactory.setReadTimeout(1000);
        restTemplate.setRequestFactory(requestFactory);

        HttpEntity<String> entity;
        entity = new HttpEntity<String>(headers);
        endpoint = resolvePathVariables(endpoint, pathVariables);
        URI uri = null;
        try {
            uri = new URI(endpoint);
        } catch (URISyntaxException e) {
            LOG.error("Error in URL::::");
            e.printStackTrace();
            return null;
        }
        LOG.info("URL::::::" + new Gson().toJson(uri));
        LOG.info("METHOD::::::" + method);
        LOG.info("HEADERS::::::" + new Gson().toJson(headers));
        return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
    }

    public <T> T postWithAuth_(String endPoint, String token, Object body, Class<T> clazz) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            org.springframework.http.HttpHeaders requestHeaders = new org.springframework.http.HttpHeaders();
            if(token != null) {
                requestHeaders.set("auth", token);
            }
            requestHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
            org.springframework.http.HttpEntity<?> requestEntity;
            if (body != null) {
                requestEntity = new org.springframework.http.HttpEntity(body, requestHeaders);
            } else {
                requestEntity = new org.springframework.http.HttpEntity(requestHeaders);
            }
            MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
            jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
            restTemplate.getMessageConverters().add(jackson);
            return restTemplate.postForObject(endPoint, requestEntity, clazz);
        } catch (Exception e) {
            LOG.error("ERROR While Request {}", endPoint, e);
            throw e;
        }
    }

}
