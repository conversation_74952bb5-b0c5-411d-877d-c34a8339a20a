package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import org.springframework.data.domain.Example;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.List;

@Repository
public interface CafeMenuAutoPushDao extends MongoRepository<CafeMenuAutoPush, String> {
    CafeMenuAutoPush findByUnitIdAndBrandId(Integer unitId, Integer brandId);

    List<CafeMenuAutoPush> findByBrandId(Integer brandId);

    List<CafeMenuAutoPush> findAllByUnitIdIn(List<Integer> unitId);
}
