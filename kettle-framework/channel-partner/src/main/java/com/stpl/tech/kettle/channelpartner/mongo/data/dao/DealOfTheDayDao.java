package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.DealOfTheDay;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DealOfTheDayDao extends MongoRepository <DealOfTheDay, String> {

    DealOfTheDay findByKettlePartnerId(Integer kettlePartnerId);
}
