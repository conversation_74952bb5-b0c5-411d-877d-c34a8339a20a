package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfileMappings;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DesiChaiCustomProfileMappingsDao extends MongoRepository<DesiChaiCustomProfileMappings, String> {

    List<DesiChaiCustomProfileMappings> findAllByUnitIdAndStatus(Integer unitId, String status);

    List<DesiChaiCustomProfileMappings> findAllByUnitIdAndStatusAndPartnerId(Integer unitId, String status,Integer partnerId);

    List<DesiChaiCustomProfileMappings> findAllByUnitIdInAndPartnerIdAndBrandId(List<Integer> unitIds,
                                                                                Integer partnerId, Integer brandId);

    List<DesiChaiCustomProfileMappings> findAllByUnitIdInAndPartnerIdAndBrandIdAndProfileId(List<Integer> unitIds,
                                                                                Integer partnerId, Integer brandId, String profileId);
}
