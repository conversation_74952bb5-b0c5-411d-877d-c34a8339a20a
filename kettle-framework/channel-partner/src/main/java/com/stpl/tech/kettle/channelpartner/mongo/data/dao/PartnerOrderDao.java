package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PartnerOrderDao extends MongoRepository<PartnerOrderDetail, String> {

    @Query("{'partnerOrderId' : ?0}")
    public List<PartnerOrderDetail> searchByPartnerOrderId(String id);

    @Query("{'partnerId' : ?0, 'partnerOrderId' : ?1}")
    public List<PartnerOrderDetail> searchByPartnerIdAndOrderId(String partnerId, String id);

    @Query("{'kettleOrderId' : ?0}")
    public PartnerOrderDetail searchByKettleOrderId(String id);
    
    @Query("{'partnerOrderId' : ?0}")
    public PartnerOrderDetail searchEntityByPartnerOrderId(String id);

    @Query("{'partnerId' : ?0, 'partnerOrderStatus' : ?1}")
    public List<PartnerOrderDetail> searchByPartnerIdAndOrderStatus(String partnerId, String partnerOrderStatus);

    @Query("{'addTime': {$gt : ?0}, 'beingProcessed' : false, " +
            "'partnerOrderStatus' : {$nin : ['NOTIFIED','RESOLVED','CANCELLED','CANCEL_REQUESTED','EDIT_CANCELLED','REJECTED', 'PLACED']}}")
    public List<PartnerOrderDetail> getPartnerPendingOrders(Date time);

    @Query("{'addTime': {$gt : ?0}, 'partnerId' : ?1, 'partnerOrderStatus' : 'PLACED', 'isNotified' : {$ne : true} }")
    public List<PartnerOrderDetail> notificationPendingOrders(Date date, String partnerId);

    @Query("{'addTime': {$gt : ?0} }")
    public List<PartnerOrderDetail> getPastOrdersByTime(Date date);

    @Query("{'addTime': {$gt : ?0} , 'addTime': {$lt : ?1}}")
    public List<PartnerOrderDetail> getOrdersByTime(Date startTime, Date endTime);

    @Query("{'unitId' : ?0, 'addTime': {$gt : ?1}, 'beingProcessed' : false, " +
        "'partnerOrderStatus' : {$nin : ['NOTIFIED','RESOLVED','CANCELLED','CANCEL_REQUESTED','EDIT_CANCELLED','REJECTED','PLACED']}}")
    public List<PartnerOrderDetail> getCafePendingOrders(Integer unitId, Date date);

    @Query("{'partnerName' : ?0 ,'partnerOrderId' : ?1}")
    public PartnerOrderDetail getProductDetailByNameAndOrderId(String partnerName, String partnerOrderId);

}
