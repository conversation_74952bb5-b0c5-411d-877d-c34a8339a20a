package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;



@Repository
public interface PartnerUnitStockSnapshotDao extends MongoRepository<PartnerUnitProductStockSnapshot, String> {

    public PartnerUnitProductStockSnapshot findByUnitIdAndPartnerId(String unitId, Integer partnerId);

    public PartnerUnitProductStockSnapshot findByUnitIdAndPartnerIdAndProductId(String unitId, Integer partnerId, Integer productId);

    public Slice<PartnerUnitProductStockSnapshot> findAllByPartnerId(Integer partnerId, Pageable pageable);

}
