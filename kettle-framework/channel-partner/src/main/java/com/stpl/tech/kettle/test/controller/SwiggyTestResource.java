package com.stpl.tech.kettle.test.controller;

import com.stpl.tech.kettle.channelpartner.controller.SwiggyOrderAbstractResource;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.test.domain.BulkAddOrderResponse;
import com.stpl.tech.kettle.test.domain.TestSwiggyOrderRequest;
import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;
import com.stpl.tech.kettle.test.service.TestSwiggyService;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SWIGGY_TEST_ROOT_CONTEXT;

@Slf4j
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + SWIGGY_TEST_ROOT_CONTEXT) // 'v1/swiggy-test'
public class SwiggyTestResource extends SwiggyOrderAbstractResource {

    @Autowired
    private TestSwiggyService testSwiggyService;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @RequestMapping(method = RequestMethod.GET, value = "get-live-sw-orders", produces = MediaType.APPLICATION_JSON)
    public TestSwiggyOrderRequest getLiveSwiggyOrders(@RequestParam String partnerName, @RequestParam Integer startOrderId, @RequestParam Integer endOrderId) {
        log.error("&&&&&&&&&&&&&&&&& GETTING SWIGGY LIVE ORDERS with startOrderId {} and endOrderId {} --------------------------", startOrderId, endOrderId);
        List<PartnerOrderDetail> partnerOrderDetailList = new ArrayList<>();
        try {
            partnerOrderDetailList = testSwiggyService.getSwiggyLiveOrders(partnerName, startOrderId, endOrderId);
            return TestSwiggyOrderRequest.builder().swiggyOrderRequestList(partnerOrderDetailList).build();
        } catch (Exception e) {
            log.error("Exception faced while fetching Swiggy live orders ::::::::::::::::::::::::", e);
            return null;
        }
    }


    @RequestMapping(method = RequestMethod.GET, value = "get-live-order", produces = MediaType.APPLICATION_JSON)
    public SwiggyOrderRequest getLiveSwiggyOrders(@RequestParam String partnerName, @RequestParam Integer orderId) {
        log.error("&&&&&&&&&&&&&&&&& GETTING Partner Order detail for LIVE ORDER  with orderId:: {} --------------------------", orderId);
        try {
            PartnerOrderDetail partnerOrderDetail = testSwiggyService.getPartnerOrder(orderId.toString());
            return (SwiggyOrderRequest) partnerOrderDetail.getPartnerOrder();
        } catch (Exception e) {
            log.error("Exception faced while fetching Swiggy live orders ::::::::::::::::::::::::", e);
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "raw-order-add", produces = MediaType.APPLICATION_JSON)
    public boolean addRawOrder(@RequestBody SwiggyOrderRequest swiggyOrderRequest) {
        log.error("&&&&&&&&&&&&&&&&& Add Raw Order Request for external order Id :: {} --------------------------", swiggyOrderRequest.getOrderId());
        Map<String, Object> failedOrderMap = new HashMap<>();

        if (!AppUtils.isProd(environmentProperties.getEnvType())) {
            try {
                return testSwiggyService.addSwiggyOrderRequest(swiggyOrderRequest, failedOrderMap);
            } catch (Exception e) {
                log.error("Exception faced while fetching Swiggy live orders ::::::::::::::::::::::::", e);
                return false;
            }
        }
        return false;
    }

    /*private boolean addOrderRequest(SwiggyOrderRequest swiggyOrderRequest) {
        SwiggyOrderResponse orderResponse = null ;
        try{
            orderResponse = addSwiggyOrder(swiggyOrderRequest);
            if(Objects.nonNull(orderResponse)){
                log.info("Response on processing  Swiggy add order request for partnerOrderId ::{} {}::::::::::::::::", swiggyOrderRequest.getOrderId(), new Gson().toJson(orderResponse));
                if(orderResponse.getStatusCode()!=200){
                    return false;
                }
            }
            return true ;
        }catch (Exception e ){
            log.error("Exception while adding order with orderId :::{}", swiggyOrderRequest.getOrderId(),e);
            return false;
        }
    }*/

    @RequestMapping(method = RequestMethod.POST, value = "bulk-order-add", produces = MediaType.APPLICATION_JSON)
    public BulkAddOrderResponse addBulkOrderRequest(@RequestParam String partnerName, Integer startOrderId, Integer endOrderId) {
        TestSwiggyOrderRequest testSwiggyOrderRequest = null;
        Map<String, Object> failedOrderMap = new HashMap<>();
        if (!AppUtils.isProd(environmentProperties.getEnvType())) {
            try {
                testSwiggyOrderRequest = testSwiggyService.sendRequestToGetLiveSwiggyOrderRequestData(partnerName, startOrderId, endOrderId);
                testSwiggyService.addSwiggyBatchedOrders(testSwiggyOrderRequest, startOrderId, endOrderId, partnerName, failedOrderMap);
            } catch (Exception e) {
                log.error("Exception while processing bulk get order request ::::::::", e);
            }

            if (!failedOrderMap.isEmpty()) {
                return BulkAddOrderResponse.builder().isSuccess(false).failedOrdersMap(failedOrderMap).build();
            } else {
                return BulkAddOrderResponse.builder().isSuccess(true).failedOrdersMap(null).build();
            }
        }
        failedOrderMap.put("Cannot add order for this env", null);
        return BulkAddOrderResponse.builder().isSuccess(false).failedOrdersMap(failedOrderMap).build();
    }
    
}


