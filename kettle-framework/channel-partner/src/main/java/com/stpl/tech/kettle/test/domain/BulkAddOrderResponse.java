package com.stpl.tech.kettle.test.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class BulkAddOrderResponse implements Serializable {
    private static final long serialVersionUID = 8591649009089771039L;

    private boolean isSuccess ;
    private Map<String, Object> failedOrdersMap ;

}
