<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Dashboard</title>
    <link rel="icon" type="image/png" sizes="32x32" href="icon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="icon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon/favicon-16x16.png">

    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/metisMenu.min.css" rel="stylesheet">
    <link href="css/sb-admin-2.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="libs/select2/select2.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="libs/popeye/popeye.min.css">


    <script type="text/javascript" src="libs/jquery/dist/jquery.min.js"></script>
    <script src="libs/angular/angular.min.js"></script>
    <script src="libs/angular/angular-cookies.min.js"></script>
    <script src="libs/angular-ui-router/release/angular-ui-router.min.js"></script>
    <script src="js/ui-bootstrap-tpls-1.3.2.min.js"></script>
    <script src="libs/popeye/popeye.min.js"></script>
    <script src="libs/select2/select2.min.js"></script>
    <script src="libs/select2/ui-select2.js"></script>
    <!-- custom method to disable back button -->
    <!--<script type="text/javascript">
        history.pushState(null, null, '');
        window.addEventListener('popstate', function (event) {
            history.pushState(null, null, '');
        });
    </script>-->
    <!-- custom method to disable back button -->
</head>
<body data-ng-app="inventoryapp">
<div ui-view></div>
<!-- Spinner code starts here-->
<div class="fullScreenLoader" data-ng-show="showFullScreenLoader">
    <img src="img/ring.gif" style="width: 200px">
</div>
<!-- JS -->
<script src="js/bootstrap.min.js"></script>
<script src="js/metisMenu.min.js"></script>
<script src="libs/chart.min.js"></script>
<script src="libs/d3.min.js"></script>
<!-- ANGULAR CUSTOM SERVICES-->
<script src="js/app.js"></script>
<script src="js/services/APIJson.js"></script>
<script src="js/services/AppUtil.js"></script>
<script src="js/fileSaver.js"></script>
<!-- ANGULAR CUSTOM CONTROLLERS-->
<script src="js/controllers/LoginController.js"></script>
<script src="js/controllers/DashboardController.js"></script>
<script src="js/controllers/HomeController.js"></script>
<script src="js/controllers/TimelineCtrl.js"></script>
<script src="js/controllers/InventoryCtrl.js"></script>
<script src="js/controllers/InventorySnapshotCtrl.js"></script>
</body>
</html>
