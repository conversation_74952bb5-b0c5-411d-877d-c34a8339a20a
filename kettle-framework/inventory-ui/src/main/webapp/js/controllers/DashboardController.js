inventoryApp.controller("DashboardController",
    ['$rootScope', '$scope', '$state', '$http', '$location', 'APIJson', 'AuthService', '$cookieStore', '$window',
        function ($rootScope, $scope, $state, $http, $location, APIJson, AuthService, $cookieStore, $window) {

            if ($location.path() === "/dashboard") {
                $location.path("dashboard/home");
            }

            $rootScope.logout = function (reload) {
                $cookieStore.remove("inventoryGlobals");
                AuthService.setAuthorization(null);
                $state.go('login');
                if (reload) {
                    $window.location.reload();
                }
            };
        }]);
