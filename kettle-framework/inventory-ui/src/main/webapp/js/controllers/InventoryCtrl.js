inventoryApp.controller("InventoryCtrl",
    ['$rootScope', '$scope', '$state', '$http', '$location', 'APIJson', 'AuthService', '$cookieStore', 'Popeye', 'AppUtil',
        function ($rootScope, $scope, $state, $http, $location, APIJson, AuthService, $cookieStore, Popeye, AppUtil) {
        $scope.selectedZone=null;
        $scope.unitZones=['NORTH','SOUTH','EAST','WEST'];
        $scope.isAdmin = false;
        $scope.init = function () {
            $scope.empId = null;
            if (AppUtil.getCurrentUserId() == 120458 || AppUtil.getCurrentDesignation() == "Admin") {
                $scope.isAdmin = true;
            }
            $scope.getUnits(AppUtil.getCurrentUserId());
            $scope.reset();
        };

        $scope.reset = function () {
            $scope.inventory = null;
            $scope.timelineData = null;
        };



        $scope.getUnits = function (amId) {
            $rootScope.showFullScreenLoader = true;
            $scope.empId=amId;
            console.log("Printing amId in get Units",amId);
            if ($scope.isAdmin) {
                $http({
                    method: 'GET',
                    url: APIJson.urls.unitMetaData.activeUnits,
                    headers: "Content-type: application/json",
                    params: {
                        'category': 'ALL'
                    }
                }).then(function success(response) {
                    $scope.units = response.data;
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            } else {
                $http({
                    method: 'POST',
                    url: APIJson.urls.userManagement.activeUnitsForUser,
                    headers: "Content-type: application/json",
                    data: {
                        employeeId: amId,
                        onlyActive: true
                    }
                }).then(function success(response) {
                    $scope.units = response.data;
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            }
        };

        $scope.setSelectedUnit = function (selectedUnit) {
            $scope.selectedUnit = selectedUnit;
            $scope.inventory = [];
        };

        $scope.getCafeInventory = function () {
            $rootScope.showFullScreenLoader = true;
            var InventoryAPI = APIJson.urls.inventory;
            var zone = $scope.selectedUnit.unitZone;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryAPI.baseUrl + "/" + zone.toLowerCase() + InventoryAPI.postfix + InventoryAPI.cafe;
            }else{
                zoneAPI = InventoryAPI.baseUrl + "/north" + InventoryAPI.postfix + InventoryAPI.cafe;
            }
            console.log(zoneAPI);
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json",
                params: {
                    'unitId': $scope.selectedUnit.id
                }
            }).then(function success(response) {
                $scope.reset();
                $scope.inventory = response.data;
                $scope.getKettleInventory();
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getSCMInventory = function () {
            $rootScope.showFullScreenLoader = true;
            var zone = $scope.selectedUnit.unitZone;
            var InventoryAPI = APIJson.urls.inventory;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryAPI.baseUrl + "/" + zone.toLowerCase() + InventoryAPI.postfix + InventoryAPI.scm;
            }else{
                zoneAPI = InventoryAPI.baseUrl + "/north" +  InventoryAPI.postfix + InventoryAPI.scm;
            }
            console.log(zoneAPI);
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json",
                params: {
                    'unitId': $scope.selectedUnit.id
                }
            }).then(function success(response) {
                $scope.reset();
                $scope.inventory = response.data;
                var i = 0;
                for (i in $scope.inventory) {
                    $scope.inventory[i].isSCM = 'Y';
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };


        $scope.getKettleInventory = function () {
            var zone = $scope.selectedUnit.unitZone;
            var InventoryAPI = APIJson.urls.inventory;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryAPI.baseUrl + "/" + zone.toLowerCase() + InventoryAPI.postfix + InventoryAPI.kettleInventory;
            }else{
                zoneAPI = InventoryAPI.baseUrl + "/north" +  InventoryAPI.postfix + InventoryAPI.kettleInventory;
            }
            console.log(zoneAPI);
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json",
                params: {
                    'unitId': $scope.selectedUnit.id
                }
            }).then(function success(response) {
                var kettleInventory = response.data;
                var productMap = [];
                for (var i in kettleInventory) {
                    productMap[kettleInventory[i].id] = kettleInventory[i].quantity;
                }
                for (var i in $scope.inventory) {
                    var product = $scope.inventory[i];
                    if (productMap[product.id] == undefined) {
                        product.kettleQty = 'N/A';
                    } else {
                        product.kettleQty = productMap[product.id];
                    }
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };


        $scope.refreshCafeInventory = function (force) {
            var zone = $scope.selectedUnit.unitZone;
            var InventoryUpdateAPI = APIJson.urls.inventoryUpdate;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryUpdateAPI.baseUrl + "/" + zone.toLowerCase() + InventoryUpdateAPI.postfix + InventoryUpdateAPI.cafe;
            }else{
                zoneAPI = InventoryUpdateAPI.baseUrl + "/north" +  InventoryUpdateAPI.postfix + InventoryUpdateAPI.cafe;
            }
            console.log(zoneAPI);
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json",
                params: {
                    'unitId': $scope.selectedUnit.id,
                    'force': force
                }
            }).then(function success(response) {
                $scope.reset();
                $scope.inventory = response.data;
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.refreshScmInventory = function (force) {
            var zone = $scope.selectedUnit.unitZone;
            var InventoryUpdateAPI = APIJson.urls.inventoryUpdate;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryUpdateAPI.baseUrl + "/" + zone.toLowerCase() + InventoryUpdateAPI.postfix + InventoryUpdateAPI.scm;
            }else{
                zoneAPI = InventoryUpdateAPI.baseUrl + "/north" + InventoryUpdateAPI.postfix + InventoryUpdateAPI.scm;
            }
            console.log(zoneAPI);
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json",
                params: {
                    'unitId': $scope.selectedUnit.id,
                    'force': force
                }
            }).then(function success(response) {
                $scope.reset();
                $scope.inventory = response.data;
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

		$scope.deleteKeys = function () {
            var zone = $scope.selectedUnit.unitZone;
            var InventoryUpdateAPI = APIJson.urls.inventoryUpdate;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryUpdateAPI.baseUrl + "/" + zone.toLowerCase() + InventoryUpdateAPI.postfix + InventoryUpdateAPI.keys;
            }else{
                zoneAPI = InventoryUpdateAPI.baseUrl + "/north" +  InventoryUpdateAPI.postfix + InventoryUpdateAPI.keys;
            }
            console.log(zoneAPI);
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json",
                params: {
                    'unitId': $scope.selectedUnit.id
                }
            }).then(function success(response) {
                $scope.reset();
                $scope.inventory = response.data;
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.downloadSheet = function (){

            $rootScope.showFullScreenLoader = true;
            var zone = $scope.selectedUnit.unitZone;
            var InventoryAPI = APIJson.urls.inventory;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryAPI.baseUrl + "/" + zone.toLowerCase() + InventoryAPI.postfix + InventoryAPI.excel;
            }else{
                zoneAPI = InventoryAPI.baseUrl + "/north" +  InventoryAPI.postfix + InventoryAPI.excel;
            }
            console.log(zoneAPI);
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json",
                params: {
                    "empId":$scope.empId
                }
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                console.log(response.data);
                console.log($scope.empId);
                if(response.data!=null && response.data===true) {
                    alert("Successfully emailed Excel Sheet");
                    console.log("Successfully sent Excel Sheet");
                }else{
                    alert("unable to send Excel Sheet");
                    console.log("Cannot send Excel Sheet");
                }
            }, function error(response) {
                alert("Eror sending Excel Sheet");
                console.log("Error Sending Excel Sheet",response );
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.refreshAllUnits = function () {
            var zone = $scope.selectedZone;
            var InventoryUpdateAPI = APIJson.urls.inventoryUpdate;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryUpdateAPI.baseUrl + "/" + zone.toLowerCase() + InventoryUpdateAPI.postfix + InventoryUpdateAPI.all;
            }else{
                zoneAPI = InventoryUpdateAPI.baseUrl + "/north" +  InventoryUpdateAPI.postfix + InventoryUpdateAPI.all;
            }
            console.log(zoneAPI);
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: zoneAPI,
                headers: "Content-type: application/json"
            }).then(function success(response) {
                $scope.reset();
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.isAddInventoryEnabled= function (){
            var aclData = $rootScope.aclData;
            if(aclData.action!=null && aclData.action["IM_SCMI_AI"]!=null){
                return true;
            }else{
                return false;
            }
        }

        $scope.getCafeProductRecipeInventory = function (item) {
            $rootScope.showFullScreenLoader = true;
            var zone = $scope.selectedUnit.unitZone;
            var InventoryAPI = APIJson.urls.inventory;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryAPI.baseUrl + "/" + zone.toLowerCase() + InventoryAPI.postfix + InventoryAPI.recipe;
            }else{
                zoneAPI = InventoryAPI.baseUrl + "/north" +  InventoryAPI.postfix + InventoryAPI.recipe;
            }
            console.log(zoneAPI);
            $http({
                method: 'GET',
                url: zoneAPI,
                params: {
                    'unitId': $scope.selectedUnit.id,
                    'productId': item.id,
                    'dimension': item.u
                }
            }).then(function success(response) {
                //$scope.reset();
                $scope.openDetail(response.data);
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };


        $scope.openSCMDataUpdateUI = function (item) {
            $scope.openSCMDetail(item, $scope.selectedUnit);
        };

        $scope.getUnitTimeLine = function () {
            $rootScope.showFullScreenLoader = true;
            var zone = $scope.selectedUnit.unitZone;
            var InventoryAPI = APIJson.urls.inventory;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryAPI.baseUrl + "/" + zone.toLowerCase() + InventoryAPI.postfix + InventoryAPI.timeline;
            }else{
                zoneAPI = InventoryAPI.baseUrl + "/north" +  InventoryAPI.postfix + InventoryAPI.timeline;
            }
            console.log(zoneAPI);
            $http({
                method: 'GET',
                url: zoneAPI,
                params: {
                    'unitId': $scope.selectedUnit.id,
                }
            }).then(function success(response) {
                $scope.reset();
                $scope.timelineData = response.data;
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.openDetail = function (inventory) {
            var mappingModal = Popeye.openModal({
                templateUrl: "checkData.html",
                controller: "checkDataCtrl",
                resolve: {
                    inventory: function () {
                        return inventory;
                    }
                },
                click: false,
                keyboard: false
            });

            mappingModal.closed.then(function (isSuccessful) {
                if (isSuccessful) {
                }
            });
        };

        $scope.openSCMDetail = function (item, unit) {
            var mappingModal = Popeye.openModal({
                templateUrl: "updateSCMData.html",
                controller: "updateSCMDataCtrl",
                resolve: {
                    item: function () {
                        return item;
                    },
                    unit: function () {
                        return unit;
                    }
                },
                click: false,
                keyboard: false
            });

            mappingModal.closed.then(function (isSuccessful) {
                if (isSuccessful) {
                }
                $scope.getSCMInventory();
            });
        };

    }]).controller('checkDataCtrl',
    ['$scope', 'inventory', '$http', 'Popeye', function ($scope, inventory, $http, Popeye) {
        $scope.inventory = inventory;
        $scope.cancel = function () {
            Popeye.closeCurrentModal();
        };

    }]).controller('updateSCMDataCtrl',
    ['$scope', '$rootScope', 'APIJson', 'item', 'unit', '$http', 'Popeye', 'AppUtil', function ($scope, $rootScope, APIJson, item, unit, $http, Popeye, AppUtil) {
        $scope.item = item;
        $scope.unit = unit;
        $scope.cancel = function () {
            Popeye.closeCurrentModal();
        };
        $scope.submit = function (item) {
            item.unitId = $scope.unit.id;
            item.unitName = $scope.unit.name;
            item.updatedById = AppUtil.getCurrentUserId();
            item.updatedBy = AppUtil.getCurrentUserName();

            $rootScope.showFullScreenLoader = true;
            item.qty = item.update;
            var zone = $scope.unit.unitZone;
            var InventoryUpdateAPI = APIJson.urls.inventoryUpdate;
            var zoneAPI;
            if(zone!=null){
                zoneAPI = InventoryUpdateAPI.baseUrl + "/" + zone.toLowerCase() + InventoryUpdateAPI.postfix + InventoryUpdateAPI.scmUpdate;
            }else{
                zoneAPI = InventoryUpdateAPI.baseUrl + InventoryUpdateAPI.postfix + InventoryUpdateAPI.scmUpdate;
            }
            console.log(zoneAPI);
            $http({
                method: 'POST',
                url: zoneAPI,
                headers: "Content-type: application/json",
                data: item
            }).then(function success(response) {
                console.log(response);
                Popeye.closeCurrentModal();
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };
    }]);
