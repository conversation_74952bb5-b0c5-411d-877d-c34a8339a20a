/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function() {
	'use strict';
	inventoryApp.factory('APIJson', APIJson);

	function APIJson() {

		var masterUrl = window.location.protocol+"//"+window.location.host;
		var inventoryUrl = window.location.protocol+"//"+window.location.host;
        var scmUrl = window.location.protocol+"//"+window.location.host;
		var SEPARATOR = "/";
		var POSTFIX = "/rest/v1";
		var MASTER_SERVICE = masterUrl + SEPARATOR + "master-service/rest/v1" + SEPARATOR;
		var INVENTORY_BASE_URL=inventoryUrl + SEPARATOR + "kettle-inventory";
		var INVENTORY_SERVICE = inventoryUrl + SEPARATOR + "kettle-inventory/rest/v1" + SEPARATOR;
        var SCM_SERVICE = scmUrl + SEPARATOR + "scm-service/rest/v1" + SEPARATOR;
		var INVENTORY_SERVICES_ROOT_CONTEXT = INVENTORY_SERVICE + "inventory-data";
		var INVENTORY_SERVICES_ROOT_CONTEXT2 =  "/inventory-data";
		var USER_SERVICES_ROOT_CONTEXT = MASTER_SERVICE + "users";
		var UNIT_METADATA_ROOT_CONTEXT = MASTER_SERVICE + "unit-metadata";
		var INVENTORY_MANAGEMENT_ROOT_CONTEXT = INVENTORY_SERVICE + "inventory-management";
		var INVENTORY_MANAGEMENT_ROOT_CONTEXT2 =  "/inventory-management";
        var USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = MASTER_SERVICE + "user-management";
        var SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "product-management/";

		var service = {};

		service.urls = {
			users : {
				login : USER_SERVICES_ROOT_CONTEXT + "/login",
				logout : USER_SERVICES_ROOT_CONTEXT + "/logout"
			},
			unitMetaData : {
				activeUnits : UNIT_METADATA_ROOT_CONTEXT + "/all-active-units",
				activeAMUnits : UNIT_METADATA_ROOT_CONTEXT + "/area-manager-units",
				trimmedProduct : UNIT_METADATA_ROOT_CONTEXT + "/unit-product/trim"
			},
            userManagement: {
                activeUnitsForUser: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/units",
            },
			inventory : {
				baseUrl : INVENTORY_BASE_URL,
				postfix : POSTFIX,
				excel: INVENTORY_SERVICES_ROOT_CONTEXT2 + '/save-cafe-inventory-sheet',
				cafe : INVENTORY_SERVICES_ROOT_CONTEXT2 + '/get-cafe-products',
				scm : INVENTORY_SERVICES_ROOT_CONTEXT2 + '/get-scm-products',
				recipe : INVENTORY_SERVICES_ROOT_CONTEXT2 + '/get-recipe',
				timeline : INVENTORY_SERVICES_ROOT_CONTEXT2 + '/get-timeline',
                kettleInventory: INVENTORY_SERVICES_ROOT_CONTEXT2 + '/kettle-inventory',
				getProductWiseInventory: INVENTORY_SERVICES_ROOT_CONTEXT2 + '/get-unitwise-product-inventory'
			},
			inventoryUpdate : {
				baseUrl : INVENTORY_BASE_URL,
				postfix : POSTFIX,
				cafe : INVENTORY_MANAGEMENT_ROOT_CONTEXT2 + "/refresh-unit",
				scm : INVENTORY_MANAGEMENT_ROOT_CONTEXT2 + "/refresh-scm-unit",
				keys : INVENTORY_MANAGEMENT_ROOT_CONTEXT2 + "/delete-mapping",
				all : INVENTORY_MANAGEMENT_ROOT_CONTEXT2 + "/refresh-all",
				scmUpdate : INVENTORY_MANAGEMENT_ROOT_CONTEXT2 + "/scm/update"
			},
			scmProductManagement: {
				getProductBasicDetail: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "products-basic-detail"
			}
		};

		return service;
	}

})();
