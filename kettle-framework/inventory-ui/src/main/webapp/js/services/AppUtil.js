/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
inventoryApp.service('AppUtil', [ '$cookieStore', function($cookieStore) {

    var service = {};
    service.setAcl = setAcl;
    service.acl = null;
    service.getAcl = getAcl;
    service.setUserData = setUserData;
    service.userData = null;
    service.getUserData = getUserData;
    service.getCurrentUser = getCurrentUser;
    service.getCurrentUserId = getCurrentUserId;
	service.getCurrentUserName = getCurrentUserName;
	service.getCurrentDesignation = getCurrentDesignation;

	
    function getCurrentUserId() {
		return $cookieStore.get('inventoryGlobals').userId;
    }

    function getCurrentUserName() {
		return $cookieStore.get('inventoryGlobals').name;
    }

    function getCurrentDesignation() {
        return $cookieStore.get('inventoryGlobals').user.designation.name;
    }

    function getAcl() {
        if (this.acl == null) {
            this.acl = JSON.parse(localStorage.getItem("acl"));
        }
        return this.acl;
    }

    function setAcl(acl) {
        this.acl = acl;
        localStorage.setItem("iacl", JSON.stringify(acl));
    }

    function getUserData() {
        if (this.userData == null) {
            this.userData = JSON.parse(localStorage.getItem("udata"));
        }
        return this.userData;
    }

    function setUserData(data) {
        this.userData = {
            id: data.id,
            name: data.name,
            department: data.department.name,
            designation: data.designation.name
        };
        localStorage.setItem("udata", JSON.stringify(this.userData));
    }

    function getCurrentUser() {
        return $cookieStore.get('inventoryGlobals').user;
    }


    return service;
}]);
