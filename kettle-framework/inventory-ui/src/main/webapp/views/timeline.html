<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">
            Inventory Timeline
            <!--<button type="button" class="btn btn-primary pull-right" style="margin-left: 10px" id="btnAddNewUnitsIDnew" ng-click="open()">
                <i class="fa fa-plus"></i> Add new unit
            </button>-->
        </h1>
    </div>

</div>
<div class="row">
    <div class="col-xs-6 form-group">
        <label>Select Unit</label>
        <select
                class="form-control"
                ui-select2="selectedUnit"
                id="inventoryUnit"
                name="inventoryUnit"
                data-placeholder="Select Unit"
                data-ng-model="selectedUnit"
                data-ng-options="unit as unit.name for unit in units track by unit.id"
                data-ng-change="setSelectedUnit(selectedUnit)"></select>
    </div>
    <div class="col-xs-6 form-group">
        <label>Select Product</label>
        <select
                class="form-control"
                ui-select2="selectedProduct"
                id="products"
                name="products"
                data-placeholder="Select Product"
                data-ng-model="selectedProduct"
                data-ng-options="product as product for product in prodsNames"
                data-ng-change="setSelectedProduct(selectedProduct)"></select>
    </div>
</div>
<div class="row">
    <div class="col-xs-3">
        <div class="form-group">
            <!--//TODO set datepicker here-->
            <span style="font-weight: bold">Select Date &nbsp;</span>
            <input type="date" data-ng-model="selectedDate" data-ng-change="setSelectedDate(selectedDate)" class="btn btn-primary"/>
        </div>
    </div>
    <div class="col-xs-3">
        <div class="form-group pull-right">
            <button class="btn btn-danger" data-ng-model="removeDate" data-ng-click="removeDate()">Clear Date</button>
        </div>
    </div>
    <div class="col-xs-3">
        <div class="form-group pull-right">
            <button class="btn btn-danger" data-ng-model="destroy" data-ng-click="destroyChart()">Destroy Chart</button>
        </div>
    </div>
    <div class="col-xs-3">
        <div class="form-group pull-right">
            <button
                    data-ng-if="isAdmin"
                    data-ng-click="plotChart()"
                    data-ng-model="plot"
                    class="btn btn-primary">Plot Timeline
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xs-12" style="text-align: right;">
        <canvas id="myChart" width="800" height="400"></canvas>
    </div>
</div>
