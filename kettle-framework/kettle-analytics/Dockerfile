# FROM alpine:3.16.2
FROM tomcat:9.0
LABEL org.opencontainers.image.authors="<EMAIL>"
ENV CATALINA_OPTS="-Dspring.profiles.active=sprod -Denv.type=sprod -Dprimary.server=false -Dprimary.channel.partner.server=true -Dhazelcast.discovery.public.ip.enabled=true"
WORKDIR $CATALINA_HOME/webapps
COPY target/kettle-analytics*.war .
EXPOSE 8080
CMD ["/usr/local/tomcat/bin/catalina.sh", "run", "-Xms6000m", "-Xmx8000m", "-XX:+UseParallelGC"]
