package com.stpl.tech.analytics.service.impl;

import com.stpl.tech.analytics.dao.UnitAppVersionMetadata;
import com.stpl.tech.analytics.data.dao.UnitAppVersionMetadataRepository;
import com.stpl.tech.analytics.dto.BuildVersionRequest;
import com.stpl.tech.analytics.mapper.DomainDataMapper;
import com.stpl.tech.analytics.service.VersionManagementService;
import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.kettle.data.model.UnitDeviceWhitelist;
import com.stpl.tech.analytics.data.dao.UnitDeviceWhitelistRepository;
import com.stpl.tech.analytics.dto.UnitDeviceWhitelistDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class VersionManagementServiceImpl implements VersionManagementService {
    @Autowired
    private UnitAppVersionMetadataRepository repository;

    @Autowired
    private UnitDeviceWhitelistRepository whitelistRepository;

    private DomainDataMapper domainDataMapper = DomainDataMapper.INSTANCE;

    private static final String BUILD_VERSION_SAVED_SUCCESSFULLY = "Screen versions saved successfully.";

    @Override
    public String saveBuildVersion(BuildVersionRequest request) {
        if (request.getScreenVersions() == null || request.getScreenVersions().isEmpty()) {
            throw new IllegalArgumentException("No screen versions provided in the request.");
        }

        List<UnitAppVersionMetadata> entities = new ArrayList<>();
        //
        // Get current time once for all entities
        for (Map.Entry<ScreenType, String> entry : request.getScreenVersions().entrySet()) {
            ScreenType screenType = entry.getKey();
            String screenVersion = entry.getValue();

            // Check if this version already exists
            UnitAppVersionMetadata latestVersion = repository.findTopByUnitIdAndScreenTypeOrderByVersionTimeDesc(
                request.getUnitId(), screenType);

            if (isVersionEqual(latestVersion, screenType, screenVersion)) {
                continue;
            }

            // Create new version entity
            UnitAppVersionMetadata entity = new UnitAppVersionMetadata();
            entity.setUnitId(request.getUnitId());
            entity.setScreenType(screenType);
            entity.setApplicationVersion(screenVersion);
            entity.setVersionTime(AppUtils.getCurrentTimestamp()); // Set current time
            entities.add(entity);
        }

        repository.saveAll(entities);
        return BUILD_VERSION_SAVED_SUCCESSFULLY;
    }

    @Override
    public List<UnitAppVersionMetadata> getVersionHistory(String unitId) {
        return repository.findByUnitIdOrderByVersionTimeDesc(unitId);
    }

    @Override
    public BuildVersionRequest getLatestVersions(String unitId) {
        List<UnitAppVersionMetadata> latestVersions = repository.findAllLatestVersionsByUnitId(unitId);
        if (latestVersions == null || latestVersions.isEmpty()) {
            return null;
        }

        BuildVersionRequest request = new BuildVersionRequest();
        request.setUnitId(unitId);
        
        // Convert the versions to a map of screen types to version strings
        Map<ScreenType, String> screenVersions = latestVersions.stream()
                .collect(Collectors.toMap(
                    UnitAppVersionMetadata::getScreenType,
                    UnitAppVersionMetadata::getApplicationVersion,
                    (existing, replacement) -> existing // In case of duplicates, keep the first one
                ));
        request.setScreenVersions(screenVersions);

        return request;
    }

    @Override
    public UnitDeviceWhitelistDto saveUnitDeviceWhitelist(UnitDeviceWhitelistDto whitelistDto) {
        UnitDeviceWhitelist entity = domainDataMapper.toUnitDeviceWhiteList(whitelistDto);
        UnitDeviceWhitelist saved = whitelistRepository.save(entity);
        return domainDataMapper.toUnitDeviceWhiteListDto(saved);
    }

    @Override
    public List<UnitDeviceWhitelistDto> getUnitDeviceWhitelistByUnitId(String unitId) {
        return domainDataMapper.toUnitDeviceWhiteListDtoList(whitelistRepository.findByUnitId(unitId));
    }

    @Override
    public UnitDeviceWhitelistDto getUnitDeviceWhitelistByMac(String macAddress) {
        UnitDeviceWhitelist entity = whitelistRepository.findByMacAddress(macAddress);
        return entity != null ? domainDataMapper.toUnitDeviceWhiteListDto(entity) : null;
    }

    private static boolean isVersionEqual(UnitAppVersionMetadata latestVersion, ScreenType screenType, String screenVersion) {
        return Objects.nonNull(latestVersion) &&
               Objects.equals(latestVersion.getScreenType(), screenType) &&
               Objects.equals(latestVersion.getApplicationVersion(), screenVersion);
    }
}
