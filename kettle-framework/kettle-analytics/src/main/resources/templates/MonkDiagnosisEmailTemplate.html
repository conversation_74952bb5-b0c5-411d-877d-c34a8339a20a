<!--
  ~ Created By Shanmukh
  -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head></head>
<body>
<div>
    <h3 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Monk Diagnosis</h3>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <thead>
        <tr>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Monk Diagnosis Event Id</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Unit Id</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Unit Name</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Monk Name</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Started By</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Entered Name</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>$data.logData.monkDiagnosisEventId</td>
            <td>$data.logData.unitId</td>
            <td>#if($data.logData.unitName) $data.logData.unitName #else - #end</td>
            <td>#if($data.logData.monkName) $data.logData.monkName #else - #end</td>
            <td>#if($data.logData.userIdsName) $data.logData.userIdsName #else - #end [#if($data.logData.userId) $data.logData.userId #else - #end]</td>
            <td>#if($data.logData.userName) $data.logData.userName #else - #end</td>
        </tr>
        </tbody>
    </table>
    <br>

    #foreach($entry in $data.logData.monkDiagnosisData.entrySet())
    <h4 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Diagnosis Check At : $entry.key</h4>
    #foreach($innerEntry in $entry.value.entrySet())
    #if($innerEntry.key == "DIAGNOSIS")
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <thead>
        <tr>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Diagnosis Code</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Code Meaning</th>
            <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Acknowledged</th>
        </tr>
        </thead>
        <tbody>
        #foreach($troubleShoot in $innerEntry.value)
        <tr #if($troubleShoot.acknowleged) style="background-color: red;"#end>
            <td>$troubleShoot.code</td>
            <td>$troubleShoot.codeMeaning</td>
            <td>#if($troubleShoot.acknowleged) Y #else - #end</td>
        </tr>
        #end
        </tbody>
    </table>
    #end
    #end
    #end
</div>
</body>
</html>
