/**
 * http://usejsdoc.org/
 */
analyticapp
    .controller(
        "ApcLeverController",
        function ($rootScope, $scope, $http, $location, APIJson) {
            $scope.init = function () {
                $scope.fetchOutlets();
                //$scope.minDate = new Date();
                $scope.maxDate = new Date();
                $scope.dates = [];
                $scope.maxDatesLength = 4;
            };

            $scope.fetchOutlets = function () {
                $http({
                    method : 'GET',
                    url : APIJson.urls.unitMetaData.activeUnits+"?category=CAFE"
                }).then(function success(response) {
                    $scope.unitList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.removeDate = function(index){
                $scope.dates.splice(index,1);
            };

            $scope.getApcLeverData = function (date) {
                if(date){
                    $scope.dates.push({date:date,unitsPenetrationCurrent:null});
                    $scope.fetchPenetrationCurrentData(date);
                } else {
                    $scope.dates = [];
                    //$scope.fetchCurrentAllSalesData();
                    $scope.fetchPenetrationCurrentData();
                    $scope.fetchPenetrationTargetData();
                    $scope.fetchPenetrationLMData();
                    $scope.fetchAllCurrentDataAtOnce();
                    $scope.fetchAllTargetDataAtOnce();
                }
            };

            $scope.fetchCurrentAllSalesData = function() {
                //console.log("Inside fetch current all sales data");
                $http({
                    method : 'GET',
                    url : APIJson.urls.analyticsData.unitSalesNew+ '?unitId=' + $scope.selectedUnit.id
                }).then(function success(response) {
                    $scope.unitsReportData = response.data;
                });
            };

            $scope.fetchAllCurrentDataAtOnce = function () {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.analyticsData.currentAllData + '?unitId=' + $rootScope.globals.currentUser.unitId,
                }).then(function success(response){
                    $scope.unitsAllCurrentData = response.data;
                });
            };

            $scope.fetchAllTargetDataAtOnce = function () {
                $http({
                    method: 'GET',
                    url: APIJson.urls.analyticsData.targetAllData + '?unitId=' + $rootScope.globals.currentUser.unitId,
                }).then(function success(response) {
                    $scope.unitsAllTargetData = response.data;
                });
            };

            $scope.fetchPenetrationTargetData = function() {
                $http({
                    method : 'GET',
                    url : APIJson.urls.analyticsData.unitPenetrationTarget+ '?unitId=' + $scope.selectedUnit.id
                }).then(function success(response) {
                    $scope.unitsPenetrationTarget = response.data;
                });
            };

            $scope.fetchPenetrationCurrentData = function(date) {
                var url = APIJson.urls.analyticsData.unitPenetrationCurrent+ '?unitId=' + $scope.selectedUnit.id;
                if(date){
                    url = url + '&businessDate='+date;
                }
                $http({
                    method : 'GET',
                    url : url
                }).then(function success(response) {
                    if(date){
                        $scope.dates[$scope.dates.length-1].unitsPenetrationCurrent = response.data;
                    }else{
                        $scope.unitsPenetrationCurrent = response.data;
                    }
                });
            };

            $scope.fetchPenetrationLMData = function() {
                $http({
                    method : 'GET',
                    url : APIJson.urls.analyticsData.unitPenetrationLastMonth+ '?unitId=' + $scope.selectedUnit.id
                }).then(function success(response) {
                    $scope.unitsPenetrationLM = response.data;
                });
            };
        });
