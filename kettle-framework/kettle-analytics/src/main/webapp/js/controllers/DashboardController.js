/**
 * http://usejsdoc.org/
 */
analyticapp
		.controller(
				"DashboardController",
				function($rootScope, $scope, $http, $location, APIJson,AuthService ) {
					$scope.init = function() {
						$scope.envType = null;
						$scope.pusher = null;
						$scope.channel = null;
						$scope.unitsReportData = null;
						//subscribeChannel();
						fetchAllSalesData();
					}
					$scope.deliveryFilter = function(item){
						return item.detail.status == 'ACTIVE' && item.detail.category == 'DELIVERY';
					};
					$scope.cafeFilter = function(item){
						return item.detail.status == 'ACTIVE' && item.detail.category == 'CAFE';
					};
					function createBinding(unitData) {
						// var channel = $scope.pusher.subscribe('sales_data');
						$scope.channel
								.bind(
										unitData.detail.id,
										function(data) {
											//console.log(data);
											$scope
													.$apply(function() {
														for ( var i in $scope.unitsReportData) {
															if ($scope.unitsReportData[i].detail.id == data.reportData.detail.id) {
																$scope.unitsReportData[i] = data.reportData;
																break;
															}
														}
													});
										});

					}

					function fetchAllSalesData() {
						$http({
							method : 'GET',
							url : APIJson.urls.analyticsData.allUnitsSales
						}).then(function success(response) {
							$scope.unitsReportData = response.data;
							$scope.unitsReportData.forEach(function(unitsData) {
								//console.log(unitsData);
								createBinding(unitsData);
							});
						}, function error(response) {
							console.log("error:" + response);
						});
					}

					function subscribeChannel() {
						$scope.envType = "dev";
						if ($location.host().indexOf("orient.chaayos.com") != -1 || $location.host().indexOf("chaudhary.chaayos.com") != -1) {
							$scope.envType = "prod";
						}
						console.log("initializing...");
						Pusher.logToConsole = true;
						$scope.pusher = new Pusher('668c61c6259750d8ab74', {
							cluster : 'eu',
							encrypted : true
						});

						$scope.channel = $scope.pusher.subscribe($scope.envType
								+ '_sales_data');

					}
				}).directive('highlighter', ['$timeout', function($timeout) {
					  return {
						    restrict: 'A',
						    scope: {
						      model: '=highlighter'
						    },
						    link: function(scope, element) {
						      scope.$watch('model', function (nv, ov) {
						    	  //console.log(scope.model);
						          element.addClass('highlight');
						          $timeout(function () {
						            element.removeClass('highlight');
						          }, 1000);
						      });
						    }
						  };
						}]);
