/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

analyticapp.controller("MainController", function($scope, $location, $http,
		$cookieStore,APIJson, AuthService) {
	
	if ($location.path() == "/dashboard") {
		$location.path("dashboard/unit");
	}
	
	$scope.init = function(){
		 $(".button-collapse").sideNav();
	};
	
	$scope.logout = function() {
		$http({
			method : 'POST',
			url : APIJson.urls.users.logout,
			data : $cookieStore.get('analyticsGlobals')
		}).then(function success(response) {
			$cookieStore.remove('analyticsGlobals');
			AuthService.setAuthorization(null);
			$location.path("/login");
		});
	};

});