/**
 * http://usejsdoc.org/
 */
analyticapp
		.controller(
				"indexctrl",
				function($rootScope, $scope, $http, $location) {
					$scope.init = function() {
						console.log("initializing...");

						$scope.unitSalesData = null;

						$scope.apcUp = false;

						Pusher.logToConsole = true;

						$scope.pusher = new Pusher('668c61c6259750d8ab74', {
							cluster : 'eu',
							encrypted : true
						});

						$scope.channel = $scope.pusher
								.subscribe('reportdata_10000');
						$scope.channel
								.bind(
										'sales_data',
										function(data) {
											$scope
													.$apply(function() {
														if ($scope.unitSalesData != null
																&& $scope.unitSalesData.data.apc.current < data.reportData.data.apc.current) {
															$scope.apcUp = true;
														} else {
															$scope.apcUp = false;
														}
														$scope.unitSalesData = data.reportData;
													});
											//console.log($scope.unitSalesData);
										});

					}
				});
