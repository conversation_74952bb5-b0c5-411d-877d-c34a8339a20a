package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.CondimentGroupManagementService;
import com.stpl.tech.master.data.model.ProductCondimentGroup;
import com.stpl.tech.master.data.model.ProductCondimentItem;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.CondimentSourceType;
import com.stpl.tech.master.domain.model.ProductCondimentGroupDTO;
import com.stpl.tech.master.domain.model.ProductCondimentItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.CONDIMENT_MANAGEMENT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@Slf4j
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + CONDIMENT_MANAGEMENT)
public class CondimentGroupManagementResource extends AbstractResources {

    @Autowired
    private CondimentGroupManagementService condimentGroupManagementService;


    @GetMapping(value = "get-all-condiment-groups")
    public List<ProductCondimentGroup> getAllCondimentGroups() throws DataNotFoundException {
        return condimentGroupManagementService.getAllCondimentGroups();
    }

    @GetMapping(value = "get-condiment-items-by-group")
    public List<ProductCondimentItem> getCondimentItemsByGroup(@RequestParam("groupId") Integer groupId) throws DataNotFoundException {
        return condimentGroupManagementService.getCondimentItemsByGroup(groupId);
    }

    @PostMapping(value = "add-condiment-group")
    public ApiResponse addCondimentGroup(@RequestBody ProductCondimentGroupDTO groupDto, HttpServletRequest request) throws DataUpdationException {
        return new ApiResponse( condimentGroupManagementService.addCondimentGroup(groupDto , getLoggedInUser(request)) );
    }

    @PostMapping(value = "update-condiment-group")
    public ApiResponse updateCondimentGroup(@RequestBody ProductCondimentGroupDTO groupDto, HttpServletRequest request) throws DataUpdationException {
        return new ApiResponse( condimentGroupManagementService.updateCondimentGroup(groupDto , getLoggedInUser(request)) );
    }


    @PostMapping(value = "add-condiment-item")
    public ApiResponse addCondimentItem(@RequestBody ProductCondimentItemDTO itemDto, HttpServletRequest request) throws DataUpdationException {
        return new ApiResponse( condimentGroupManagementService.addCondimentItem(itemDto, getLoggedInUser(request)) );
    }


    @PostMapping(value = "toggle-condiment-status")
    public ApiResponse toggleCondimentStatus(@RequestBody ProductCondimentItemDTO itemDto, HttpServletRequest request) throws DataUpdationException {
        return new ApiResponse( condimentGroupManagementService.toggleCondimentStatus(itemDto, getLoggedInUser(request)) );
    }


    @PostMapping(value = "update-condiment-item")
    public ApiResponse updateCondimentItem(@RequestBody ProductCondimentItemDTO itemDto, HttpServletRequest request) throws DataUpdationException {
        return new ApiResponse( condimentGroupManagementService.updateCondimentItem(itemDto, getLoggedInUser(request)) );
    }

    @PostMapping(value = "download-condiment-groups-excel")
    public View downloadCondimentGroupsExcel(@RequestBody(required = false) List<Integer> groupIds ) throws Exception {
        return condimentGroupManagementService.prepareCondimentGroupsExcelData(groupIds);
    }

    @GetMapping(value = "get-condiment-source-types")
    public List<String> getSourceTypes() {
        return Arrays.stream(CondimentSourceType.values())
                .map(CondimentSourceType::toString)
                .collect(Collectors.toList());
    }

}
