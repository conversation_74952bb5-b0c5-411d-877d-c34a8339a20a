package com.stpl.tech.scm.service.config;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.stpl.tech.master.core.external.interceptor.ACLInterceptor;
import com.stpl.tech.master.core.external.interceptor.ApiTokenInterceptor;
import com.stpl.tech.master.core.external.interceptor.SessionAuthInterceptor;
import com.stpl.tech.scm.core.SCMServiceConstants;

@Configuration
@EnableWebMvc
@EnableAspectJAutoProxy
@ComponentScan({"com.stpl.tech.scm.service.controller","com.stpl.tech.scm.service.printer"})
public class WebMVCConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private ACLInterceptor aclInterceptor;

    @Autowired
    private SessionAuthInterceptor sessionAuthInterceptor;

    @Autowired
    private ApiTokenInterceptor apiTokenInterceptor;

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        StringHttpMessageConverter converter = new StringHttpMessageConverter();
        converter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_PLAIN));
        converters.add(new MappingJackson2HttpMessageConverter());
        converters.add(converter);
        super.configureMessageConverters(converters);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(sessionAuthInterceptor).addPathPatterns("/**");
        registry.addInterceptor(aclInterceptor).addPathPatterns("/**");
        registry.addInterceptor(apiTokenInterceptor).addPathPatterns("/**");
    }

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver commonsMultipartResolver(){
        CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
        commonsMultipartResolver.setDefaultEncoding(SCMServiceConstants.CHARSET);
        commonsMultipartResolver.setMaxUploadSize(8388608);
        return commonsMultipartResolver;
    }

    @Bean
    WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> enableDefaultServlet() {
        return (factory) -> factory.setRegisterDefaultServlet(true);
    }
}