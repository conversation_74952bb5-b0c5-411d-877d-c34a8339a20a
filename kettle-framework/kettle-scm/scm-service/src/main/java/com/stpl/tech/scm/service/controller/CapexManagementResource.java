package com.stpl.tech.scm.service.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.scm.data.transport.model.SoPoPayload;
import com.stpl.tech.scm.domain.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.CapexManagementService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexRequestDetailData;
import com.stpl.tech.scm.data.model.CapexTemplateData;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.CAPEX_MANAGEMENT_ROOT_CONTEXT)
public class CapexManagementResource extends AbstractSCMResources {

	private static final Logger LOG = LoggerFactory.getLogger(CapexManagementResource.class);

	@Autowired
	CapexManagementService capexManagementService;

	@Autowired
	private ExcelViewGenerator excelViewGenerator;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private EnvProperties props;

	@Autowired
	private SCMCache scmCache;

	@RequestMapping(method = RequestMethod.GET, value = "getAllCapexData", produces = MediaType.APPLICATION_JSON)
	public List<CapexAuditDetail> getCapexRequestDataNew(@RequestParam(required = false) Integer unitId, @RequestParam(required = false) String version, @RequestParam(required = false) String status) {
		return capexManagementService.getCapexAuditList(unitId, version, status);
	}

	@RequestMapping(method = RequestMethod.GET, value = "getVersionList", produces = MediaType.APPLICATION_JSON)
	public Set<String> getVersionList() {
		return capexManagementService.getCapexVersionList();
	}

	@RequestMapping(method = RequestMethod.GET, value = "validateUnit")
	public boolean validateUnitForCapex(@RequestParam Integer unitId,@RequestParam String type) throws SumoException, IOException {
		return capexManagementService.validateUnitForCapex(unitId, type);
	}

	@RequestMapping(method = RequestMethod.POST, value = "createCapex")
	public View createCapexData(@RequestBody CapexRequestDetail capexRequestData) throws SumoException, IOException {
		CapexRequestDetailData capexRequestDetailData = capexManagementService.createCapexData(capexRequestData);
		return excelViewGenerator.generateCapexFIle(capexRequestDetailData);
	}

	@RequestMapping(method = RequestMethod.POST, value = "getCapexFile")
	public View getCapexFile(@RequestBody CapexAuditDetail capexAuditDetail)
			throws SumoException, IOException {
		return excelViewGenerator.getCapexFile(capexAuditDetail);
	}

	@RequestMapping(method = RequestMethod.GET, value ="change-capex-status")
	public boolean changeCapexStatus(@RequestParam Integer capexRequestId, @RequestParam String status, @RequestParam String userId, @RequestParam String comment) throws SumoException {
		return capexManagementService.changeCapexStatus(capexRequestId, status, userId, comment);
	}

	@RequestMapping(method = RequestMethod.GET, value ="initiate-closure-state")
	public String initiateClosureState(@RequestParam Integer capexRequestId, @RequestParam String status, @RequestParam String userId, @RequestParam String comment) throws SumoException {
		return capexManagementService.initiateClosureState(capexRequestId, status, userId, comment);
	}

	@RequestMapping(method = RequestMethod.POST, value = "edit-capex-request")
	public View editCapexFile(@RequestBody CapexAuditDetail capexAuditDetail)
			throws SumoException, IOException {
		CapexAuditDetailData capexAuditDetailData = capexManagementService.getCapexFileVersion(capexAuditDetail);
		return excelViewGenerator.getNewCapexFile(capexAuditDetailData,capexAuditDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "get-department-budget", consumes = MediaType.MULTIPART_FORM_DATA)
	public List<DepartmentBudgetVO> showDeptBudget(HttpServletRequest request,
			@RequestParam(value = "file") final MultipartFile file) throws IOException, SumoException {
		return capexManagementService.getDepartmentData(file);
	}

	@RequestMapping(method = RequestMethod.POST, value = "uploadCapexFile", consumes = MediaType.MULTIPART_FORM_DATA)
	public boolean uploadDebitBalanceSheet(HttpServletRequest request,
			@RequestParam(value = "file") final MultipartFile file, @RequestParam(value = "uploadedBy") final String uploadedBy) throws IOException, SumoException {
		return capexManagementService.uploadCapexSheet(file,uploadedBy);
	}

	@RequestMapping(method = RequestMethod.POST, value = "validate-capex", consumes = MediaType.MULTIPART_FORM_DATA)
	public CapexRequestDetail validateSheet(HttpServletRequest request,
			@RequestParam(value = "file") final MultipartFile file) throws IOException, SumoException {
		return capexManagementService.validateCapexSheet(file);
	}

	@RequestMapping(method = RequestMethod.GET, value = "show-capex-budget")
	public List<DepartmentBudgetVO> showDeptForApproveCapex(@RequestParam Integer capexAuditId)
			throws SumoException, IOException {
		return capexManagementService.showDepartmentBudgetForApproval(capexAuditId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "approval-status-change")
	public boolean approvalStatusBudget(@RequestParam Integer capexAuditId,@RequestParam String status,@RequestParam String type, @RequestParam String userId ,@RequestParam Boolean isRequiredForL2L3Approval)
			throws SumoException, IOException {
		return capexManagementService.approvalStatusBudget(capexAuditId, status, type, userId,isRequiredForL2L3Approval);
	}

	@RequestMapping(method = RequestMethod.POST, value = "approve-capex-budget")
	public boolean approveCapexData(@RequestBody CapexAuditDetail capexAuditData,@RequestParam Integer userId)
			throws SumoException, IOException {
		return capexManagementService.approveCapexBudget(capexAuditData,userId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "fetch-budget-details")
	public List<CapexBudgetDetail> fetchBudgetDetails(@RequestParam(required = false) Integer unitId, @RequestParam Integer capexRequestId)
			throws SumoException, IOException {
		return capexManagementService.fetchBudgetDetails(unitId,capexRequestId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-Closure-Comment")
	public String getClosureComment(@RequestParam Integer capexRequestId)
			throws SumoException, IOException {
		return capexManagementService.getClosureComment(capexRequestId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-closure-sheet", consumes = MediaType.MULTIPART_FORM_DATA)
    public boolean uploadPaymentSheet(@RequestParam(value = "file") final MultipartFile file,
                                                   @RequestParam(value = "sheetName") final String sheetName,
                                                   @RequestParam(value = "unitId") final String unitId,
                                                   @RequestParam(value = "capexRequestId") final String capexRequestId
    ) throws IOException, SumoException {
        return capexManagementService.uploadClosureSheets(file, sheetName, unitId, capexRequestId);
    }

	@RequestMapping(method = RequestMethod.GET, value = "download-closure-sheet")
	public void editCapexFile(HttpServletResponse response,@RequestParam String sheetName, @RequestParam String unitId, @RequestParam String capexRequestId)
			throws SumoException, IOException {
		String fileName = sheetName +"-"+ unitId +"-"+ capexRequestId+"."+MimeType.PDF;
		CapexTemplateData capexTemplateData = scmCache.getCapexTemplate("Renovation");
    	FileDetail fileDetail = new FileDetail(props.getS3Bucket(),props.getCapexClosureBucket()+ "/" + fileName,
    			capexTemplateData.getUrl()+"/"+"chaayosdevtest/"+ props.getCapexClosureBucket() +"/"+fileName);
		File file = fileArchiveService.getFileFromS3(props.getBasePath() + File.separator + "s3", fileDetail);
		if (file!=null) {
			response.setContentType(MimeType.PDF.value());
			response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
			byte[] bytesArray = new byte[(int) file.length()];
			response.setContentLength(bytesArray.length);
			try {
				OutputStream outputStream = response.getOutputStream();
				InputStream inputStream = new FileInputStream(file);
				int counter = 0;
				while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
					outputStream.write(bytesArray, 0, counter);
					outputStream.flush();
				}
				outputStream.close();
				inputStream.close();
			} catch (IOException e) {
				LOG.error("Encountered error while writing file to response stream",e);
				throw e;
			} finally {
				response.getOutputStream().flush();
				file.delete(); // delete the temporary file created after completing request
			}
		}

	}

	@RequestMapping(method = RequestMethod.GET, value = "get-budget-comparision",produces = MediaType.APPLICATION_JSON)
	public List<BudgetComparisionDetail> getBudgetComaprision(@RequestParam Integer unitId,@RequestParam Integer capexRequestId,
															  @RequestParam Integer currentCapexId){
		List<BudgetComparisionDetail> result = capexManagementService.getBudgetComparision(unitId,capexRequestId,currentCapexId);
		return result;
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-so-by-department",produces = MediaType.APPLICATION_JSON)
	public List<ServiceOrderShort> getSOsByDepartment(@RequestParam(required = false) Integer unitId,@RequestParam Integer capexRequestId,
															  @RequestParam Integer departmentId){
		return capexManagementService.getSOsByDepartment(unitId,capexRequestId,departmentId,null);
	}

	@GetMapping("/get-so-by-capex")
	public ServiceOrderSummary getSoByCapexId(@RequestParam Integer capexRequestId, @RequestParam(required = false) Integer vendorId){
			return  capexManagementService.getSoByCapexId(capexRequestId,vendorId);
	}

	@GetMapping("/get-po-by-capex")
	public List<PurchaseOrder> getPoByCapexId(@RequestParam Integer capexRequestId, @RequestParam(required = false) Integer vendorId){
		return  capexManagementService.getPoByCapexId(capexRequestId,vendorId);
	}

	@PostMapping("/get-capex-validation-by-so-po")
	public Map<Integer,Boolean> getCapexValidation(@RequestBody SoPoPayload soPoPayload){
		return capexManagementService.getCapexValidationBySoPo(soPoPayload.getSoPos(),soPoPayload.getType())
				.entrySet().stream().filter(e-> !e.getValue())
				.collect(Collectors.toMap(e->e.getKey(), e->e.getValue()));
	}
}
