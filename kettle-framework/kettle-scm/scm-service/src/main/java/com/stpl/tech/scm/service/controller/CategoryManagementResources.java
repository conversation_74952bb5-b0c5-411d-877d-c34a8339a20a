package com.stpl.tech.scm.service.controller;

import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;
import javax.xml.datatype.DatatypeConfigurationException;

import com.stpl.tech.scm.core.exception.SumoException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.CategoryManagementService;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;

/**
 * Created by Rahul Singh on 05-05-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.CATEGORY_MANAGEMENT_ROOT_CONTEXT)
public class CategoryManagementResources {

    @Autowired
    private CategoryManagementService categoryManagementService;

    // Category Resources

    @RequestMapping(method = RequestMethod.GET, value = "category", produces = MediaType.APPLICATION_JSON)
    public CategoryDefinition viewCategory(@RequestParam final int categoryId) {
        return categoryManagementService.viewCategory(categoryId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "categories", produces = MediaType.APPLICATION_JSON)
    public List<CategoryDefinition> viewAllCategories() {
        return categoryManagementService.viewAllCategories();
    }

    @RequestMapping(method = RequestMethod.POST, value = "category", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean addNewCategory(@RequestBody final CategoryDefinition categoryDefinition) throws SumoException {
        return categoryManagementService.addNewCategory(categoryDefinition);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean updateCategory(@RequestBody final CategoryDefinition categoryDefinition) {
        return categoryManagementService.updateCategory(categoryDefinition);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean deactivateCategory(@RequestBody final int categoryId) {
        return categoryManagementService.deactivateCategory(categoryId);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean activateCategory(@RequestBody final int categoryId) {
        return categoryManagementService.activateCategory(categoryId);
    }


    // Category Attribute Mapping Resources


    @RequestMapping(method = RequestMethod.GET, value = "category-attribute-mappings", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, List<CategoryAttributeMapping>> getAllCategoryAttributeMappings() {
        return categoryManagementService.getAllCategoryAttributeMappings();
    }

    @RequestMapping(method = RequestMethod.GET, value = "category-attribute-mapping", produces = MediaType.APPLICATION_JSON)
    public CategoryAttributeMapping getCategoryAttributeMapping(@RequestParam final int categoryAttributeMappingId) {
        return categoryManagementService.getCategoryAttributeMapping(categoryAttributeMappingId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "category-attribute-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean addCategoryAttributeMapping(@RequestBody final List<CategoryAttributeMapping> categoryAttributeMappings) throws SumoException {
        return categoryManagementService.addCategoryAttributeMapping(categoryAttributeMappings);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-attribute-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean updateCategoryAttributeMapping(@RequestBody final List<CategoryAttributeMapping> categoryAttributeMappings) {
        return categoryManagementService.updateCategoryAttributeMapping(categoryAttributeMappings);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-attribute-mapping-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean deactivateCategoryAttributeMapping(@RequestBody final int categoryAttributeMappingId) {
        return categoryManagementService.deactivateCategoryAttributeMapping(categoryAttributeMappingId);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-attribute-mapping-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean activateCategoryAttributeMapping(@RequestBody final int categoryAttributeMappingId) {
        return categoryManagementService.activateCategoryAttributeMapping(categoryAttributeMappingId);
    }


    // Category Attribute Value Resources


    @RequestMapping(method = RequestMethod.GET, value = "category-attribute-values", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, List<CategoryAttributeValue>> getAllCategoryAttributeValues() {
        return categoryManagementService.getAllCategoryAttributeValues();
    }

    @RequestMapping(method = RequestMethod.GET, value = "category-attribute-values-by-category", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, List<CategoryAttributeValue>> getAllCategoryAttributeValuesByCategory() {
        return categoryManagementService.getAllCategoryAttributeValuesByCategory();
    }

    @RequestMapping(method = RequestMethod.GET, value = "category-attribute-value", produces = MediaType.APPLICATION_JSON)
    public CategoryAttributeValue getCategoryAttributeValue(@RequestParam final int categoryAttributeValueId) {
        return categoryManagementService.getCategoryAttributeValue(categoryAttributeValueId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "category-attribute-value", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean addCategoryAttributeValue(@RequestBody final List<CategoryAttributeValue> categoryAttributeValues) throws DatatypeConfigurationException, SumoException {
        return categoryManagementService.addCategoryAttributeValue(categoryAttributeValues);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-attribute-value", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean updateCategoryAttributeValue(@RequestBody final List<CategoryAttributeValue> categoryAttributeValues) throws DatatypeConfigurationException {
        return categoryManagementService.updateCategoryAttributeValue(categoryAttributeValues);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-attribute-value-deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean deactivateCategoryAttributeValue(@RequestBody final int categoryAttributeValueId) throws DatatypeConfigurationException {
        return categoryManagementService.deactivateCategoryAttributeValue(categoryAttributeValueId);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "category-attribute-value-activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean activateCategoryAttributeValue(@RequestBody final int categoryAttributeValueId) throws DatatypeConfigurationException {
        return categoryManagementService.activateCategoryAttributeValue(categoryAttributeValueId);
    }


    // Sub Category Definition Resources


    @RequestMapping(method = RequestMethod.GET, value = "sub-category-definitions", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, SubCategoryDefinition> getAllSubCategoryDefinitions() {
        return categoryManagementService.getAllSubCategoryDefinitions();
    }
}
