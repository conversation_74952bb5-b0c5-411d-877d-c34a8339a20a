package com.stpl.tech.scm.service.controller;

import com.google.gson.Gson;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.scm.UnitDayWiseItemData;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimate;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimateRequest;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.ReferenceOrderManagementService;
import com.stpl.tech.scm.core.service.impl.OrderScheduleClone;
import com.stpl.tech.scm.core.service.impl.RegularOrderUnitBrand;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.webservice.KettleServiceClientEndpoints;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.model.ExceptionDateEntry;
import com.stpl.tech.scm.data.model.ForecastReportResponse;
import com.stpl.tech.scm.data.model.ForecastReportScmResponse;
import com.stpl.tech.scm.domain.model.EstimationShortData;
import com.stpl.tech.scm.domain.model.ExpiryProduct;
import com.stpl.tech.scm.domain.model.ForecastAuthRequest;
import com.stpl.tech.scm.domain.model.ForecastAuthResponse;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.RefCreateRequest;
import com.stpl.tech.scm.domain.model.RefCreateRequestFilters;
import com.stpl.tech.scm.domain.model.ReferenceOrder;
import com.stpl.tech.scm.domain.model.ReferenceOrderMenuItem;
import com.stpl.tech.scm.domain.model.ReferenceOrderResponse;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.notification.email.SumoErrorNotification;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.EstimationSalesDataRequest;
import com.stpl.tech.scm.service.model.ProductCategoryData;
import com.stpl.tech.scm.service.model.SalesAmountRequest;
import com.stpl.tech.scm.service.model.UnitReferenceData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.http.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 13-06-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.REFERENCE_ORDER_MANAGEMENT_ROOT_CONTEXT)
public class ReferenceOrderManagementResources extends AbstractSCMResources {

    private static final Logger LOG = LoggerFactory.getLogger(ReferenceOrderManagementResources.class);

    @Autowired
    private ReferenceOrderManagementService referenceOrderManagementService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private ExcelViewGenerator view;

    @Autowired
    private EnvProperties env;

    @RequestMapping(method = RequestMethod.GET, value = "reference-order-find", produces = MediaType.APPLICATION_JSON)
    public List<ReferenceOrder> getReferenceOrders(@RequestParam(required = false) final Integer requestingUnitId,
                                                   @RequestParam(required = true) final String startDate, @RequestParam(required = true) final String endDate,
                                                   @RequestParam(required = false) final SCMOrderStatus status,
                                                   @RequestParam(required = false) final Integer referenceOrderId) {
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getNextDate(SCMUtil.parseDate(endDate));
        return referenceOrderManagementService.getReferenceOrders(requestingUnitId, start, end, status,
                referenceOrderId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "expiry-product", produces = MediaType.APPLICATION_JSON)
    public List<IdCodeName> getExpiryProduct() {
        return referenceOrderManagementService.getExpiryProduct();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-new-expiry-products", produces = MediaType.APPLICATION_JSON)
    public List<ExpiryProduct> getNewExpiryProducts(@RequestParam Integer unitId,@RequestParam(required = false) Integer days,@RequestParam String lastDate) {
        Date date = AppUtils.getDate(lastDate,"yyyy-MM-dd");
        return referenceOrderManagementService.getNewExpiryProducts(unitId,date);
    }

    @RequestMapping(method = RequestMethod.GET, value = "reference-order", produces = MediaType.APPLICATION_JSON)
    public ReferenceOrder getReferenceOrder(@RequestParam(required = false) final Integer referenceOrderId) {
        return referenceOrderManagementService.getReferenceOrder(referenceOrderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "reference-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Integer addReferenceOrder(@RequestBody final ReferenceOrder referenceOrder) throws InventoryUpdateException, SumoException {
        return referenceOrderManagementService.addReferenceOrder(referenceOrder);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-fulfilment-units", produces = MediaType.APPLICATION_JSON)
    public Map<String, Integer> getFulfilmentUnits(@RequestParam Integer requestingUnitId) {
        LOG.info("Got request to find the fulfilment units of :: {}",masterCache.getUnit(requestingUnitId).getName());
        return referenceOrderManagementService.getFulfilmentUnits(requestingUnitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "new-reference-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public ReferenceOrderResponse addNewReferenceOrder(@RequestBody final ReferenceOrder referenceOrder) throws InventoryUpdateException, SumoException {
        if (Objects.nonNull(referenceOrder.getOrderEvent())) {
            if (referenceOrderManagementService.validateReferenceOrderTime(referenceOrder.getOrderEvent())) {
                return referenceOrderManagementService.addNewReferenceOrder(referenceOrder);
            } else {
                throw new SumoException("Can not Place Regular Order..!", "Regular Order Can not be placed after <b>9.30 AM </b>");
            }
        }
        else {
            if (AppUtils.isDev(env.getEnvType())) {
                LOG.info("Allowing to order in dev environment");
                return referenceOrderManagementService.addNewReferenceOrder(referenceOrder);
            }
            throw new SumoException("Can not Place Regular Order..!", "Regular Order Event is Missing..!");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-exception-date", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ExceptionDateEntry> generateExceptionDate(@RequestBody List<ExceptionDateEntry> dateEntries) {
        return referenceOrderManagementService.generateExceptionDateEntries(dateEntries);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-exception-date", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ExceptionDateEntry> getExceptionDate(@RequestParam String businessDate) {
        return referenceOrderManagementService.getExceptionDateEntries(AppUtils.getDateWithoutTime(AppUtils.getDate(businessDate, "yyyy-MM-dd")));
    }

	@RequestMapping(method = RequestMethod.POST, value = "reference-order-estimates", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public UnitReferenceData getReferenceOrderEstimates(@RequestBody final UnitReferenceData referenceOrder) {
		LOG.info("Getting reference order estimates for :" + referenceOrder);
		List<Integer> daysOfWeek = AppUtils.daysOfWeek(AppUtils.getDate(referenceOrder.getFulfillmentDate()),
				referenceOrder.getNoOfDays());
		List<Integer> remainingDays = AppUtils.daysOfWeek(AppUtils.getNextDate(AppUtils.getBusinessDate()),
				AppUtils.getDate(referenceOrder.getFulfillmentDate()));
		List<Integer> overalldays = new ArrayList<>();
		if (daysOfWeek != null && daysOfWeek.size() > 0) {
			overalldays.addAll(daysOfWeek);
		}
		if (remainingDays != null && remainingDays.size() > 0) {
			overalldays.addAll(remainingDays);
		}
		UnitWiseDayOfWeekWiseItemEstimate dayWiseEstimate = getUnitWiseEstimates(referenceOrder.getUnitId(),
				overalldays);
		for (Integer overallday : overalldays) {
			LOG.info("Will be calculating the sales for days : " + overallday);
		}
		UnitCategory unitCategory = masterCache.getUnitBasicDetail(referenceOrder.getUnitId()).getCategory();
		for (ProductCategoryData category : referenceOrder.getCategoryList()) {
			boolean roundup = true;
			for (ReferenceOrderMenuItem item : category.getProductList()) {
				EstimateData estimates = new EstimateData();
				for (int dayOfWeek : daysOfWeek) {
					if (dayWiseEstimate != null && dayWiseEstimate.getEstimates().containsKey(dayOfWeek)
							&& dayWiseEstimate.getEstimates().get(dayOfWeek).containsKey(item.getProductId())
							&& dayWiseEstimate.getEstimates().get(dayOfWeek).get(item.getProductId())
									.containsKey(item.getDimension())) {
						estimates.add(dayWiseEstimate.getEstimates().get(dayOfWeek).get(item.getProductId())
								.get(item.getDimension()), referenceOrder.getNoOfDays(), roundup);
						roundup = false;
					}
				}
				// Find Request IDS for ordering days
				// call stred proc with request ids
				estimates.calculate(item, unitCategory);
				for (int dayOfWeek : remainingDays) {
					if (dayWiseEstimate != null && dayWiseEstimate.getEstimates().containsKey(dayOfWeek)
							&& dayWiseEstimate.getEstimates().get(dayOfWeek).containsKey(item.getProductId())
							&& dayWiseEstimate.getEstimates().get(dayOfWeek).get(item.getProductId())
									.containsKey(item.getDimension())) {
						estimates.saleQuantity = estimates.saleQuantity.add(dayWiseEstimate.getEstimates()
								.get(dayOfWeek).get(item.getProductId()).get(item.getDimension()).getQuantity());
					}
				}
				estimates.saleQuantity = estimates.saleQuantity.setScale(0, RoundingMode.FLOOR);
				item.setSaleQuantity(estimates.saleQuantity.floatValue());
			}
		}
		return referenceOrder;
	}

	private UnitWiseDayOfWeekWiseItemEstimate getUnitWiseEstimates(int unitId, List<Integer> daysOfWeek) {
		try {
			UnitWiseDayOfWeekWiseItemEstimateRequest request = new UnitWiseDayOfWeekWiseItemEstimateRequest(unitId,
					daysOfWeek);
			LOG.info("Getting Estimates for Item Consumption from kettle for {}", request);
			String endPoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_UNIT_ITEM_ESTIMATE;
			return WebServiceHelper.exchangeWithAuth(endPoint, env.getAuthToken(), HttpMethod.POST,
					UnitWiseDayOfWeekWiseItemEstimate.class, JSONSerializer.toJSON(request), null);
		} catch (URISyntaxException e) {
			LOG.error("Error in getting estimates for unit {}", unitId, e);
			return null;
		}
	}

    @RequestMapping(method = RequestMethod.POST, value = "suggesting-order-estimates", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitReferenceData suggestOrderingEstimate(@RequestBody final UnitReferenceData referenceOrder) {

        LOG.info("Getting reference order estimates for :" + referenceOrder);
        List<Integer> productsIds = new ArrayList<>();
        for (Product product : masterCache.getAllProducts()) {
            if (referenceOrder.getBufferedCategoryList().contains(product.getType())) {
                productsIds.add(product.getId());
            }
        }
        String productIds = productsIds.stream().map(String::valueOf)
                .collect(Collectors.joining(","));
        LOG.info("product ids are: " + productIds);
        List<Date> remainingDates = new ArrayList<>();
        List<Date> orderingDates = new ArrayList<>();

        // remaining days estimation
        for (EstimationSalesDataRequest remaining : referenceOrder.getSalesData()) {
            if (remaining.getDayType().equals(AppConstants.REMAINING_DAY)) {
                remainingDates.add(AppUtils.getDate(remaining.getDate()));
//                List<String> dates = AppUtils.getListDateAfterDays(remaining.getDate(), env.getOrderDayDiff(), env.getCalculationDate());
                List<String> dates = referenceOrderManagementService.getDateWithoutException(referenceOrder.getUnitId(), remaining.getDate(),
                        env.getOrderDayDiff(), env.getCalculationDate());
                String date = dates.stream().map(String::
                        valueOf)
                        .collect(Collectors.joining(","));
                LOG.info("dates  are: " + date);
                for (SalesAmountRequest salesAmountRequest : remaining.getBrands()) {
                   //calculating split sales
                    BigDecimal deliverySale = AppUtils.divide(AppUtils.multiply(salesAmountRequest.getSaleAmount(), salesAmountRequest.getDeliverySalePercentage()), new BigDecimal(100));
                    BigDecimal dineInPercentage = AppUtils.subtract(new BigDecimal(100), salesAmountRequest.getDeliverySalePercentage());
                    BigDecimal dineInSale = AppUtils.divide(AppUtils.multiply(salesAmountRequest.getSaleAmount(), dineInPercentage), new BigDecimal(100));

                    referenceOrderManagementService.getEstimationData(salesAmountRequest.getId(), referenceOrder.getUnitId(), remaining.getDate(), salesAmountRequest.getSaleAmount(), dineInSale, deliverySale, date, 0, 0, null, AppConstants.REMAINING_DAY);
                }

                LOG.info("Will be calculating the sales for remaining day : " + remaining.getDate());
            }
        }
        //set remaining day quantity for estimation here
        if (remainingDates.size() > 0 && remainingDates != null) {
            List<Integer> remainingRequestIds = referenceOrderManagementService.getRequestIdsForEstimation(referenceOrder.getUnitId(), remainingDates);
            String requestId = remainingRequestIds.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
            LOG.info("request ids for remaining days are: " + requestId);
            List<EstimationShortData> remainingEstimationList = referenceOrderManagementService.getEstimationQuantity(referenceOrder.getUnitId(), requestId);
            addQuantity(referenceOrder, remainingEstimationList, AppConstants.REMAINING_DAY, masterCache,false, null, false, new HashMap<>(), null);
        }

        // ordering days estimation
        for (EstimationSalesDataRequest ordering : referenceOrder.getSalesData()) {
            if (ordering.getDayType().equals(AppConstants.ORDERING_DAY)) {
                orderingDates.add(AppUtils.getDate(ordering.getDate()));
//                List<String> dates = AppUtils.getListDateAfterDays(ordering.getDate(), env.getOrderDayDiff(), env.getCalculationDate());
                List<String> dates = referenceOrderManagementService.getDateWithoutException(referenceOrder.getUnitId(), ordering.getDate(),
                        env.getOrderDayDiff(), env.getCalculationDate());
                String date = dates.stream().map(String::valueOf)
                        .collect(Collectors.joining(","));
                LOG.info("dates  are: " + date);
                for (SalesAmountRequest salesAmountRequest : ordering.getBrands()) {
                    //calculating split sales
                    BigDecimal deliverySale = AppUtils.divide(AppUtils.multiply(salesAmountRequest.getSaleAmount(), salesAmountRequest.getDeliverySalePercentage()), new BigDecimal(100));
                    BigDecimal dineInPercentage = AppUtils.subtract(new BigDecimal(100), salesAmountRequest.getDeliverySalePercentage());
                    BigDecimal dineInSale = AppUtils.divide(AppUtils.multiply(salesAmountRequest.getSaleAmount(), dineInPercentage), new BigDecimal(100));

                    referenceOrderManagementService.getEstimationData(salesAmountRequest.getId(), referenceOrder.getUnitId(), ordering.getDate(), salesAmountRequest.getSaleAmount(), dineInSale, deliverySale, date, referenceOrder.getCategoryBufferPercentage(), env.getBuffer(), productIds, AppConstants.ORDERING_DAY);
                }
                LOG.info("Will be calculating the sales for ordering day : " + ordering.getDate());
            }
        }

        //set ordering day quantity for estimation here
        if (orderingDates.size() > 0 && orderingDates != null) {
            List<Integer> orderingRequestIds = referenceOrderManagementService.getRequestIdsForEstimation(referenceOrder.getUnitId(), orderingDates);
            String orderingIds = orderingRequestIds.stream().map(String::valueOf)
                    .collect(Collectors.joining(","));
            LOG.info("request ids for ordering days  are: " + orderingIds);
            List<EstimationShortData> orderingEstimateList = referenceOrderManagementService.getEstimationQuantity(referenceOrder.getUnitId(), orderingIds);
            addQuantity(referenceOrder, orderingEstimateList, AppConstants.ORDERING_DAY, masterCache,false, null, false, new HashMap<>(), null);
        }
        return referenceOrder;
    }

    @RequestMapping(method = RequestMethod.POST, value = "suggesting-order-estimates-v1", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitReferenceData suggestOrderingEstimateV1(@RequestBody final UnitReferenceData referenceOrder) throws IOException, SumoException {
        ForecastAuthRequest forecastAuthRequest=new ForecastAuthRequest(env.getFountain9UserName(),env.getFountain9Password(),env.getFountain9IsSuperUser());
        LOG.info("Getting Token from Fountain9 for Unit ::{}",referenceOrder.getUnitId());
        ForecastAuthResponse forecastAuthResponse= WebServiceHelper.postWithAuth(env.getFountain9AccessTokenUrl(),null,forecastAuthRequest,ForecastAuthResponse.class);
        LOG.info("Received Token from Fountain9 for Unit ::{}",referenceOrder.getUnitId());
        RefCreateRequest refCreateRequest = getRefCreateRequest(referenceOrder);
        Map<String,Integer> uriVariables=new HashMap<>();
        uriVariables.put("dataset_id",4);
        LOG.info("Calling Fountain 9 For Predictive Quantity for Unit ::{}",referenceOrder.getUnitId());
        HttpResponse httpResponse;
        LOG.info("For Menu Suggestions Request Body is : {}",new Gson().toJson(refCreateRequest));
        String endPoint = "";
        try {
            endPoint = getEndPoint(uriVariables);
            httpResponse = WebServiceHelper.postWithBearer(env.getFountain9ForecastReportUrl(), forecastAuthResponse.getAccess(), refCreateRequest, uriVariables, false);
        }
        catch (Exception e) {
            sendF9ErrorNotification(referenceOrder, refCreateRequest, endPoint, e, false);
            referenceOrder.setTotalSale((float) 0);
            return referenceOrder;
        }
        LOG.info("### Received response from fountain 9 for Menu Products ###");
        Pair<String, List<ForecastReportResponse>> report = referenceOrderManagementService.getForecastReport(httpResponse,refCreateRequest);
        referenceOrder.setRefreshDate(report.getKey());
        List<ForecastReportResponse> forecastReportResponseList = report.getValue();
        UnitReferenceData referenceOrder1 = closeHttpRequest(referenceOrder, refCreateRequest, httpResponse, endPoint, false);
        if (Objects.nonNull(referenceOrder1)) {
            return referenceOrder1;
        }
        LOG.info("Calling for F9 Scm Suggestions ::: ");
        HttpResponse httpScmResponse;
        refCreateRequest = getRefCreateRequestScm(refCreateRequest);
        LOG.info("For SCM Suggestions Request Body is : {}",new Gson().toJson(refCreateRequest));
        try {
            httpScmResponse = WebServiceHelper.postWithBearer(env.getFountain9ForecastReportUrl(), forecastAuthResponse.getAccess(), refCreateRequest, uriVariables, false);
        }
        catch (Exception e) {
            sendF9ErrorNotification(referenceOrder, refCreateRequest, endPoint, e, true);
            referenceOrder.setTotalSale((float) 0);
            return referenceOrder;
        }
        LOG.info("### Received response from fountain 9 for Menu Products ###");
        List<ForecastReportScmResponse> forecastScmReportResponseList = referenceOrderManagementService.getForecastScmReport(httpScmResponse,refCreateRequest,report.getKey(),
                referenceOrder.getRequestedBy());
        UnitReferenceData referenceOrder2 = closeHttpRequest(referenceOrder, refCreateRequest, httpScmResponse, endPoint, true);
        if (Objects.nonNull(referenceOrder2)) {
            return referenceOrder2;
        }
        LOG.info("Processing Fountain9 Predictive Regular Order for Unit ::{}",referenceOrder.getUnitId());
        List<String> remainingDays = new ArrayList<>();
        List<String> orderingDays = new ArrayList<>();
        List<Date> remDays = new ArrayList<>();
        List<Date> orderDays = new ArrayList<>();
        List<EstimationShortData> estimationShortDataRemainingList = new ArrayList<>();
        List<EstimationShortData> estimationShortDataOrderingList = new ArrayList<>();
        setDays(remainingDays,orderingDays,referenceOrder,remDays,orderDays);
        Collections.sort(remDays);
        Collections.sort(orderDays);
        Map<String, Map<String, Boolean>> dayWiseBrandStatus= referenceOrderManagementService.getDayWiseBrandStatus(referenceOrder.getUnitId());
        referenceOrder.setScmSuggestions(getScmSuggestionsMap(forecastScmReportResponseList, dayWiseBrandStatus, referenceOrder));
        setSalesData(forecastReportResponseList,referenceOrder,remainingDays, dayWiseBrandStatus);
        referenceOrderManagementService.saveSuggestions(forecastReportResponseList,forecastScmReportResponseList,referenceOrder.getUnitId());
        referenceOrderManagementService.convertForecastEstimationDetail(forecastReportResponseList,referenceOrder.getUnitId(),remainingDays,
                estimationShortDataRemainingList,estimationShortDataOrderingList);
        Boolean overrideMpq = Objects.nonNull(referenceOrder.getBrandName()) && ( referenceOrder.getBrandName().equalsIgnoreCase("Ghee and Turmeric") || referenceOrder.getBrandName().equalsIgnoreCase("BOTH"));
        LOG.info("Flag of over ride MPQ is : {}",overrideMpq);
        addQuantity(referenceOrder, estimationShortDataRemainingList, AppConstants.REMAINING_DAY, masterCache,true,remDays,overrideMpq, dayWiseBrandStatus, remDays.get(remDays.size() - 1));
        addQuantity(referenceOrder, estimationShortDataOrderingList, AppConstants.ORDERING_DAY, masterCache,true,orderDays,overrideMpq, dayWiseBrandStatus, orderDays.get(orderDays.size() - 1));
        return referenceOrder;
    }

    private UnitReferenceData closeHttpRequest(UnitReferenceData referenceOrder, RefCreateRequest refCreateRequest, HttpResponse httpResponse, String endPoint, Boolean isScm)
            throws IOException, SumoException {
        try {
            if (httpResponse.getStatusLine().getStatusCode() == 200 && httpResponse.getEntity() != null && httpResponse.getEntity().getContent() != null) {
                httpResponse.getEntity().getContent().close();
            }
            else {
                sendF9ErrorNotification(referenceOrder, refCreateRequest, endPoint, httpResponse, isScm);
                Objects.requireNonNull(httpResponse.getEntity()).getContent().close();
                String messsage = "### Error Occurred while getting" + (isScm ? " SCM " : " MENU ") + "Suggestions from Fountain 9 API ####";
                LOG.info(messsage);
                referenceOrder.setTotalSale((float) 0);
                return referenceOrder;
            }
        } catch (Exception e) {
            httpResponse.getEntity().getContent().close();
            LOG.error("Error in closing response content", e);
            throw new SumoException(httpResponse.getStatusLine().getReasonPhrase());
        }
        return null;
    }

    private Map<String, Map<Integer, BigDecimal>> getScmSuggestionsMap(List<ForecastReportScmResponse> forecastScmReportResponseList, Map<String, Map<String, Boolean>> dayWiseBrandStatus, UnitReferenceData referenceData) {
        try {
            Map<String, Map<Integer, BigDecimal>> result = new HashMap<>();
            LOG.info("Day wise Brand Status is : {}", new Gson().toJson(dayWiseBrandStatus));
            Map<String, Boolean> finalBrandWiseStatus = getFinalBrandWiseStatus(referenceData, dayWiseBrandStatus);

            LOG.info("Final Day wise Status for SCM is : {}", new Gson().toJson(finalBrandWiseStatus));


            for (ForecastReportScmResponse scmResponse : forecastScmReportResponseList) {
                try {
                    String dayType = AppUtils.getFormattedTime(scmResponse.getDate(), "EEEE").toUpperCase(Locale.ROOT);
                    if (finalBrandWiseStatus.containsKey(dayType)) {
                        if (!finalBrandWiseStatus.get(dayType)) {
                            LOG.info("Cafe : {} is not functional on {} for SCM", masterCache.getUnit(referenceData.getUnitId()).getName(), dayType);
                            scmResponse.setNonFunctional(AppConstants.YES);
                            scmResponse.setPredictedFinal(BigDecimal.ZERO);
                            scmResponse.setSafetyStock(BigDecimal.ZERO);
                        }
                    } else {
                        LOG.info("Can not find the cafe is functional or not for day type : {} for SCM", dayType);
                        scmResponse.setNonFunctional(AppConstants.YES);
                        scmResponse.setPredictedFinal(BigDecimal.ZERO);
                        scmResponse.setSafetyStock(BigDecimal.ZERO);
                    }
                } catch (Exception e) {
                    LOG.error("Exception occurred while checking with cafe day wise functional status for SCM products");
                }

                String dayType = AppUtils.getFormattedTime(scmResponse.getDate(), "yyyy-MM-dd");
                BigDecimal total = scmResponse.getPredictedFinal().add(scmResponse.getSafetyStock());
                if (result.containsKey(dayType)) {
                    Map<Integer, BigDecimal> innerMap = result.get(dayType);
                    if (innerMap.containsKey(scmResponse.getSkuId())) {
                        innerMap.put(scmResponse.getSkuId(), innerMap.get(scmResponse.getSkuId()).add(total));
                        result.put(dayType, innerMap);
                    } else {
                        innerMap.put(scmResponse.getSkuId(), total);
                        result.put(dayType, innerMap);
                    }
                } else {
                    Map<Integer, BigDecimal> innerMap = new HashMap<>();
                    innerMap.put(scmResponse.getSkuId(), total);
                    result.put(dayType, innerMap);
                }
            }
            return result;
        } catch (Exception e) {
            LOG.error("Exception Occurred While Making map of Scm Suggestions :: ",e);
            return null;
        }
    }

    private void sendF9ErrorNotification(UnitReferenceData referenceOrder, RefCreateRequest refCreateRequest, String endPoint, HttpResponse httpResponse, Boolean isScm) {
        try {
            String subject = "Failed to get " + (isScm ? "SCM " : "MENU") + " Suggestions" +
                    "from Fountain 9 for unit : " + masterCache.getUnit(referenceOrder.getUnitId()).getName() + " [" + referenceOrder.getUnitId() + "]";
            String errorMessage = httpResponse.getStatusLine().getStatusCode() + " - " + httpResponse.getStatusLine().getReasonPhrase();
            SumoErrorNotification error = new SumoErrorNotification(subject, errorMessage, env.getEnvType() , endPoint, new Gson().toJson(refCreateRequest));
            updateEmailsAndSlack(error, referenceOrder.getUnitId());
            List<String> f9Emails = new ArrayList<>(Arrays.asList("<EMAIL>","<EMAIL>","<EMAIL>"));
            error.getToEmailList().addAll(f9Emails);
            error.sendEmail();
        }
        catch (Exception exe) {
            LOG.error("Error Occurred While Sending Email and slack Notification for F9 Failure :: ", exe);
        }
    }

    private void sendF9ErrorNotification(UnitReferenceData referenceOrder, RefCreateRequest refCreateRequest, String endPoint, Exception e, Boolean isScm) {
        try {
            String subject = "Failed to get " + (isScm ? "SCM " : "MENU") + " Suggestions" +
                    " from Fountain 9 for unit : " + masterCache.getUnit(referenceOrder.getUnitId()).getName() + " [" + referenceOrder.getUnitId() + "]";
            SumoErrorNotification error = new SumoErrorNotification(subject, e.getMessage(), e, env.getEnvType() , endPoint, new Gson().toJson(refCreateRequest));
            updateEmailsAndSlack(error, referenceOrder.getUnitId());
            List<String> f9Emails = new ArrayList<>(Arrays.asList("<EMAIL>","<EMAIL>","<EMAIL>"));
            error.getToEmailList().addAll(f9Emails);
            error.sendEmail();
        }
        catch (Exception exe) {
            LOG.error("Error Occurred While Sending Email and slack Notification for F9 Failure :: ", exe);
        }
    }

    public void updateEmailsAndSlack(SumoErrorNotification error, int unitId) {
        List<String> emailList = new ArrayList<String>();
        List<String> slackList = new ArrayList<String>();
        Unit u = masterCache.getUnit(unitId);
        if (Objects.nonNull(u.getUnitEmail())) {
            emailList.add(u.getUnitEmail());
        }
        if (Objects.nonNull(u.getChannel())) {
            slackList.add(u.getChannel());
        }
        addSlackAndEmail(u.getManagerId(), emailList, slackList);
        if (u.getCafeManager() != null) {
            addSlackAndEmail(u.getCafeManager().getId(), emailList, slackList);
        }
        error.setToEmailList(emailList);
    }

    private void addSlackAndEmail(Integer empId, List<String> emailList, List<String> slackList) {
        EmployeeBasicDetail e2 = masterCache.getEmployeeBasicDetail(empId);
        if (e2 != null && !AppUtils.isBlank(e2.getEmailId())) {
            emailList.add(e2.getEmailId());
            if (!AppUtils.isBlank(e2.getSlackChannel())) {
                slackList.add(e2.getSlackChannel());
            }
        }
    }

    private String getEndPoint(Map<String, Integer> uriVariables) {
        String endpoint = env.getFountain9ForecastReportUrl();
        if (uriVariables != null) {
            endpoint += "?";
            StringBuilder endpointBuilder = new StringBuilder(endpoint);
            for (String key : uriVariables.keySet()) {
                endpointBuilder.append(key).append("=").append(uriVariables.get(key).toString());
            }
            endpoint = endpointBuilder.toString();
        }
        return endpoint;
    }

    private void setDays(List<String> remainingDays, List<String> orderingDays, UnitReferenceData referenceData, List<Date> remDays, List<Date> orderDays){
        for(EstimationSalesDataRequest estimationSalesData : referenceData.getSalesData()){
            if(AppConstants.REMAINING_DAY.equals(estimationSalesData.getDayType())){
                remainingDays.add(AppUtils.generateSimpleDateFormat(estimationSalesData.getDate()));
                remDays.add(estimationSalesData.getDate());
            }else{
                orderingDays.add(AppUtils.generateSimpleDateFormat(estimationSalesData.getDate()));
                orderDays.add(estimationSalesData.getDate());
            }
        }
    }
    private void setSalesData(List<ForecastReportResponse> forecastReportResponseList, UnitReferenceData referenceData, List<String> days, Map<String, Map<String, Boolean>> dayWiseBrandStatus){
        Map<String,BigDecimal> dineInSale= new HashMap<>();
        Map<String,BigDecimal> deliverySale= new HashMap<>();
        for(ForecastReportResponse forecastReportResponse : forecastReportResponseList){
            try {
                if (dayWiseBrandStatus.containsKey(forecastReportResponse.getBrand())) {
                    Map<String , Boolean> innerMap = dayWiseBrandStatus.get(forecastReportResponse.getBrand());
                    String dayType = AppUtils.getFormattedTime(forecastReportResponse.getDate(),"EEEE").toUpperCase(Locale.ROOT);
                    if (innerMap.containsKey(dayType)) {
                        if (!innerMap.get(dayType)) {
                            LOG.info("Cafe : {} is not functional on {} for brand : {}",masterCache.getUnit(referenceData.getUnitId()).getName(),dayType,forecastReportResponse.getBrand());
                            forecastReportResponse.setNonFunctional(AppConstants.YES);
                            forecastReportResponse.setPredictedFinal(BigDecimal.ZERO);
                            forecastReportResponse.setSafetyStock(BigDecimal.ZERO);
                            continue;
                        }
                    } else {
                        LOG.info("Can not find the cafe is functional or not for brand : {} and for day type : {}",forecastReportResponse.getBrand(),dayType);
                        forecastReportResponse.setNonFunctional(AppConstants.YES);
                        forecastReportResponse.setPredictedFinal(BigDecimal.ZERO);
                        forecastReportResponse.setSafetyStock(BigDecimal.ZERO);
                        continue;
                    }
                } else {
                    LOG.info("Can not find the cafe is functional or not for Brad : {}", forecastReportResponse.getBrand());
                    forecastReportResponse.setNonFunctional(AppConstants.YES);
                    forecastReportResponse.setPredictedFinal(BigDecimal.ZERO);
                    forecastReportResponse.setSafetyStock(BigDecimal.ZERO);
                    continue;
                }
            } catch (Exception e) {
                LOG.error("Exception occurred while checking with cafe brand day wise functional status");
            }
            if(days.contains(AppUtils.generateSimpleDateFormat(forecastReportResponse.getDate()))){
                continue;
            }
            String brand=forecastReportResponse.getBrand();
            if((AppConstants.DELIVERY).equals(forecastReportResponse.getOrderSource())){
                if(deliverySale.containsKey(brand)){
                    deliverySale.put(brand,forecastReportResponse.getValue().add(deliverySale.get(brand)));
                }else{
                    deliverySale.put(brand,forecastReportResponse.getValue());
                }
            }else{
                if(dineInSale.containsKey(brand)){
                    dineInSale.put(brand,forecastReportResponse.getValue().add(dineInSale.get(brand)));
                }else{
                    dineInSale.put(brand,forecastReportResponse.getValue());
                }
            }
        }
        BigDecimal total = BigDecimal.ZERO;
        for (BigDecimal saleData : dineInSale.values()) {
            total = total.add(saleData);
        }
        for (BigDecimal saleData : deliverySale.values()) {
            total = total.add(saleData);
        }
        referenceData.setTotalSale(total.floatValue());
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            LOG.info("### Got Zero Sale Data from Fountain 9 ###");
            referenceData.setTotalSale((float) -1);
        }
        referenceData.setDeliverySale(deliverySale);
        referenceData.setDineInSale(dineInSale);
    }

    private RefCreateRequest getRefCreateRequestScm(RefCreateRequest request) {
        RefCreateRequest refCreateRequest=new RefCreateRequest();
        refCreateRequest.setFilters(request.getFilters());
        refCreateRequest.setCalculate_safety_stock(true);
        refCreateRequest.setMax_capping_safety_stock_multiplier("10");
        refCreateRequest.setLevel("cafe_id");
        refCreateRequest.setMetric(env.getFountain9Metric());
        refCreateRequest.setNumber_of_days(request.getNumber_of_days());
        refCreateRequest.setTime_granularity(env.getFountain9TimeGranularity());
        refCreateRequest.setStart_date(request.getStart_date());
        refCreateRequest.setEnd_date(request.getEnd_date());
        List<String> dimensionsToBeAggregatedOn = Arrays.asList(env.getDimensionsAggregatedOn().split(","));
        refCreateRequest.setDimensions_to_be_aggregated_on(dimensionsToBeAggregatedOn);
        List<String> columns = Arrays.asList(env.getFountain9ScmColumns().split(","));
        refCreateRequest.setColumns(columns);
        refCreateRequest.setBom_only(true);
        return refCreateRequest;
    }

    private RefCreateRequest getRefCreateRequest(UnitReferenceData referenceOrder) {
        RefCreateRequest refCreateRequest=new RefCreateRequest();
        RefCreateRequestFilters refCreateRequestFilters=new RefCreateRequestFilters();
        List<String> cafes=new ArrayList<>();
        cafes.add(masterCache.getUnit(referenceOrder.getUnitId()).getCostCenterName());
        refCreateRequestFilters.setCafe_id(cafes);
        if (referenceOrder.getBrandName() != null) {
            refCreateRequestFilters.setBrand(Collections.singletonList(referenceOrder.getBrandName()));
        }
        List<EstimationSalesDataRequest> salesData= referenceOrder.getSalesData();
        refCreateRequest.setStart_date(AppUtils.getSQLFormattedDate(salesData.get(0).getDate()));
        refCreateRequest.setEnd_date(AppUtils.getSQLFormattedDate(salesData.get(salesData.size()-1).getDate()));
        refCreateRequest.setMetric(env.getFountain9Metric());
        refCreateRequest.setTime_granularity(env.getFountain9TimeGranularity());
        refCreateRequest.setNumber_of_days(referenceOrder.getNoOfDays());
        refCreateRequest.setFilters(refCreateRequestFilters);
        refCreateRequest.setCalculate_safety_stock(true);
        String maxCap;
        if (Objects.nonNull(referenceOrder.getBrandName())) {
            maxCap = referenceOrderManagementService.getMaxCap(referenceOrder.getUnitId(),referenceOrder.getBrandName());
        }
        else {
            maxCap = "20";
        }
        List<String> cols = Arrays.asList(env.getFountain9Columns().split(","));
        List<String> columns = new ArrayList<>(cols);
        if (!maxCap.equalsIgnoreCase("-1")) {
            refCreateRequest.setLevel("cafe_id");
            refCreateRequest.setMax_capping_safety_stock_multiplier(maxCap);
            columns.add("safety_stock");
        }
        refCreateRequest.setColumns(columns);
        return refCreateRequest;
    }

    @RequestMapping(method = RequestMethod.POST, value = "sales-percentage", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitReferenceData getSalesPercentage(@RequestBody final UnitReferenceData referenceOrder) {
        for (EstimationSalesDataRequest remaining : referenceOrder.getSalesData()) {
            List<String> dates = referenceOrderManagementService.getDateWithoutException(referenceOrder.getUnitId(), remaining.getDate(),
                    env.getOrderDayDiff(), env.getCalculationDate());
            String date = dates.stream().map(String::
                    valueOf)
                    .collect(Collectors.joining(","));
            LOG.info("dates  are: " + date);
            for (SalesAmountRequest salesAmountRequest : remaining.getBrands()) {
                BigDecimal data = referenceOrderManagementService.getSalesPercentage(referenceOrder.getUnitId(), salesAmountRequest.getId(), date);
                salesAmountRequest.setDeliverySalePercentage(data);
            }
        }
        return referenceOrder;
    }

    @RequestMapping(method = RequestMethod.POST, value = "sales-percentage-for-units", consumes = MediaType.APPLICATION_JSON)
    public View getSalesPercentage(@RequestBody final List<UnitReferenceData> referenceOrderUnits) {
        for(UnitReferenceData referenceOrder : referenceOrderUnits) {
            for (EstimationSalesDataRequest remaining : referenceOrder.getSalesData()) {
                List<String> dates = referenceOrderManagementService.getDateWithoutException(referenceOrder.getUnitId(), remaining.getDate(),
                    env.getOrderDayDiff(), env.getCalculationDate());
                String date = dates.stream().map(String::
                    valueOf)
                    .collect(Collectors.joining(","));
                LOG.info("dates  are: " + date);
                for (SalesAmountRequest salesAmountRequest : remaining.getBrands()) {
                    BigDecimal totalDeliveryPercentage = referenceOrderManagementService.getSalesPercentage(referenceOrder.getUnitId(), salesAmountRequest.getId(), date);
                    BigDecimal totalAmount = referenceOrderManagementService.getSalesAmount(referenceOrder.getUnitId(), salesAmountRequest.getId(), date);
                    salesAmountRequest.setDeliverySalePercentage(totalDeliveryPercentage);
                    salesAmountRequest.setSaleAmount(totalAmount);
                }
            }
        }
        return view.getChaayosAndGntSales(referenceOrderUnits);
    }

    public static void addQuantity(UnitReferenceData referenceData, List<EstimationShortData> estimateRequestDataList, String dayType, MasterDataCache masterDataCache,
                                   Boolean isFountain9, List<Date> dates, Boolean overrideMpq, Map<String, Map<String, Boolean>> dayWiseBrandStatus, Date lastDate) {
        Map<String, Boolean> finalBrandWiseStatus = getFinalBrandWiseStatus(referenceData, dayWiseBrandStatus);
        for (EstimationShortData estimateRequestData : estimateRequestDataList) {
            boolean flag = false;
            try {
                if (estimateRequestData.getQuantity() >= 0) {
                    for (ProductCategoryData productCategoryData : referenceData.getCategoryList()) {
                        if (estimateRequestData.getCategoryId() == productCategoryData.getId()) {
                            for (ReferenceOrderMenuItem referenceOrderMenuItem : productCategoryData.getProductList()) {
                                if (referenceOrderMenuItem.getProductId() == estimateRequestData.getProductId() && referenceOrderMenuItem.getDimension().equals(estimateRequestData.getDimension())) {
                                    if (dayType.equals(AppConstants.ORDERING_DAY)) {
                                        if (isFountain9) {
                                            Boolean status = finalBrandWiseStatus.getOrDefault(AppUtils.getFormattedTime(AppUtils.getDate(estimateRequestData.getDate(), "yyyy-MM-dd"),"EEEE").toUpperCase(Locale.ROOT), false);
                                            if (status) {
                                                Float reqQuantity = referenceOrderMenuItem.getRequestedQuantity();
                                                if (Objects.nonNull(reqQuantity)) {
                                                    referenceOrderMenuItem.setRequestedQuantity(reqQuantity + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                } else {
                                                    referenceOrderMenuItem.setRequestedQuantity(Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                }
                                                Float quantity = referenceOrderMenuItem.getQuantity();
                                                if (Objects.nonNull(quantity)) {
                                                    referenceOrderMenuItem.setQuantity(quantity + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                } else {
                                                    referenceOrderMenuItem.setQuantity(Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                }

                                                BigDecimal originalQuantity = referenceOrderMenuItem.getOriginalOrderingQuantity();
                                                if (Objects.nonNull(originalQuantity)) {
                                                    referenceOrderMenuItem.setOriginalOrderingQuantity(originalQuantity.add(estimateRequestData.getOriginalQuantity()));
                                                } else {
                                                    referenceOrderMenuItem.setOriginalOrderingQuantity(estimateRequestData.getOriginalQuantity());
                                                }


                                                Map<String, Float> map = referenceOrderMenuItem.getDateOrderings();
                                                if (map.containsKey(estimateRequestData.getDate())) {
                                                    Float qty = map.get(estimateRequestData.getDate());
                                                    map.put(estimateRequestData.getDate(), qty + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                } else {
                                                    map.put(estimateRequestData.getDate(), Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                }
                                                referenceOrderMenuItem.setDateOrderings(map);
                                            }
                                        } else {
                                            Float reqQuantity = referenceOrderMenuItem.getRequestedQuantity();
                                            if (Objects.nonNull(reqQuantity)) {
                                                referenceOrderMenuItem.setRequestedQuantity(reqQuantity + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                            } else {
                                                referenceOrderMenuItem.setRequestedQuantity(Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                            }

                                            Float quantity = referenceOrderMenuItem.getQuantity();
                                            if (Objects.nonNull(quantity)) {
                                                referenceOrderMenuItem.setQuantity(quantity + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                            } else {
                                                referenceOrderMenuItem.setQuantity(Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                            }
                                        }
                                    } else if (dayType.equals(AppConstants.REMAINING_DAY)) {
                                        if (isFountain9) {
                                            Boolean status = finalBrandWiseStatus.getOrDefault(AppUtils.getFormattedTime(AppUtils.getDate(estimateRequestData.getDate(), "yyyy-MM-dd"),"EEEE").toUpperCase(Locale.ROOT), false);
                                            if (status) {
                                                Float salesQuantity = referenceOrderMenuItem.getSaleQuantity();
                                                if (Objects.nonNull(salesQuantity)) {
                                                    referenceOrderMenuItem.setSaleQuantity(salesQuantity + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                } else {
                                                    referenceOrderMenuItem.setSaleQuantity(Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                }

                                                BigDecimal originalQuantity = referenceOrderMenuItem.getOriginalSaleQuantity();
                                                if (Objects.nonNull(originalQuantity)) {
                                                    referenceOrderMenuItem.setOriginalSaleQuantity(originalQuantity.add(estimateRequestData.getOriginalQuantity()));
                                                } else {
                                                    referenceOrderMenuItem.setOriginalSaleQuantity(estimateRequestData.getOriginalQuantity());
                                                }

                                                Map<String, Float> map = referenceOrderMenuItem.getDateRemaining();
                                                if (map.containsKey(estimateRequestData.getDate())) {
                                                    Float qty = map.get(estimateRequestData.getDate());
                                                    map.put(estimateRequestData.getDate(), qty + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                } else {
                                                    map.put(estimateRequestData.getDate(), Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                                }
                                                referenceOrderMenuItem.setDateRemaining(map);
                                            }
                                        }
                                        else {
                                            Float quantity = referenceOrderMenuItem.getSaleQuantity();
                                            if (Objects.nonNull(quantity)) {
                                                referenceOrderMenuItem.setSaleQuantity(quantity + Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                            } else {
                                                referenceOrderMenuItem.setSaleQuantity(Float.parseFloat(estimateRequestData.getQuantity().toString()));
                                            }
                                        }
                                    }
                                    ProductBasicDetail pd = masterDataCache.getProductBasicDetail(referenceOrderMenuItem.getProductId());
                                    if (referenceOrderMenuItem.getRequestedQuantity() == 0 && estimateRequestData.getCategoryId() == 9 && pd.getStatus().equals(ProductStatus.ACTIVE)) {
                                        if (isFountain9) {
                                            if (finalBrandWiseStatus.getOrDefault(AppUtils.getFormattedTime(AppUtils.getDate(estimateRequestData.getDate(), "yyyy-MM-dd"),"EEEE").toUpperCase(Locale.ROOT), false)) {
                                                referenceOrderMenuItem.setRequestedQuantity(2);
                                            }
                                        } else {
                                            referenceOrderMenuItem.setRequestedQuantity(2);
                                        }
                                    }
                                    flag = true;
                                    break;
                                }
                            }
                        }
                        if (flag) {
                            break;
                        }

                    }
                }
            } catch (Exception e) {
                LOG.error("Exception Caught For Product {}", estimateRequestData.getProductId());
            }
        }

        // changing the last date values
        if (isFountain9) {
            String dayName = AppUtils.getFormattedTime(lastDate,"EEEE").toUpperCase(Locale.ROOT);
            for (ProductCategoryData productCategoryData : referenceData.getCategoryList()) {
                for (ReferenceOrderMenuItem referenceOrderMenuItem : productCategoryData.getProductList()) {
                    String lastOrderingDate = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                    if (dayType.equals(AppConstants.REMAINING_DAY)) {
                        BigDecimal originalSaleQty = referenceOrderMenuItem.getOriginalSaleQuantity();
                        Float saleQuantity = referenceOrderMenuItem.getSaleQuantity();
                        if (Objects.nonNull(originalSaleQty) && Objects.nonNull(saleQuantity)) {
                            //mpq -> minimum principal Quantity
                            BigDecimal mpqRemainingQuantity = referenceOrderMenuItem.getOriginalSaleQuantity().subtract(BigDecimal.valueOf(referenceOrderMenuItem.getSaleQuantity()));
                            mpqRemainingQuantity = mpqRemainingQuantity.setScale(0, RoundingMode.HALF_UP);
                            LOG.info("MPQ Remaining quantity for product : {} is : {}", referenceOrderMenuItem.getProductName(),mpqRemainingQuantity);
                            referenceOrderMenuItem.setSaleQuantity(referenceOrderMenuItem.getSaleQuantity() + mpqRemainingQuantity.floatValue());
                            Map<String, Float> remainingMap = referenceOrderMenuItem.getDateRemaining();
                            if (remainingMap.containsKey(lastOrderingDate) && finalBrandWiseStatus.getOrDefault(dayName, false)) {
                                remainingMap.put(lastOrderingDate, remainingMap.get(lastOrderingDate) + mpqRemainingQuantity.floatValue());
                                referenceOrderMenuItem.setDateRemaining(remainingMap);
                            }
                        }

                        if (Objects.nonNull(referenceOrderMenuItem.getDateRemaining())) {
                            Map<String, Float> remainingMap = referenceOrderMenuItem.getDateRemaining();
                            Float totalSale = (float) 0;
                            for (Map.Entry<String, Float> entry : remainingMap.entrySet()) {
                                if (Objects.nonNull(overrideMpq) && overrideMpq && Objects.nonNull(referenceOrderMenuItem.getDimension()) &&
                                        referenceOrderMenuItem.getDimension().equalsIgnoreCase("Family")
                                        && entry.getValue() <= 3) {
                                    LOG.info("Over riding the MPQ(Remaining Day) for product : {} and dimension : {}",referenceOrderMenuItem.getProductName(),referenceOrderMenuItem.getDimension());
                                    remainingMap.put(entry.getKey(),entry.getValue() - 1);
                                    if (remainingMap.get(entry.getKey()) < 0) {
                                        remainingMap.put(entry.getKey(),(float) 0);
                                    }
                                }
                                totalSale += entry.getValue();
                            }
                            referenceOrderMenuItem.setSaleQuantity(totalSale);
                            referenceOrderMenuItem.setDateRemaining(remainingMap);
                        }
                    } else {
                        BigDecimal orderingQty = referenceOrderMenuItem.getOriginalOrderingQuantity();
                        Float qty = referenceOrderMenuItem.getQuantity();
                        if (Objects.nonNull(orderingQty) && Objects.nonNull(qty)) {
                            BigDecimal mpqOrderingQuantity = referenceOrderMenuItem.getOriginalOrderingQuantity().subtract(BigDecimal.valueOf(referenceOrderMenuItem.getQuantity()));
                            mpqOrderingQuantity = mpqOrderingQuantity.setScale(0, RoundingMode.HALF_UP);
                            LOG.info("MPQ Ordering quantity for product : {} is : {}",referenceOrderMenuItem.getProductName(), mpqOrderingQuantity);
                            referenceOrderMenuItem.setRequestedQuantity(referenceOrderMenuItem.getRequestedQuantity() + mpqOrderingQuantity.floatValue());
                            referenceOrderMenuItem.setQuantity(referenceOrderMenuItem.getQuantity() + mpqOrderingQuantity.floatValue());
                            Map<String, Float> orderingMap = referenceOrderMenuItem.getDateOrderings();
                            if (orderingMap.containsKey(lastOrderingDate) && finalBrandWiseStatus.getOrDefault(dayName, false)) {
                                orderingMap.put(lastOrderingDate, orderingMap.get(lastOrderingDate) + mpqOrderingQuantity.floatValue());
                                referenceOrderMenuItem.setDateOrderings(orderingMap);
                            }
                        }

                        if (Objects.nonNull(referenceOrderMenuItem.getDateOrderings())) {
                            Map<String, Float> orderingMap = referenceOrderMenuItem.getDateOrderings();
                            Float totalOrdering = (float) 0;
                            for (Map.Entry<String, Float> entry : orderingMap.entrySet()) {
                                if (Objects.nonNull(overrideMpq) && overrideMpq && Objects.nonNull(referenceOrderMenuItem.getDimension()) &&
                                        referenceOrderMenuItem.getDimension().equalsIgnoreCase("Family")
                                        && entry.getValue() <= 3) {
                                    LOG.info("Over riding the MPQ(Ordering Day) for product : {} and dimension : {}",referenceOrderMenuItem.getProductName(),referenceOrderMenuItem.getDimension());
                                    orderingMap.put(entry.getKey(),entry.getValue() - 1);
                                    if (orderingMap.get(entry.getKey()) < 0) {
                                        orderingMap.put(entry.getKey(),(float) 0);
                                    }
                                }
                                totalOrdering += entry.getValue();
                            }
                            referenceOrderMenuItem.setRequestedQuantity(totalOrdering);
                            referenceOrderMenuItem.setQuantity(totalOrdering);
                            referenceOrderMenuItem.setDateOrderings(orderingMap);
                        }
                    }
                }
            }
        }
    }

    private static Map<String, Boolean> getFinalBrandWiseStatus(UnitReferenceData referenceData, Map<String, Map<String, Boolean>> dayWiseBrandStatus) {
        Map<String, Boolean> finalBrandWiseStatus = new HashMap<>();
        for (Map.Entry<String, Map<String, Boolean>> entry : dayWiseBrandStatus.entrySet()) {
            Map<String, Boolean> innerMap = new HashMap<>();
            if (Objects.nonNull(referenceData.getBrandName())) {
                if (referenceData.getBrandName().equalsIgnoreCase(entry.getKey())) {
                    innerMap = dayWiseBrandStatus.getOrDefault(referenceData.getBrandName(), new HashMap<>());
                }
            } else {
                innerMap = dayWiseBrandStatus.getOrDefault("WareHouse", new HashMap<>());
            }
            for (Map.Entry<String, Boolean> innerEntry : innerMap.entrySet()) {
                if (finalBrandWiseStatus.containsKey(innerEntry.getKey())) {
                    LOG.info("Day type : {} is already found in finalBrandWiseStatus",innerEntry.getKey());
                } else {
                    finalBrandWiseStatus.put(innerEntry.getKey(), innerEntry.getValue());
                }
            }
        }
        return finalBrandWiseStatus;
    }

    @RequestMapping(method = RequestMethod.POST, value = "clone-day-close-data", produces = MediaType.APPLICATION_JSON)
    public void cloneDayCloseDataForUnit(@RequestBody IdCodeName idCodeName) {
        int newUnitId = idCodeName.getId();
        int cloningUnitId = Integer.parseInt(idCodeName.getCode());
         referenceOrderManagementService.cloneDayCloseDataForUnit(newUnitId, cloningUnitId);

    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-ordering-schedule", produces = MediaType.APPLICATION_JSON)
    public List<RegularOrderUnitBrand> getUnitOrderingSchedule(@RequestParam Integer unitId) {
        LOG.info("Getting ordering schedule for Unit : {} ",masterCache.getUnit(unitId).getName());
        return referenceOrderManagementService.getUnitOrderingSchedule(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all-ordering-schedules", produces = MediaType.APPLICATION_JSON)
    public List<RegularOrderUnitBrand> getAllOrderingSchedules() {
        LOG.info("Getting ordering schedule for All Available Calenders");
        return referenceOrderManagementService.getAllOrderingSchedules();
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-unit-ordering-schedule",consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Boolean addUnitOrderingSchedule(@RequestBody List<RegularOrderUnitBrand> unitBrandList,@RequestParam Integer userId,@RequestParam Boolean isUpdate) throws SumoException {
        LOG.info("Getting ordering schedule for Unit : {} ",masterCache.getUnit(unitBrandList.get(0).getUnitId()).getName());
        if (!isUpdate) {
            return referenceOrderManagementService.addUnitOrderingSchedule(unitBrandList, userId);
        }
        else {
            return referenceOrderManagementService.updateUnitOrderingSchedule(unitBrandList, userId);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-fountain9-units")
    public List<Integer> getFountain9Units(@RequestParam Integer unitId,@RequestParam Boolean isForceLookUp) {
        LOG.info("Getting fountain9 Units");
        return referenceOrderManagementService.getFountain9Units(unitId,isForceLookUp);
    }

    @RequestMapping(method = RequestMethod.GET, value = "available-units-for-schedule")
    public OrderScheduleClone getAvailableUnitsForSchedule() {
        LOG.info("Getting Available Units For Schedule");
        return referenceOrderManagementService.getAvailableUnitsForSchedule();
    }

    @RequestMapping(method = RequestMethod.POST, value = "clone-unit-ordering-schedule")
    public Boolean cloneUnitOrderingSchedule(@RequestBody OrderScheduleClone orderScheduleClone, @RequestParam Integer userId) throws SumoException {
        LOG.info("Cloning Unit Ordering schedules ");
        return referenceOrderManagementService.cloneUnitOrderingSchedule(orderScheduleClone, userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-for-f9-orders")
    public Boolean checkForF9Orders(@RequestParam Integer requestingUnitId, @RequestParam Integer fulfilmentUnitId,@RequestParam String fulfilmentDate) {
        LOG.info("Checking for f9 Orders..!");
        return referenceOrderManagementService.checkForF9Orders(requestingUnitId,fulfilmentUnitId,AppUtils.getDate(fulfilmentDate,"yyyy-MM-dd"));
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-fountain9-comparison-report")
    public Boolean getFountain9ComparisonReport(@RequestParam(required = false) List<Integer> unitIds) {
        LOG.info("Generating Fountain 9 Comparison Report..!");
        return referenceOrderManagementService.getFountain9ComparisonReport(unitIds);
    }

//    @Scheduled(cron = "0 0 8 * * MON", zone = "GMT+05:30")
    public void generateFountain9SalesReport() {
        LOG.info("Generating Fountain 9 Comparison Report through Cron..!");
        referenceOrderManagementService.getFountain9ComparisonReport(null);
    }



    private static class EstimateData {
        private BigDecimal quantity = BigDecimal.ZERO;
        private BigDecimal dineInQuantity = BigDecimal.ZERO;
        private BigDecimal deliveryQuantity = BigDecimal.ZERO;
        private BigDecimal takeawayQuantity = BigDecimal.ZERO;
        private BigDecimal saleQuantity = BigDecimal.ZERO;

        public void add(UnitDayWiseItemData data, int noOfDays, boolean roundUp) {
            this.dineInQuantity = this.dineInQuantity.add(data.getDineInQuantity());
            this.deliveryQuantity = this.deliveryQuantity.add(data.getDeliveryQuantity());
            this.takeawayQuantity = this.takeawayQuantity.add(data.getTakeawayQuantity());
            BigDecimal val = new BigDecimal(data.getQuantity().floatValue());
            if (roundUp) {
                val = val.setScale(0, RoundingMode.CEILING);
                if (noOfDays > 1) {
                    val = val.add(new BigDecimal(1));
                }
            } else {
                val = val.setScale(0, RoundingMode.FLOOR);
            }
            this.quantity = this.quantity.add(val);
        }

        public void calculate(ReferenceOrderMenuItem data, UnitCategory category) {
            this.quantity = (this.quantity.add(this.quantity.multiply(new BigDecimal(0.1D))));
            if (this.quantity.compareTo(BigDecimal.ZERO) > 0) {
                switch (category) {
                    case CAFE:
                        this.dineInQuantity = this.deliveryQuantity.add(this.takeawayQuantity).compareTo(this.quantity) > 0
                                ? BigDecimal.ZERO
                                : this.quantity.subtract(this.deliveryQuantity.add(this.takeawayQuantity));
                        break;
                    case DELIVERY:
                        this.deliveryQuantity = this.dineInQuantity.add(this.takeawayQuantity).compareTo(this.quantity) > 0
                                ? BigDecimal.ZERO : this.quantity.subtract(this.dineInQuantity.add(this.takeawayQuantity));
                        break;
                    case TAKE_AWAY:
                        this.takeawayQuantity = this.dineInQuantity.add(this.deliveryQuantity).compareTo(this.quantity) > 0
                                ? BigDecimal.ZERO : this.quantity.subtract(this.dineInQuantity.add(this.deliveryQuantity));
                        break;
                    default:
                        break;
                }
                this.dineInQuantity = this.dineInQuantity.setScale(0, RoundingMode.FLOOR);
                this.deliveryQuantity = this.deliveryQuantity.setScale(0, RoundingMode.FLOOR);
                this.takeawayQuantity = this.takeawayQuantity.setScale(0, RoundingMode.FLOOR);
                this.quantity = this.dineInQuantity.add(this.deliveryQuantity).add(this.takeawayQuantity);
                data.setQuantity(this.quantity.floatValue());
                data.setRequestedQuantity(this.quantity.floatValue());
                data.setDineInQuantity(this.dineInQuantity.floatValue());
                data.setDeliveryQuantity(this.deliveryQuantity.floatValue());
                data.setTakeawayQuantity(this.takeawayQuantity.floatValue());
            }

        }

    }

    }
