package com.stpl.tech.scm.service.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.ServiceOrderManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.ServiceOrderCreateVO;
import com.stpl.tech.scm.domain.model.BulkSoDetail;
import com.stpl.tech.scm.domain.model.CostCenter;
import com.stpl.tech.scm.domain.model.CostElement;
import com.stpl.tech.scm.domain.model.CostElementPriceUpdate;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.EmployeeCostCenterMap;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.ListData;
import com.stpl.tech.scm.domain.model.ListDetail;
import com.stpl.tech.scm.domain.model.ListType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.ServiceOrder;
import com.stpl.tech.scm.domain.model.ServiceOrderItem;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceOrderSummary;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.CostElementCreationVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.SERVICE_ORDER_MANAGEMENT_ROOT_CONTEXT)
public class ServiceOrderManagementResource extends AbstractSCMResources {

	private static final Logger LOG = LoggerFactory.getLogger(ServiceOrderManagementResource.class);

	@Autowired
	private ServiceOrderManagementService serviceOrderService;

	@Autowired
	private ExcelViewGenerator excelViewGenerator;

	@RequestMapping(method = RequestMethod.GET, value = "cost-center/all", produces = MediaType.APPLICATION_JSON)
	public List<CostCenter> getAllCostCentersData() throws SumoException {
		return serviceOrderService.getCostCentersData();
	}

	@RequestMapping(method = RequestMethod.GET, value = "cost-center", produces = MediaType.APPLICATION_JSON)
	public List<CostCenter> getCostCentersData(@RequestParam Integer empId) throws SumoException {
		return serviceOrderService.getCostCentersDataForEmployee(empId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "cost-center-create", produces = MediaType.APPLICATION_JSON)
	public CostCenter createCentersData(@RequestBody CostCenter costCenter) throws SumoException {
		return serviceOrderService.createCentersData(costCenter);
	}

	@RequestMapping(method = RequestMethod.POST, value = "cost-element-create", produces = MediaType.APPLICATION_JSON)
	public CostElement createElementData(@RequestBody CostElementCreationVO vo) throws SumoException {
		return serviceOrderService.createElementDataOld(vo.getCostCenterId(), vo.getCostElement());
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-service-order", produces = MediaType.APPLICATION_JSON)
	public List<Integer> createServiceOrder(@RequestParam(required = false) Integer documentId,
											@RequestBody List<ServiceOrderCreateVO> requestVO, @RequestParam(required = false) Integer approvalOfHodDocId) throws SumoException {
		return serviceOrderService.createServiceOrder(documentId, requestVO, approvalOfHodDocId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-employee-cost-center-map", produces = MediaType.APPLICATION_JSON)
	public boolean createEmployeeCostCenterMap(HttpServletRequest request ,@RequestBody EmployeeCostCenterMap requestVO) throws SumoException {
		LOG.info("Request to create Employee Cost Center Map order for empId : {} by costCenterId : {}",
				requestVO.getEmployee().getId(), requestVO.getCostCenter().getId());
		return serviceOrderService.createEmployeeCostCenterMap(requestVO.getEmployee().getId(),
				requestVO.getCostCenter().getId(),getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.POST, value = "remove-employee-cost-center-mapping", produces = MediaType.APPLICATION_JSON)
	public boolean removeEmployeeCostCenterMap(HttpServletRequest request ,@RequestBody EmployeeCostCenterMap requestVO) throws SumoException {
		LOG.info("Request to Remove Employee Cost Center Map order for empId : {} by costCenterId : {}",
				requestVO.getEmployee().getId(), requestVO.getCostCenter().getId());
		return serviceOrderService.removeEmployeeCostCenterMap(requestVO.getEmployee().getId(), requestVO.getCostCenter().getId()
		,getLoggedInUser(request));
	}

	@RequestMapping(method = RequestMethod.GET, value = "created-orders", produces = MediaType.APPLICATION_JSON)
	public List<ServiceOrder> getCreatedOrders(@RequestParam(required = false) Integer vendorId,
											   @RequestParam(required = false) Integer bccId,
			@RequestParam(required = false) Integer serviceOrderId, @RequestParam(required = false) String startDate,
			@RequestParam(required = false) String endDate, @RequestParam(required = false) Integer userId,
			@RequestParam(required = false, defaultValue = "false") boolean isView, @RequestParam(required = false, defaultValue = "false") boolean showAll) {

		List<ServiceOrderStatus> statusList = isView ? Arrays.asList(ServiceOrderStatus.values())
				: Arrays.asList(ServiceOrderStatus.CREATED, ServiceOrderStatus.PENDING_APPROVAL_L1,
						ServiceOrderStatus.PENDING_APPROVAL_L2, ServiceOrderStatus.PENDING_APPROVAL_L3,
						ServiceOrderStatus.PENDING_APPROVAL_L4, ServiceOrderStatus.PENDING_APPROVAL_L5,
						ServiceOrderStatus.PENDING_APPROVAL_L6,ServiceOrderStatus.FIN_APPROVAL_L1);

		return serviceOrderService.findServiceOrders(bccId,vendorId, serviceOrderId, userId, statusList,
				SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), showAll);
	}

	@RequestMapping(method = RequestMethod.GET, value = "created-orders-short", produces = MediaType.APPLICATION_JSON)
	public ServiceOrderSummary getCreatedOrdersShort(@RequestParam(required = false) Integer vendorId,
														 @RequestParam(required = false) Integer bccId,
														 @RequestParam(required = false) Integer serviceOrderId, @RequestParam(required = false) String startDate,
														 @RequestParam(required = false) String endDate, @RequestParam(required = false) Integer userId,
														 @RequestParam(required = false, defaultValue = "false") boolean isView, @RequestParam(required = false, defaultValue = "false") boolean showAll,
													 @RequestParam(required = false,value = "costCenterId") Integer costCenterId) {

		List<ServiceOrderStatus> statusList = isView ? Arrays.asList(ServiceOrderStatus.values())
				: Arrays.asList(ServiceOrderStatus.CREATED, ServiceOrderStatus.PENDING_APPROVAL_L1,
				ServiceOrderStatus.PENDING_APPROVAL_L2, ServiceOrderStatus.PENDING_APPROVAL_L3,
				ServiceOrderStatus.PENDING_APPROVAL_L4, ServiceOrderStatus.PENDING_APPROVAL_L5,
				ServiceOrderStatus.PENDING_APPROVAL_L6,ServiceOrderStatus.FIN_APPROVAL_L1);

		return serviceOrderService.findServiceOrdersShort(bccId,vendorId, serviceOrderId, userId, statusList,
				SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), showAll,costCenterId);
	}



	@RequestMapping(method = RequestMethod.GET, value = "pending-orders", produces = MediaType.APPLICATION_JSON)
	public List<ServiceOrder> getPendingOrders(@RequestParam(required = false) Integer vendorId,
			@RequestParam(required = false) Integer dispatchId, @RequestParam(required = false) Integer userId,
			@RequestParam(required = false) Integer companyId, @RequestParam(required = false) Integer locationId, @RequestParam(required = false, defaultValue = "false") boolean showAll) {
		return serviceOrderService.findPendingServiceOrders(vendorId, dispatchId, userId, companyId, locationId, showAll);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-sos-for-advance", produces = MediaType.APPLICATION_JSON)
	public List<ServiceOrder> getSosForAdvance(@RequestParam Integer vendorId) throws SumoException {
		return serviceOrderService.getSosForAdvance(vendorId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "submit-adjustment-refund", produces = MediaType.APPLICATION_JSON)
	public Boolean submitAdvanceAdjustmentRefund(@RequestBody VendorAdvancePayment vendorAdvancePayment, @RequestParam Integer createdBy) throws SumoException {
		return serviceOrderService.submitAdvanceAdjustmentRefund(vendorAdvancePayment,createdBy);
	}

	@RequestMapping(method = RequestMethod.POST, value = "approve-reject-adjustment-refund", produces = MediaType.APPLICATION_JSON)
	public Boolean approveRejectAdjustmentRefund(@RequestBody VendorAdvancePayment vendorAdvancePayment, @RequestParam Integer approvedBy,@RequestParam String approveReject) throws SumoException {
		return serviceOrderService.approveRejectAdjustmentRefund(vendorAdvancePayment,approvedBy,approveReject);
	}

	/*
	 * @RequestMapping(method = RequestMethod.POST, value =
	 * "approve-so/{soId}/{userId}", produces = MediaType.APPLICATION_JSON) public
	 * boolean approveServiceOrder(@PathVariable(value = "soId") Integer soId,
	 *
	 * @PathVariable(value = "userId") Integer userId) throws SumoException { return
	 * serviceOrderService.approveServiceOrder(soId, userId); }
	 */

	@RequestMapping(method = RequestMethod.POST, value = "reject-so/{soId}/{userId}", produces = MediaType.APPLICATION_JSON)
	public boolean rejectServiceOrder(@PathVariable(value = "soId") Integer soId,
			@PathVariable(value = "userId") Integer userId) throws SumoException {
		return serviceOrderService.rejectServiceOrder(soId, userId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "cancel-so/{soId}/{userId}", produces = MediaType.APPLICATION_JSON)
	public boolean cancelServiceOrder(@PathVariable(value = "soId") Integer soId,
			@PathVariable(value = "userId") Integer userId) throws SumoException {
		return serviceOrderService.cancelServiceOrder(soId, userId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "close-so/{soId}/{userId}", produces = MediaType.APPLICATION_JSON)
	public boolean closeServiceOrder(@PathVariable(value = "soId") Integer soId,
			@PathVariable(value = "userId") Integer userId) throws SumoException {
		return serviceOrderService.closeServiceOrder(soId, userId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-sr-ids-for-so/{soId}", produces = MediaType.APPLICATION_JSON)
	public List<Integer> getServiceReceiveIdsForServiceOrder(@PathVariable(value = "soId") Integer soId,
									 @RequestParam(value = "srStatus") String currentSrStatus) throws SumoException {
		return serviceOrderService.getServiceReceiveIdsForServiceOrder(soId, currentSrStatus);
	}

	@RequestMapping(method = RequestMethod.GET, value = "cost-element/all", produces = MediaType.APPLICATION_JSON)
	public List<CostElement> getAllCostElementData() throws SumoException {
		return serviceOrderService.getCostElementsData();
	}

	@RequestMapping(method = RequestMethod.POST, value = "costElement-status", produces = MediaType.APPLICATION_JSON)
	public String updateStatus(@RequestBody CostElement costElement) throws SumoException {
		return serviceOrderService.updateCostElementStatus(costElement);
	}

	@RequestMapping(method = RequestMethod.GET, value = "getCostElementList", produces = MediaType.APPLICATION_JSON)
	public Map<String, List<ListDetail>> getListData(@RequestParam String baseType)  {
		return serviceOrderService.getListDatas(baseType);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-cost-element", produces = MediaType.APPLICATION_JSON)
	public boolean createElementData(@RequestBody CostElement vo) throws SumoException {
		return serviceOrderService.createElementData(vo);
	}

	@RequestMapping(method = RequestMethod.POST, value = "add-list-detail" , produces = MediaType.APPLICATION_JSON)
	public boolean addListDetail(@RequestBody ListDetail listDetail) throws SumoException{
		return serviceOrderService.saveListDetailData(listDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-list-detail" , produces = MediaType.APPLICATION_JSON)
	public boolean updateListDetail(@RequestBody ListDetail listDetail) throws SumoException{
		return serviceOrderService.updateListDetailData(listDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "add-subCategory-data" , produces = MediaType.APPLICATION_JSON)
	public boolean addSubCategoryDetail(@RequestBody ListType listType) throws SumoException{
		return serviceOrderService.saveSubCategoryData(listType);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-subCategory-data" , produces = MediaType.APPLICATION_JSON)
	public boolean updateSubCategoryData(@RequestBody ListType listType) throws SumoException{
		return serviceOrderService.updateSubCategoryData(listType);
	}

	@RequestMapping(method = RequestMethod.POST, value = "add-subSubCategory-data" , produces = MediaType.APPLICATION_JSON)
	public boolean addSubSubCategoryDetail(@RequestBody ListData listData) throws SumoException{
		return serviceOrderService.saveSubSubCategoryData(listData);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-subSubCategory-data" , produces = MediaType.APPLICATION_JSON)
	public boolean updateSubSubCategoryDetail(@RequestBody ListData listData) throws SumoException{
		return serviceOrderService.updateSubSubCategoryData(listData);
	}

	@RequestMapping(method = RequestMethod.GET, value = "mapped-costcenter", produces = MediaType.APPLICATION_JSON)
	public List<VendorDetail> getVendorData(@RequestParam Integer costCenterId){
		return serviceOrderService.getVendorData(costCenterId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "mapped-costelements", produces = MediaType.APPLICATION_JSON)
	public List<CostElement> getCostElementsData(@RequestParam Integer costCenterId, @RequestParam Integer vendorId){
		return serviceOrderService.getCostElementsData(costCenterId, vendorId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "selected-costelement", produces = MediaType.APPLICATION_JSON)
	public CostElementPriceUpdate getSelectedCostElement(@RequestParam Integer costCenterId, @RequestParam Integer vendorId, @RequestParam Integer costElementId){
		return serviceOrderService.getSelectedCostElement(costCenterId, vendorId, costElementId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "tag-list", produces = MediaType.APPLICATION_JSON)
	public List<ServiceOrder> getTagDataList(@RequestParam Integer costCenterId, @RequestParam String tagName){
		return serviceOrderService.getTagList(costCenterId, tagName);
	}

	@RequestMapping(method = RequestMethod.GET, value = "tag-names-list", produces = MediaType.APPLICATION_JSON)
	public List<String> getTagNamesList(@RequestParam Integer costCenterId){
		return serviceOrderService.getTagNamesList(costCenterId);
	}


	@RequestMapping(method = RequestMethod.GET, value = "get-vendorData", produces = MediaType.APPLICATION_JSON)
	public boolean getTagDataList(@RequestParam Integer locationId){
		return serviceOrderService.getVendorDetailData(locationId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "change-status", produces = MediaType.APPLICATION_JSON)
	public String changeStatus(@RequestParam Integer soId, @RequestParam String newStatus, @RequestParam String currentStatus, @RequestParam BigDecimal amount, @RequestParam Integer userId) throws SumoException{
		return serviceOrderService.changeLevelStatus(soId, newStatus, currentStatus, amount, userId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "check-unit-budget", produces = MediaType.APPLICATION_JSON)
	public boolean getUnitCheckBudget(@RequestParam Integer unitId){
		return serviceOrderService.getUnitCheckBudget(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "get-department-data-unit", produces = MediaType.APPLICATION_JSON)
	public List<ServiceOrderItem> getDepartmentData(@RequestBody List<ServiceOrderItem> requestVO) throws SumoException {
		return serviceOrderService.getDepartmentDataForUnit(requestVO);
	}

	@RequestMapping(method = RequestMethod.GET, value = "budget-category-list", produces = MediaType.APPLICATION_JSON)
	public List<String> getBudgetCategory(){
		return serviceOrderService.getBudgetCategory();
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-so-document", consumes = MediaType.MULTIPART_FORM_DATA)
	public DocumentDetail uploadSoDocument(HttpServletRequest request,
										 @RequestParam(value = "type") FileType type,
										 @RequestParam(value = "mimeType") MimeType mimeType,
										 @RequestParam(value = "userId") Integer userId,
										 @RequestParam(value = "docType") DocUploadType docType,
										 @RequestParam(value = "file") final MultipartFile file) {
		return serviceOrderService.uploadSoDocument(type, mimeType, docType, userId, file);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-bulk-so", consumes = MediaType.MULTIPART_FORM_DATA)
	public List<BulkSoDetail> uploadBulkSo(@RequestParam(value = "file") final MultipartFile file) throws IOException, SumoException {
		return serviceOrderService.readUploadedSOFile(file);
	}

	@RequestMapping(method = RequestMethod.POST, value = "download-bulk-so")
	public View downloadBulkSo(@RequestBody List<BulkSoDetail> bulkSoDetails) throws SumoException, IOException {
		LOG.info("Got a request to download Sample Bulk SO Sheet");
		return excelViewGenerator.generateBulkSoSheet(bulkSoDetails);
	}

	@RequestMapping(method = RequestMethod.POST, value ="upload-required-documents" ,consumes = MediaType.MULTIPART_FORM_DATA)
	public DocumentDetail uploadRequiredDocuments(HttpServletRequest request,
												 @RequestParam FileType type,
												 @RequestParam MimeType mimeType,
												 @RequestParam Integer userId,
												 @RequestParam DocUploadType docType,
												 @RequestParam MultipartFile file,
												  @RequestParam String docName
												  ){
		return serviceOrderService.uploadRequiredDocuments(type,mimeType,userId,docType,file,docName);
	}

	@RequestMapping(method = RequestMethod.POST,value = "show-required-documents" ,produces = MediaType.APPLICATION_JSON)
	public List<String> showRequiredDocuments(@RequestBody List<Integer> costElementIds){
		return serviceOrderService.showRequiredDocuments(costElementIds);
	}

	@PostMapping(value = "upload-approval-of-hod", consumes = MediaType.MULTIPART_FORM_DATA )
	public DocumentDetail uploadApprovalOfHod(HttpServletRequest request,
											  @RequestParam(value = "type") FileType type,
											  @RequestParam(value = "mimeType") MimeType mimeType,
											  @RequestParam(value = "userId") Integer userId,
											  @RequestParam(value = "docType") DocUploadType docType,
											  @RequestParam(value = "file") final MultipartFile file,
											  @RequestParam String docName) throws DocumentException, IOException, SumoException {
		return serviceOrderService.uploadApprovalOfHod(type, mimeType, docType, userId, file, docName);
	}

	@Scheduled(cron="0 0 11 * * 1", zone = "GMT+05:30")
	@GetMapping("/send-capex-close-notification")
	public Boolean sendCapexCloseNotification(){
		return serviceOrderService.sendCapexCloseNotification();
	}
}
