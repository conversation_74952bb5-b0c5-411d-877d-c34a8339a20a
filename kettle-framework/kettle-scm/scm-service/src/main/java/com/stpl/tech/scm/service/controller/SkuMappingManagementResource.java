package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.UnitBusinessType;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.core.util.SCMConstants;
import com.stpl.tech.scm.core.util.model.SkuDataAndTaxData;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.SkuData;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorType;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.scm.service.model.MappingStatusChange;
import com.stpl.tech.scm.service.model.MappingUpdate;
import com.stpl.tech.scm.service.model.SkuLookupData;
import com.stpl.tech.scm.service.model.UnitVendorSkuLookup;
import com.stpl.tech.scm.service.model.VendorLookupData;
import com.stpl.tech.scm.service.model.VendorSkuMappingVO;
import com.stpl.tech.util.AppConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Mohit
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT)
public class SkuMappingManagementResource extends AbstractSCMResources{

    @Autowired
    private SkuMappingService mappingService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private SCMConstants SCMConstants;

    @RequestMapping(method = RequestMethod.POST, value = "sku-for-unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> searchSkuMappingsForUnit(@RequestBody final int unitId) {

        return mappingService.searchSkuMappingsForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-for-sku", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> searchUnitMappingsForSku(@RequestBody final int skuId) {
        return mappingService.searchUnitMappingsForSku(skuId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-skuProfile-for-unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<unitSkuMappingDetail> getSkusProfileForUnit(@RequestBody List<Integer> skus, @RequestParam int unitId) {
        return mappingService.getSkusProfileForUnit(unitId, skus);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-sku-profile", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Boolean updateSkuProfiles(@RequestBody Map<Integer, String> skuListWithInventoryListId, @RequestParam int unitId, @RequestParam String profile) {
        return mappingService.updateSkuProfiles(skuListWithInventoryListId, unitId, profile);
    }


    @RequestMapping(method = RequestMethod.POST, value = "update-unit-for-sku", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateUnitMappingsForSku(@RequestBody final MappingUpdate mapping) {
        return mappingService.updateUnitMappingsForSku(mapping.getEmployeeId(), mapping.getEmployeeName(),
            mapping.getId(), mapping.getMappingIds(), mapping.getMappedSkuInventorys(),mapping.getMappedProductionUnit(), mapping.getMappedPackagingId() , mapping.getMappedTaxCodes()
            , mapping.getMappedVoDiscontinuedFrom(), mapping.getMappedRoDiscontinuedFrom());
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-sku-for-unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateSkuMappingsForUnit(@RequestBody final MappingUpdate mapping) {
        return mappingService.updateSkuMappingsForUnit(mapping.getEmployeeId(), mapping.getEmployeeName(),
            mapping.getId(), mapping.getMappingIds(), mapping.getMappedSkuInventorys(),mapping.getMappedProductionUnit(), mapping.getMappedPackagingId()
                ,mapping.getMappedTaxCodes(), mapping.getMappedVoDiscontinuedFrom(), mapping.getMappedRoDiscontinuedFrom());
    }

    @RequestMapping(method = RequestMethod.POST, value = "sku-for-vendor", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> searchSkuMappingsForVendor(@RequestBody final int unitId) {
        return mappingService.searchSkuMappingsForVendor(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-for-sku", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> searchVendorMappingsForSku(@RequestBody final int skuId) {
        return mappingService.searchVendorMappingsForSku(skuId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-for-business", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> searchVendorMappingsForBusiness(@RequestBody final int businessId) {
        return mappingService.searchVendorMappingsForBusiness(businessId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-vendor-for-sku", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateVendorMappingsForSku(@RequestBody final MappingStatusChange mapping) {
        return mappingService.updateVendorSkuMapping(mapping.getEmployeeId(), mapping.getEmployeeName(),
                mapping.getVendorId(), mapping.getSkuId(), mapping.getStatus());
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-vendor-for-business", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean addVendorMappingsForBusiness(@RequestBody final MappingUpdate mapping) {
        return mappingService.addVendorMappingsForBusiness(mapping.getEmployeeId(), mapping.getEmployeeName(),
                mapping.getId(), mapping.getMappingIds());
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-vendor-for-sku", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean addVendorMappingsForSku(@RequestBody final MappingUpdate mapping) {
        return mappingService.addVendorMappingsForSku(mapping.getEmployeeId(), mapping.getEmployeeName(),
                mapping.getId(), mapping.getMappingIds());
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-sku-for-vendor", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean addSkuMappingsForVendor(@RequestBody final VendorSkuMappingVO mapping) {
        return mappingService.addSkuMappingsForVendor(mapping.getEmployeeId(), mapping.getEmployeeName(),
                mapping.getVendorId(), mapping.getSkuIds());
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-sku-for-vendor-alias", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean addSkuMappingsForVendorAlias(@RequestBody final IdCodeName mapping) throws SumoException {
        return mappingService.addSkuMappingsForVendorAlias(mapping);
    }


    @RequestMapping(method = RequestMethod.GET, value = "all-sku", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> getAllSKU() {
        return mappingService.allSKU();
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-unit", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> getAllUnits() {
        return mappingService.allUnits();
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-by-business-type", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> getUnitsByBusinessType(@RequestParam(required = false)UnitBusinessType type) {
        if(type ==null){
            type = UnitBusinessType.COCO;
        }
        return mappingService.unitsByBusinessType(type);
    }

    @RequestMapping(method = RequestMethod.GET, value = "all-vendors", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeNameStatus> getAllVendors() {
        return mappingService.allVendors();
    }

    @RequestMapping(method = RequestMethod.POST, value = "sku-packaging", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<PackagingDefinition> getSkuPackaging(@RequestBody final int skuId) {
        List<PackagingDefinition> packs = new ArrayList<>();
        List<SkuPackagingMapping> skuPkgs = scmCache.getSkuPackagingMappings(skuId);
        if (skuPkgs != null && !skuPkgs.isEmpty()) {
            skuPkgs.forEach(skuPackagingMapping -> {
                packs.add(scmCache.getPackagingDefinition(skuPackagingMapping.getPackagingId()));
            });
        }
        return packs;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-sites", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<Location> getVendorSites(@RequestBody final int vendorId) {
        List<Location> locations = new ArrayList<>();
        Set<String> locationIds = new HashSet<>();
        VendorDetail vendor = scmCache.getVendorDetail(vendorId);
        if (vendor != null) {
            for (VendorDispatchLocation vdl : vendor.getDispatchLocations()) {
                locationIds.add(vdl.getLocationId());
            }
        }
        for (String id : locationIds) {
            locations.add(masterCache.getAllLocations().get(id));
        }
        return locations;
    }

    @RequestMapping(method = RequestMethod.POST, value = "search-sku-price", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<SkuPriceDetail> searchPricesBySku(@RequestBody final SkuLookupData data) {
        return mappingService.searchPricesBySku(data.getSkuId(), data.getLocation());
    }

    @RequestMapping(method = RequestMethod.POST, value = "search-vendor-sku-price", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<SkuPriceDetail> searchPricesBySku(@RequestBody final VendorLookupData data) {
        return mappingService.searchPricesByVendor(data.getVendorId(), data.getLocation());
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-sku-price", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean addSkuPrice(@RequestBody final SkuPriceUpdate data) {
        return mappingService.addPrice(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-sku-price", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateSkuPrice(@RequestBody final SkuPriceUpdate data) throws SumoException {
        return mappingService.updatePrices(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-sku-price-status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateSkuPriceStatus(@RequestBody final SkuPriceUpdate data) {
        return mappingService.updatePriceStatus(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "cancel-sku-price-update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean cancelSkuPriceUpdate(@RequestBody final SkuPriceUpdate data) {
        return mappingService.cancelPriceUpdate(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-unit-sku-vendor-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<UnitVendorSkuMapping> searchUnitSkuVendorMapping(@RequestBody final UnitVendorSkuLookup data) {
        return mappingService.searchSkuMappingsForVendorAndUnit(data.getUnitId(), data.getVendorId());
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-vendors-for-unit", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<VendorDetail> getVendorsForUnit(@RequestParam final int unitId, @RequestParam(required = false) VendorType type) {
        List<VendorDetail> vendors = new ArrayList<>(mappingService.searchVendorsForUnit(unitId));
        if (type != null) {
            vendors = vendors.stream().filter(vendor -> vendor.getType().equals(type)).collect(Collectors.toList());
        }
        return vendors;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-business-types", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<IdCodeName> getBusinessTypes() {
        return mappingService.getBusinessTypes();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-vendors-for-unit-trimmed", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public Collection<IdCodeName> getVendorsForUnitTrimmed(@RequestParam final int unitId) {
        return mappingService.searchVendorsForUnitTrimmed(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "sku-mapped-for-vendor", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public Collection<SkuData> getVendorsForUnit(@RequestParam final int unitId,
                                                 @RequestParam final int vendorId) {
        return mappingService.getSkusMappedToVendor(unitId, vendorId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "update-unit-sku-vendor-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<UnitVendorSkuMapping> updateUnitSkuVendorMapping(@RequestBody final UpdateUnitVendorSkuMapping data) {
        return mappingService.updateSkuMappingsForVendorAndUnit(data);
    }

    @RequestMapping(method = RequestMethod.GET, value = "sku-price-and-tax-list", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<SkuDataAndTaxData> getSkuTaxData(@RequestParam int dispatchId, @RequestParam int vendorId,
                                                 @RequestParam int deliveryUnitId , @RequestParam Boolean toPickFromMapping) {
        return mappingService.getSkuAndTaxData(dispatchId, deliveryUnitId, vendorId,toPickFromMapping);
    }

    @RequestMapping(method = RequestMethod.GET, value = "sku-price-and-tax-list-for-profile", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<SkuDataAndTaxData> getSkuTaxDataForProfile(@RequestParam int dispatchId, @RequestParam int vendorId,
                                                 @RequestParam int deliveryUnitId, @RequestParam String roleIds , @RequestParam Boolean toPickFromMapping) {

        List<Integer> roles = Arrays.stream(roleIds.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
        List<Integer> subCategories = mappingService.getSubCategories(roles);
        List<SkuDataAndTaxData> skus = mappingService.getSkuAndTaxData(dispatchId, deliveryUnitId, vendorId , toPickFromMapping);
        if(subCategories!=null){
            skus = skus.stream()
                    .filter(data -> subCategories.contains(data.getSkuData().getSubCategory()))
                    .collect(Collectors.toList());
        }
        return skus;
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-distance-mapping", produces = MediaType.APPLICATION_JSON)
    public List<String> getUnitDistanceMapping(@RequestParam final int firstUnitId, @RequestParam final int secondUnitId) {
    	return mappingService.getUnitDistanceMapping(firstUnitId,secondUnitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-unit-distance-mapping", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean updateUnitDistanceMapping(@RequestParam final int firstUnitId,@RequestParam final String firstMappingId,@RequestParam final String firstDistance,@RequestParam final int secondUnitId,@RequestParam String secondMappingId, @RequestParam String secondDistance) throws SumoException {
    	Integer firstMapId = null;
    	Integer secondMapId = null;
    	if(!firstMappingId.equalsIgnoreCase("null")) {
    	 firstMapId = Integer.valueOf(firstMappingId);
        }
        if(!secondMappingId.equalsIgnoreCase("null")) {
         secondMapId = Integer.valueOf(secondMappingId);
        }
    	return mappingService.updateMappingsForUnit(firstUnitId,firstMapId,new BigDecimal(firstDistance),secondUnitId,secondMapId,new BigDecimal(secondDistance));
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-sku-leadTime", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateSkuLeadTime(@RequestBody IdIndex data) {
        return mappingService.updateLeadTime(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "sibling-skus-inventoy-list", produces = MediaType.APPLICATION_JSON)
    public Map<Integer,Integer> getSiblingSkuInventoryListId(@RequestBody List<Integer> unitIds , @RequestParam(required = true) Integer skuId ) {
        return mappingService.getSibLingSkusInventoryListId(unitIds,skuId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-sku-packaging-tax-mapping", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, SkuPackagingTaxMapping> getUnitSkuPackagingTaxMapping(@RequestParam(required = true) Integer skuId , @RequestParam(required = true) Integer packagingId ) {
        return mappingService.getAllUnitSkuPackagingTaxMappingByStatus(skuId,packagingId,Arrays.asList(AppConstants.ACTIVE,AppConstants.IN_ACTIVE));
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-unit-sku-packaging-tax-mapping", produces = MediaType.APPLICATION_JSON)
    public Boolean updateUnitSkuPackagingTaxMapping(HttpServletRequest request, @RequestBody Map<Integer,String> unitToTaxMap, @RequestParam(required = true) Integer skuId , @RequestParam(required = true) Integer packagingId ) {
        return mappingService.updateUnitSkuPackagingTaxMapping(skuId,packagingId,unitToTaxMap,getLoggedInUser(request));
    }

    @GetMapping("/convert-unit-distance-to-zipcode")
    public  Boolean converUnitDistanceToZipcode() throws SumoException {
        return mappingService.converUnitDistanceToZipCodeDistance();
    }

}
