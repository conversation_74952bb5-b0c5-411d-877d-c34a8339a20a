package com.stpl.tech.scm.service.controller;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.TransportManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.transport.model.EWayWrapper;
import com.stpl.tech.scm.data.transport.model.TransportMode;
import com.stpl.tech.scm.domain.model.EWayBill;
import com.stpl.tech.scm.domain.model.EWayResponse;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.Vehicle;
import com.stpl.tech.scm.domain.model.VehicleDispatch;
import com.stpl.tech.scm.service.model.EWayDataRequest;
import com.stpl.tech.scm.service.model.ListRequest;
import org.apache.commons.io.IOUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.TRANSPORT_MANAGEMENT_ROOT_CONTEXT)
public class TransportManagementResource extends AbstractSCMResources {

	private static final Logger LOG = LoggerFactory.getLogger(TransportManagementResource.class);

	@Autowired
	private TransportManagementService transportManagementService;

	@RequestMapping(method = RequestMethod.GET, value = "transport-mode", produces = MediaType.APPLICATION_JSON)
	public TransportMode[] getAllTransportModes() {
		return TransportMode.values();
	}

	@RequestMapping(method = RequestMethod.GET, value = "vehicles", produces = MediaType.APPLICATION_JSON)
	public List<IdCodeName> getAllVehicles() {
		return transportManagementService.getAllVehicles();
	}

	@RequestMapping(method = RequestMethod.GET, value = "vehicles/mode", produces = MediaType.APPLICATION_JSON)
	public List<Vehicle> getAllVehiclesByMode(@RequestParam final TransportMode transportMode,@RequestParam(required = false) String date) {
		Date d = null;
		if (date != null) {
			LOG.info("Getting Suggested Vehicle List of dispatch date : {}",date);
			d = SCMUtil.getDate(SCMUtil.parseDate(date));
		}
		return transportManagementService.getAllVehiclesByMode(transportMode,d);
	}

	@Deprecated
	@RequestMapping(method = RequestMethod.GET, value = "dispatch", produces = MediaType.APPLICATION_JSON)
	public VehicleDispatch getVehicleDispatchForDate(@RequestParam final Integer vehicleId,
			@RequestParam final String date) throws SumoException {
		LOG.info("Request to get Dispatch data for vehicle Id: {} and date: {}", vehicleId, date);
		Date d = SCMUtil.getDate(SCMUtil.parseDate(date));
		return transportManagementService.getVehicleDispatchForDate(vehicleId, null, null, d, null);
	}

	@RequestMapping(method = RequestMethod.GET, value = "dispatch/search", produces = MediaType.APPLICATION_JSON)
	public List<VehicleDispatch> searchVehicleDispatchForDate(@RequestParam final Integer vehicleId,
			@RequestParam final String date, @RequestParam final String registrationNumber) {
		LOG.info("Request to get Dispatch data for vehicle Id: {} and date: {}", vehicleId, date);
		Date d = SCMUtil.getDate(SCMUtil.parseDate(date));
		return transportManagementService.searchVehicleDispatchForDate(vehicleId, d, registrationNumber);
	}

	@RequestMapping(method = RequestMethod.GET, value = "dispatch/create", produces = MediaType.APPLICATION_JSON)
	public VehicleDispatch createVehicleDispatchForDate(@RequestParam final Integer vehicleId,
                                                        @RequestParam(required = false) String transporter,
                                                        @RequestParam(required = false) String docket,
														@RequestParam(required = false) String regNumber,
			@RequestParam final String date) throws SumoException {
		LOG.info("Request to get Dispatch data for vehicle Id: {} and date: {}", vehicleId, date);
		Date d = SCMUtil.getDate(SCMUtil.parseDate(date));
		return transportManagementService.getVehicleDispatchForDate(vehicleId,transporter, docket, d, regNumber);
	}

	@RequestMapping(method = RequestMethod.GET, value = "dispatch/history", produces = MediaType.APPLICATION_JSON)
	public List<VehicleDispatch> getVehicleDispatchForDate(@RequestParam final Integer vehicleId,
			@RequestParam final String startDate, @RequestParam final String endDate) {
		LOG.info("Request to get Dispatch History data for vehicle Id: {} and startDate: {} , endDate: {}", vehicleId,
				startDate, endDate);
		Date d1 = SCMUtil.getDate(SCMUtil.parseDate(startDate));
		Date d2 = SCMUtil.getDate(SCMUtil.parseDate(endDate));

		return transportManagementService.getVehicleDispatchHistory(vehicleId, d1, d2);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pending-transfer-orders", produces = MediaType.APPLICATION_JSON)
	public List<TransferOrder> getPendingTransferOrdersForEway(@RequestBody final EWayDataRequest request) {
		LOG.info("Request to get pending transfer orders for Eway");
		return transportManagementService.getPendingTransferOrdersForEway(request.getTransferringUnitList(), request.getStartDate(),
				request.getEndDate(), request.getReceivingUnitList());
	}

	@RequestMapping(method = RequestMethod.POST, value = "consignment/create", produces = MediaType.APPLICATION_JSON)
	public boolean createConsignment(@RequestBody final ListRequest request) throws SumoException {
		LOG.info("Request to create consignment");
		return transportManagementService.createConsignment(request.getId(), request.getToList());
	}

	@RequestMapping(method = RequestMethod.POST, value = "consignment/cancel", produces = MediaType.APPLICATION_JSON)
	public boolean cancelConsignment(@RequestBody final Integer consignmentId) throws SumoException {
		LOG.info("Request to cancel consignment");
		return transportManagementService.cancelConsignment(consignmentId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "consignment/cancel/eway", produces = MediaType.APPLICATION_JSON)
	public boolean cancelEWay(@RequestBody final Integer ewayId) throws SumoException {
		LOG.info("Request to cancel consignment");
		return transportManagementService.cancelEWay(ewayId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "dispatch/eway/download/json", produces = MediaType.APPLICATION_JSON)
	public void downloadEwayJson(HttpServletResponse response ,@RequestParam final Integer dispatchId) throws SumoException, IOException {
		LOG.info("Request to download JSON for dispatch Id {} ", dispatchId);
		transportManagementService.generateEWayJson(dispatchId,response);
	}

	@RequestMapping(method = RequestMethod.GET, value = "dispatch/eway/start", produces = MediaType.APPLICATION_JSON)
	public List<EWayWrapper> generateEWayFormat(@RequestParam final Integer dispatchId,
                                                @RequestParam boolean forceEway) throws SumoException, IOException {
		LOG.info("Request to start Dispatch for dispatch Id {} and download Eway JSON", dispatchId);
		return transportManagementService.startDispatch(dispatchId, forceEway);
	}

	@RequestMapping(method = RequestMethod.POST, value = "dispatch/eway/parse", consumes = MediaType.MULTIPART_FORM_DATA)
	public List<EWayResponse> uploadDocument(@RequestParam(value = "file") final MultipartFile file,
			@RequestParam Integer dispatchId) throws IOException, InvalidFormatException, NoSuchFieldException, IllegalAccessException {
		LOG.info("Request to upload E-Way bill numbers original file name- " + file.getOriginalFilename() + " and name "
				+ file.getName());
		if (file.getName().endsWith("zip")) {
			return parseEwayZip(file, dispatchId);
		} else {
			return transportManagementService.parseEwayResponseFile(file, dispatchId);
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "dispatch/eway/update", consumes = MediaType.APPLICATION_JSON)
	public boolean updateEwayBillNumber(@RequestBody final List<EWayResponse> list) throws IOException {
		LOG.info("Request to upload E-Way bill numbers");
		return transportManagementService.updateEwayData(list);
	}

	@RequestMapping(method = RequestMethod.GET, value = "dispatch/download", produces = MediaType.APPLICATION_JSON)
	public VehicleDispatch getEwayBills(@RequestParam final Integer dispatchId) {
		return transportManagementService.getDispatchById(dispatchId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "dispatch/eway/fetch", produces = MediaType.APPLICATION_JSON)
	public EWayBill fetchEwayBill(@RequestParam Integer ewayId) throws IOException {
		LOG.info("Request to Fetch E-Way bill");
		return transportManagementService.fetchEwayBill(ewayId);
	}


	private List<EWayResponse> parseEwayZip(MultipartFile file, int dispatchId) throws IOException {
        List<EWayResponse> responseList = new ArrayList<>();
        File zip = File.createTempFile(UUID.randomUUID().toString(), "temp");
        FileOutputStream o = new FileOutputStream(zip);
        IOUtils.copy(file.getInputStream(), o);
		o.close();
        try {
            ZipFile zipFile = new ZipFile(zip);
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while(entries.hasMoreElements()){
                ZipEntry entry = entries.nextElement();
                InputStream stream = zipFile.getInputStream(entry);
                responseList.addAll(transportManagementService.parseEwayResponseFile(stream, entry.getName(), dispatchId));
            }
        } catch (ZipException | InvalidFormatException | NoSuchFieldException | IllegalAccessException e) {
            LOG.error("Exception while parsing zip ::", e);
        } finally {
            zip.delete();
        }
        return responseList;
    }

	@RequestMapping(method = RequestMethod.GET, value = "get-suggested-units", produces = MediaType.APPLICATION_JSON)
	public Map<String, List<Integer>> getSuggestedUnits(@RequestParam Integer vehicleId , @RequestParam String date) {
		Date d = SCMUtil.getDate(SCMUtil.parseDate(date));
		LOG.info("Getting suggested Units during dispatch for vehicle id : {} and date is {}",vehicleId,date);
		return transportManagementService.getSuggestedUnits(vehicleId,d);
	}
}
