/**
 *
 */
package com.stpl.tech.scm.service.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class MappingUpdate implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -8184285265195113373L;
	private int id;
	private List<Integer> mappingIds;
	private Map<Integer,String> mappedSkuInventorys;
	private int employeeId;
	private String employeeName;
	private Map<Integer,String> mappedProductionUnit;
	private Map<Integer, String> mappedPackagingId;
	private Map<Integer,String> mappedTaxCodes;
	private Map<Integer,Date> mappedVoDiscontinuedFrom;
	private Map<Integer,Date> mappedRoDiscontinuedFrom;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public List<Integer> getMappingIds() {
		return mappingIds;
	}

	public void setMappingIds(List<Integer> mappingIds) {
		this.mappingIds = mappingIds;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public Map<Integer,String> getMappedSkuInventorys() {
		return mappedSkuInventorys;
	}

	public void setMappedSkuInventorys(Map<Integer, String> mappedSkuInventorys) {
		this.mappedSkuInventorys = mappedSkuInventorys;
	}

	public Map<Integer, String> getMappedProductionUnit() {
		return mappedProductionUnit;
	}

	public void setMappedProductionUnit(Map<Integer, String> mappedProductionUnit) {
		this.mappedProductionUnit = mappedProductionUnit;
	}

	public Map<Integer, String> getMappedPackagingId() {
		return mappedPackagingId;
	}

	public void setMappedPackagingId(Map<Integer, String> mappedPackagingId) {
		this.mappedPackagingId = mappedPackagingId;
	}

	public Map<Integer, String> getMappedTaxCodes() {
		return mappedTaxCodes;
	}

	public void setMappedTaxCodes(Map<Integer, String> mappedTaxCodes) {
		this.mappedTaxCodes = mappedTaxCodes;
	}

	public Map<Integer, Date> getMappedVoDiscontinuedFrom() {
		return mappedVoDiscontinuedFrom;
	}

	public void setMappedVoDiscontinuedFrom(Map<Integer, Date> mappedVoDiscontinuedFrom) {
		this.mappedVoDiscontinuedFrom = mappedVoDiscontinuedFrom;
	}

	public Map<Integer, Date> getMappedRoDiscontinuedFrom() {
		return mappedRoDiscontinuedFrom;
	}

	public void setMappedRoDiscontinuedFrom(Map<Integer, Date> mappedRoDiscontinuedFrom) {
		this.mappedRoDiscontinuedFrom = mappedRoDiscontinuedFrom;
	}
}
