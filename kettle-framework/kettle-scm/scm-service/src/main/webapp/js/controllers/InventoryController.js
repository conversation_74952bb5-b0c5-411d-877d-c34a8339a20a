/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 22-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('inventoryCtrl', ['$rootScope','$scope','$interval','authService','appUtil','apiJson',
        '$toastService','$http','$alertService','packagingService','pagerService','$state','Popeye','previewModalService', 'metaDataService','uiGridConstants',
        function ($rootScope,$scope,$interval,authService,appUtil,apiJson,$toastService,$http,$alertService,
                  packagingService,pagerService,$state,Popeye,previewModalService,metaDataService,uiGridConstants) {

                $scope.currentUser = appUtil.getCurrentUser();

                $scope.frequencies = ["DAILY", "WEEKLY", "MONTHLY"];
                $scope.checkEmpty = appUtil.checkEmpty;


                $scope.productList = [];
                $scope.submitted = false;
                $scope.isPreview = false;
                $scope.pendingGR = {};
                $scope.pendingWastage = 0;
                $scope.packagingMappings = {};
                $scope.showPreview = previewModalService.showPreview;

                $scope.preview = function () {
                    $scope.isPreview = !$scope.isPreview;
                };


                $scope.$on("$destroy", function () {
                    saveInventory($scope.getFinalProductList());
                    $interval.cancel($scope.refreshInterval);
                });


            $scope.finalSubmission = function () {
                $scope.submitted = true;
                var finalProductsList = $scope.getFinalProductList();
                saveInventory(finalProductsList);
                var inventoryList = angular.copy(finalProductsList);
                inventoryList = inventoryList.filter(function (product) {
                    return appUtil.isEmptyObject(product.notAvl) && !appUtil.isEmptyObject(product.stockValue);
                }).map(function (product) {
                    delete product.packagingMappings;
                    return product;
                });
                var currentUser = appUtil.getCurrentUser();
                var requestObject = {
                    unit: currentUser.unitId,
                    generatedBy: currentUser.userId,
                    eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                    inventoryResponse: inventoryList
                };
                var businessDate = null;
                if ($scope.dayCloseBusinessDate != null) {
                    businessDate = appUtil.formatDate($scope.dayCloseBusinessDate, "yyyy-MM-dd");
                }

                    $http.post(apiJson.urls.stockManagement.updateInventory + "?stockType=" + $scope.frequencyValue + "&businessDate=" + businessDate, requestObject)
                        .success(function (response) {
                            if (response && (appUtil.isEmptyObject(response.data) || appUtil.isEmptyObject(response.data.errorType))) {
                                if (response == 1) {
                                    $scope.productList = [];
                                    $scope.resetFinalProductList();
                            $scope.inventoryUpdated = true;
                                    appUtil.removeSavedInventoryList();
                                    $interval.cancel($scope.refreshInterval);
                                    $toastService.create("You have successfully updated inventory");
                                    //metaDataService.getRegularOrderingEvents(appUtil.getCurrentUser().unitId, function (events) {
                                    //    if (events.length > 0) {
                                    //        $state.go("menu.refOrderCreateV1", {orderingEvents: events});
                                    //    }
                                    //});
                                if (!$scope.isOpening) {
                                $state.go("menu.varianceEdit");
                            }} else if (response == 0) {
                                    $toastService.create("You are trying to upload wrong inventory list");
                                } else if (response == -1) {
                                    $toastService.create("Some exception has occurred. Please try again!");
                                } else if (response == 404) {
                                $alertService.alert("Kettle Day Close Failed..!", "Please Check the Kettle Day Close Of the Unit..! <br> All the Data Will be Cleared..!", function (result) {
                                    $scope.resetFinalProductList();
                                    appUtil.removeSavedInventoryList();
                                    $scope.submitted = false;
                                    $scope.init();
                                }, false);
                            } else if (response == 405) {
                                $alertService.alert("Kettle Day Close Not Found..!", "Please Check the Kettle Day Close Of the Unit..! <br> All the Data Will be Cleared..!", function (result) {
                                    $scope.resetFinalProductList();
                                    appUtil.removeSavedInventoryList();
                                    $scope.submitted = false;
                                    $scope.init();
                                }, false);
                            }
                            } else if (!appUtil.isEmptyObject(response.data) && !appUtil.isEmptyObject(response.data.errorType)) {
                                $toastService.create("You have entered negative stock for some products. Please check and submit again.");
                            } else {
                                $toastService.create("Upload failed. Try again!");
                                $scope.submitted = false;
                            }
                        }).error(function (response) {
                        if (!appUtil.isEmptyObject(response.payload)) {
                            var message = "Following Products are not available at your Cafe: <br/>";
                            var products = response.payload;
                            var productIds = products.map(function (p) {
                                return p.productId;
                            });
                            var names = [];
                            $scope.getFinalProductList().forEach(function (p) {
                                if (productIds.indexOf(p.productId) != -1) {
                                    names.push(p.productName);
                                    p.stockValue = null;
                                    p["notAvl"] = true;
                                }
                            });
                            message += names.join(", ");
                            message += "<br><b>Removing them from the list. Click Yes to submit again!</b><br>";
                            $alertService.confirm(response.errorType, message, function (result) {
                                if (result) {
                                    $scope.finalSubmission();
                                }
                            }, false);
                        } else if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, function (result) {
                            }, false);
                        } else {
                            $toastService.create("Upload Failed! Please try again");
                        }
                        $scope.submitted = false;
                    });
                };

            $scope.submit = function(){
                if (!appUtil.isEmptyObject($scope.dayCloseBusinessDate) && appUtil.getCurrentBusinessDate() != appUtil.formatDate($scope.dayCloseBusinessDate,"yyyy-MM-dd")) {
                    $alertService.confirm("Are you sure?", "You Cannot Edit Variance after the submission of Day close ..!", function (result) {
                        if (result) {
                            $scope.finalSubmission();
                        }
                    });
                } else {
                    $scope.finalSubmission();
                }
            };

                $scope.fillZeroForAllProducts = function () {
                    angular.forEach($scope.getFinalProductList(), function (product) {
                        angular.forEach(product.packagingMappings, function (packaging) {
                            packaging.value = 0;
                            $scope.removeMapping(product.packagingMappings, packaging, product, true);
                        });
                    });
                };

                $scope.clearMappings = function () {
                    $scope.resetFinalProductList();
                $scope.selectedPackagings = [];
                    appUtil.removeSavedInventoryList();
                    predictExpectedValues($scope.frequencyValue, true);
                    $scope.getFinalProductList().forEach(function (product) {
                        product.stockValue = product.expectedValue;
                        product.variance = 0;
                    });
                $scope.currentProductFillingType = "MANDATORY";
            };

                function checkForPendingGR(unitId) {
                    var promise = $http.get(apiJson.urls.stockManagement.checkPendingGR + "?unit=" + unitId)
                        .success(function (response) {
                            console.log("response checkForPendingGR", response);
                            $scope.pendingGR = response;
                            return response.data;
                        }).error(function (response) {
                            console.log("Could not get pending GR count");
                        });
                    return promise;
                }

                function checkVarianceAcknowledgement(unitId) {
                $http.get(apiJson.urls.stockManagement.checkVarianceAcknowledgement+"?id="+unitId)
                    .success(function(response){
                        console.log("response checkForVarianceAcknowledgeemnt",response);
                        $scope.varianceBlocking = response.varianceBlocking;
                    }).error(function(response){
                        console.log("Could not get acknowledgement data");
                    });
            }

            function checkWastageEntered(unitId) {
                    $http.get(apiJson.urls.stockManagement.checkWastageEntered + "?unit=" + unitId)
                        .success(function (response) {
                            if (!$scope.checkEmpty(response) && response > 0) {
                                $scope.pendingWastage = response;
                            }
                        }).error(function (response) {
                        console.log("Could not get pending GR count");
                    });
                }

                function checkTransfersCreated(unitId) {
                    $http.get(apiJson.urls.stockManagement.checkPendingTransfers + "?unitId=" + unitId)
                        .success(function (response) {
                            if (!$scope.checkEmpty(response) && response > 0) {
                                $scope.pendingTransfers = response;
                                $toastService.create("Found " + response + " unsettled special order(s). " +
                                    "Please settle all special order(s) first.");
                                $state.go('menu.acknowledgeRO');
                            }
                        }).error(function (err) {
                        console.log("Could not get pending special order count", err);
                    });
                }

                $scope.init = function () {
                    $scope.productFillingTypes = ["MANDATORY", "OPTIONAL", "ALL"];
                $scope.resetFinalProductList();
                $scope.searchProduct = null;
                $scope.currentProductFillingType = "MANDATORY";
                $scope.dayCloseBusinessDate = null;
                $scope.unitId = appUtil.getCurrentUser().unitId;
                $scope.currentUserId = appUtil.getCurrentUser().userId;
                $scope.isOpening = false;
                checkTransfersCreated($scope.unitId);
                $scope.checkClosingInitiated($scope.unitId);
                checkInventoryUpdated($scope.unitId);
                checkIfOpeningNeeded($scope.unitId);
                checkForPendingGR($scope.unitId);
                checkWastageEntered($scope.unitId);
                checkVarianceAcknowledgement($scope.unitId);
                $scope.edited = false;             //edited stock flag initialized to false
                $scope.packagingMappings = appUtil.getMetadata().packagingMappings;
                $scope.inventoryUpdated = false;
                $scope.lastSaved = null;
                var savedInventory = appUtil.getSavedInventoryList();
                if(!$scope.checkEmpty(savedInventory) && (savedInventory.unit == $scope.unitId)){
                    $scope.productList = savedInventory.products;
                    $scope.frequencyValue = savedInventory.stockType;
                    $scope.fixedAssetsOnly = (savedInventory.stockType == "FIXED_ASSETS");
                    $scope.lastSaved = savedInventory.lastSaved;
                    setProducts($scope.productList);
                    $scope.seggregateMandatoryOptionalProducts($scope.productList);
                }else{
                    appUtil.removeSavedInventoryList();
                    // $scope.selectFrequency("DAILY");
                }
                $scope.refreshInterval = $interval(function(){
                    if(!$scope.checkEmpty($scope.getFinalProductList())){
                        saveInventory($scope.getFinalProductList(),$scope.selectedPackagings);
                    }
                },10000);
                $scope.inventoryEventValid = true;
                $scope.dayClosePending = false;
                $scope.showGrid = false;
            };

            $scope.setSearchProduct = function (text) {
                $scope.searchProduct = text;
            };

            $scope.setProductFillingType = function (currentType , type) {
                $scope.searchProduct = null;
                if (type == 'previous') {
                    if (currentType == 'OPTIONAL') {
                        $scope.currentProductFillingType = "MANDATORY";
                    }
                    if (currentType == 'ALL') {
                        $scope.currentProductFillingType = "OPTIONAL";
                    }
                } else {
                    if (currentType == 'MANDATORY') {
                        var missedProductQuantity = [];
                        var currentProductList = $scope.productsByFillingType.MANDATORY;
                        if (currentProductList.length > 0) {
                            for (var i=0;i<currentProductList.length;i++) {
                                var packCheck = false;
                                for (var j=0;j<currentProductList[i].packagingMappings.length ;j++) {
                                    if (!appUtil.isEmptyObject(currentProductList[i].packagingMappings[j].value)) {
                                        packCheck = true;
                                        break;
                                    }
                                }
                                if (!packCheck) {
                                    missedProductQuantity.push(currentProductList[i].productName);
                                }
                            }
                            if (missedProductQuantity.length > 0) {
                                $toastService.create("Please Enter Quantity for the Products : " + missedProductQuantity.join(","));
                                return false;
                            }
                        }
                        $scope.currentProductFillingType = "OPTIONAL";
                    }
                    if (currentType == 'OPTIONAL') {
                        $scope.currentProductFillingType = "ALL";
                    }
                }
                $scope.productsByFillingType.ALL = $scope.productsByFillingType.MANDATORY.concat($scope.productsByFillingType.OPTIONAL);
                window.scrollTo({ top: 0, behavior: 'smooth' });
            };

                $scope.clearSavedHistory = function () {
                    appUtil.removeSavedInventoryList();
                    $scope.productList = null;
                    $scope.lastSaved = null;
                    $scope.resetFinalProductList();// $scope.selectFrequency("DAILY");
                };

                $scope.getProducts = function () {
                    if (isEmpty($scope.frequencyValue) || $scope.frequencyValue == undefined) {
                        $toastService.create("Please select stock take type event first");
                        return;
                    }
                    predictExpectedValues($scope.frequencyValue, true);
                };

                $scope.selectFrequencyAtIndex = function (index) {
                    var savedInventory = appUtil.getSavedInventoryList();
                    if ($scope.checkEmpty(savedInventory)) {
                        $scope.frequencyValue = $scope.frequencies[index];
                    } else {
                        $toastService.create("You cannot change the list once saved, Click on Clear Saved history to reset", 10000);
                        $scope.frequencyValue = savedInventory.stockType;
                    }
                    $scope.inventoryEventValid = true;
                    $scope.checkStockEventAvailable();
                };

                function checkAllStockEventsAvailable() {
                    if (!appUtil.isWarehouseOrKitchen()) {
                        var currentUser = appUtil.getCurrentUser();
                        var requestObject = {
                            unit: currentUser.unitId,
                            generatedBy: currentUser.userId,
                            eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                            inventoryResponse: null
                        };
                        var  businessDate = null;
                    if($scope.dayCloseBusinessDate != null){
                        businessDate = appUtil.formatDate($scope.dayCloseBusinessDate,"yyyy-MM-dd");
                    }$http({
                            method: "POST",
                            url: apiJson.urls.stockManagement.validateStockEvents,
                            data: requestObject,
                        params : {
                            eventBusinessDate : businessDate
                        }
                        }).then(function (response) {
                            if (response != null) {
                                $scope.availableFrequencies = [];
                                $scope.stockTakeEventTypes = response.data;
                                var flag1 = false;
                                var flag2 = false;
                                for (var i = 0; i < $scope.stockTakeEventTypes.length; i++) {
                                    if ($scope.stockTakeEventTypes[i].valid == true && i == 0) {
                                        $scope.availableFrequencies[0] = $scope.frequencies[1];
                                        flag1 = true;
                                    } else if ($scope.stockTakeEventTypes[i].valid == true && i == 1) {
                                        if (flag1 == false) {
                                            $scope.availableFrequencies[0] = $scope.frequencies[2];
                                            flag2 = true;
                                        } else {
                                            $scope.availableFrequencies[1] = $scope.frequencies[2];
                                            flag2 = true;
                                        }
                                    }
                                }
                                if (flag1 == false && flag2 == false) {
                                    $scope.availableFrequencies[0] = $scope.frequencies[0];
                                }
                                $scope.frequencies = $scope.availableFrequencies;
                                if ($scope.currentUser.userId == 125200) {
                                    $scope.frequencies.push("FIXED_ASSETS")
                                }
                            }
                        }, function (response) {
                            console.log(response);
                        })
                    }
                }

                $scope.checkStockEventAvailable = function () {
                    if (!appUtil.isWarehouseOrKitchen() && ($scope.frequencyValue == "WEEKLY" || $scope.frequencyValue == 'MONTHLY')) {
                        var currentUser = appUtil.getCurrentUser();
                        var requestObject = {
                            unit: currentUser.unitId,
                            generatedBy: currentUser.userId,
                            eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                            inventoryResponse: null
                        };
                        $http.post(apiJson.urls.stockManagement.validateStockEvent
                            + "?stockType=" + $scope.frequencyValue, requestObject).success(function (response) {
                            console.log(response);
                            if (response != null) {
                                if (response.valid != null) {
                                    $scope.inventoryEventValid = response.valid;
                                    $scope.inventoryValidationErrorMsg = response.errorMsg;
                                } else {
                                    $scope.inventoryEventValid = false;
                                    $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                                }
                            } else {
                                $scope.inventoryEventValid = false;
                                $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                            }
                        }).error(function (response) {
                            $scope.inventoryEventValid = false;
                            if (response.errorMsg != null) {
                                $scope.inventoryValidationErrorMsg = response.errorMsg;
                            } else {
                                $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                            }
                        })
                    } else {
                        $scope.inventoryEventValid = true;
                        $scope.inventoryValidationErrorMsg = null;
                    }
                };

                $scope.selectFrequency = function (frequency) {
                    $scope.frequencyValue = frequency;
                };

                $scope.checkClosingInitiated = function (unitId) {
                    $http({
                        method: "POST",
                        url: apiJson.urls.stockManagement.closingInitiated,
                        data: {
                            unitId: unitId,
                            businessDate: appUtil.getCurrentBusinessDate()
                        }
                    }).then(function (response) {
                        $scope.dayCloseEvent = response.data;
                        if ($scope.dayCloseEvent != null && $scope.dayCloseEvent != "") {
                            $scope.closingInitiated = true;
                            $scope.dayCloseBusinessDate = $scope.dayCloseEvent.businessDate;
                            checkAllStockEventsAvailable();
                            predictExpectedValues($scope.frequencyValue, false);
                        } else {
                            checkAllStockEventsAvailable();
                            $scope.closingInitiated = false;
                            $toastService.create("Closing Event Not Initiated !!");
                        }
                    }, function () {
                        console.log("Insert");
                    })
                };

                $scope.removeMapping = function (selectedPackagings, selected, product, makeZero) {
                    selected.value = null;
                    if (makeZero != undefined && makeZero != null && makeZero) {
                        selected.value = 0;
                    }
                    calculateVariance(selectedPackagings, product);
                };

                $scope.showFixedAssets = function (frequency) {
                    return frequency == "FIXED_ASSETS";
                };

            $scope.changeStock = function(product,value,packaging){
                if (!appUtil.isEmptyObject(value)) {
                    // if (appUtil.isFloat(value)) {
                    //     if (packaging.unitOfMeasure == "PC" || packaging.unitOfMeasure == "SACHET") {
                    //         $toastService.create("Quantity Can not be in Decimal value for : " + packaging.unitOfMeasure);
                    //         packaging.value = null;
                    //         value = 0;
                    //     }
                    //     if (packaging.packagingType != "LOOSE") {
                    //         $toastService.create("Quantity Can not be in Decimal value as the packaging is Not Loose");
                    //         packaging.value = null;
                    //         value = 0;
                    //     }
                    // }
                    if ((!$scope.checkEmpty(value) && parseFloat(value) >= 0)) {
                        var productId = product.productId;
                        var mappings = product.packagingMappings;
                        if (!$scope.checkEmpty(value) && !$scope.checkEmpty(packaging)) {
                            calculateVariance(mappings, product);
                            $scope.edited = true;                   //edited stock flag turn to true
                        }
                    } else if (!$scope.checkEmpty(value) && parseFloat(value) < 0) {
                        $toastService.create("Please select a value greater than or equal to zero");
                    }
                }else {
                    $scope.removeMapping(product.packagingMappings,packaging,product,false)
                }
            };

                function checkIfOpeningNeeded(unitId) {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.stockManagement.checkOpening,
                        params: {
                            unitId: unitId
                        }
                    }).then(function (response) {
                        $scope.isOpening = response.data;
                        if ($scope.isOpening) {
                            $scope.selectFrequency("MONTHLY");
                        }
                    }, function (response) {
                        console.log(response);
                        $scope.isOpening = false;
                    });
                }

                function saveInventory(products) {
                    var finalList = $scope.getFinalProductList();if (!$scope.checkEmpty(finalList) && finalList.length > 0 && $scope.edited) {
                        $toastService.create("Auto saving the inventory list");
                        $scope.lastSaved = Date.now();
                        appUtil.setSavedInventoryList({
                            unit: $scope.unitId,
                            products: !$scope.checkEmpty(products) ? products : finalList,
                            stockType: $scope.frequencyValue,
                            lastSaved: $scope.lastSaved
                        });
                        $scope.edited = false; // reset edited flag so that it can be re-saved on edit
                    }
                }

                $scope.getFinalProductList = function () {
                console.log("data is : ", $scope.productsByFillingType);
                var mandatoryProducts = !appUtil.isEmptyObject($scope.productsByFillingType.MANDATORY) ? $scope.productsByFillingType.MANDATORY : [];
                var optionalProducts = !appUtil.isEmptyObject($scope.productsByFillingType.OPTIONAL) ? $scope.productsByFillingType.OPTIONAL : [];
                $scope.productsByFillingType.ALL = mandatoryProducts.concat(optionalProducts);
                return $scope.productsByFillingType.ALL;
            };

            $scope.resetFinalProductList = function () {
                $scope.currentProductFillingType = "MANDATORY";
                $scope.productsByFillingType = {};
                $scope.mandatoryFillProducts = {};
                $scope.optionalFillProducts = {};
                $scope.productsByFillingType.MANDATORY = Object.values($scope.mandatoryFillProducts);
                $scope.productsByFillingType.OPTIONAL = Object.values($scope.optionalFillProducts);
                $scope.productsByFillingType.ALL = $scope.productsByFillingType.MANDATORY.concat($scope.productsByFillingType.OPTIONAL);
            };

            $scope.seggregateMandatoryOptionalProducts = function (products) {
                if (!appUtil.isEmptyObject(products)) {
                    $scope.productsByFillingType = {};
                    $scope.mandatoryFillProducts = {};
                    $scope.optionalFillProducts = {};
                    for (var i = 0; i < products.length; i++) {
                        var product = products[i];
                        if (!appUtil.isEmptyObject(product.opening) && product.opening != 0) {
                            $scope.mandatoryFillProducts[product.productId] = product;
                            continue;
                        }
                        if (!appUtil.isEmptyObject(product.transferred) && product.transferred != 0) {
                            $scope.mandatoryFillProducts[product.productId] = product;
                            continue;
                        }
                        if (!appUtil.isEmptyObject(product.wasted) && product.wasted != 0) {
                            $scope.mandatoryFillProducts[product.productId] = product;
                            continue;
                        }
                        if (!appUtil.isEmptyObject(product.received) && product.received != 0) {
                            $scope.mandatoryFillProducts[product.productId] = product;
                            continue;
                        }
                        if (!appUtil.isEmptyObject(product.consumption) && product.consumption != 0) {
                            $scope.mandatoryFillProducts[product.productId] = product;
                            continue;
                        }
                        $scope.optionalFillProducts[product.productId] = product;
                    }
                    $scope.productsByFillingType.MANDATORY = Object.values($scope.mandatoryFillProducts);
                    $scope.productsByFillingType.OPTIONAL = Object.values($scope.optionalFillProducts);
                    $scope.productsByFillingType.ALL = $scope.productsByFillingType.MANDATORY.concat($scope.productsByFillingType.OPTIONAL);
                }
            };function predictExpectedValues(frequencyValue, refresh) {
                    var currentUser = appUtil.getCurrentUser();
                    $http({
                        method: 'POST',
                        url: apiJson.urls.stockManagement.predictExpectedValues,
                        data: {
                            unitId: currentUser.unitId,
                            stockTakeType: frequencyValue,
                            userId: currentUser.userId,
                            stockEventType: $scope.isOpening ? "OPENING" : "CLOSING",
                        businessDate : $scope.dayCloseBusinessDate
                    }
                }).then(function(response){
                    if(!appUtil.checkEmpty(response)){
                        $scope.fixedAssetsOnly = (frequencyValue == "FIXED_ASSETS");
                        var productList = response.data.inventoryResponse;
                        packagingService.getAllPackagingMappings(function(packagingMappings){
                            $scope.packagingMappings = packagingMappings;
                            setProducts(productList);
                        });
                        var saved = appUtil.getSavedInventoryList();
                        if(!$scope.checkEmpty(saved) && !$scope.checkEmpty(saved.products)){
                            var savedList = angular.copy(saved.products);
                            var savedProductMap = [];

                                savedList.forEach(function (product) {
                                    savedProductMap[product.productId] = product;
                                });

                                productList.forEach(function (product) {
                                    var savedProduct = savedProductMap[product.productId];
                                    if (!$scope.checkEmpty(savedProduct) &&
                                        !$scope.checkEmpty(savedProduct.packagingMappings)) {
                                        product.packagingMappings = savedProduct.packagingMappings;calculateVariance(savedProduct.packagingMappings, product);
                                    }
                                });
                            }

                            $scope.productList = productList; // assigning products after massaging
                            $scope.seggregateMandatoryOptionalProducts($scope.productList);

                        }
                    }, function (response) {
                        console.log(response);
                    });
                }

                function getProductName(scmProductList, toFind) {
                    for (var index in scmProductList) {
                        var product = scmProductList[index];
                        if (product.productId == toFind.productId) {
                            toFind.productName = product.productName;
                            toFind.unitOfMeasure = product.unitOfMeasure;
                            return toFind;
                        }
                    }
                }





                function findValue(packaging, selectedPackagings) {
                    if (!appUtil.isEmptyObject(selectedPackagings)) {
                        for (var i = 0; i < selectedPackagings.length; i++) {
                            var selected = selectedPackagings[i];
                            if (packaging.packagingId == selected.packagingId) {
                                return selected.value;
                            }
                        }
                    }
                }

                function setProducts(productList) {
                    if (productList == undefined) {
                        productList = $scope.productList;
                    }
                    if ($scope.checkEmpty(packagingService.definitions)) {
                        $scope.packagings = appUtil.getPackagingMap();
                        packagingService.setAllProfiles($scope.packagings);
                    }
                    $rootScope.showSpinner = true;
                    var scmProductList = appUtil.getScmProductDetails();
                    productList.forEach(function (product) {
                        var productId = product.productId;
                        product = getProductName(scmProductList, product);
                        if (!appUtil.isEmptyObject(product)) {
                            var pkgValueMap = {};
                            if (!appUtil.isEmptyObject(product.packagingMappings)) {
                                product.packagingMappings.forEach(function (mpg) {
                                    if (!appUtil.isEmptyObject(mpg) && !appUtil.isEmptyObject(mpg.packagingId)) {
                                    pkgValueMap[mpg.packagingId] = mpg.value;
                                }
                                });
                            }
                            product = packagingService.setMappingsByProduct(product, $scope.packagingMappings[productId]);
                            product.packagingMappings.forEach(function (pkg) {
                            if (!appUtil.isEmptyObject(pkg)) {
                                pkg.value = (!appUtil.isEmptyObject(pkg.packagingId) && !appUtil.isEmptyObject(pkgValueMap[pkg.packagingId])) ? pkgValueMap[pkg.packagingId] : null;}
                            });
                        }
                    });
                    $rootScope.showSpinner = false;
                }

                function addToMappings(mappings, packaging, value) {
                    /* var added = false;
                     mappings.forEach(function(mapping){
                         if(mapping.packaging.packagingCode == packaging.packagingCode){
                             added = true;
                             mapping.value = value;
                         }
                     });
                     if(!added){
                         mappings.push({value:value,packaging:packaging});
                     }
                     return mappings;*/
                }

                function checkIfMappingsHaveValues(mappings) {
                    return mappings.filter(function (mapping) {
                        return !appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value);
                    }).length > 0;
                }

                $scope.getOrderBy = function () {
                if ($scope.currentProductFillingType == 'ALL') {
                    return ['duplicateStockValue', 'productName'];
                } else {
                    return ['productName'];
                }
            };function calculateVariance(mappings, product) {
                    if (mappings.length > 0 && checkIfMappingsHaveValues(mappings)) {
                        product.stockValue = 0;

                        mappings.forEach(function (mapping) {
                            if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value)) {
                                product.stockValue = parseFloat(product.stockValue)
                                    + (parseFloat(mapping.value) * parseFloat(mapping.conversionRatio));
                            }
                        });
                        product.variance = parseFloat(product.expectedValue - product.stockValue);
                    } else {
                        product.stockValue = !appUtil.isEmptyObject(product.expectedValue) ? parseFloat(product.expectedValue) : 0;product.variance = 0;
                }
                    product.duplicateStockValue =null;
                angular.forEach(product.packagingMappings, function (mapping) {
                    if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value)) {
                        if (product.duplicateStockValue == null) {
                            product.duplicateStockValue = 0;
                        }
                        product.duplicateStockValue +=mapping.value;
                    }
                    });
                }

                function checkInventoryUpdated(unitId) {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.stockManagement.inventoryUpdated,
                        data: {
                            unitId: unitId,
                            businessDate: appUtil.getCurrentBusinessDate()
                        }
                    }).then(function (response) {
                        $scope.inventoryUpdated = response.data;

                        if (response.data && notFixedAssets()) {
                            appUtil.removeSavedInventoryList();
                            $interval.cancel($scope.refreshInterval);
                            $scope.lastSaved = null;
                        }
                    }, function (response) {
                        console.log("got error", response);
                    });
                }

                $scope.refreshDayclose = function () {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.stockManagement.kettleDayCloseFromSumo,
                        params: {
                            unitId: $scope.unitId,
                        }
                    }).then(function (response) {
                        console.log(response);
                        if (response.data == true) {
                            $scope.checkClosingInitiated($scope.unitId)
                            $toastService.create("Day Closed Successfully Completed From Kettle!");
                        } else {
                            $toastService.create("Day Closed Cannot Be Completed Because Of Some Error!");
                        }
                    });
                }


                function notFixedAssets() {
                    var inventory = appUtil.getSavedInventoryList();
                    return !appUtil.isEmptyObject(inventory) && inventory.stockType != "FIXED_ASSETS";
                }
            }
        ]
    );
