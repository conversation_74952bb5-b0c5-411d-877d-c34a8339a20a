'use strict';
angular.module('scmApp').controller('viewServiceOrderCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService','$alertService','metaDataService','Popeye', '$window','$timeout',
    function ($rootScope, $stateParams, $scope, apiJson, $http, appUtil, $toastService,$alertService,metaDataService, Popeye, $window, $timeout) {

        function getCreatedSOs(view,startDate,endDate) {
            $scope.fetchSOs(view,startDate,endDate,false,null);
        }

        function getCreatedSOsShort(view,startDate,endDate) {
            $scope.fetchSOs(view,startDate,endDate,true,null);
        }

        $scope.fetchSOs = function (view,startDate,endDate,isShort , callback){
            $scope.pendingApprovalL1 = null;
            $scope.pendingApprovalL2 = null;
            $scope.approvedAmount = null;
            $scope.inProgessAmount = null;
            $scope.finApprovalL1 =null;
            if(appUtil.isEmptyObject(startDate)){
                $toastService.create("Please select a start date first");
                return;
            }
            if(appUtil.isEmptyObject(endDate)){
                $toastService.create("Please select a end date first");
                return;
            }

            var params = {
                isView:view,
                startDate:startDate,
                endDate:endDate,
                userId: appUtil.getCurrentUser().userId,
                showAll : $scope.showAll
            };

            if(!appUtil.isEmptyObject($scope.vendorSelected)){
                params["vendorId"] = $scope.vendorSelected.id;
            }
            if(!appUtil.isEmptyObject($scope.bccSelected)){
                params["bccId"] = $scope.bccSelected.id;
            }

            if(!appUtil.isEmptyObject($scope.serviceOrderId)){
                params["serviceOrderId"] = $scope.serviceOrderId;
            }

            if(!appUtil.isEmptyObject($scope.costCenterSelected)){
               params["costCenterId"] = $scope.costCenterSelected.id;
            }
            if(callback!=null){
                params["serviceOrderId"] = $scope.selectedSoId;
            }

            $scope.endpoint = apiJson.urls.serviceOrderManagement.createdOrders;
            if(isShort){
                $scope.endpoint =  apiJson.urls.serviceOrderManagement.createdOrdersShort;
            }
            $http({
                method: "GET",
                url: $scope.endpoint,
                params: params
            }).then(function (response) {
                console.log(response);
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Orders found!");
                } else {
                    if(callback!=null){
                        callback(response.data[0]);
                    }
                    else{
                        if(isShort){
                            $scope.soRequestShort = response.data.serviceOrderShortList;
                            if(!appUtil.isEmptyObject($scope.vendorSelected)){
                                $scope.pendingApprovalL1 = response.data.pendingApprovalL1;
                                $scope.pendingApprovalL2 = response.data.pendingApprovalL2;
                                $scope.pendingApprovalL3 = response.data.pendingApprovalL3;
                                $scope.approvedAmount = response.data.approvedAmount;
                                $scope.inProgessAmount = response.data.inProgessAmount;
                                $scope.finApprovalL1 = response.data.finApprovalL1;
                            }
                        }
                        else
                            $scope.soRequest = response.data;
                    }
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });

        }

        $scope.setEnterredText = function (text) {
            $scope.enterredText = text;
        };

        function updateSO(url, callback) {
            $alertService.confirm("Are you sure?","",function(result){
                if(result){
                    $http({
                        method: "POST",
                        url: url
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response)) {
                            callback(response.data);
                        }
                    }, function (response) {
                        if(response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        } else {
                            $toastService.create("Error Occurred While Updating the SO...!");
                            console.log("Encountered error at backend", response);
                        }
                    });
                }
            });

        }

        $scope.init = function () {
            var currentDate = appUtil.getCurrentBusinessDate();
            if(!appUtil.isEmptyObject(currentDate)){
                $scope.startDate = appUtil.formatDate(appUtil.calculatedDate(-30, currentDate), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.showViewActions = $stateParams.viewSO;
                $scope.createdSO = $stateParams.createdSO;
                $scope.soRequest = [];
                $scope.isCapexId=false;
                $scope.capexRequestId ="";
                $scope.summaryDepartmentList = [];
                $scope.capexSummaryLoading =false;
                $scope.soRequestShort = [];
                $scope.bccIds = [];
                $scope.selectedSO = null;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.showAll = false;
                $scope.minRefundDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");

                 metaDataService.getCostCenters(function (costCenters) {
                                    $scope.costCenters = costCenters;
                 });

                metaDataService.getServiceVendors(function(vendorsForUnit){
                    $scope.vendors = vendorsForUnit;
                });

                metaDataService.getAllBusinessCostCenters(function(bcc){
                    $scope.bcc = bcc;
                });
                $scope.gridOptions = appUtil.getGridOptions($scope);
                $scope.soList=[];

                //getCreatedSOs($scope.showViewActions, $scope.startDate, $scope.endDate,$scope.selectedVendor);
            }
        };

        $scope.reset = function () {
          $scope.selectedSKU = null;
          $scope.selectedVendor = null;
        };

        $scope.selectVendor = function (vendor) {
          $scope.vendorSelected = vendor;
        };

        $scope.selectBcc = function (bcc) {
            $scope.bccSelected = bcc;
        };

        $scope.selectCostCenter = function (costCenterId) {
                    $scope.costCenterSelected = costCenterId;
                };

        $scope.selectSKU = function (sku) {
            $scope.selectedSKU = sku;
        };

        $scope.downloadDocumentById = function (id){
            metaDataService.downloadDocumentById(id);
        }

        $scope.downloadSO = function(invoice){
            metaDataService.downloadDocument(invoice);
        };

        $scope.getSOs = function(showAll){
          $scope.showAll = showAll
          getCreatedSOsShort($scope.showViewActions,$scope.startDate,$scope.endDate);
        };

        $scope.changeCapexId = function(capexId){
            $scope.capexRequestId = capexId;
        }

        $scope.getSoByCapex = function(){
            var params={};

            if(!appUtil.isEmptyObject($scope.vendorSelected)){
                params["vendorId"] = $scope.vendorSelected.id;
            }

            if(!appUtil.isEmptyObject($scope.capexRequestId)){
                params["capexRequestId"] = $scope.capexRequestId;
            }else{
                alert("Please Provide Capex Request Id");
                return;
            }
               
            $http({
                method:"GET",
                url : apiJson.urls.capexManagement.getSoByCapex,
                params : params
            }).then(function success(response){
                console.log(response);
                $scope.soRequestShort = response.data.serviceOrderShortList;
            }, function error(err){
                console.log(err);
            });

                $scope.fetchBudgetSummary();            
            
        }

        

        $scope.showDetailsSO =  function  (so) {
            $scope.selectedSoId = so.id
            $scope.fetchSOs($scope.showViewActions,appUtil.convertToDateWithoutTime(so.generationTime) ,appUtil.convertToDateWithoutTime(so.generationTime),false,$scope.showDetails);

        };

        $scope.checkCapexId = function(){
            $scope.isCapexId = !$scope.isCapexId;
            $scope.vendorSelected = null;
            $scope.soRequestShort = null;
            $scope.capexSummaryShow = false;
            $scope.summaryDepartmentList= [];
            $scope.capexRequestId=null;
            $timeout(function(){ $('#enterCapexId').val(null).trigger('change');});
        }

        $scope.fetchBudgetSummary = function(){
            $scope.capexSummaryShow = false;
            $scope.capexSummaryLoading = true;
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.fetchBudgetDetails,
                params: {
                    capexRequestId: $scope.capexRequestId
                }
            }).then(function success(response) {
                if (response) {
                    if(response!=null && response.data.length>0){
                        $scope.summaryDepartmentList = response.data.filter(function(el){ return el.totalSO != 0});
                        $scope.capexSummaryShow = true;
                    }
                } else {
                    $toastService.create("Error in showing Capex Summary.");
                }
                $scope.capexSummaryLoading = false;
            }, function error(response) {
                console.log("error:" + response);
                $scope.capexSummaryLoading = false;
            });
        }


        $scope.showSO = function (item) {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getSOByDepartment,
                params: {
                    capexRequestId:  $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    $scope.soList = response.data;
                    $scope.selectedItem = item.departmentName;
                    if (item.expanded != null) {
                        item.expanded = !item.expanded;
                    } else {
                        item.expanded = true;
                    }
                    var gridItemaArr = [];
                    $scope.soList.forEach(function (so) {
                        so.orderItems.forEach(function (soItem) {
                            var receivingPrice = soItem.receivedQuantity * soItem.unitPrice;
                            soItem.receivingAmount = (receivingPrice + (receivingPrice * soItem.taxRate) / 100);
                            soItem.vendorName = so.vendorName;
                            soItem.status = so.status;
                            gridItemaArr.push(soItem);
                        })
                    });
                    if (item.expanded === true) {
                        $scope.gridOptions.data = gridItemaArr;
                        $scope.gridOptions.columnDefs = $scope.showSOs();
                    }
                    if (item.expanded === false) {
                        $scope.selectedItem = null;
                    }
                }
            });

        }
        $scope.isSelectedItem = function (srItem) {
            return srItem.expanded == true;
        }

        $scope.showSOs = function () {
            return [
                {
                    field: 'serviceOrderId',
                    name: 'serviceOrderId',
                    enableCellEdit: false,
                    displayName: 'SO Id'
                }, {
                    field: 'id',
                    name: 'serviceOrderItemId',
                    displayName: 'Service Order Item id'
                }
                , {
                    field: 'costElementName',
                    name: 'costElementName',
                    enableCellEdit: false,
                    displayName: 'Cost Element Name'
                }, {
                    field: 'vendorName',
                    name: 'vendorName',
                    enableCellEdit: false,
                    displayName: 'Vendor Name'
                }, {
                    field: 'serviceDescription',
                    name: 'serviceDescription',
                    enableCellEdit: false,
                    displayName: 'Service Description',
                }, {
                    field: 'totalCost',
                    name: 'totalCost',
                    enableCellEdit: false,
                    displayName: 'Cost'
                }, {
                    field: 'totalTax',
                    name: 'totalTax',
                    enableCellEdit: false,
                    displayName: 'Tax'
                }, {
                    field: 'amountPaid',
                    name: 'amountPaid',
                    enableCellEdit: false,
                    displayName: 'Total Amount'
                }, {
                    field: 'receivingAmount',
                    name: 'receivingAmount',
                    enableCellEdit: false,
                    displayName: 'Receiving Amount'
                }, {
                    field: 'paidAmount',
                    name: 'paidAmount',
                    enableCellEdit: false,
                    displayName: 'Paid Amount',
                },
                , {
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'Status',
                }
            ];
        };


        $scope.showDetails = function (so) {
            var viewDetailModal = Popeye.openModal({
                templateUrl: "viewSODetail.html",
                controller: "viewSODetailCtrl",
                resolve: {
                    so: function () {
                        return so;
                    },
                    check: function () {
                        return false;
                    },
                    status: function () {
                        return false;
                    }
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });
            viewDetailModal.closed.then(function (check) {
            });
        }

        $scope.changeStatusSO = function (item,status){
            $scope.selectedSoId = item.id
            $scope.fetchSOs($scope.showViewActions,appUtil.convertToDateWithoutTime(item.generationTime) ,appUtil.convertToDateWithoutTime(item.generationTime),
                false,function (item){
            $scope.changeStatus(item,status)
            } )
        }

        $scope.changeStatus = function(item, status){
          if(status == "APPROVED" && item.type != "OPEX"){
            var modalInstance = Popeye.openModal({
                templateUrl: 'departmentModal.html',
                controller: 'departmentModalCtrl',
                size: 'lg',
                windowClass: 'my-modal-popup',
                resolve: {
                	serviceItemsDept : function () {
                        return item;
                    },
                    status:function () {
                        return status == 'APPROVED' ? true : false;
                    }
                },
                click: false,
                keyboard: false
            });

            modalInstance.closed.then(function(check) {
                    if(check){
                    	$scope.updateStatus(item, status);
                    }
            });
           }
           else{
        	   $scope.showDetailsStatus(item, status);
        	//	$scope.updateStatus(item, status);
           }
        };

        $scope.showDetailsStatus = function (so, status) {
            console.log(status)
            var viewDetailModal = Popeye.openModal({
                templateUrl: "viewSODetail.html",
                controller: "viewSODetailCtrl",
                resolve: {
                    so: function(){
                        return so;
                    },
                    check: function(){
                        return true;
                    },
                    status:function () {
                       return status == 'APPROVED' ? true : false;
                    }
                },
                modalClass:'custom-modal',
                click: false,
                keyboard: false
            });
            viewDetailModal.closed.then(function(state) {
            	if(state){
                	$scope.updateStatus(so, status);
            	}
        });
        };

        $scope.updateStatus = function(item, status){
            $http({
                method: "GET",
                url: apiJson.urls.serviceOrderManagement.changeStatus,
                params: {
                	soId: item.id,
                	newStatus: status,
                	currentStatus: item.status,
                	amount: item.totalAmount,
                	userId: appUtil.getCurrentUser().userId
                },
            }).then(function success(response) {
            	if(response.data!=null){
            	$toastService.create("Status updated successfully.");
            	if(response.data == "APPROVED" || response.data.includes("REJECTED")){
                  $scope.soRequestShort = $scope.soRequestShort.filter(function(so){
                       return so.id != item.id
                  });
            	}else{
            	   $scope.soRequestShort.forEach(function(so){
            	      if(so.id == item.id){
            	        so.status = response.data;
            	      }
            	   });
            	}
            	//getCreatedSOsShort($scope.showViewActions,$scope.startDate,$scope.endDate);
            	} else {
                    $toastService.create("Error in updating Service Order Status.");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }


        $scope.approve = function (approveID) {
            var url = apiJson.urls.serviceOrderManagement.approveSO + "/" + approveID + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if(updated){
                    $alertService.alert("Congratulations!!",
                        "Service Order: "+approveID+" approved successfully <br> Vendor has also been notified",
                        function(result){
                            getCreatedSOsShort($scope.showViewActions,$scope.startDate,$scope.endDate);
                        });
                }else{
                    $toastService.create("Service Order approval failed! Please try again later..");
                }
            });
        };

        $scope.reject = function (approveID, index) {
            var url = apiJson.urls.serviceOrderManagement.rejectSO + "/" + approveID + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if(updated){
                    $toastService.create("Service Order rejected successfully");
                    $scope.soRequestShort[index].status = "REJECTED";
                }else{
                    $toastService.create("Service Order rejection failed! Please try again later..");
                }
            });
        };

        $scope.getSosForAdvance = function () {
            $scope.pendingPoSo = [];
            $scope.errorMessage = null;
            $http({
                method: 'GET',
                url: apiJson.urls.paymentRequestManagement.getSosForAdvance,
                params: {
                    vendorId: $scope.cancelObject.vendorId
                }
            }).then(function success(response) {
                $scope.pendingPoSo = response.data;
                var finalPoSo = [];
                for (var i = 0; i < $scope.pendingPoSo.length; i++) {
                    if ($scope.pendingPoSo[i].id != $scope.cancelObject.soId) {
                        finalPoSo.push($scope.pendingPoSo[i]);
                    }
                }
                $scope.pendingPoSo = finalPoSo;
                angular.forEach($scope.pendingPoSo, function (poSo) {
                    poSo.maxLimit = parseFloat((poSo.totalAmount + appUtil.getAdvanceBufferAmount(poSo.totalAmount)).toFixed(0));
                });
            }, function error(response) {
                $scope.pendingPoSo = [];
                if(response.data.errorMsg != null) {
                    $scope.errorMessage = response.data.errorMsg;
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                } else{
                    $toastService.create("Error Occurred While Creating Advance Payment...!");
                    console.log("error:" + response);
                }
            });
        };

        $scope.disableSosSelection = function ($event, po, poSoList) {
            $scope.poSoSelected = null;
            if ($event.target.checked) {
                if (po.maxLimit < $scope.cancelObject.completeAvailableAmount) {
                    $toastService.create("Can not adjust to the SO which has less amount than the remaining advance..!");
                    po.checked = false;
                    return;
                }
                po.disable = false;
                angular.forEach(poSoList, function (poSo) {
                    if (po.id == poSo.id) {
                        poSo.disable =  false;
                        $scope.poSoSelected = po;
                    } else {
                        poSo.disable = true;
                    }
                });
            } else {
                $scope.poSoSelected = null;
                angular.forEach(poSoList, function (poSo) {
                    poSo.disable = false;
                });
            }
        };

        $scope.setRefundDate = function (refundDate) {
            $scope.cancelObject.refundDate = refundDate;
        };

        $scope.closeAdjustRefund= function (so) {
            $scope.poSoSelected = null;
            so.adjustOrRefund = false;
        };

        $scope.setAdjustOrRefund = function (flag) {
            $scope.cancelObject.adjustOrRefund = flag;
            $scope.poSoSelected = null;
            $scope.cancelObject.refundDate = null;
            $scope.enterredText = null;
            if ($scope.cancelObject.adjustOrRefund == 'Adjust') {
                $scope.getSosForAdvance();
            }
        };

        $scope.submitAdjustmentRefund = function (poSo,so) {
            if (poSo != undefined && poSo != undefined) {
                $scope.cancelObject.selectedSoPo = poSo.id;
            }
            var type = so.status == 'APPROVED' ? 'Cancel' : 'Close';
            $scope.cancelObject.finalAvailable = $scope.cancelObject.completeAvailableAmount;
            $http({
                method: 'POST',
                url: apiJson.urls.paymentRequestManagement.submitAdjustmentRefund,
                params: {
                    createdBy: appUtil.getCurrentUser().userId
                },
                data: $scope.cancelObject
            }).then(function success(response) {
                if (response.status == 200) {
                    if (response.data) {
                        $toastService.create("So "+type+" Initiated..!");
                        $scope.init();
                    } else {
                        $toastService.create("Error While Initiating the So "+type+" Process..!");
                    }
                }
            }, function error(response) {
                if(response.data.errorMsg != null) {
                    $scope.errorMessage = response.data.errorMsg;
                    if (response.data.errorTitle == 'Advance Related to this is already Sent for adjustment') {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () {
                            $scope.init();
                        }, true);
                    } else {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () {
                            $scope.setAdjustOrRefund('Adjust');
                        }, true);
                    }
                } else{
                    $toastService.create("Error While Initiating the So Cancel Process..!");
                    console.log("error:" + response);
                }
            });
        };

        $scope.openHodActionsModal = function (soData) {
            var hodActionsModal = Popeye.openModal({
                templateUrl: "hodActionsModal.html",
                controller: "hodSoActionsModalCtrl",
                modalClass: 'custom-modal',
                resolve: {
                    so: function () {
                        return soData;
                    }
                },
                click: false,
                keyboard: false
            });

            hodActionsModal.closed.then(function (result) {
                if (result) {
                    $scope.init();
                }
            });
        };

        $scope.cancel = function (selectedOrder, index) {
            if (selectedOrder.vendorAdvancePayments != null) {
                var msg = "";
                var advanceAdjustInitiatedMsg = "";
                var pendingHodApprovalMsg = "";
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'INITIATED') {
                        msg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }
                    if (selectedOrder.vendorAdvancePayments[i].selectedSoPo != null) {
                        msg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }

                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'PENDING_HOD_APPROVAL') {
                        pendingHodApprovalMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }

                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'ADJUST_INITIATED') {
                        advanceAdjustInitiatedMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }
                }
                if (msg != "") {
                    $alertService.alert("Please Settle the Vendor Advance Related to this SO", msg + "<br>" +
                        "Please Settle the vendor Advance to CANCEL the Purchase Order..!", function () {
                    }, false);
                    return false;
                }
                if (pendingHodApprovalMsg != "") {
                    $alertService.alert("This SO is already sent for Adjustment", msg + "<br>" +
                        "Please get the HOD Approval to cancel this SO..!", function () {
                    }, false);
                    return false;
                }

                if (msg != "") {
                    $alertService.alert("This SO has Some Adjustment of Vendor Advance", msg + "<br>" +
                        "Please get the HOD Approval Of  SO..!", function () {
                    }, false);
                    return false;
                }

                var allCreated = true;
                var totalPrAmount = 0;
                var totalAvailableAmount = 0;
                var totalBlockedAmount = 0;
                var firstCreated = null;
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'CREATED' && firstCreated == null) {
                        firstCreated = selectedOrder.vendorAdvancePayments[i];
                    }
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus != 'CREATED') {
                        allCreated = false;
                        break;
                    } else {
                        totalPrAmount = totalPrAmount + selectedOrder.vendorAdvancePayments[i].prAmount;
                        totalAvailableAmount = totalAvailableAmount + selectedOrder.vendorAdvancePayments[i].availableAmount;
                        totalBlockedAmount = totalBlockedAmount + selectedOrder.vendorAdvancePayments[i].blockedAmount;
                    }
                }

                if (allCreated && totalPrAmount === totalAvailableAmount) {
                    $scope.cancelObject = angular.copy(firstCreated);
                    $scope.cancelObject.completePrAmount = angular.copy(totalPrAmount);
                    $scope.cancelObject.completeAvailableAmount = angular.copy(totalAvailableAmount);
                    $scope.cancelObject.completeBlockedAmount = angular.copy(totalBlockedAmount);
                    $toastService.create("Adjust/Refund the Vendor Advance to Some Other SO..!");
                    angular.forEach($scope.poRequest, function (po) {
                        po.adjustOrRefund = false;
                    });
                    $scope.poSoSelected = null;
                    selectedOrder.adjustOrRefund = true;
                    return;
                }
            }

            findProvisionalSrLinkedToSo(selectedOrder.id).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $alertService.confirm("These SR(s) would be CANCELLED",
                        "Some unapproved/provisional SR(s) were found <br><b>" + "SR ID's " + response.data + "</b>" +
                        "<br>Do you want to continue?",
                        function (isConfirmed) {
                            if (isConfirmed) {
                                sendCancelSORequest(selectedOrder, index);
                            }
                        });
                } else {
                    sendCancelSORequest(selectedOrder, index);
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });

        };

        $scope.close = function (closureId, index, selectedOrder) {
            if (selectedOrder.vendorAdvancePayments != null) {
                var msg = "";
                var advanceAdjustInitiatedMsg = "";
                var pendingHodApprovalMsg = "";
                var pendingSrs = [];
                var pendingPrs = [];
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].pendingSrs != null && selectedOrder.vendorAdvancePayments[i].pendingSrs.length > 0) {
                        for (var j = 0; j < selectedOrder.vendorAdvancePayments[i].pendingSrs.length; j++) {
                            if (pendingSrs.indexOf(selectedOrder.vendorAdvancePayments[i].pendingSrs[j]) === -1) {
                                pendingSrs.push(selectedOrder.vendorAdvancePayments[i].pendingSrs[j]);
                            }
                        }
                    }

                    if (selectedOrder.vendorAdvancePayments[i].pendingPrs != null && selectedOrder.vendorAdvancePayments[i].pendingPrs.length > 0) {
                        for (var j = 0; j < selectedOrder.vendorAdvancePayments[i].pendingPrs.length; j++) {
                            if (pendingPrs.indexOf(selectedOrder.vendorAdvancePayments[i].pendingPrs[j]) === -1) {
                                pendingPrs.push(selectedOrder.vendorAdvancePayments[i].pendingPrs[j]);
                            }
                        }
                    }
                    if (selectedOrder.vendorAdvancePayments[i].selectedSoPo != null) {
                        msg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }

                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'ADJUST_INITIATED') {
                        advanceAdjustInitiatedMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }

                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'PENDING_HOD_APPROVAL') {
                        pendingHodApprovalMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }
                }
                if (pendingSrs.length > 0) {
                    $alertService.alert("Please Settle all the pending SR's related to this SO..!", "Pending SR's related to this SO are : " +
                        pendingSrs.join(",") + "<br><b>Settle Pending SR's/Complete the Payment Of above SR's</b>", function () {
                    }, true);
                    return false;
                }

                if (pendingPrs.length > 0) {
                    $alertService.alert("Please Settle all the pending PR's related to this SO..!", "Pending P's related to this SO are : " +
                        pendingPrs.join(",") + "<br><b>Settle Pending PR's/Complete the Payment Of above SR's</b>", function () {
                    }, true);
                    return false;
                }
                if (msg != "") {
                    $alertService.alert("Please Settle the Vendor Advance Related to this SO", msg + "<br>" +
                        "Please Settle the vendor Advance to Close the Service Order..!", function () {
                    }, true);
                    return false;
                }
                if (advanceAdjustInitiatedMsg != "") {
                    $alertService.alert("This SO has Some Adjustment of Vendor Advance", msg + "<br>" +
                        "Please get the HOD Approval to cancel this SO..!", function () {
                    }, false);
                    return false;
                }
                if (pendingHodApprovalMsg != "") {
                    $alertService.alert("This SO is already sent for Adjustment", "Adjustment of Advance of Rs : " + msg + "<br>" +
                        "Please get the HOD Approval to Close this SO..!", function () {
                    }, true);
                    return false;
                }
                var hasCreated = false;
                var firstCreated = null;
                var totalPrAmount = 0;
                var totalAvailableAmount = 0;
                var totalBlockedAmount = 0;
                var advPaymentIds = [];
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus === 'CREATED' && firstCreated == null) {
                        hasCreated = true;
                        firstCreated = selectedOrder.vendorAdvancePayments[i];
                    }
                    totalPrAmount = totalPrAmount + selectedOrder.vendorAdvancePayments[i].prAmount;
                    totalAvailableAmount = totalAvailableAmount + selectedOrder.vendorAdvancePayments[i].availableAmount;
                    totalBlockedAmount = totalBlockedAmount + selectedOrder.vendorAdvancePayments[i].blockedAmount;
                    advPaymentIds.push(selectedOrder.vendorAdvancePayments[i].advancePaymentId);
                }

                if (hasCreated && totalAvailableAmount > 0) {
                    $scope.cancelObject = angular.copy(firstCreated);
                    $scope.cancelObject.completePrAmount = angular.copy(totalPrAmount);
                    $scope.cancelObject.completeAvailableAmount = angular.copy(totalAvailableAmount);
                    $scope.cancelObject.completeBlockedAmount = angular.copy(totalBlockedAmount);
                    $toastService.create("Adjust/Refund the Vendor Advance to Some Other SO to Close this SO..!");
                    angular.forEach($scope.soRequestShort, function (so) {
                        so.adjustOrRefund = false;
                    });
                    $scope.poSoSelected = null;
                    selectedOrder.adjustOrRefund = true;
                    return;
                }
            }
            findProvisionalSrLinkedToSo(closureId).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $alertService.confirm("These SR(s) would be CANCELLED",
                        "Some unapproved/provisional SR(s) were found <br><b>" + "SR ID's " + response.data + "</b>" +
                        "<br>Do you want to continue?",
                        function (isConfirmed) {
                            if (isConfirmed) {
                                sendCloseSORequest(closureId, index);
                            }
                        });
                } else {
                    sendCloseSORequest(closureId, index);
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

        function sendCancelSORequest(selectedOrder, index) {
            var url = apiJson.urls.serviceOrderManagement.cancelSO + "/" + selectedOrder.id + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if(updated){
                    $toastService.create("Service Order cancelled successfully");
                    $scope.soRequestShort[index].status = "CANCELLED";
                }else{
                    $toastService.create("Service Order cancellation failed! Please try again later..");
                }
            });
        }

        function sendCloseSORequest(closureId, index) {
            var url = apiJson.urls.serviceOrderManagement.closeSO + "/" + closureId + "/" + $scope.currentUser.userId;
            updateSO(url, function (updated) {
                if (updated) {
                    $toastService.create("Service Order closed successfully");
                    $scope.soRequestShort[index].status = "CLOSED";
                } else {
                    $toastService.create("Service Order closure failed! Please try again later..");
                }
            });
        }

        $scope.printInvoice = function(soInvoiceDocument, index) {
            metaDataService.downloadDocument(soInvoiceDocument);
        };

        function findProvisionalSrLinkedToSo(soId) {
            var url = apiJson.urls.serviceOrderManagement.getSRIdsForSO + "/" + soId;
            return $http({
                method: "GET",
                params: {
                    srStatus: "PROVISIONAL"
                },
                url: url
            });
        }
    }
]).controller('viewSODetailCtrl', ['$scope','so', 'appUtil','apiJson','$http', 'Popeye', '$toastService','$alertService','check','status','metaDataService',
	function ($scope,so,appUtil,apiJson,$http, Popeye,$toastService,$alertService,check,status,metaDataService) {
    $scope.initViewModal = function () {
    	$scope.billAmount = 0;
    	$scope.totalTaxes = 0;
    	$scope.paidAmount = 0;
        $scope.so = so;
        $scope.check = check;
        $scope.status=status;
        $scope.getSummaryData();
    };

    $scope.getSummaryData = function (){
    	for(var x = 0 ; x < $scope.so.orderItems.length ; x++){
    		$scope.billAmount = $scope.so.orderItems[x].totalCost + $scope.billAmount;
    		$scope.totalTaxes = $scope.so.orderItems[x].totalTax + $scope.totalTaxes;
    		$scope.paidAmount = $scope.so.orderItems[x].amountPaid + $scope.paidAmount;
    	}
    }

    $scope.downloadDocument = function(document){
        metaDataService.downloadDocument(document);
    };

    $scope.downloadDocumentById = function (id){
            metaDataService.downloadDocumentById(id);
    }

    $scope.submit = function(){
 		$scope.closeModal(true);
 	}

 	$scope.cancel = function(){
 		$scope.closeModal(false);
 	}

 	 $scope.closeModal = function closeModal(status) {
         Popeye.closeCurrentModal(status);
     }


}]).controller('departmentModalCtrl', ['$scope', 'appUtil','apiJson','$http', 'Popeye', '$toastService','$alertService','serviceItemsDept','status','metaDataService',
     function ($scope, appUtil,apiJson,$http, Popeye,$toastService,$alertService,serviceItemsDept,status,metaDataService) {


 	$scope.init = function (){
        $scope.billAmount = 0;
        $scope.totalTaxes = 0;
        $scope.paidAmount = 0;
 		$scope.summaryItem = [];
 		$scope.status=status;
 		$scope.selectedSOR = serviceItemsDept;
 		$scope.getDepartmentData(serviceItemsDept);
        $scope.getSummaryData();
 	}

     $scope.getSummaryData = function (){
         for(var x = 0 ; x < $scope.selectedSOR.orderItems.length ; x++){
             $scope.billAmount = $scope.selectedSOR.orderItems[x].totalCost + $scope.billAmount;
             $scope.totalTaxes = $scope.selectedSOR.orderItems[x].totalTax + $scope.totalTaxes;
             $scope.paidAmount = $scope.selectedSOR.orderItems[x].amountPaid + $scope.paidAmount;
         }
     }

         $scope.downloadDocumentById = function (id){
             metaDataService.downloadDocumentById(id);
         }


 	$scope.getDepartmentData = function(serviceItemsDeptData){
 	   $http({
           url: apiJson.urls.serviceOrderManagement.getDepartmentData,
           method: "POST",
           data : serviceItemsDeptData.orderItems
       }).then(function (response) {
           if(response.data){
        	   $scope.summaryItem = response.data
           }
       }, function (response) {
           console.log(response);
       });
 	}

 	$scope.submit = function(){
        Popeye.closeCurrentModal(true);
 	}

 	$scope.cancel = function(){
        Popeye.closeCurrentModal(false);
 	}

}]).controller('hodSoActionsModalCtrl', ['$scope', 'so', 'Popeye','$http','apiJson', 'appUtil', '$toastService','$alertService',
    function ($scope, so,Popeye,$http,apiJson, appUtil, $toastService, $alertService) {
        $scope.so = so;

        $scope.initHodModal = function () {
            $scope.amount = null;
            $scope.lastStatus = null;
            $scope.isAdjusted = null;
            $scope.selectedSoPo = null;
            $scope.refundSelectedDate = null;
            $scope.setAdvanceDetails();
        };


        $scope.close = function () {
            Popeye.closeCurrentModal(true);
        };

        $scope.setAdvanceDetails = function () {
            var amount = 0;
            for (var i = 0; i < $scope.so.vendorAdvancePayments.length; i++) {
                amount += $scope.so.vendorAdvancePayments[i].availableAmount;
                if ($scope.so.vendorAdvancePayments[i].selectedSoPo != null) {
                    $scope.selectedSoPo = $scope.so.vendorAdvancePayments[i].selectedSoPo;
                }
                if ($scope.so.vendorAdvancePayments[i].lastPoSoStatus != null) {
                    $scope.lastStatus = $scope.so.vendorAdvancePayments[i].lastPoSoStatus;
                }
                if ($scope.so.vendorAdvancePayments[i].selectedSoPo != null) {
                    $scope.isAdjusted = true;
                }
                if ($scope.so.vendorAdvancePayments[i].refundDate != null) {
                    $scope.refundSelectedDate = $scope.so.vendorAdvancePayments[i].refundDate;
                }
            }
            $scope.amount = amount;
        };

        $scope.approveRejectAdjustmentRefund = function(value) {

            var type = $scope.lastStatus == 'APPROVED' ? 'Cancel' : 'Close';
            $http({
                method: 'POST',
                url: apiJson.urls.paymentRequestManagement.approveRejectAdjustmentRefund,
                params: {
                    approvedBy: appUtil.getCurrentUser().userId,
                    approveReject:value
                },
                data: $scope.so.vendorAdvancePayments[0]
            }).then(function success(response) {
                if (response.status == 200) {
                    if (response.data) {
                        $toastService.create("SO "+type+" " + value);
                        $scope.close();
                    } else {
                        $toastService.create("Can not"+ type +" the Service Order..! ( " + value + " )" );
                    }
                }
            }, function error(response) {
                if(response.data.errorMsg != null) {
                    $scope.errorMessage = response.data.errorMsg;
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                } else{
                    $toastService.create("Error While submitting the  Process..!");
                    console.log("error:" + response);
                }
            });
        };
    }]);
