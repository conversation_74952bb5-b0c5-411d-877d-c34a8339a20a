'use strict';

angular.module('scmApp')
    .controller('wastageWHCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','productService','$toastService','$alertService','metaDataService',
        function ($rootScope, $scope, apiJson, $http, appUtil, productService,$toastService,$alertService,metaDataService) {


            $scope.checkEmpty = appUtil.checkEmpty;
            function getWastageEvents(unitId,businessDate, callback) {
                $http({
                    method:'POST',
                    url:apiJson.urls.stockManagement.wastageEvents,
                    data:{
                        unitId:unitId,
                        businessDate:businessDate
                    }
                }).then(function(response){
                    callback(response.data);
                },function(response){
                   console.log("got error",response);
                });
            }
            
            function getWhWastageEventsForDays(unitId,startDate,endDate, callback) {
                $http({
                    method:'POST',
                    url:apiJson.urls.stockManagement.wastageEventsForDays,
                    params:{
                        startDate:startDate,
                        endDate:endDate,
                        unitId:unitId
                    }
                }).then(function(response){
                    callback(response.data);
                },function(response){
                   console.log("got error",response);
                });
            }
            
            $scope.init = function () {
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.unitId = $scope.currentUser.unitId;
                $scope.userId = $scope.currentUser.userId;
                $scope.comment = null;
                $scope.minDate = appUtil.getTimeInPast(7);
                $scope.businessDate = appUtil.getCurrentBusinessDate();
                $scope.wastedItems = [];
                $scope.todaysWastage = [];
                $scope.wastageEvents = [];
                $scope.skuInventoryMap = {};

                metaDataService.getUnitToSkuMappings($scope.unitId,function(response){
                    $rootScope.showFullScreenLoader = true;
                    $scope.skuIdsForUnit = response;
                    console.log("Mapped SKU Id size ::::",$scope.skuIdsForUnit.length);
                    setProducts();
                    $rootScope.showFullScreenLoader = false;
                });
                $scope.getSkuInventoryData();
            };

            $scope.getSkuInventoryData = function () {
                $scope.availableInventorySkuIds = [];
                $http({
                    url: apiJson.urls.stockManagement.cafeProductStockAtHand,
                    method: 'GET',
                    params: {
                        unitId: $scope.unitId,
                        keyType: "SKU"
                    }
                }).then(function success(stock) {
                    if (appUtil.isEmptyObject(stock.data)) {
                        $toastService.create("could not fetch current Inventory for this unit");
                    } else {
                        $scope.skuInventoryMap = stock.data;
                        angular.forEach(stock.data , function (value , key) {
                            if (value > 0) {
                                $scope.availableInventorySkuIds.push(key);
                            }
                        });
                        console.log("inventory map is : ",$scope.skuInventoryMap);
                    }
                }, function error() {
                    $toastService.create("could not fetch current stock at hand for this unit");
                });
            };

            $scope.AddBulkSkuWastage = function () {
                $scope.wastedItems = [];
                $scope.missedBulkSkuWastage = [];
                angular.forEach($scope.skuInventoryMap, function (value, key) {
                    if (value > 0) {
                        $scope.addNewItem(key, value);
                    }
                });
                if ($scope.missedBulkSkuWastage.length > 0) {
                    $toastService.create("Unable to set Quantities for Product : " + $scope.missedBulkWastage.join(","));
                }
            };

            function setProducts(){
                $scope.products =[];
                var skuMap = {};
                Object.values(appUtil.getSkuProductMap()).concat().forEach(function(skuList){
                    for(var i in skuList){
                        var sku = skuList[i];
                        if($scope.skuIdsForUnit.indexOf(sku.skuId)!=-1){
                            skuMap[sku.skuId] = sku;
                        }
                    }
                });
                $scope.products = Object.values(skuMap);
                console.log("Mapped SKU Id size after mapping to products ::::",$scope.products.length);
                $scope.packagingMap = appUtil.getPackagingMap();
                $scope.selectedProductId = $scope.products[0].skuId;
                getWastageEvents($scope.unitId,appUtil.getCurrentBusinessDate(), function(items){
                    $scope.todaysWastage = items;
                    $scope.groupTodayWastageBySkuId(items);
                });
            }

            function calculateWastage(item) {
                var currentQuantity = item.quantity;
                item.quantity = 0;
                for(var index in item.skuPackagings){
                    var mapping = item.skuPackagings[index];
                    if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.quantity)){
                        item.quantity += (mapping.quantity * mapping.packaging.conversionRatio);
                    }
                }

                if(item.quantity<0){
                    $toastService.create("Wastage quantity cannot be less than zero");
                    item.quantity = currentQuantity;
                }
            }

            function getSKU(skuId){
                for (var i in $scope.products){
                    if($scope.products[i].skuId==skuId){
                        return $scope.products[i];
                    }
                }
                return null;
            }

            $scope.changeQuantity = function(roi){
              calculateWastage(roi);
            };

            $scope.addNewItem = function(skuId, qty){
                var found = false;
                if(!appUtil.isEmptyObject($scope.businessDate) && !appUtil.checkEmpty(skuId)){
                    $scope.wastedItems.forEach(function (roi) {
                        if(roi.sku.skuId == skuId){
                            found = true;
                            $toastService.create("Product already added!");
                            return false;
                        }
                    });
                    if(!found){
                        var selectedSKU = getSKU(skuId);
                        if(selectedSKU!=null){
                            var item = {
                                sku : selectedSKU,
                                skuId : selectedSKU.skuId,
                                quantity: 0,
                                comment:"Wasted",
                                uom: selectedSKU.unitOfMeasure,
                                skuPackagings: selectedSKU.skuPackagings.map(function(mapping){
                                    return {
                                        packaging: $scope.packagingMap[mapping.packagingId],
                                        mapping:mapping,
                                        quantity:null
                                    };
                                })
                            };
                            $scope.wastedItems.push(item);
                            if (qty != undefined && qty != null && qty > 0) {
                                var check = false;
                                angular.forEach(item.skuPackagings, function (pack) {
                                    if (pack.packaging.packagingType == "LOOSE") {
                                        check = true;
                                        pack.quantity = qty;
                                        $scope.changeQuantity(item);
                                    }
                                });
                                if (!check) {
                                    $scope.missedBulkSkuWastage.push(item.sku.skuName);
                                }
                            }
                            console.log("WastedItems are : ",$scope.wastedItems);
                        }else{
                            $toastService.create("No SKU found for the selected ID : " + skuId);
                        }
                    }
                }else{
                    if(appUtil.isEmptyObject($scope.businessDate)){
                        $toastService.create("Please select a valid business date before adding wastage");
                    }
                    if(appUtil.checkEmpty(skuId)){
                        $toastService.create("Please select a valid product before adding wastage");
                    }
                }
            };

            $scope.getInventoryData = function (skuId,selectedSKU,callBack) {
                var skuList = [];
                skuList.push(skuId);
                $http({
                    url: apiJson.urls.warehouseClosing.stockAtHand,
                    method: 'POST',
                    data: {
                        unitId: $scope.unitId,
                        skuIds: skuList
                    }
                }).then(function success(stock) {
                    if (appUtil.isEmptyObject(stock.data)) {
                        $toastService.create("could not fetch current stock at hand for this unit");
                    } else {
                        stock.data.forEach(function (item) {
                            $scope.skuInventoryMap[skuId] = item;
                        });
                        console.log("inventory map is : ",$scope.skuInventoryMap);
                        if (callBack != undefined && callBack != null) {
                            callBack();
                        }
                    }
                }, function error() {
                    $toastService.create("could not fetch current stock at hand for this unit");
                    $toastService.create("Can not add wastage of the " + selectedSKU.skuName+".Please Add after 5 minutes..!");
                });
            };


            $scope.sendWastageItems = function(wastedItems){
                console.log("Wasted Items ::::",wastedItems);
                var wastage = {
                        unitId:$scope.unitId,
                        status: "SETTLED",
                        businessDate: $scope.businessDate,
                        generatedBy: $scope.userId,
                        items : []
                };
                if(wastedItems.length > 0){
                    if(checkIfValid(wastedItems)){
                        $alertService.confirm("Are you sure? ","Please check if the values are correct.", function(retVal){
                            if (retVal) {
                        	//removing packagingMappings from product
                                var wastedItemsFinal = angular.copy(wastedItems);
                                wastedItemsFinal = wastedItemsFinal.map(function(item){
                                    item.price = item.sku.unitPrice;
                                    item.cost = item.sku.unitPrice * item.quantity;
                                    delete item.skuPackagings;
                                    delete item.sku;
                                    return item;
                                });
                                wastedItemsFinal.forEach(function(item){
                            		wastage.items.push(item);
                                });
                                var wastages = [];
                                wastages.push(wastage);
                                $http({
                                    method:'POST',
                                    url:apiJson.urls.stockManagement.wastageEvent,
                                    data:wastages
                                }).then(function(response){
                                    if(response.data!=undefined && response.data!=null){
                                        $toastService.create("Added wastage successfully");
                                        var result = response.data;
                                        console.log("result is ",result);
                                        if (result[0].autoBookedProducts != null && result[0].autoBookedProducts.length > 0) {
                                            var bookedProducts = result[0].autoBookedProducts.join();
                                            $toastService.create("Production Booking Done for Products : " + bookedProducts);
                                        }
                                        $scope.init();
                                    }else{
                                        $toastService.create("Could not add wastage items! Please try again later...");
                                    }
                                },function(response){
                                    if(response.data.errorCode!=null) {
                                        $alertService.alert(response.data.errorTitle, response.data.errorMsg,null, true);
                                    }else{
                                        $toastService.create("Could not Add Wastage...!");
                                        console.log("error:" + response);
                                    }
                                });
                            }
                        });

                    }else{
                        $toastService.create("Wrong values! Please check again");
                    }
                }
            };

            function checkIfValid(wastedItems){
                var zeroItemList = wastedItems.filter(function(item){
                    var quantity = item.quantity;
                    if(quantity==undefined || quantity == 0 || quantity < 0){
                        return false;
                    }
                    if($scope.userId == 125200 || $scope.userId == 140199) {
                        return true;
                    }
                    if((item.uom == 'KG' || item.uom == 'L') && quantity <= 500) {
                        return true;
                    }
                    if((item.uom == 'PC' || item.uom == 'SACHET' || item.uom == "PKT") && quantity <= 1000) {
                        return true;
                    }
                    if(item.uom == 'M' || item.uom == 'KM') {
                        return true;
                    }
                    return false;
                });
                if (zeroItemList.length == wastedItems.length) {
                    return true;
                }
                else {
                    var missedSkuNames = [];
                    for (var i=0;i<wastedItems.length;i++) {
                        var found = false;
                        for (var j = 0; j < zeroItemList.length; j++) {
                            if (wastedItems[i].sku.skuId == zeroItemList[j].sku.skuId) {
                                found = true;
                                break;
                            }
                        }
                        if (!found) {
                            missedSkuNames.push(wastedItems[i].sku.skuName);
                        }
                    }
                    $toastService.create("Please Enter the wastage for all the Sku's ...!  "+missedSkuNames.join(","));
                    return false;
                }
            }

            $scope.removeMapping = function(roi,$index){
                roi.skuPackagings[$index].quantity = null;
                calculateWastage(roi);
            };

            $scope.removeItem = function($index){
                $scope.wastedItems.splice($index,1);
            };


            $scope.getWastageEvents = function(){
                if(!appUtil.checkEmpty($scope.selectedDate)){
                    getWastageEvents($scope.unitId,$scope.selectedDate, function(items){
                        if(items!=null){
                            $scope.wastageEvents = items;
                            $scope.groupTodayWastageBySkuId(items);
                        }else{
                            $scope.wastageEvents = [];
                            $toastService.create("No Wastage Events found");
                        }
                    });
                }else {
                    $toastService.create("Please select a date first");
                }
            };
            
            $scope.getWhWastageEventsForDays = function(){
                if (appUtil.checkEmpty($scope.wastageStartDate)) {
                    $toastService.create("Please select a start Date..!");
                    return false;
                }
                if (appUtil.checkEmpty($scope.wasatgeEndDate)) {
                    $toastService.create("Please select an End Date..!");
                    return false;
                }
                getWhWastageEventsForDays($scope.unitId,$scope.wastageStartDate,$scope.wasatgeEndDate, function(items){
                    $scope.wastageEvents = items;
                    if(items == null || items.length == 0){
                        $toastService.create("No Wastage Events found");
                    }
                    else {
                        $scope.groupWastageBySkuId($scope.wastageEvents);
                    }
                });
            };

            $scope.groupTodayWastageBySkuId = function (list) {
                var items = [];
                angular.forEach(list,function (wastage) {
                    angular.forEach(wastage.items,function (item) {
                        items.push(item);
                    });
                });
                console.log("items are : ",items);
                $scope.groupedTodaySkuWastageList = [];
                var todaySkuList = [];
                for(var i in items){
                    if(todaySkuList.indexOf(items[i].skuId) < 0) {
                        var groupedTodayBySkuIdListObj = getGroupedBySkuListObj();
                        console.log("i item is ",items[i]);
                        groupedTodayBySkuIdListObj.skuId = items[i].skuId;
                        groupedTodayBySkuIdListObj.details.skuName = items[i].sku.skuName;
                        groupedTodayBySkuIdListObj.details.unitOfMeasure = items[i].sku.unitOfMeasure;
                        groupedTodayBySkuIdListObj.details.total++;
                        groupedTodayBySkuIdListObj.details.totalAmount += items[i].totalAmount;
                        groupedTodayBySkuIdListObj.details.totalQuantity += items[i].quantity;
                        groupedTodayBySkuIdListObj.details.skuWastageList.push(items[i]);
                        $scope.groupedTodaySkuWastageList.push(groupedTodayBySkuIdListObj);
                        todaySkuList.push(items[i].skuId);
                    } else {
                        for(var j in $scope.groupedTodaySkuWastageList){
                            if($scope.groupedTodaySkuWastageList[j].skuId == items[i].skuId){
                                $scope.groupedTodaySkuWastageList[j].details.total++;
                                $scope.groupedTodaySkuWastageList[j].details.totalAmount += items[i].totalAmount;
                                $scope.groupedTodaySkuWastageList[j].details.totalQuantity += items[i].quantity;
                                $scope.groupedTodaySkuWastageList[j].details.skuWastageList.push(items[i]);
                            }
                        }
                    }
                }
                console.log("grouped is ", $scope.groupedTodaySkuWastageList);
            };

            $scope.groupWastageBySkuId = function (list) {
                var wastageItemList = [];
                angular.forEach(list,function (wastage) {
                    angular.forEach(wastage.items,function (item) {
                        wastageItemList.push(item);
                    });
                });
                console.log("wastage Items list is : ",wastageItemList);
                $scope.groupedSkuWastageList = [];
                var skuList = [];
                for(var i in wastageItemList){
                    if(skuList.indexOf(wastageItemList[i].skuId) < 0) {
                        var groupedBySkuIdListObj = getGroupedBySkuListObj();
                        console.log("i item is ",wastageItemList[i]);
                        groupedBySkuIdListObj.skuId = wastageItemList[i].skuId;
                        groupedBySkuIdListObj.details.skuName = wastageItemList[i].sku.skuName;
                        groupedBySkuIdListObj.details.unitOfMeasure = wastageItemList[i].sku.unitOfMeasure;
                        groupedBySkuIdListObj.details.total++;
                        groupedBySkuIdListObj.details.totalAmount += wastageItemList[i].totalAmount;
                        groupedBySkuIdListObj.details.totalQuantity += wastageItemList[i].quantity;
                        groupedBySkuIdListObj.details.skuWastageList.push(wastageItemList[i]);
                        $scope.groupedSkuWastageList.push(groupedBySkuIdListObj);
                        skuList.push(wastageItemList[i].skuId);
                    } else {
                        for(var j in $scope.groupedSkuWastageList){
                            if($scope.groupedSkuWastageList[j].skuId == wastageItemList[i].skuId){
                                $scope.groupedSkuWastageList[j].details.total++;
                                $scope.groupedSkuWastageList[j].details.totalAmount += wastageItemList[i].totalAmount;
                                $scope.groupedSkuWastageList[j].details.totalQuantity += wastageItemList[i].quantity;
                                $scope.groupedSkuWastageList[j].details.skuWastageList.push(wastageItemList[i]);
                            }
                        }
                    }
                }
                console.log("grouped is ", $scope.groupedSkuWastageList);
            };

            function getGroupedBySkuListObj(){
                var obj = {};
                obj.skuId = null;
                var innerObj = {};
                innerObj.skuName = "";
                innerObj.total = 0;
                innerObj.totalAmount = 0;
                innerObj.totalQuantity = 0;
                innerObj.unitOfMeasure = "";
                innerObj.skuWastageList = [];
                obj.details = innerObj;
                return obj;
            }

            $scope.closeWhWastageView = function () {
                $scope.wastageEvents = [];
                $scope.groupedSkuWastageList = [];
                $scope.isBySku = false;
            };

            $scope.initializeWhWastage = function () {
                $scope.maxWastageDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                $scope.wastageStartDate = null;
                $scope.wasatgeEndDate = null;
            };

            $scope.setStartDate = function (startDate) {
                $scope.wastageStartDate = startDate;
                $scope.setEndDate(null);
                $scope.wastageEvents = [];
                $scope.groupedSkuWastageList = [];
            };

            $scope.setEndDate = function (endDate) {
                if ($scope.wastageStartDate == undefined || $scope.wastageStartDate == null || $scope.wastageStartDate == '') {
                    $toastService.create("Please Select the Start Date first..!");
                    $scope.wasatgeEndDate = null;
                    return false;
                }
                $scope.wasatgeEndDate = endDate;
                $scope.wastageEvents = [];
                $scope.groupedSkuWastageList = [];
            };

            function getEventItems(list) {
                var result = [];
                angular.forEach(list,function (wastage) {
                    angular.forEach(wastage.items,function (item) {
                        result.push(item);
                    });
                });
                return result;
            }

            $scope.generateWhWastageSheet = function () {
                $http({
                    url: apiJson.urls.stockManagement.generateWastageSheet,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    data: getEventItems($scope.wastageEvents),
                    params: {
                        "unitId":$scope.unitId
                    },
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(
                        function (data) {
                            var startDateString = appUtil.getFormattedDate($scope.wastageStartDate);
                            var endDateString = appUtil.getFormattedDate($scope.wasatgeEndDate);
                            var unit = appUtil.getUnitData();
                            var fileName =  "Wastages_" + unit.name+"_" +startDateString+"_to_"+endDateString + ".xlsx";
                            var blob = new Blob(
                                [data],
                                {
                                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                }, fileName);
                            saveAs(blob, fileName);
                        }).error(function (err) {
                    console.log("Error during getting data", err);
                });
            }

            $scope.cancelWastage = function(item){
                var currentStatus = item.status;
                $alertService.confirm("Are you sure?","", function(retVal){
                    if (retVal ){
                        $http({
                            method:'POST',
                            url:apiJson.urls.stockManagement.cancelWastageEvent,
                            data:item.wastageId
                        }).then(function(response){
                            if(!appUtil.checkEmpty(response) && response.data){
                                getWastageEvents($scope.unitId,appUtil.getCurrentBusinessDate(), function(items){
                                    $scope.todaysWastage = items;
                                    $scope.groupTodayWastageBySkuId(items);
                                });
                                $scope.getSkuInventoryData();
                                $toastService.create("Cancelled wastage successfully");
                            }else{
                                $toastService.create("Wastage cannot be cancelled! Please be sure that you are cancelling before day close process...");
                            }
                        },function(response){
                            console.log("Got error ::::",response);
                        });
                    }
                });

            };
        }
    ]
);