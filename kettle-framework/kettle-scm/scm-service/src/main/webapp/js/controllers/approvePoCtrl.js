'use strict';
angular.module('scmApp').controller('approvePoCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService', '$alertService', 'metaDataService', 'Popeye', 'uiGridConstants', '$timeout',
    function ($rootScope, $stateParams, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, Popeye, uiGridConstants, $timeout ) {

        function getCreatedPOs(view, startDate, endDate, poId, unitId) {
            if (appUtil.isEmptyObject(startDate)) {
                $toastService.create("Please select a start date first");
                return;
            }
            if (appUtil.isEmptyObject(endDate)) {
                $toastService.create("Please select a end date first");
                return;
            }

            var params = {
                deliveryUnitId: $scope.currentUser.unitId,
                isView: view,
                startDate: startDate,
                endDate: endDate,
                purchaseOrderId: poId
            };

            if (!appUtil.isEmptyObject($scope.vendorSelected)) {
                params["vendorId"] = $scope.vendorSelected.vendorId;
            }

            if (!appUtil.isEmptyObject($scope.selectedSKU)) {
                params["skus"] = [$scope.selectedSKU];
            }

            if (!appUtil.isEmptyObject($scope.selectedStatus)) {
                params["status"] = $scope.selectedStatus;
            }

            if (!appUtil.isEmptyObject($scope.unitSelected)) {
                params["deliveryUnitId"] = $scope.unitSelected.id;
            }

            $http({
                method: "GET",
                url: apiJson.urls.requestOrderManagement.getCreatedOrders,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Orders found!");
                } else {
                    $scope.poRequest = response.data.filter($scope.byApprover);
                    // console.log($scope.poRequest);
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }
        function getAmount(pkg, qty, tax, isExempted, item) {
            isExempted = isExempted != undefined ? isExempted : false;
            var price = item.unitPrice;
            var ratio = item.conversionRatio;
            var gst = 0;
            var otherTaxes = 0;
            var amount = parseFloat((price * qty).toFixed(6));
            var obj = {
                amount: amount,
                cgst: { value: null, percentage: null },
                igst: { value: null, percentage: null },
                sgst: { value: null, percentage: null },
                others: { taxes: [], value: null, percentage: null },
                tax: null
            };
            if (!isExempted) {
                if ($scope.currentUnit.location.state.code.toString() != $scope.selectedDispatchLocation.address.stateCode.toString()) {
                    obj.igst = getTaxValue(item.igst.percentage, amount);
                    obj.tax = obj.igst.value
                } else {
                    obj.cgst = getTaxValue(item.cgst.percentage, amount);
                    obj.sgst = getTaxValue(item.sgst.percentage, amount);
                    obj.tax = obj.cgst.value + obj.sgst.value;
                }

                if (item.otherTaxes && item.otherTaxes.length > 0) {
                    for (var index in item.otherTaxes) {
                        var otherTax = item.otherTaxes[index];
                        var valueAndTax = getTaxValue(otherTax.tax, getApplicableAmount(otherTax.applicability, obj));
                        var changedTax = {
                            taxName: otherTax.type,
                            taxCategory: otherTax.type,
                            percentage: valueAndTax.percentage,
                            value: valueAndTax.value
                        };
                        obj.others.taxes.push(changedTax);
                        otherTaxes += valueAndTax.value;
                    }
                    obj.tax += otherTaxes;
                }
            }
            return obj;
        }

        function getTaxText(taxes) {
            var text = "";
            for (var key in taxes) {
                if (key != "amount" && key != "others" && key != "tax" && !appUtil.isEmptyObject(taxes[key].value)) {
                    text += key.toUpperCase() + ":" + taxes[key].percentage + "% ";
                }
            }
            if (taxes.others.taxes.length > 0) {
                var tempTaxes = angular.copy(taxes.others.taxes);
                for (var i in tempTaxes) {
                    text += tempTaxes[i].taxName.toUpperCase() + ":" + tempTaxes[i].percentage + "% ";
                }
            }
            return text;
        }
        $scope.initCharges = function () {
            $scope.selectedPOR.billAmount = 0;
            $scope.selectedPOR.totalTaxes = 0;
            $scope.selectedPOR.paidAmount = 0;
        };
        $scope.changeValues = function (items) {
            $scope.initCharges();
            angular.forEach(items, function (item) {
                $scope.selectedPOR.billAmount += item.totalCost;
                $scope.selectedPOR.totalTaxes += item.totalTax;
            });
            $scope.selectedPOR.paidAmount = $scope.selectedPOR.billAmount + $scope.selectedPOR.totalTaxes;

        };
        $scope.updateItem = function (item, id) {

            var amountAndTax = getAmount(item.pkg, item.packagingQty, item.taxes, item.exemptItem, item);
            $scope.selectedPOR.orderItems[id].totalCost = amountAndTax.amount;
            $scope.selectedPOR.orderItems[id].totalTax = amountAndTax.tax;
            $scope.selectedPOR.orderItems[id].requestedQuantity = $scope.selectedPOR.orderItems[id].packagingQty * item.conversionRatio;
            $scope.selectedPOR.orderItems[id].edit = false
            $scope.changeValues($scope.selectedPOR.orderItems);
            updatePOSkuQty(item, id);
        };

        $scope.editItem = function (item ,id , isEdit){
        if(item.packagingQty == null)
        {
        item.packagingQty = 0 ;

        }
            if(item.packagingQty <= 0)
                {
                $toastService.create("Packaging Quantity Value should be greater than 0 , Kindly check edited Packaging Quantity Value !");
                return ;
                }
                        $scope.selectedPOR.orderItems[id].edit = isEdit;

                        if(!isEdit)
                        {
                        var newPendingEditQtyIndex = [] ;
                                                                       for(var ind in $scope.pendingEditQtyIndex)
                                                                       {
                                                                       if($scope.pendingEditQtyIndex[ind].id != id)
                                                                       {
                                                                      newPendingEditQtyIndex[ind] = $scope.pendingEditQtyIndex[ind] ;
                                                                       }
                                                                       }
                                                                       $scope.pendingEditQtyIndex = newPendingEditQtyIndex ;



                        $scope.updateItem(item,id) ;
                        }
                        else
                        {
                        $scope.pendingEditQtyIndex.push({'id':id , 'item':item}) ;




                        }
                    };


        $scope.downloadExcell = function () {
            var params = {
                className: "com.stpl.tech.scm.domain.model.PurchaseOrder"
            }
            var jsonStrings = [];
            for (var i = 0; i < $scope.poRequest.length; i++) {
                jsonStrings.push(JSON.stringify($scope.poRequest[i]));
            }
            metaDataService.downloadExcell(jsonStrings, params);
        }
        function updatePOSkuQty(item, id) {
            var url = apiJson.urls.requestOrderManagement.updatePoBeforeApproval;
            var poDetails = {
                totalTax: $scope.selectedPOR.totalTaxes,
                totalBillAmount: $scope.selectedPOR.billAmount,
                packagingQuantity: item.packagingQty,
                poId: $scope.selectedPOR.id,
                sgstValue: item.sgst ? item.sgst.value : null,
                igstValue: item.igst ? item.igst.value : null,
                cgstValue: item.cgst ? item.cgst.value : null,
                updatedBy: $scope.currentUser.userId,
                amount: item.totalCost,
                tax: item.totalTax,
                poDetailId: item.id,

            }

            $http({
                method: "POST",
                url: url,
                data: poDetails

            }).then(function (response) {
                if (response.data) {
                $scope.validInput[id] = false ;
                    $toastService.create("Packaging Quantity updated Successfully ");
                }
                else
                {
                $scope.validInput[id] = true ;
                $toastService.create("Packaging Quantity not updated  due to some reason , Kindly change edited quantity! ");
               $scope.selectedPOR.orderItems[id] = angular.copy($scope.copyOfSelectedPoR.orderItems[id]);
                $scope.changeValues($scope.selectedPOR.orderItems);

                }
            }, function (response) {
            $scope.validInput[id] = false ;
                if (response.data.errorMsg != null) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                }
                else
                {
                $toastService.create("Packaging Quantity not updated  due to some reason , Kindly change edited quantity! ");
                }

                $scope.selectedPOR.orderItems[id] = angular.copy($scope.copyOfSelectedPoR.orderItems[id]);
                 $scope.changeValues($scope.selectedPOR.orderItems);

            });

        }


        function updatePO(url, callback) {
            $alertService.confirm("Are you sure?", "", function (result) {
                if (result) {
                    $http({
                        method: "POST",
                        url: url
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response)) {
                            callback(response.data);
                        }
                    }, function (response) {
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        }
                        callback(false);
                        console.log("Encountered error at backend", response);
                    });
                }
            });

        }

        $scope.byApprover = function (poR) {
            if ($scope.showViewActions) {
                return true;
            } else {
                if ($scope.approverLevel == 1) {
                    return poR.paidAmount <= 50000;
                }

                if ($scope.approverLevel == 2) {
                    return true;
                }
            }
        };

        $scope.openViewChart = function (skuData) {
            if (appUtil.isEmptyObject($scope.approvedSku[skuData.skuId])) {
                $toastService.create("No consumption data found for the product");
                return;
            }
            var consumptionModal = Popeye.openModal({
                templateUrl: 'consumptionApprove.html',
                controller: "consumptionApproveCtrl",
                modalClass: 'modal',
                resolve: {
                    sku: function () {
                        return $scope.approvedSku[skuData.skuId];
                    },
                    selectedVendor: function () {
                        return $scope.selectedVendor;
                    },
                    selectedDispatchLocation: function () {
                        return $scope.selectedDispatchLocation;
                    },
                    currentUnit: function () {
                        return $scope.currentUnit;
                    },
                    pendingForSku: function () {
                        if (!appUtil.isEmptyObject($scope.skuLookUp) && !appUtil.isEmptyObject($scope.skuLookUp[skuData.skuId])) {
                            return $scope.skuLookUp[skuData.skuId];
                        } else {
                            return null;
                        }
                    },
                },
                click: true,
                keyboard: true
            });
        };

        $scope.init = function () {
            var currentDate = appUtil.getCurrentBusinessDate();
            if (!appUtil.isEmptyObject(currentDate)) {
                $scope.approvedSku = {};
                $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.endDate = $scope.startDate;
                $scope.showViewActions = $stateParams.viewPO;
                $scope.currentUnit = appUtil.getUnitData();
                $scope.createdPO = $stateParams.createdPO;
                $scope.poRequest = [];
                $scope.availableReasons = ['Raw material issue', 'Labour issue', 'Transport issue', 'Production issue', 'Holiday', 'Payment issue', 'Others'];
                $scope.userMappingUnits = appUtil.getUnitList();
                $scope.selectedPO = null;
                $scope.selectedUnit = null;
                $scope.txnStatus = ["", "CREATED", "APPROVED", "REJECTED", "CLOSED", "CANCELLED", "IN_PROGRESS"];
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.charts = [];
                $scope.validInput = {} ;
                $scope.chartOptions = [];
                $scope.isPurchaser = true;
                $scope.showGrid = false;
                $scope.pendingEditQtyIndex = [] ;
                $scope.enterredText = null;
                $scope.approverLevel = 1;
                for (var i in $rootScope.purchaseRoles) {
                    var role = $rootScope.purchaseRoles[i];
                    if (role.code.indexOf("_L2") != -1) {
                        $scope.approverLevel = 2;
                    }
                }
                $scope.isCapexId=false;
                $scope.capexRequestId =null;
                $scope.summaryDepartmentList = [];
                $scope.capexSummaryLoading =false;
                metaDataService.getVendorsForUnit($scope.currentUser.unitId, function (vendorsForUnit) {
                    $scope.vendors = vendorsForUnit;
                });

                metaDataService.getSkuListForUnit($scope.currentUser.unitId, function (skuForUnitList) {
                    $scope.skus = skuForUnitList;
                });

                getCreatedPOs($scope.showViewActions, $scope.startDate, $scope.endDate, $scope.poId, $scope.currentUser.unitId);
            }
        };
        var getTaxValue = function (taxValue, amount) {
            return {
                value: parseFloat(((amount * taxValue) / 100).toFixed(6)),
                percentage: taxValue
            };
        };

        $scope.getPoByCapex = function(){
            var params={};

            if(!appUtil.isEmptyObject($scope.vendorSelected)){
                params["vendorId"] = $scope.vendorSelected.id;
            }

            if(!appUtil.isEmptyObject($scope.capexRequestId)){
                params["capexRequestId"] = $scope.capexRequestId;
            }else{
                alert("Please Provide Capex Request Id");
                return;
            }
               
            $http({
                method:"GET",
                url : apiJson.urls.capexManagement.getPoByCapex,
                params : params
            }).then(function success(response){
                console.log(response);
                $scope.poRequest = response.data;
            }, function error(err){
                console.log(err);
            });

                $scope.fetchBudgetSummary();
            
        }


        $scope.changeCapexId = function(capexId){
            $scope.capexRequestId = capexId;
        }
        $scope.checkCapexId = function(){
            $scope.isCapexId = !$scope.isCapexId;
            $scope.capexSummaryShow = false;
            $scope.summaryDepartmentList= [];
            $scope.capexRequestId=null;
            $scope.poRequest=[]
            $timeout(function(){ $('#enterPoCapexId').val(null).trigger('change');  });
        }

        $scope.fetchBudgetSummary = function(){
            $scope.capexSummaryShow = false;
            $scope.capexSummaryLoading = true;
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.fetchBudgetDetails,
                params: {
                    capexRequestId: $scope.capexRequestId
                }
            }).then(function success(response) {
                if (response) {
                    if(response!=null && response.data.length>0){
                        $scope.summaryDepartmentList = response.data.filter(function(el){ return el.totalPO != 0});
                        $scope.capexSummaryShow = true;
                    }
                } else {
                    $toastService.create("Error in showing Capex Summary.");
                }
                $scope.capexSummaryLoading = false;
            }, function error(response) {
                console.log("error:" + response);
                $scope.capexSummaryLoading = false;
            });
        }

        $scope.currentUnit = appUtil.getUnitData();
        $scope.showCharts = function (condition) {
            $scope.showChart = condition;
        };

        $scope.showPriceHistoryGrid = function () {
            $scope.showGrid = !$scope.showGrid;
        };

        $scope.reset = function () {
            $scope.selectedSKU = null;
            $scope.selectedVendor = null;
        };

        $scope.selectVendor = function (vendor) {
            $scope.vendorSelected = vendor;
        };

        $scope.selectUnit = function (unit) {
            $scope.unitSelected = unit;
        };

        $scope.selectSKU = function (sku) {
            $scope.selectedSKU = sku;
        };

        $scope.downloadPO = function (poInvoice) {
            metaDataService.downloadDocument(poInvoice);
        };

        $scope.getPOs = function () {
            getCreatedPOs($scope.showViewActions, $scope.startDate, $scope.endDate, $scope.poId, $scope.currentUser.unitId);
        };

        $scope.showDetails = function (index) {
            $scope.selectedPO = $scope.poRequest[index];
        };

        $scope.gridOptions = {
            enableGridMenu: true,
            exporterExcelFilename: 'download.xlsx',
            exporterExcelSheetName: 'Sheet1',
            enableColumnMenus: true,
            saveFocus: false,
            enableRowSelection: true,
            enableFiltering: true,
            saveScroll: true,
            enableSelectAll: true,
            multiSelect: true,
            enableColumnResizing: true,
            exporterMenuPdf: false,
            exporterMenuExcel: true,
            fastWatch: true,
            onRegisterApi: function (gridApi) {
                $scope.gridApi = gridApi;
            },
        };
        $scope.gridOptions.columnDefs = [
            { name: 'skuName', displayName: 'ORDER ITEM' },
            { name: 'skuId', displayName: 'SKU ID' },
            { name: 'price', displayName: 'Price' },
            { name: 'priceUpdateDate', displayName: 'Update Date' }
        ]
        $scope.expandAllRows = function () {
            $scope.gridApi.expandable.expandAllRows();
        };
        $scope.collapseAllRows = function () {
            $scope.gridApi.expandable.collapseAllRows();
        };
        $scope.showApproveDetails = function (poR, index) {
            $scope.charts = [];
            $scope.chartOptions = [];
            $scope.showGrid = false;
            $scope.priceHistoryDisabled = false;
            $scope.selectedPOR = $scope.poRequest[index];
            $scope.copyOfSelectedPoR = angular.copy($scope.poRequest[index]);
            $scope.selectedVendor = $scope.selectedPOR.generatedForVendor;
            $scope.selectedDispatchLocation = $scope.selectedPOR.dispatchLocation;
            $scope.getPriceAndTaxData();
            $scope.getPendingOrders();
            console.log($scope.selectedPOR);

            $scope.selectedpoRId = poR;
            $scope.seletedpoRIndex = index;

            try {
                $scope.orderItems = $scope.selectedPOR.orderItems;
                var data = $scope.orderItems;
                var gridData = [];
                console.log("data ::::", data);
                var k = 0;
                for (var i = 0; i < data.length; i++) {
                    if (data[i].priceHistory != null) {
                        for (var j = data[i].priceHistory.length - 1; j >= 0; j--) {
                            gridData[k] = {};
                            gridData[k]['skuName'] = data[i].skuName;
                            gridData[k]['skuId'] = data[i].skuId;
                            gridData[k]['price'] = data[i].priceHistory[j].key;
                            gridData[k]['priceUpdateDate'] = data[i].priceHistory[j].value;
                            k++;
                        }

                    }
                }
                $scope.gridOptions.data = gridData;

                var OrderItems = $scope.selectedPOR.orderItems;

                for (var k = 0; k < OrderItems.length; k++) {
                    var date = [];
                    var price = [];
                    if (OrderItems[k].priceHistory != null) {
                        for (var i = OrderItems[k].priceHistory.length - 1; i >= 0; i--) {
                            date.push(OrderItems[k].priceHistory[i].value.substring(0, 11));
                            price.push(OrderItems[k].priceHistory[i].key);
                        }
                        $scope.chartOptions.push({
                            options: {
                                chart: {
                                    type: 'line',
                                    height: 230,
                                    width: 270,
                                    reflow: true
                                },
                                title: {
                                    text: OrderItems[k].skuName
                                },
                                xAxis: {
                                    categories: date,
                                    title: {
                                        text: 'Updation Date'
                                    },
                                },
                                yAxis: {
                                    categories: date,
                                    title: {
                                        text: 'Price'
                                    },
                                },
                                plotOptions: {
                                    line: {
                                        dataLabels: {
                                            enabled: true
                                        },
                                        enableMouseTracking: false
                                    }
                                },
                                series: [{
                                    showInLegend: false,
                                    data: price
                                }]
                            }
                        });
                    } else {
                        $scope.chartOptions.push({
                            options: {
                                chart: {
                                    type: 'line',
                                    height: 230,
                                    width: 270,
                                    reflow: true
                                },
                                title: {
                                    text: OrderItems[k].skuName
                                },
                                subtitle: {
                                    text: "No Price History Available"
                                }
                            }
                        });
                    }
                };
            } catch (e) {
                console.log(e);
            }
        };

        $scope.createCharts = function () {
            if ($scope.priceHistoryDisabled == false) {
                for (var k = 0; k < $scope.chartOptions.length; k++) {
                    var element = document.getElementById("myChart-" + k);
                    $scope.charts[k] = Highcharts.chart(element, $scope.chartOptions[k].options);
                }
            }
        }
        // Approve consumption data//
        $scope.getPriceAndTaxData = function () {
            if (!appUtil.isEmptyObject($scope.selectedPOR.generatedForVendor.id)
                && !appUtil.isEmptyObject($scope.selectedPOR.dispatchLocation.dispatchId)) {
                getSKUPriceAndTaxesForProfile()
            } else {
                $toastService.create("Please select Vendor Dispatch Location correctly");
            }
        };

        function getSKUPriceAndTaxesForProfile() {
            var roleIds = $rootScope.purchaseRoles.map(function (role) { return role.id; }).join(",");
            metaDataService.getSkuPricesAndTaxesForProfile($scope.selectedPOR.generatedForVendor.id,
                $scope.selectedPOR.dispatchLocation.dispatchId, $scope.currentUnit.id, roleIds, true, function (skuAndTaxData) {
                    if (skuAndTaxData.length > 0) {
                        $scope.skuList = skuAndTaxData;
                        $scope.getConsumptionData($scope.skuList);
                    } else {
                        $toastService.create("Could not fetch prices for the dispatch location");
                        $scope.skuList = [];
                        $scope.packagingList = [];
                    }
                });
        }
        $scope.getConsumptionData = function (skuList) {
            var skuArr = skuList.map(function (sku) {
                return sku.id;
            });
            var skuStr = skuArr.join(",");
            $http({
                method: "GET",
                url: apiJson.urls.requestOrderManagement.purchaseConsumption,
                params: {
                    skus: skuStr,
                    unitId: $scope.currentUnit.id,
                    days: 90
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.getCurrentStock(skuArr, response.data, addToSkuList);
                }
            }, function (error) {
                console.log(error);
            });
        };

        $scope.getCurrentStock = function (skuList, consumptionData, callback) {
            $http({
                url: apiJson.urls.warehouseClosing.stockAtHand,
                method: 'POST',
                data: {
                    unitId: $scope.currentUnit.id,
                    skuIds: skuList
                }
            }).then(function (stock) {
                if (appUtil.isEmptyObject(stock.data)) {
                    $toastService.create("could not fetch current stock at hand for this unit");
                } else {
                    if (typeof callback == "function") {
                        callback(consumptionData, stock.data);
                    }
                }
            }, function () {
                $toastService.create("could not fetch current stock at hand for this unit");
            });
        };

        function addToSkuList(response, currentStock) {

            var consumption = [];

            for (var i in response) {
                if (appUtil.isEmptyObject(consumption[response[i].id])) {
                    consumption[response[i].id] = {};
                    consumption[response[i].id]['total'] = 0;
                }
                consumption[response[i].id][response[i].date] = response[i].qty;
                consumption[response[i].id]['total'] += response[i].qty;
            }

            $scope.skuList.forEach(function (sku) {
                var consumed = 0, total = 0;
                if (!appUtil.isEmptyObject(consumption[sku.id])) {
                    total = consumption[sku.id]['total'];
                    delete consumption[sku.id]['total'];
                    consumed = consumption[sku.id];
                }
                sku['consumed'] = consumed;
                sku['totalConsumed'] = total.toFixed(2);
                sku['currentStock'] = findInCurrentStock(currentStock, sku.id);
                $scope.approvedSku[sku.id] = sku;
            });
        }

        function makeSkuLookUp(pendingPOs) {
            $scope.skuLookUp = {};
            for (var i in pendingPOs) {
                for (var j in pendingPOs[i].orderItems) {
                    if (appUtil.isEmptyObject($scope.skuLookUp[pendingPOs[i].orderItems[j].skuId])) {
                        $scope.skuLookUp[pendingPOs[i].orderItems[j].skuId] = {
                            poList: [],
                            received: 0,
                            requested: 0
                        };
                    }
                    var sku = $scope.skuLookUp[pendingPOs[i].orderItems[j].skuId];
                    if (sku.poList.indexOf(pendingPOs[i].receiptNumber) == -1) {
                        sku.poList.push(pendingPOs[i].receiptNumber);
                        sku.received += pendingPOs[i].orderItems[j].receivedQuantity;
                        sku.requested += pendingPOs[i].orderItems[j].requestedAbsoluteQuantity;
                    }
                }
            }
        }

        $scope.getPendingOrders = function () {
            if ($scope.selectedDispatchLocation == null) {
                return;
            }
            $http({
                method: "GET",
                url: apiJson.urls.requestOrderManagement.getPendingPOs,
                params: {
                    vendorId: $scope.selectedVendor.id,
                    deliveryUnitId: $scope.currentUnit.id,
                    dispatchId: $scope.selectedDispatchLocation.dispatchId,
                    startDate: appUtil.formatDate(appUtil.getDate(-365), "yyyy-MM-dd"),
                    endDate: appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd"),
                    purchaseOrderId: $scope.poId,
                    status: $scope.selectedStatus
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    makeSkuLookUp(response.data);
                }
            }, function (error) {
                console.log(error);
            });
        };
        function findInCurrentStock(skuList, skuId) {
            for (var i in skuList) {
                if (skuList[i].skuId == skuId) {
                    var value = skuList[i].stockValue;
                    console.log("stockvalue", value);
                    return skuList[i].stockValue;
                }
            }
            return 0;
        }
        $scope.closeModal = function(){
            $("#viewPOApproveDetails").closeModal();
        }


        $scope.getApprove = function (approveID, index ) {
        for(var ind in $scope.pendingEditQtyIndex)
        {
        if($scope.pendingEditQtyIndex[ind].item.packagingQty <= 0)
        {
        $toastService.create("Packaging Quantity Value should be greater than 0 , Kindly check edited Packaging Quantity Value !");
                        return ;
        }
        $scope.editItem($scope.pendingEditQtyIndex[ind].item , $scope.pendingEditQtyIndex[ind].id , false) ;
        }

            var url = apiJson.urls.requestOrderManagement.approvePO + "/" + approveID + "/" + $scope.currentUser.userId;
            $("#viewPOApproveDetails").closeModal();
            updatePO(url, function (updated) {
                if (updated) {
                    $alertService.alert("Congratulations!!",
                        "Purchase Order: " + approveID + " approved successfully <br> Vendor has also been notified",
                        function (result) {
                            $scope.poId = null;
                            getCreatedPOs($scope.showViewActions, $scope.startDate, $scope.endDate, $scope.poId, $scope.currentUser.unitId);
                        });
                } else {
                    $toastService.create("Purchase Order approval failed! Please try again later..");
                }
            });
        };

        $scope.getReject = function (approveID, index) {
            var url = apiJson.urls.requestOrderManagement.rejectPO + "/" + approveID + "/" + $scope.currentUser.userId;
            updatePO(url, function (updated) {
                if (updated) {
                    $toastService.create("Purchase Order rejected successfully");
                    $scope.poRequest[index].status = "REJECTED";
                } else {
                    $toastService.create("Purchase Order rejection failed! Please try again later..");
                }
            });
        };

        $scope.getPosForAdvance = function () {
            $scope.pendingPoSo = [];
            $scope.errorMessage = null;
            $http({
                method: 'GET',
                url: apiJson.urls.paymentRequestManagement.getPosForAdvance,
                params: {
                    vendorId: $scope.cancelObject.vendorId
                }
            }).then(function success(response) {
                $scope.pendingPoSo = response.data;
                var finalPoSo = [];
                for (var i = 0; i < $scope.pendingPoSo.length; i++) {
                    if ($scope.pendingPoSo[i].id != $scope.cancelObject.poId) {
                        finalPoSo.push($scope.pendingPoSo[i]);
                    }
                }
                $scope.pendingPoSo = finalPoSo;
                angular.forEach($scope.pendingPoSo, function (poSo) {
                    poSo.maxLimit = parseFloat((poSo.paidAmount + appUtil.getAdvanceBufferAmount(poSo.paidAmount)).toFixed(0));
                });
            }, function error(response) {
                $scope.pendingPoSo = [];
                if(response.data.errorMsg != null) {
                    $scope.errorMessage = response.data.errorMsg;
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                } else{
                    $toastService.create("Error Occurred While Creating Advance Payment...!");
                    console.log("error:" + response);
                }
            });
        };

        $scope.setEnterredText = function (text) {
            $scope.enterredText = text;
        };

        $scope.disablePosSelection = function ($event, po, poSoList) {
            $scope.poSoSelected = null;
            if ($event.target.checked) {
                if (po.maxLimit < $scope.cancelObject.completeAvailableAmount) {
                    $toastService.create("Can not adjust to the PO which has less amount than the remaining advance..!");
                    po.checked = false;
                    return;
                }
                po.disable = false;
                angular.forEach(poSoList, function (poSo) {
                    if (po.id == poSo.id) {
                        poSo.disable =  false;
                        $scope.poSoSelected = po;
                    } else {
                        poSo.disable = true;
                    }
                });
            } else {
                $scope.poSoSelected = null;
                angular.forEach(poSoList, function (poSo) {
                    poSo.disable = false;
                });
            }
            $scope.advanceAmount = null;
        };

        $scope.setRefundDate = function (refundDate) {
            $scope.cancelObject.refundDate = refundDate;
        };

        $scope.closeAdjustRefund= function (po) {
            $scope.poSoSelected = null;
            po.adjustOrRefund = false;
        };

        $scope.setAdjustOrRefund = function (flag) {
            $scope.cancelObject.adjustOrRefund = flag;
            $scope.poSoSelected = null;
            $scope.cancelObject.refundDate = null;
            $scope.enterredText = null;
            if ($scope.cancelObject.adjustOrRefund == 'Adjust') {
                $scope.getPosForAdvance();
            }
        };

        $scope.submitAdjustmentRefund = function (poSo,po) {
            if (poSo != undefined && poSo != undefined) {
                $scope.cancelObject.selectedSoPo = poSo.id;
            }
            var type = po.status == 'APPROVED' ? 'Cancel' : 'Close';
            $scope.cancelObject.finalAvailable = $scope.cancelObject.completeAvailableAmount;
            $http({
                method: 'POST',
                url: apiJson.urls.paymentRequestManagement.submitAdjustmentRefund,
                params: {
                    createdBy: appUtil.getCurrentUser().userId
                },
                data: $scope.cancelObject
            }).then(function success(response) {
                if (response.status == 200) {
                    if (response.data) {
                        $toastService.create("PO "+type+" Initiated..!");
                        $scope.getPOs();
                    } else {
                        $toastService.create("Error While Initiating the PO "+type+" Process..!");
                    }
                }
            }, function error(response) {
                if(response.data.errorMsg != null) {
                    $scope.errorMessage = response.data.errorMsg;
                    if (response.data.errorTitle == 'Advance Related to this is already Sent for adjustment') {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () {
                            $scope.getPOs();
                        }, true);
                    } else {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () {
                            $scope.setAdjustOrRefund('Adjust');
                        }, true);
                    }
                } else{
                    $toastService.create("Error While Initiating the PO Cancel Process..!");
                    console.log("error:" + response);
                }
            });
        };

        $scope.openPoHodActionsModal = function (poData) {
            var hodPoActionsModal = Popeye.openModal({
                templateUrl: "hodPoActionsModal.html",
                controller: "hodPoActionsModalCtrl",
                modalClass: 'custom-modal',
                resolve: {
                    po: function () {
                        return poData;
                    }
                },
                click: false,
                keyboard: false
            });

            hodPoActionsModal.closed.then(function (result) {
                if (result) {
                    $scope.getPOs();
                }
            });
        };

        $scope.cancelPO = function (approveID, index, selectedOrder) {
            if (selectedOrder.vendorAdvancePayments != null) {
                var msg = "";
                var initiatedMsg = "";
                var advanceAdjustInitiatedMsg = "";
                var pendingHodApprovalMsg = "";
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'INITIATED') {
                        initiatedMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }
                    if (selectedOrder.vendorAdvancePayments[i].selectedSoPo != null) {
                        msg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }

                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'PENDING_HOD_APPROVAL') {
                        pendingHodApprovalMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }

                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'ADJUST_INITIATED') {
                        advanceAdjustInitiatedMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }
                }
                if (msg != "") {
                    $alertService.alert("Please Settle the Vendor Advance Related to this PO", msg + "<br>" +
                        "Please Settle the vendor Advance to CANCEL the Purchase Order..!", function () {
                    }, false);
                    return false;
                }
                if (initiatedMsg != "") {
                    $alertService.alert("Please Settle the Vendor Advance Related to this PO", initiatedMsg + "<br>" +
                        "Please Settle the vendor Advance to CANCEL the Purchase Order..!", function () {
                    }, false);
                    return false;
                }
                if (pendingHodApprovalMsg != "") {
                    $alertService.alert("This PO is already sent for Adjustment", pendingHodApprovalMsg + "<br>" +
                        "Please get the HOD Approval to cancel this PO..!", function () {
                    }, false);
                    return false;
                }
                if (advanceAdjustInitiatedMsg != "") {
                    $alertService.alert("This PO has Some Adjustment of Vendor Advance", advanceAdjustInitiatedMsg + "<br>" +
                        "Please get the HOD Approval Of  PO..!", function () {
                    }, false);
                    return false;
                }

                var allCreated = true;
                var totalPrAmount = 0;
                var totalAvailableAmount = 0;
                var totalBlockedAmount = 0;
                var advPaymentIds = [];
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus != 'CREATED') {
                        allCreated = false;
                        break;
                    } else {
                        totalPrAmount = totalPrAmount + selectedOrder.vendorAdvancePayments[i].prAmount;
                        totalAvailableAmount = totalAvailableAmount + selectedOrder.vendorAdvancePayments[i].availableAmount;
                        totalBlockedAmount = totalBlockedAmount + selectedOrder.vendorAdvancePayments[i].blockedAmount;
                        advPaymentIds.push(selectedOrder.vendorAdvancePayments[i].advancePaymentId);
                    }
                }

                if (allCreated && totalPrAmount === totalAvailableAmount) {
                    $scope.cancelObject = angular.copy(selectedOrder.vendorAdvancePayments[0]);
                    $scope.cancelObject.completePrAmount = angular.copy(totalPrAmount);
                    $scope.cancelObject.completeAvailableAmount = angular.copy(totalAvailableAmount);
                    $scope.cancelObject.completeBlockedAmount = angular.copy(totalBlockedAmount);
                    $toastService.create("Adjust/Refund the Vendor Advance to Some Other PO..!");
                    angular.forEach($scope.poRequest, function (po) {
                        po.adjustOrRefund = false;
                    });
                    $scope.poSoSelected = null;
                    selectedOrder.adjustOrRefund = true;
                    return;
                }
            }

            var url = apiJson.urls.requestOrderManagement.cancelPO + "/" + approveID + "/" + $scope.currentUser.userId;
            updatePO(url, function (updated) {
                if (updated) {
                    $toastService.create("Purchase Order cancelled successfully");
                    $scope.poRequest[index].status = "CANCELLED";
                } else {
                    $toastService.create("Purchase Order cancellation failed! Please try again later..");
                }
            });
        };

        $scope.description = function (selectedpoR, index) {
            $scope.selectedpoR = selectedpoR;
            if (appUtil.formatDate(appUtil.calculatedDate(1, selectedpoR.expiryDate), "yyyy-MM-dd") < appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd")) {
                $scope.minDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
            }
            else {
                $scope.minDate = appUtil.formatDate(appUtil.calculatedDate(1, selectedpoR.expiryDate), "yyyy-MM-dd");
            }
            $scope.maxDate = appUtil.formatDate(appUtil.calculatedDate(selectedpoR.leadTime, selectedpoR.expiryDate), "yyyy-MM-dd");
        };

        $scope.changeSelectedDate = function (date) {
            $scope.updatedExpiryDate = date;

        };

        $scope.extendPO = function (extendID, vendorID, extendsiondescription, selectedReason, index) {
            if (appUtil.isEmptyObject(selectedReason)) {
                $toastService.create("Please select a reason first");
                return;
            }

            if (appUtil.isEmptyObject(extendsiondescription)) {
                $toastService.create("Please provide a description first");
                return;
            }
            if (appUtil.isEmptyObject($scope.updatedExpiryDate)) {
                $toastService.create("Please select a Extension Date first");
                return;
            }
            var reqObj = {
                purchaseOrderId: extendID,
                vendorId: vendorID,
                description: extendsiondescription,
                extensionReason: selectedReason,
                updatedBy: $scope.currentUser.userId,
                updatedExpiryDate: $scope.updatedExpiryDate
            };

            $http({
                method: "POST",
                url: apiJson.urls.requestOrderManagement.extendPO,
                data: reqObj
            }).then(function success(response) {
                if (response.data) {
                    $toastService.create("Purchase Order Extended successfully");
                    $scope.selectedpoR.hideExtend = true;
                    $scope.selectedReason = null;
                    $scope.extensionDescription = null;
                    $scope.extendedDate = null;
                    $scope.selectedpoR.expiryDate = $scope.updatedExpiryDate;
                    return;
                } else {
                    $toastService.create("Purchase Order Extension failed! Please try again later..");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.closePO = function (closureId, index, selectedOrder){

            if (selectedOrder.vendorAdvancePayments != null) {
                var msg = "";
                var pendingGrs = [];
                var pendingPrs = [];
                var advanceAdjustInitiatedMsg = "";
                var pendingHodApprovalMsg = "";
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].pendingGrs != null && selectedOrder.vendorAdvancePayments[i].pendingGrs.length > 0) {
                        for (var j=0;j<selectedOrder.vendorAdvancePayments[i].pendingGrs.length;j++) {
                            if (pendingGrs.indexOf(selectedOrder.vendorAdvancePayments[i].pendingGrs[j]) === -1) {
                                pendingGrs.push(selectedOrder.vendorAdvancePayments[i].pendingGrs[j]);
                            }
                        }
                    }

                    if (selectedOrder.vendorAdvancePayments[i].pendingPrs != null && selectedOrder.vendorAdvancePayments[i].pendingPrs.length > 0) {
                        for (var j=0;j<selectedOrder.vendorAdvancePayments[i].pendingPrs.length;j++) {
                            if (pendingPrs.indexOf(selectedOrder.vendorAdvancePayments[i].pendingPrs[j]) === -1) {
                                pendingPrs.push(selectedOrder.vendorAdvancePayments[i].pendingPrs[j]);
                            }
                        }
                    }
                    if (selectedOrder.vendorAdvancePayments[i].selectedSoPo != null) {
                        msg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }

                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'ADJUST_INITIATED') {
                        advanceAdjustInitiatedMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus == 'PENDING_HOD_APPROVAL') {
                        pendingHodApprovalMsg += "Advacne Id : " + selectedOrder.vendorAdvancePayments[i].advancePaymentId + " Adjustment of Advance of Rs : " + selectedOrder.vendorAdvancePayments[i].prAmount + "<br>";
                    }
                }
                if (pendingGrs.length > 0) {
                    $alertService.alert("Please Settle all the pending GR's related to this PO..!","Pending GR's related to this PO are : " +
                        pendingGrs.join(",") + "<br><b>Settle Pending GR's/Complete the Payment Of above GR's</b>",function () {}, true);
                    return false;
                }

                if (pendingPrs.length > 0) {
                    $alertService.alert("Please Settle all the pending PR's related to this PO..!","Pending P's related to this PO are : " +
                        pendingPrs.join(",") + "<br><b>Settle Pending PR's/Complete the Payment Of above GR's</b>",function () {}, true);
                    return false;
                }
                if (msg != "") {
                    $alertService.alert("This PO has Some Adjustment of Vendor Advance", msg + "<br>" +
                        "Please get the HOD Approval to cancel this PO..!", function () {
                    }, false);
                    return false;
                }
                if (advanceAdjustInitiatedMsg != "") {
                    $alertService.alert("This PO has Some Adjustment of Vendor Advance", advanceAdjustInitiatedMsg + "<br>" +
                        "Please get the HOD Approval Of PO..!",function () {}, true);
                    return false;
                }
                if (pendingHodApprovalMsg != "") {
                    $alertService.alert("This PO is already sent for Adjustment",pendingHodApprovalMsg + "<br>" +
                        "Please get the HOD Approval to Close this PO..!",function () {}, true);
                    return false;
                }
                var hasCreated = false;
                var totalPrAmount = 0;
                var totalAvailableAmount = 0;
                var totalBlockedAmount = 0;
                var advPaymentIds = [];
                for (var i = 0; i < selectedOrder.vendorAdvancePayments.length; i++) {
                    if (selectedOrder.vendorAdvancePayments[i].advanceStatus === 'CREATED') {
                        hasCreated = true;
                    }
                        totalPrAmount = totalPrAmount + selectedOrder.vendorAdvancePayments[i].prAmount;
                        totalAvailableAmount = totalAvailableAmount + selectedOrder.vendorAdvancePayments[i].availableAmount;
                        totalBlockedAmount = totalBlockedAmount + selectedOrder.vendorAdvancePayments[i].blockedAmount;
                        advPaymentIds.push(selectedOrder.vendorAdvancePayments[i].advancePaymentId);
                }

                if (hasCreated && totalAvailableAmount > 0) {
                    $scope.cancelObject = angular.copy(selectedOrder.vendorAdvancePayments[0]);
                    $scope.cancelObject.completePrAmount = angular.copy(totalPrAmount);
                    $scope.cancelObject.completeAvailableAmount = angular.copy(totalAvailableAmount);
                    $scope.cancelObject.completeBlockedAmount = angular.copy(totalBlockedAmount);
                    $toastService.create("Adjust/Refund the Vendor Advance to Some Other PO..!");
                    angular.forEach($scope.poRequest, function (po) {
                        po.adjustOrRefund = false;
                    });
                    $scope.poSoSelected = null;
                    selectedOrder.adjustOrRefund = true;
                    return;
                }
            }
            var url = apiJson.urls.requestOrderManagement.closePO + "/" + closureId + "/" + $scope.currentUser.userId;
            updatePO(url, function (updated) {
                if (updated) {
                    $toastService.create("Purchase Order closed successfully");
                    $scope.poRequest[index].status = "CLOSED";
                } else {
                    $toastService.create("Purchase Order closure failed! Please try again later..");
                }
            });
        };
    }
]).controller('consumptionApproveCtrl', ['$scope', 'sku', 'selectedVendor', 'selectedDispatchLocation', 'currentUnit', 'pendingForSku', 'appUtil',
    function ($scope, sku, selectedVendor, selectedDispatchLocation, currentUnit, pendingForSku, appUtil) {

        function createChartOptions(sku) {
            //var labels = Object.keys(sku['consumed']).map(function(label){return appUtil.formatDate(label,"yyyy-MM-dd");});
            var data = [];
            var labels = prepareRollingLabels();
            var dateWise = prepareDistribution(sku['consumed']);
            console.log("datewise", dateWise);
            for (var i in dateWise) {
                data[i] = parseFloat(dateWise[i]);
            }
            return {
                chart: { type: 'column', marginLeft: 70 },
                title: { text: 'Consumption over last 90 days' },
                xAxis: { categories: labels },
                yAxis: { title: { text: 'Qty' }, labels: { enabled: true }, min: 0 },
                series: [{
                    name: sku.skuData.name,
                    data: data
                }]
            };
        }

        function prepareRollingLabels() {
            var date1 = appUtil.formatDate(appUtil.getTimeInPast(90), "yyyy-MM-dd");
            var date2 = appUtil.formatDate(appUtil.getTimeInPast(60), "yyyy-MM-dd");
            var date3 = appUtil.formatDate(appUtil.getTimeInPast(30), "yyyy-MM-dd");
            var date4 = appUtil.formatDate(appUtil.getTimeInPast(0), "yyyy-MM-dd");
            return [date1 + " to " + date2, date2 + " to " + date3, date3 + " to " + date4];
        }

        function prepareDistribution(consumption) {
            var toDate = new Date();
            var distribution = [0, 0, 0];
            for (var i in consumption) {
                var chunk = Math.abs(Math.ceil(appUtil.datediff(i, toDate) / 30) - 3);
                console.log(chunk);
                if (appUtil.isEmptyObject(distribution[chunk])) {
                    distribution[chunk] = 0;
                }
                distribution[chunk] = parseFloat(distribution[chunk]) + parseFloat(consumption[i]);
            }
            return distribution;
        }

        $scope.initChart = function () {
            $scope.sku = sku;
            $scope.chartOptions = createChartOptions(sku);
            $scope.selectedVendor = selectedVendor;
            $scope.selectedDispatchLocation = selectedDispatchLocation;
            $scope.currentUnit = currentUnit;
            $scope.pendingForSku = pendingForSku;
        };

    }
]).controller('hodPoActionsModalCtrl', ['$scope', 'po', 'Popeye','$http','apiJson', 'appUtil', '$toastService','$alertService',
    function ($scope, po,Popeye,$http,apiJson, appUtil, $toastService, $alertService) {
        $scope.po = po;

        $scope.initHodActionsModal = function () {
            $scope.amount = null;
            $scope.lastStatus = null;
            $scope.isAdjusted = null;
            $scope.selectedPo = null;
            $scope.refundSelectedDate = null;
            $scope.setAdvanceDetails();
        };


        $scope.close = function () {
            Popeye.closeCurrentModal(true);
        };

        $scope.setAdvanceDetails = function () {
            var amount = 0;
            for (var i = 0; i < $scope.po.vendorAdvancePayments.length; i++) {
                amount += $scope.po.vendorAdvancePayments[i].availableAmount;
                if ($scope.po.vendorAdvancePayments[i].selectedSoPo != null) {
                    $scope.selectedPo = $scope.po.vendorAdvancePayments[i].selectedSoPo;
                }
                if ($scope.po.vendorAdvancePayments[i].lastPoSoStatus != null) {
                    $scope.lastStatus = $scope.po.vendorAdvancePayments[i].lastPoSoStatus;
                }
                if ($scope.po.vendorAdvancePayments[i].selectedSoPo != null) {
                    $scope.isAdjusted = true;
                }
                if ($scope.po.vendorAdvancePayments[i].refundDate != null) {
                    $scope.refundSelectedDate = $scope.po.vendorAdvancePayments[i].refundDate;
                }
            }
            $scope.amount = amount;
        };

        $scope.approveRejectAdjustmentRefund = function(value) {
            var type = $scope.lastStatus == 'APPROVED' ? 'Cancel' : 'Close';
            $http({
                method: 'POST',
                url: apiJson.urls.paymentRequestManagement.approveRejectAdjustmentRefund,
                params: {
                    approvedBy: appUtil.getCurrentUser().userId,
                    approveReject:value
                },
                data: $scope.po.vendorAdvancePayments[0]
            }).then(function success(response) {
                if (response.status == 200) {
                    if (response.data) {
                        $toastService.create("PO "+ type + " " + value);
                        $scope.close();
                    } else {
                        $toastService.create("Can not"+ type +" the Purchase Order..! (" + value + ")");
                    }
                }
            }, function error(response) {
                if(response.data.errorMsg != null) {
                    $scope.errorMessage = response.data.errorMsg;
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                } else{
                    $toastService.create("Error While submitting the  Process..!");
                    console.log("error:" + response);
                }
            });
        };
    }]);
