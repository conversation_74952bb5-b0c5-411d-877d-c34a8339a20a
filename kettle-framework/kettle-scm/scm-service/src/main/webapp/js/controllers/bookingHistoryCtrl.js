/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('scmApp')
    .controller(
        'bookingHistoryCtrl',
        [
            '$rootScope',
            '$scope',
            'apiJson',
            '$http',
            'appUtil',
            '$toastService',
            'metaDataService',
            '$timeout',
            '$alertService',
            'previewModalService',
            function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, metaDataService, $timeout,
                      $alertService, previewModalService) {

                $scope.init = function () {
                    $scope.reset();
                    $scope.startDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                    $scope.endDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                    $scope.getbookingHistory();
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.gridOptions = appUtil.getGridOptions($scope);
                };

                $scope.reset = function (val) {
                    $scope.bookingHistory = null;
                };

                $scope.downloadExcell = function (){
                    var params = {
                        className : "com.stpl.tech.scm.domain.model.ProductionBooking"
                    }
                    var jsonStrings = [];
                    for(var i = 0;i<$scope.bookingHistory.length;i++){
                        jsonStrings.push(JSON.stringify($scope.bookingHistory[i]));
                    }
                    metaDataService.downloadExcell(jsonStrings,params);
                }

                $scope.getbookingHistory = function () {
                    if ($scope.checkDates()) {
                        var payload = {
                            unitId: appUtil.getCurrentUser().unitId,
                            startDate: $scope.startDate,
                            endDate: $scope.endDate
                        };
                        $http({
                            url: apiJson.urls.productionBooking.bookings,
                            method: "POST",
                            data: payload
                        }).then(function (response) {
                            $scope.bookingHistory = response.data;
                            $scope.gridOptions.columnDefs = $scope.gridColumns();
                            $scope.gridOptions.data = $scope.bookingHistory;
                        }, function (response) {
                            console.log(response);
                        });
                    } else {
                        $toastService.create("Please select valid dates");
                    }
                };

                $scope.gridColumns  = function () {
                    return [{
                        field: 'bookingId',
                        name: 'id',
                        enableCellEdit: false,
                        displayName: 'PB No.'
                    }, {
                        field: 'productName',
                        name: 'name',
                        enableCellEdit: false,
                        displayName: 'Product Name'
                    }, {
                        field: 'unitOfMeasure',
                        name: 'uom',
                        enableCellEdit: false,
                        displayName: 'UOM'
                    }, {
                        field: 'quantity',
                        name: 'quantity',
                        enableCellEdit: false,
                        displayName: 'Quantity'
                    }, {
                        field: 'generationTime',
                        name: 'Date',
                        enableCellEdit: false,
                        displayName: 'Date',
                        cellFilter: 'date:\'yyyy-MM-dd\'', width: 150,
                        sortingAlgorithm: function (aDate, bDate) {
                            var a=new Date(aDate);
                            var b=new Date(bDate);
                            if (a < b) {
                                return -1;
                            }
                            else if (a > b) {
                                return 1;
                            }
                            else {
                                return 0;
                            }
                        }
                    },
                        {field: 'generationTime',
                            name: 'Time',
                            enableCellEdit: false,
                            displayName: 'Time',
                            cellFilter: 'date:\'HH:mm:ss\'', width: 150,
                            },
                        {
                            field: 'generatedBy.name',
                            name: 'generatedBy',
                            enableCellEdit: false,
                            displayName: 'generated By'
                        },
                        {
                            field: 'bookingStatus',
                            name : 'Action',
                            cellTemplate :' <div ng-if= "grid.appScope.isCreatedStatus(row.entity.bookingStatus) " class="ui-grid-cell-contents"> <button type="button" class = "btn btn-xs-small" acl-action="TRNBHC" ng-click="grid.appScope.cancelBooking($event,row.entity.bookingId)" >Cancel </button> </div> <span ng-if="!grid.appScope.isCreatedStatus(row.entity.bookingStatus)">{{row.entity.bookingStatus}}</span> '
                        }
                        ]
                }

                $scope.isCreatedStatus = function (status){
                    return status === "CREATED";
                }

                $scope.checkDates = function () {
                    return $scope.startDate != null && $scope.endDate != null
                        && new Date($scope.startDate).getTime() <= new Date($scope.endDate).getTime();
                }

                $scope.cancelBooking = function (event, bookingId) {
                    var msg = "This action cannot be reverted back. All the SKU inventory consumption will be reverted.";
                    $alertService.confirm("Are you sure?", msg, function (result) {
                        if (result) {
                            var payload = {
                                empId: appUtil.getCurrentUser().user.id,
                                id: bookingId
                            };

                            $http({
                                url: apiJson.urls.productionBooking.cancel,
                                method: "POST",
                                data: payload
                            }).then(function (response) {
                                if (response.data) {
                                    $toastService.create("Booking Cancelled!");
                                    $scope.getbookingHistory();
                                } else {
                                    $toastService.create("Booking Cancellation Failed!");
                                }
                            }, function (response) {
                                console.log(response);
                            });
                        }
                    });

                    //event.stopPropagation();
                };

            }]);
