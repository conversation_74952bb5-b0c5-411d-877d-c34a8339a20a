scmApp.controller(
    'businessToCustomerCtrl',
    [
        '$rootScope',
        '$scope',
        '$interval',
        'apiJson',
        '$http',
        'appUtil',
        '$toastService',
        'uiGridConstants',
        '$alertService',
        '$timeout',
        'metaDataService',
        'previewModalService',
        function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $toastService, $uiGridConstants,
                  $alertService, $timeout, metaDataService,previewModalService) {

            $scope.init = function () {
                $scope.mappingList = [{
                    id: 1,
                    name: 'Business To Vendor Mapping'
                }];
                $scope.businessTypes = ["ECOM","B2B_SALES","CHAAYOS"];

                // $scope.businessTypeList = [{
                //     id: 1,
                //     name: 'ECOM'
                // }, {
                //     id: 2,
                //     name: 'B2B_SALES'
                // }, {
                //     id: 3,
                //     name: 'CHAAYOS'
                // }];
                 metaDataService.getBusinessTypes(function (businessType) {
                     $scope.businessTypeList = businessType;
                });
                $scope.currentUnit = appUtil.getUnitData();
                $scope.initializeGridOptions();
                $scope.reset();
            };

            $scope.reset = function () {
                $scope.gridDataId = null;
                $scope.updateMappingRequested = false;
                $scope.gridOptions.data = null;
            };

            $scope.initializeGridOptions = function () {
                $scope.gridOptions = {
                    enableColumnMenus: false,
                    saveFocus: false,
                    enableRowSelection: true,
                    enableFiltering: true,
                    saveScroll: true,
                    enableSelectAll: true,
                    multiSelect: true,
                    enableColumnResizing : true,
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.selection.on.rowSelectionChanged($scope, function (row) {
                            if (row.isSelected) {
                                row.entity.selectData = true;
                                row.entity.mappingStatus = 'ACTIVE';
                                row.entity.check = true;
                            } else {
                                row.entity.selectData = false;
                                row.entity.check = false;
                                row.entity.mappingStatus = 'IN_ACTIVE'
                            }
                        });
                        gridApi.selection.on.rowSelectionChangedBatch($scope, function (rows) {
                            rows.forEach(function (row) {
                                if (row.isSelected) {
                                    row.entity.selectData = true;
                                    row.entity.mappingStatus = 'ACTIVE';
                                    row.entity.check = false;
                                } else {
                                    row.entity.selectData = false;
                                    row.entity.check = false;
                                    row.entity.mappingStatus = 'IN_ACTIVE'
                                }
                            });
                        });
                    }
                };
                $scope.gridOptions.multiSelect = true;
            };

            $scope.businessToChaayosVendorMapping = function () {
                return [{
                    field: 'id',
                    name: 'id',
                    displayName: 'Vendor Id'
                }, {
                    field: 'name',
                    name: 'name',
                    displayName: 'Vendor Name'
                }, {
                    field: 'status',
                    name: 'status',
                    displayName: 'status'
                }, {
                    field: 'mappingStatus',
                    name: 'mappingStatus',
                    displayName: 'mapping status'
                }];
            };

            $scope.businessToVendorMapping = function () {
                return [{
                    field: 'id',
                    name: 'id',
                    displayName: 'Customer Id'
                }, {
                    field: 'name',
                    name: 'name',
                    displayName: 'Customer Name'
                }, {
                    field: 'status',
                    name: 'status',
                    displayName: 'Customer Status'
                }, {
                    field: 'mappingStatus',
                    name: 'mappingStatus',
                    displayName: 'Mapping Status'
                }];
            };

            $scope.searchMappingShow = function () {
                $scope.searchData('search', 1);
            };

            $scope.deactivateAllVendorList = function () {
                for (var i = 0; i < $scope.vendorList.length; i++) {
                    $scope.vendorList[i].selectData = false;
                    $scope.vendorList[i].check = false;
                    $scope.vendorList[i].mappingStatus = 'IN_ACTIVE';
                }
            };

            $scope.preSelectData = function () {
                $scope.gridApi.grid.modifyRows($scope.gridOptions.data);
                $scope.gridOptions.data.forEach(function (row, index) {
                    if (row.selectData) {
                        $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                    } else {
                        $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                    }
                });
                $(".ui-grid-header-canvas").css("height","54px");
            };

            $scope.getVendorListForBusiness = function (businessId, callback) {
                $http({
                    url: apiJson.urls.skuMapping.getVendorForBusiness,
                    method: 'POST',
                    data: businessId
                }).then(function (response) {
                    if(!appUtil.isEmptyObject(response) && typeof callback == "function"){
                        callback(response.data);
                    }else {
                        $toastService.create("Could not find vendors mapped to the selected BusinessType");
                    }
                }, function (response) {
                    console.log("got error", response);
                });
            }

            $scope.refreshBusinesssToVendorMapping = function () {
                $scope.getVendorListForBusiness($scope.bussType.id,function(businessVendorList) {
                    $scope.deactivateAllVendorList();
                    for (var j = 0; j < businessVendorList.length; j++) {
                        for (var i = 0; i < $scope.vendorList.length; i++) {
                            if ($scope.vendorList[i].id == businessVendorList[j].id) {
                                if ('ACTIVE' == businessVendorList[j].mappingStatus) {
                                    $scope.vendorList[i].check = true;
                                    $scope.vendorList[i].selectData = true;
                                    $scope.vendorList[i].mappingStatus = businessVendorList[j].mappingStatus;
                                }
                                break;
                            }
                        }
                    }
                    if($scope.bussType.name == "RETAIL"){
                        $scope.gridOptions.columnDefs = $scope.businessToChaayosVendorMapping();
                    }else{
                        $scope.gridOptions.columnDefs = $scope.businessToVendorMapping();
                    }
                    $scope.gridOptions.data = $scope.vendorList;
                    $scope.preSelectData();
                });
            };

            $scope.backPreview = function () {
                $scope.updateMappingRequested = false;
                $timeout(function () {
                    $scope.preSelectData();
                });
            };

            $scope.searchData = function (type, id) {
                if (type == 'search') {
                    $scope.gridDataId = id;
                }
                $scope.refreshBusinesssToVendorMapping();
            };

            $scope.changeType = function (txnType) {
                $scope.reset();
                $scope.mappingTypeData = "Business To Vendor Mapping"
                var type = txnType == "RETAIL" ? "VENDOR" : "CUSTOMER";

                //explicit condition for ECOM as its requirement is not clear
                metaDataService.getAllVendors().then(function (vendors) {
                        if (vendors != null) {
                            $scope.vendorListOriginal = vendors;
                            $scope.vendorList = [];
                            for (var k=0;k<$scope.vendorListOriginal.length;k++){
                                if($scope.vendorListOriginal[k].category == type){
                                    $scope.vendorList.push($scope.vendorListOriginal[k]);
                                }
                            }
                            //clear the select2 after change in type of transaction
                            $timeout(function () {
                                $('.select2Input').select2('val', '');
                            });
                        } else {
                            $toastService.create("Could not fetch vendors");
                        }
                    });
            };

            $scope.submitMapping = function () {
                $scope.allGridViewShow = [];
                $scope.gridOptions.data.forEach(function (row) {
                    if (row.selectData == true) {
                        $scope.allGridViewShow.push(row);
                    }
                });
                if($scope.allGridViewShow)
                $scope.updateMappingRequested = true;
            };

            $scope.updateMapping = function () {
                $alertService.confirm("Are you sure?",
                    "Requested mappings will be marked as active, others will be marked as Inactive. ",
                    function (yes) {
                        if (yes) {
                            $scope.submitUpdateMappingRequest();
                        }
                    });
            };

            $scope.submitUpdateMappingRequest = function () {
                var currentUser = appUtil.getCurrentUser();
                var idsList = [];
                $scope.allGridViewShow.forEach(function (ListBusinessToVendorShow) {
                    if (ListBusinessToVendorShow!=null && ListBusinessToVendorShow.selectData == true) {
                        idsList.push(ListBusinessToVendorShow.id);
                    }
                });
                var payload = {
                    id: $scope.bussType.id,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name,
                    mappingIds: idsList
                };
                var targetUrl = null;
                if ($scope.mappingType.name == "Business To Vendor Mapping") {
                    targetUrl = apiJson.urls.skuMapping.updateVendorForBusiness;
                }
                if (targetUrl != null) {
                    $http({
                        url: targetUrl,
                        method: 'POST',
                        data: payload
                    }).then(function (response) {
                        $toastService.create($scope.bussType.name + " Updated Successfully!");
                        $scope.reset();
                    }, function (response) {
                        console.log("got error", response);
                    });
                } else {
                    console.log("No Mapping Assigned");
                }
            };
        }
    ]
);
