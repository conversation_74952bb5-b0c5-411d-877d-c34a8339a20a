/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('createDispatchCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window','$state',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
                  previewModalService, Popeye, $timeout, $window,$state) {

            $scope.init = function () {
                $scope.showSearchDispatchScreen = true;
                $scope.showCreateDispatchScreen = false;
                $scope.showConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = false;
                $scope.transportModes = [];
                $scope.getTransportModes();
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.unitList = appUtil.getUnitList();
                $scope.minDate = appUtil.getDate(-1);
                $scope.maxDate = appUtil.getDate(1);
                $scope.showValidGstText = false;
                $scope.inValidRegNumber = false;
            };

            $scope.validateRegdNumber = function(v){
                $scope.inValidRegNumber = validateRegdNumber(v);
            };

            $scope.goToSearchDispatchScreen = function () {
                $scope.showSearchDispatchScreen = true;
                $scope.showCreateDispatchScreen = false;
                $scope.showCreateConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = false;
            };

            $scope.goToCreateDispatchScreen = function () {
                $scope.showSearchDispatchScreen = false;
                $scope.showCreateDispatchScreen = true;
                $scope.showCreateConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = false;
            };

            $scope.goToConsignmentDetailScreen = function () {
                $scope.showSearchDispatchScreen = false;
                $scope.showCreateDispatchScreen = false;
                $scope.showCreateConsignmentScreen = false;
                $scope.showConsignmentDetailScreen = true;
            };

            $scope.goToCreateConsignmentScreen = function () {
                $scope.showSearchDispatchScreen = false;
                $scope.showCreateDispatchScreen = false;
                $scope.showCreateConsignmentScreen = true;
                $scope.showConsignmentDetailScreen = false;
            };

            $scope.getTransportModes = function () {
                $http({
                    url: apiJson.urls.transportManagement.transportModes,
                    method: 'GET',
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        response.map(function (item, index) {
                            $scope.transportModes.push({id: index, name: item});
                        });
                    } else {
                        $toastService.create("Error loading transport modes.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error loading transport modes", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.setDispatchDate = function (date) {
                $scope.dispatchDate = date;
                $scope.selectedTransportMode = null;
                $scope.vehicles = [];
                $scope.selectedVehicle = null;
            };

            $scope.getVehicles = function () {
                $scope.vehicles = [];
                $scope.selectedVehicle = null;
                if($scope.dispatchDate == undefined || $scope.dispatchDate == null){
                    $toastService.create("Please select dispatch date.");
                    $scope.selectedTransportMode = null;
                    return;
                }
                $scope.showTransporterFields = $scope.selectedTransportMode.name != "ROAD";
                $http({
                    url: apiJson.urls.transportManagement.vehiclesByTransportMode,
                    method: 'GET',
                    params: {
                        transportMode: $scope.selectedTransportMode.name,
                        date: $scope.dispatchDate
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.vehicles = response;
                    } else {
                        $toastService.create("Error loading vehicles.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error loading vehicles", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.validateAndSaveId = function(id){
                $scope.showValidGstText = false;
                if (!appUtil.isEmptyObject(id) && validateGst(id)){
                    $scope.transporterId = id.toUpperCase();
                }else {
                    $scope.showValidGstText = true;
                }
            };

            $scope.validateAndSaveName = function(docketNumber){
                $scope.docketNumber = docketNumber.toUpperCase();
            };



            $scope.getDispatchList = function () {
                if($scope.dispatchDate == null){
                    $toastService.create("Please select dispatch date.");
                    return;
                }
                if($scope.selectedTransportMode == null){
                    $toastService.create("Please select transport mode.");
                    return;
                }
                if($scope.selectedVehicle == null){
                    $toastService.create("Please select vehicle.");
                    return;
                }
                if(!$scope.validateNumber($scope.selectedVehicle.registrationNumber)){
                    $toastService.create("Invalid Vehicle Number : "+$scope.selectedVehicle.registrationNumber);
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.searchDispatch,
                    method: 'GET',
                    params: {date: $scope.dispatchDate, vehicleId: $scope.selectedVehicle.vehicleId,registrationNumber : $scope.selectedVehicle.registrationNumber}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.dispatchList = response;
                        $scope.goToCreateDispatchScreen();
                        $scope.setShowCreateDispatch();
                    } else {
                        $toastService.create("Error loading dispatch data.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error loading dispatch data", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.validateNumber = function(str){
            	var pattern =  /[A-Z][A-Z][0-9]{0,2}[A-Z]{0,2}[0-9][0-9][0-9][0-9]$/i;
            	 var result = str.match(pattern);
            	 if(result != null && result == str){
            		 return true;
            	 }
            	 return false;
            }
            $scope.setShowCreateDispatch = function () {
                if ($scope.selectedVehicle.multiDispatch == true) {
                    $scope.showCreateDispatch = true;
                    for (var i = 0; i < $scope.dispatchList.length; i++) {
                        if ($scope.dispatchList[i].status != "SETTLED") {
                            $scope.showCreateDispatch = false;
                            break;
                        }
                    }
                } else {
                    $scope.showCreateDispatch = ($scope.dispatchList.length == 0);
                }
            };

            $scope.createDispatch = function () {
                var params = {date: $scope.dispatchDate, vehicleId: $scope.selectedVehicle.vehicleId};
                if ($scope.selectedTransportMode.name!="ROAD"){
                    if($scope.showValidGstText){
                        $toastService.create("Please enter proper GSTIN number for transporter");
                        return;
                    }
                    if(appUtil.isEmptyObject($scope.docketNumber)){
                        $toastService.create("Please enter proper Docket number for transporter");
                        return;
                    }
                    params["transporter"] = $scope.transporterId;
                    params["docket"] = $scope.docketNumber;
                }else{
                    params["regNumber"] = $scope.selectedVehicle.registrationNumber;
                }
                $http({
                    url: apiJson.urls.transportManagement.createDispatch,
                    method: 'GET',
                    params: params
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.dispatchData = response;
                        $scope.dispatchList.push($scope.dispatchData);
                        $scope.setShowCreateDispatch();
                    } else {
                        $toastService.create("Error creating dispatch.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error loading dispatch data", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.selectedConsignmentUnits = [];
            $scope.selectedConsignmentReceivingUnits = [];
            $scope.multiSelectSetting = {
                enableSearch: true, template: '<b> {{option.name}}</b>', scrollable: true,
                scrollableHeight: '250px',clearSearchOnClose: true,
                groupByTextProvider: function(groupValue) { if (groupValue === 'Suggested') { return 'Suggested'; } else { return 'All'; } },
                groupBy: 'isSuggested'
            };

            $scope.addConsignment = function (type, dispatchItem) {
                $scope.consignmentType = type;
                $scope.dispatchData = dispatchItem;
                $scope.goToCreateConsignmentScreen();
                $scope.clearCreateConsignmentData();
                if ($scope.allUnitsList != null) {
                    $scope.consignmentUnits = $scope.filterUnits($scope.allUnitsList, type);
                    $scope.consignmentReceivingUnits = $scope.filterUnits($scope.allUnitsList, type == "TRANSFER" ? "RECEIVING" : "TRANSFER");
                    $scope.getSuggestedUnits(type);
                } else {
                    $scope.getUnitsForConsignment(type);
                }
            };

            $scope.selectVehicle = function (vehicle) {
                $scope.selectedVehicle = vehicle;
                if($scope.selectedTransportMode.name!="ROAD"){
                    $scope.validateAndSaveId(vehicle.registrationNumber);
                }else{
                    $scope.transporterId = null;
                }
            };

            $scope.clearCreateConsignmentData = function () {
                $scope.consignmentUnits = [];
                $scope.consignmentUnitsSuggested = [];
                $scope.consignmentReceivingUnitsSuggested = [];
                $scope.suggestedUnits = [];
                $scope.selectedConsignmentUnits = [];
                $scope.selectedConsignmentReceivingUnits = [];
                $scope.selectedConsignmentUnit = null;
                $scope.selectedConsignmentReceivingUnit = null;
                $scope.consignmentItems = [];
                $scope.startDate = null;
                $scope.endDate = null;
            };

            $scope.selectAllConsignmentItems = function () {
                $scope.selectAllConsignments = !$scope.selectAllConsignments;
                $scope.consignmentItems.map(function (item) {
                    item.selected = $scope.selectAllConsignments;
                });
            };

            $scope.getUnitsForConsignment = function (type) {
                $http({
                    url: apiJson.urls.unitMetadata.allUnitsList,
                    method: 'GET'
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.allUnitsList = response;
                        $scope.consignmentUnits = $scope.filterUnits($scope.allUnitsList, type);
                        $scope.consignmentReceivingUnits = $scope.filterUnits($scope.allUnitsList, type == "TRANSFER" ? "RECEIVING" : "TRANSFER");
                        $scope.getSuggestedUnits(type);
                    } else {
                        $toastService.create("Error getting units.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting units.", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.getSuggestedUnits = function (type) {
                $http({
                    url: apiJson.urls.transportManagement.getSuggestedUnits,
                    method: 'GET',
                    params: {
                         vehicleId: $scope.selectedVehicle.vehicleId,
                         date : $scope.dispatchDate
                        }
                }).then(function success(response) {
                    console.log(response);
                    if (response.data != null && response.status == 200) {
                        $scope.suggestedUnits  = response.data;
                        $scope.consignmentUnitsSuggested = $scope.getFromSuggestedUnits(angular.copy($scope.consignmentUnits), $scope.suggestedUnits,type);
                        $scope.consignmentReceivingUnitsSuggested = $scope.getToSuggestedUnits(angular.copy($scope.consignmentReceivingUnits), $scope.suggestedUnits,type);
                    }
                }, function error(response) {
                    $toastService.create("Error in getting the Suggested Units..!");
                    $scope.suggestedUnits = [];
                    $scope.consignmentUnitsSuggested = [];
                    $scope.consignmentReceivingUnitsSuggested = [];
                });
            };

            $scope.getFromSuggestedUnits = function (units,suggestedUnits,type) {
                var result = [];
                var suggested = [];
                if (type == "TRANSFER") {
                    suggested = suggestedUnits["From"];
                }
                else {
                    suggested = suggestedUnits["To"];
                }
                console.log("from is ",suggested);
                for (var i = 0;i < units.length;i++) {
                    console.log("current unit is ",unit);
                    if (suggested.indexOf(units[i].id) != -1) {
                        var unit = units[i];
                        console.log("Suggested for ",unit.name);
                        unit.isSuggested = "Suggested";
                        result.unshift(unit);
                    }
                    else {
                        var unit = units[i];
                        console.log("All ",unit.name);
                        unit.isSuggested = "All";
                        result.push(unit);
                    }
                    console.log("unit updated is ",unit);
                }
                return result;
            };

            $scope.getToSuggestedUnits = function (units,suggestedUnits,type) {
                var result = [];
                var suggested = [];
                if (type == "TRANSFER") {
                    suggested = suggestedUnits["To"];
                }
                else {
                    suggested = suggestedUnits["From"];
                }
                for (var i = 0;i < units.length;i++) {
                    unit = units[i];
                    if (suggested.indexOf(units[i].id) != -1) {
                        var unit = units[i];
                        unit.isSuggested = "Suggested";
                        result.unshift(unit);
                    }
                    else {
                        var unit = units[i];
                        unit.isSuggested = "All";
                        result.push(unit);
                    }
                }
                return result;
            };

            $scope.filterUnits = function (units, type) {
                var unitList = [];
                units.map(function (unit) {
                    if (unit.status == "ACTIVE") {
                        if (type == "TRANSFER") {
                            if (unit.category == "WAREHOUSE" || unit.category == "KITCHEN") {
                                unitList.push(unit);
                            }
                        } else {
                            // WAREHOUSE filter added to make DKC units also available, since they are of Warehouse category
                            if (unit.category == "CAFE" || unit.category == "CHAI_MONK" || unit.category == "WAREHOUSE" || unit.category == "KITCHEN") {
                                unitList.push(unit);
                            }
                        }
                    }
                });
                return unitList;
            };

            $scope.setSelectedConsignmentUnit = function (unit) {
                $scope.selectedConsignmentUnit = unit;
            };

            $scope.setSelectedConsignmentReceivingUnit = function (unit) {
                $scope.selectedConsignmentReceivingUnit = unit;
            };

            $scope.addSelectedConsignmentUnit = function () {
                console.log("selected unit is : ",$scope.selectedConsignmentUnit);
                if ($scope.selectedConsignmentUnit == null) {
                    $toastService.create("Please Select an unit and Add ..!");
                    return false;
                }
                var found = false;
                $scope.selectedConsignmentUnits.map(function (unit) {
                    console.log("unit in map is : ", unit);
                    if (unit.id === $scope.selectedConsignmentUnit.id) {
                        found = true;
                    }
                });
                if (!found) {
                    $scope.selectedConsignmentUnits.push($scope.selectedConsignmentUnit);
                }
            };

            $scope.removeConsignmentUnit = function (index) {
                $scope.selectedConsignmentUnits.splice(index, 1);
            };

            $scope.addSelectedConsignmentReceivingUnit = function () {
                if ($scope.selectedConsignmentReceivingUnit == null) {
                    $toastService.create("Please Select an unit and Add ..!");
                    return false;
                }
                var found = false;
                $scope.selectedConsignmentReceivingUnits.map(function (unit) {
                    if (unit.id === $scope.selectedConsignmentReceivingUnit.id) {
                        found = true;
                    }
                });
                if (!found) {
                    $scope.selectedConsignmentReceivingUnits.push($scope.selectedConsignmentReceivingUnit);
                }
            };

            $scope.removeConsignmentReceivingUnit = function (index) {
                $scope.selectedConsignmentReceivingUnits.splice(index, 1);
            };

            $scope.getConsignmentItems = function () {
                var unitIds = [], receivingUnitIds = [];
                $scope.selectedConsignmentUnits.map(function (item) {
                    unitIds.push(item.id);
                });
                if (unitIds.length === 0) {
                    $toastService.create("Please select from unit.");
                    return;
                }
                $scope.selectedConsignmentReceivingUnits.map(function (item) {
                    receivingUnitIds.push(item.id);
                });
                if($scope.consignmentType == "RECEIVING" && receivingUnitIds.length == 0){
                    $toastService.create("Please select to unit.");
                    return;
                }
                if ($scope.startDate == null || $scope.startDate == undefined) {
                    $toastService.create("Please Select Start Date");
                    return;
                }
                if ($scope.endDate == null || $scope.endDate == undefined) {
                    $toastService.create("Please Select End Date");
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.pendingTOs,
                    method: 'POST',
                    data: {
                        transferringUnitList: unitIds,
                        receivingUnitList: receivingUnitIds,
                        startDate: $scope.startDate,
                        endDate: $scope.endDate
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.consignmentItems = response;
                        $scope.selectAllConsignments = false
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.createConsignment = function () {
                var itemIds = [];
                $scope.consignmentItems.map(function (item) {
                    if (item.selected === true) {
                        itemIds.push(item.id);
                    }
                });
                if (itemIds.length === 0) {
                    $toastService.create("Please select consignment item.");
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.createConsignment,
                    method: 'POST',
                    data: {id: $scope.dispatchData.dispatchId, toList: itemIds, empId: appUtil.createGeneratedBy().id}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null && response === true) {
                        $scope.goToCreateDispatchScreen();
                        $scope.getDispatchList();
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if(response.errorTitle === "distance mapping not Found"){
                        var unitIds = response.errorMsg.split(",");
                        var sourceId = unitIds[0];
                        var destinationId = unitIds[1];
                        getUnitMapDistance(sourceId,destinationId);
                    }else{
                        $alertService.alert("Error getting items", response.errorMsg, function () {
                        }, true)
                    }

                });
            };

            function openDistanceViewModal(distanceMapResponse){
                var distanceViewModal = Popeye.openModal({
                    templateUrl: "unitDistanceView.html",
                    controller: "distanceViewModalCtrl",
                    resolve: {
                        distanceMapResponse : function (){
                            return distanceMapResponse;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });
            }



            function getUnitMapDistance(source, destination){
                $http({
                    url: apiJson.urls.scmMetadata.getUnitMapDistance,
                    method: "GET",
                    params : {
                        sourceId : source,
                        destinationId : destination
                    }
                }).then(
                    function success(response) {
                        console.log("distance : ",response.data.distance);
                        openDistanceViewModal(response.data);
                    }, function error(response) {
                        $alertService.alert(response.errorTitle, response.errorMsg, function () {
                        }, true)
                    });
            }


            $scope.changeForceEway = function(value){
                $scope.forceEway = value;
            };

            $scope.startDispatch = function (dispatchData) {
                $scope.dispatchData = dispatchData;
                if (!$scope.dispatchData) {
                    $toastService.create("Please add dispatch;");
                    return;
                }
                if ($scope.dispatchData.consignmentList.length === 0) {
                    $toastService.create("Please add consignment;");
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.startDispatch,
                    method: 'GET',
                    params: {
                        dispatchId: $scope.dispatchData.dispatchId,
                        forceEway: ($scope.forceEway!=undefined) ? $scope.forceEway: false
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $toastService.create("Dispatch status updated successfully.");
                        $scope.getDispatchList();
                    } else {
                        $toastService.create("Error starting dispatch.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.downloadEwayFile = function (dispatchData) {
                $scope.dispatchData = dispatchData;
                if (!$scope.dispatchData) {
                    $toastService.create("Please add dispatch;");
                    return;
                }
                if ($scope.dispatchData.consignmentList.length === 0) {
                    $toastService.create("Please add consignment;");
                    return;
                }
                $http({
                    url: apiJson.urls.transportManagement.downloadEwayFile,
                    method: 'GET',
                    params: {dispatchId: $scope.dispatchData.dispatchId},
                    responseType: 'arraybuffer'
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        var blob = new Blob([response], {type: appUtil.mimeTypes["JSON"]}, "E-WayBill_JSON" + ".json");
                        saveAs(blob, "dispatch_" + $scope.dispatchData.dispatchId + ".json");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.getDispatchDetailForPrint = function (dispatchData) {
                $scope.dispatchData = dispatchData;
                $http({
                    url: apiJson.urls.transportManagement.getDispatchDetail,
                    method: 'GET',
                    params: {dispatchId: $scope.dispatchData.dispatchId}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.dispatchData = response;
                        decorateObjectForPrinting();
                        $timeout(function () {
                            $window.print();
                        }, 0);
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            function decorateObjectForPrinting() {
                var unitMap = {};
                $scope.unitList.map(function (item) {
                    unitMap[item.id] = item;
                });
                $scope.dispatchData.consignmentList.map(function (consignment) {
                    consignment.ewaybills.map(function (ewb) {
                        ewb.transferOrder.generationUnitId.address = unitMap[ewb.transferOrder.generationUnitId.id].address;
                        ewb.transferOrder.generatedForUnitId.address = unitMap[ewb.transferOrder.generatedForUnitId.id].address;
                        ewb.transferOrder.generationUnitId.tin = unitMap[ewb.transferOrder.generationUnitId.id].tin;
                        ewb.transferOrder.generatedForUnitId.tin = unitMap[ewb.transferOrder.generatedForUnitId.id].tin;
                        ewb.transferOrder.generationUnitId.city = unitMap[ewb.transferOrder.generationUnitId.id].city;
                        ewb.transferOrder.generatedForUnitId.city = unitMap[ewb.transferOrder.generatedForUnitId.id].city;
                        var total = 0,
                            availableTaxes = [];
                        ewb.transferOrder.transferOrderItems.map(function (item) {
                            total += (item.total + item.tax);
                            $scope.calculateTaxes(item, availableTaxes);
                        });
                        ewb.transferOrder.totalPrice = Math.round(total);
                        ewb.transferOrder.totalPriceInWords = appUtil.inWords(Math.round(total));
                        ewb.transferOrder.availableTaxes = availableTaxes;
                    });
                });
            }

            $scope.calculateTaxes = function (item, availableTaxes) {
                item.gst = 0;
                item.gstPercentage = 0;
                item.other = 0;
                item.otherPercentage = 0;
                if (item.taxes == null || item.taxes.length === 0) {
                    return;
                }
                for (var i in item.taxes) {
                    if (availableTaxes.indexOf(item.taxes[i].code) < 0) {
                        availableTaxes.push(item.taxes[i].code);
                    }
                    if (item.taxes[i].type === 'GST') {
                        item.gst = item.gst + item.taxes[i].value;
                        item.gstPercentage = item.taxes[i].percentage;
                    } else {
                        item.other = item.other + item.taxes[i].value
                    }
                }
                if (item.total > 0) {
                    item.otherPercentage = item.other / item.total * 100;
                }
            };

            $scope.viewConsignmentData = function (item) {
                $scope.viewConsignmentItem = item;
                $scope.goToConsignmentDetailScreen();
            };

            $scope.getTODetails = function (id) {
                $http({
                    url: apiJson.urls.transferOrderManagement.transferOrder,
                    method: 'GET',
                    params: {transferOrderId: id}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.viewEwayBill = {type: "TO", transferOrder: response};
                        openEwayBillPreviewModal();
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.getEwayBillDetails = function (id) {
                $http({
                    url: apiJson.urls.transportManagement.getEwayBillDetails,
                    method: 'GET',
                    params: {ewayId: id}
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.viewEwayBill = response;
                        $scope.viewEwayBill.type = "EWAY";
                        openEwayBillPreviewModal();
                    } else {
                        $toastService.create("Error getting items.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };

            function openEwayBillPreviewModal() {
                var mappingModal = Popeye.openModal({
                    templateUrl: "ewayBillPreviewModal.html",
                    controller: "ewayBillPreviewModalCtrl",
                    resolve: {
                        viewEwayBill: function () {
                            return $scope.viewEwayBill;
                        }
                    },
                    modalClass: "popeye-modal-large",
                    click: false,
                    keyboard: false
                });

                mappingModal.closed.then(function (data) {

                });
            };

            $scope.cancelConsignment = function (id) {
                $http({
                    url: apiJson.urls.transportManagement.cancelConsignment,
                    method: 'POST',
                    data: id
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null && response === true) {
                        $toastService.create("Consignment cancelled.");
                        $scope.getDispatchList();
                    } else {
                        $toastService.create("Error in cancellation.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Error getting items", response.errorMsg, function () {
                    }, true)
                });
            };
        }]
    )
    .controller('ewayBillPreviewModalCtrl', ['$scope', 'viewEwayBill', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
            function ($scope, viewEwayBill, appUtil, $toastService, apiJson, $http, Popeye) {
                $scope.viewEwayBill = viewEwayBill;
                $scope.closeModal = function () {
                    Popeye.closeCurrentModal();
                };
            }
        ]
    ).controller('distanceViewModalCtrl', ['$scope', 'distanceMapResponse','appUtil', '$toastService', 'apiJson', '$http', 'Popeye','$window',
        function ($scope, distanceMapResponse,appUtil, $toastService, apiJson, $http, Popeye,$window) {
        $scope.init = function (){
            $scope.googleMapDistance =  distanceMapResponse.distance;
            $scope.sourcePinCode = distanceMapResponse.sourcePinCode;
            $scope.destinationPinCode =  distanceMapResponse.destinationPinCode;
            $scope.distanceMapResponse = distanceMapResponse;
            $scope.govtEwayUrl = "https://einvoice1.gst.gov.in/Others/GetPinCodeDistance";
            $scope.userInput = null;
            $scope.govtSiteDistance = null;
            $scope.copiedText = null;
            $scope.isValid = null;
        }
            $scope.copyToClipboard = function (copyText) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(copyText);
                } else {
                    var textArea = document.createElement("textarea");
                    textArea.value = copyText;
                    // Avoid scrolling to bottom
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? 'successful' : 'unsuccessful';
                        console.log('Fallback: Copying text command was ' + msg);
                    } catch (err) {
                        console.error('Fallback: Oops, unable to copy', err);
                    }
                    document.body.removeChild(textArea);
                }
                $scope.copiedText = copyText;
                $toastService.create("Successfully Copied");
            };

        $scope.isCopied = function (text){
            return  $scope.copiedText === text;
        }

        $scope.isUnderTenPercent = function (userInput){
            var maxDistance = userInput + userInput/10 ;
            var minDistance = userInput -  userInput/10;
            if($scope.googleMapDistance <= maxDistance && $scope.googleMapDistance >= minDistance){
                return true;
            }else {
                return false;
            }


        }

        $scope.setDistance = function (distance){
            $scope.govtSiteDistance = distance;
            if($scope.isUnderTenPercent(distance) == false){
                $scope.isValid = false;
            }else{
                  $scope.submitDistanceData();
            }

        }

            $scope.submitDistanceData = function(){
                $http({
                    url: apiJson.urls.skuMapping.updateUnitDistanceMapping,
                    method: "POST",
                    params : {
                        firstUnitId : $scope.distanceMapResponse.sourceUnitId,
                        firstMappingId : "null",
                        firstDistance: $scope.googleMapDistance,
                        secondUnitId : $scope.distanceMapResponse.destinationUnitId,
                        secondMappingId : "null",
                        secondDistance: $scope.googleMapDistance
                    }
                }).then(
                    function success(response) {
                        if(response.data){
                            $scope.isValid = true;
                            $toastService.create("Unit Distance is updated successfully");

                        }else{
                            $scope.isValid = false;
                            $toastService.create("Something went wrong.");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        $toastService.create("Something went wrong.");

                    });
            }
            $scope.goToLink  = function (){
                $window.open("https://einvoice1.gst.gov.in/Others/GetPinCodeDistance", "_blank")

            }

        $scope.closeModal = function () {
                Popeye.closeCurrentModal();
        };

    }
    ]
);

