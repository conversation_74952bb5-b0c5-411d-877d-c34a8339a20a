/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('grOrderActionCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','$toastService','previewModalService','$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService,previewModalService,$window) {

            $scope.init = function () {
                $scope.selecteGrOrderId = $rootScope.selecteGrOrderId;
                $scope.getGrOrderDetail();
                $scope.getImagesData();
                $scope.unitData = appUtil.getUnitData();
                $scope.goodsReceiveDetail = null;
                $scope.unitList = appUtil.getUnitList();
                $scope.showPreview = previewModalService.showPreview;
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.imageLinks =[];
            };

            $scope.backToGrOrderMgt = function(){
                $location.path("/menu/grOrderMgt");
            }
            $scope.onImageClick = function(url){
                console.log("image click ",url)
                $window.open(url, '_blank');
            }

            $scope.getGrOrderDetail = function(){
                if(!angular.isUndefined($scope.selecteGrOrderId) && $scope.selecteGrOrderId!=null){
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.goodReceived+"?grId="+$scope.selecteGrOrderId
                    }).then(function success(response) {
                        console.log(response.data);
                        $scope.goodsReceiveDetail = response.data;
                        $scope.generationUnitData = appUtil.findUnitDetail($scope.goodsReceiveDetail.generationUnitId.id);
                        $scope.calculateTotalPrice();
                        getTrOrderDetail($scope.goodsReceiveDetail.transferOrderId);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };
            
            $scope.getImagesData= function(){
                if(!angular.isUndefined($scope.selecteGrOrderId) && $scope.selecteGrOrderId!=null){
                    $http({
                        method: "POST",
                        data:$scope.selecteGrOrderId,
                        url: apiJson.urls.goodsReceivedManagement.getPorImageUrls,
                    }).then(function (response) {
                        $scope.imageLinks = response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

           function getTrOrderDetail(trOrderId){
                if(!angular.isUndefined(trOrderId) && trOrderId!=null){
                    $http({
                        method: "GET",
                        url: apiJson.urls.transferOrderManagement.transferOrder+"?transferOrderId="+trOrderId
                    }).then(function success(response) {
                        var transferOrderDetail = response.data;
                        $scope.goodsReceiveDetail.documentType = transferOrderDetail.type == 'TRANSFER' ? "STOCK TRANSFER NOTE (INTERNAL)" : "INVOICE";
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }

            $scope.calculateTotalPrice = function () {
                var total = 0;
                $scope.goodsReceiveDetail.goodsReceivedItems.forEach(function (item) {
                    total += (item.negotiatedUnitPrice*item.receivedQuantity);
                });
                $scope.totalPrice = total;
                $scope.totalPriceInWords = appUtil.inWords(Math.round(total));
            }

            $scope.cancelGoodsReceiveOrder = function(){
                if(!angular.isUndefined($scope.selecteGrOrderId) && $scope.selecteGrOrderId!=null){
                    $http({
                        method: "PUT",
                        url: apiJson.urls.goodsReceivedManagement.cancelGR,
                        data: $scope.selecteGrOrderId
                    }).then(function success(response) {
                        if(response.data!=null && response.data==true){
                            $toastService.create("Goods Receiving Cancelled Successfully!");
                            $scope.showPendingGr = true;
                            $scope.getPendingGRs();
                        }else{
                            $toastService.create("Something went wrong. Please try again!");
                        }
                        $scope.showPendingGr = true;
                        $scope.getPendingGRs();
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }

        }]);
