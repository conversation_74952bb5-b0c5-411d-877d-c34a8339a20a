/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('manageVendorDebitBalanceCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService',
        '$fileUploadService', '$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, $fileUploadService, $alertService) {

            $scope.init = function () {
                $scope.showNoPR = false;
                $scope.companyList =  appUtil.getCompanyList();
                $scope.selectedCompany =appUtil.getDefaultCompany();
                $scope.companyMap = appUtil.getCompanyMap();
            };
            
            $scope.changeCompany = function(){
            	console.log("selected company",$scope.selectedCompany);
            };

            $scope.downloadBalanceSheet = function () {
                $http({
                    url: apiJson.urls.vendorManagement.downloadDebitBalanceSheet,
                    method: 'GET',
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    },
                    params :{companyId : $scope.selectedCompany.id}
                }).success(function (data) {
                    if (appUtil.checkEmpty(data)) {
                        var fileName = "VENDOR_DEBIT_BALANCE_Sheet_" + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss") + ".xls";
                        var blob = new Blob([data], {
                            type: 'c'
                        }, fileName);
                        saveAs(blob, fileName);
                    } else {
                        $toastService.create("Could not fetch balance sheet. Please try again later");
                    }
                }).error(function (err) {
                    $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                });
            };

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload Balance Sheet", "Find", function (file) {
                    $scope.uploadDebitBalanceSheet(file);
                });
            };

            $scope.uploadDebitBalanceSheet = function (file) {
                $rootScope.showFullScreenLoader = true;
                $scope.showNoDB = false;
                var fd = new FormData(document.forms[0]);
                fd.append("file", file);
                $http({
                    url: apiJson.urls.vendorManagement.uploadDebitBalanceSheet,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $scope.dbs = response;
                    } else {
                        $scope.dbs = [];
                        $scope.showNoDB = true;
                        $toastService.create("Could not find any valid balances from sheet.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error in uploaded sheet.");
                    }
                });
            };

            $scope.submitBalances = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.vendorManagement.saveDebitBalances,
                    method: 'POST',
                    data: $scope.dbs,
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null && response == true) {
                        $toastService.create("Balances updated successfully.");
                        $scope.dbs = [];
                    } else {
                        $toastService.create("Could not update balances");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Could not update balances");
                    }
                });
            }
        }]
    );
