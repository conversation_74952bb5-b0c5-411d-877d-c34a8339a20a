

angular.module('scmApp')
    .controller('nonScannableAssetMappingCtrl', ['$http', '$scope', 'appUtil', 'metaDataService', '$toastService', 'productService',
        'apiJson', '$alertService', '$timeout',
        function ($http, $scope, appUtil, metaDataService,
                  $toastService, productService, apiJson, $alertService, $timeout) {
            $scope.init = function () {
                $scope.productList = [];
                $scope.nonScannableProducts = [];
                productService.getAllProducts(function (products) {
                    $scope.products = filterByCategory(products);
                    initializeGrid();
                });

                $scope.gridOptions = appUtil.getGridOptions($scope);
                $scope.gridOptions.rowTemplate = 'rowView.html';
                $scope.gridOptions.columnDefs = getColDefs();
            }

            function initializeGrid() {
                getCurrentMapping();
            }

            function getColDefs() {
                return [
                    {
                        field: 'productId',
                        name: 'productId',
                        enableCellEdit: false,
                        displayName: 'Product ID',
                        enableFiltering: true
                    }, {
                        field: 'productName',
                        name: 'productName',
                        displayName: 'Product Name',
                        enableFiltering: true
                    },{
                       field: 'subCategoryDefinition.name',
                        name : 'subCategoryDefinition',
                        displayName: 'Sub Category',
                        enableFiltering: true
                    }
                ]
            }

            $scope.submitData = function (){
                var selectedProducts = $scope.gridApi.selection.getSelectedRows();
                var productIds = [];
                for(var i = 0;i<selectedProducts.length;i++){
                    productIds.push(selectedProducts[i].productId);
                }
                $http({
                    url: apiJson.urls.assetManagement.saveNonScannableProducts,
                    method: "POST",
                    data : productIds
                }).then(function (response) {
                    if (response.data != undefined && response.data != null) {
                        $toastService.create("successfully Saved Mappings !!");
                    }
                }, function (response) {
                    console.log(response);
                    $toastService.create("Server Error");
                });
            }

            function preSelectData() {
                $scope.gridOptions.data = $scope.products;
                $timeout(function () {
                    $scope.gridApi.grid.modifyRows($scope.products);
                });
                $timeout(function () {
                    $scope.gridOptions.data.forEach(function (row, index) {
                        if ($scope.nonScannableProducts.indexOf(row.productId) != -1) {
                            $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                        } else {
                            $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                        }
                    });
                });

                $timeout(function () {
                    $scope.$apply();
                })
            }

            function getCurrentMapping() {
                $http({
                    url: apiJson.urls.assetManagement.getNonScannableProducts,
                    method: "GET"
                }).then(function (response) {
                    if (response.data != undefined && response.data != null) {
                        $scope.nonScannableProducts = response.data;
                    }
                    preSelectData();
                }, function (response) {
                    console.log(response);
                    $toastService.create("Server Error");
                });
            }

            function filterByCategory(products) {
                return products.filter(function (product) {
                    return product.categoryDefinition.id == 3;
                })
            }
        }]
    );


