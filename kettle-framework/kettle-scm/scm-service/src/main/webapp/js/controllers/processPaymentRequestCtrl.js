/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
        .controller('processPaymentRequestCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$alertService', '$fileUploadService', '$timeout','Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $alertService,
                  $fileUploadService, $timeout, Popeye) {

            $scope.init = function () {
                $scope.grFlag = false;
                $scope.srFlag = false;
                initCheckBoxModal();
                $scope.selectView = true;
                $scope.dateTypes = ['GR Creation Date', 'PR Creation Date', 'Payment Date']
                $scope.availableDate = angular.copy($scope.dateTypes);
                $scope.selectedDateType ='Payment Date';
                $scope.selectedPRStatus = null;
                $scope.itemDeviations = [];
                $scope.invoiceDeviations = [];
                $scope.invoiceRejections = [];
                $scope.queryReasons = [];
                $scope.expireSoPo = [];
                $scope.mandatoryDocuments = [];
                $scope.adhocPaymentReason = null;
                $scope.extraChargeTypes = ["FREIGHTAGE","TCS","ADJUSTMENT","FREIGHTAGE_WITH_TAX"];
                $scope.extraChargeTaxes = [{"type": "SGST", "taxAmount": null},
                    {"type": "CGST", "taxAmount": null},
                    {"type": "IGST", "taxAmount": null}
                ];
                $scope.freightageWithoutTax = 0;
                $scope.extraChargeType = null;

                 $scope.bccList = [];
                metaDataService.getSCMMetaData(function (metadata) {
                    $scope.prRequestTypes = angular.copy(metadata.paymentRequestTypes);
                    $scope.prRequestTypes.push({code :"MILK_BAKERY",
                    name :"MILK_BAKERY"});
                    $scope.prRequestType = $scope.prRequestTypes == null ? null : $scope.prRequestTypes[0];
                    $scope.prStatusList = [{id: null, name: ""}];
                    metadata.paymentRequestStatus.map(function (status, index) {
                        if(["CREATED","ACKNOWLEDGED","APPROVED","SENT_FOR_PAYMENT", "PAID"].indexOf(status) >= 0){
                            $scope.prStatusList.push({id: index, name: status});
                        }
                    });
                    metaDataService.getCompanyList(function(companies){
                        $scope.companyMap = {};
                        for(var i in companies){
                            $scope.companyMap[companies[i].id] = companies[i];
                        }
                    });
                    //$scope.prStatusList = $scope.prStatusList.concat(metadata.paymentRequestStatus);
                    metadata.paymentDeviations.map(function (deviation) {
                        if (deviation.deviationType == "DEVIATION") {
                            if (deviation.deviationLevel == "INVOICE_ITEM") {
                                $scope.itemDeviations.push(deviation);
                            } else if (deviation.deviationLevel == "INVOICE") {
                                $scope.invoiceDeviations.push(deviation);
                            }
                        } else if (deviation.deviationType == "REJECTION") {
                            $scope.invoiceRejections.push(deviation);
                            $scope.queryReasons.push(deviation);
                        }
                    })
                });
                $scope.selectedStatus = null;
                $scope.unitList = [{id: null, name: ""}];
                $scope.selectedUnit = null;
                $scope.vendorList = [];
                $scope.selectedVendor = null;
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.selectAllPrs = false;
                $scope.companyList = appUtil.getCompanyList();
                $scope.selectedCompany = appUtil.getDefaultCompany();
                $scope.getCompanyMappedUnits();
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.showValidFilingText = false;
                $scope.requestingUnitsForView = "";
                getAllVendors();
            };

            $scope.downloadDocumentById = function (id){
                metaDataService.downloadDocumentById(id);
            };

            $scope.setRequestType = function (type) {
                $scope.prRequestType = type;
                if ($scope.prRequestType.code == 'ADVANCE_PAYMENT') {
                    $scope.availableDate = ['PR Creation Date'];
                    $scope.changeSelectedDate($scope.availableDate[0]);
                } else {
                    $scope.availableDate = angular.copy($scope.dateTypes);
                    $scope.changeSelectedDate($scope.availableDate[0]);
                }
                $scope.prs = [];
                $scope.vendorPRs = [];
                $scope.showNoPR = true;
            };

            $scope.changeFreightageTax = function (tax) {
                 if (tax < 0) {
                     $toastService.create("Tax amount can not be NEGATIVE..!");
                     return false;
                 }
                  $scope.freightageWithoutTax = $scope.viewPr.paymentInvoice.extraCharges;
                  var totalTax = 0;
                  angular.forEach($scope.extraChargeTaxes, function (entry) {
                      totalTax= totalTax + (appUtil.isEmptyObject(entry.taxAmount) ? 0 : entry.taxAmount);
                  });
                  if ($scope.freightageWithoutTax < totalTax) {
                      $toastService.create("Tax can not be greater than the Extra charges ..!");
                      angular.forEach($scope.extraChargeTaxes, function (entry) {
                          entry.taxAmount = null;
                      });
                      return false;
                  } else {
                      $scope.freightageWithoutTax = $scope.freightageWithoutTax - totalTax;
                  }
            };

            $scope.getCompanyMappedUnits = function () {
                $scope.unitList = [{id: null, name: ""}];
                $scope.selectedUnit = null;
                appUtil.getUnitList().map(function (unit) {
                    if ($scope.selectedCompany.id == unit.companyId) {
                        $scope.unitList.push(unit);
                    }
                });
                if ($scope.unitList != null && $scope.unitList.length > 0) {
                    $scope.selectedUnit = $scope.unitList[0];
                }
                $scope.getBanksOfCompany();
                resetUnit();
                resetVendor();
                getAllVendors();
            };

            function initCheckBoxModal() {
                $scope.checkBoxModal = {};
                $scope.checkBoxModal.checkAllTdsRate = false;
                $scope.checkBoxModal.updatedTdsRate = null;
            }

            $scope.changeAllTdsRate = function () {
                if ($scope.checkBoxModal.checkAllTdsRate === true) {
                    if ($scope.checkBoxModal.updatedTdsRate == (null || undefined)) {
                        alert("Please enter Tds Rate");
                        $scope.checkBoxModal.checkAllTdsRate = false;
                        return false;
                    } else {
                        for (var i = 0; i < $scope.viewPr.paymentInvoice.paymentInvoiceItems.length; i++) {
                                $scope.viewPr.paymentInvoice.paymentInvoiceItems[i].tdsRate = $scope.checkBoxModal.updatedTdsRate;
                                $scope.changeProposedAmount($scope.viewPr.paymentInvoice.paymentInvoiceItems[i]);
                        }
                    }
                }
                else if ($scope.checkBoxModal.checkAllTdsRate === false) {
                    for (var i = 0; i < $scope.viewPr.paymentInvoice.paymentInvoiceItems.length; i++) {
                        $scope.checkBoxModal.updatedTdsRate=null;
                        $scope.viewPr.paymentInvoice.paymentInvoiceItems[i].tdsRate = $scope.checkBoxModal.updatedPrice;
                        $scope.changeProposedAmount($scope.viewPr.paymentInvoice.paymentInvoiceItems[i]);
                    }
                }
            };
            $scope.isMulitpleUnit= function(data){
                return data.split(",").length!==1
            }

            $scope.showBccData = function (data){
                $scope.bccList = data.split(",");
                var modal = document.getElementById("showBccModal");
                modal.style.display = "block";
                console.log("bcc data");
            }
            $scope.hideBccData = function (){
                $scope.bccList = [];
                var modal = document.getElementById("showBccModal");
                modal.style.display = "none";
                console.log("bcc data");
             }

            function getAllVendors() {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.vendorManagement.vendorsTrimmed,
                    method: 'GET'
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $scope.vendorList = [];
                    response.map(function (item) {
                        $scope.vendorList.push(item);
                    });
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Could not get vendors list.");
                    }
                });
            };

               $scope.loadMandatoryDoc = function () {
               $http({
                method : "POST",
                url : apiJson.urls.paymentRequestManagement.getMandatoryReqDoc+"?prId="+$scope.viewPr.paymentRequestId
               }).then(function success(response) {
               $scope.mandatoryDocuments=[];
                   for(var x in response.data){
                    $scope.mandatoryDocuments.push({name :response.data[x].key, code :response.data[x].value})
                    }
                   }), function error(response) {
                    console.log("error:" + response);
               };
             }

            function resetUnit() {
                $timeout(function () {
                    $('#unitListProcessPayment').val('').trigger('change');
                });
            }

            function resetVendor() {
                $timeout(function () {
                    $('#vendorListProcessPayment').val('').trigger('change');
                });
            }

            $scope.getBanksOfCompany = function () {
                $http({
                    url: apiJson.urls.paymentRequestManagement.banksOfCompany,
                    method: 'POST',
                    data: $scope.selectedCompany.id,
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $scope.bankList = response;
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Could not get banks of company.");
                    }
                });
            };

            $rootScope.unitDetail=null;
            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                $rootScope.unitDetail=unit;
                resetVendor();
                $scope.getMappedVendors();
            };

            $scope.changeSelectedDate = function(selectedType){
                $scope.selectedDateType = selectedType;
            }
            $scope.getMappedVendors = function () {
                if ($scope.selectedUnit != null && $scope.selectedUnit.id !== null) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.skuMapping.getVendorsForUnitTrimmed + "?unitId=" + $scope.selectedUnit.id
                    }).then(function success(response) {
                        $scope.vendorList = [];
                        response.data.map(function (item) {
                            //if (item.status === "ACTIVE") {
                            $scope.vendorList.push(item);
                            //}
                        });
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }

            };

            $scope.findPrs = function () {
                $scope.srType = null;
                $scope.checkBoxModal.checkAllTdsRate = false;
                $scope.checkBoxModal.updatedTdsRate = null;
                $scope.showNoPR = false;
                $scope.selectAllPrs = false;
                /*if ($scope.selectedUnit.id == null || $scope.selectedUnit.id == "") {
                    $toastService.create("Please select unit");
                } else */
                if ($scope.startDate == null || $scope.startDate == "") {
                    $toastService.create("Please select start date");
                } else if ($scope.endDate == null || $scope.endDate == "") {
                    $toastService.create("Please select end date");
                } else if ($scope.selectedDateType == null || $scope.selectedDateType == "") {
                    $toastService.create("Please select the date type...!");
                }
                else {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.paymentRequestManagement.getCompanyPaymentRequestForProcess,
                        params: {
                            type: $scope.prRequestType.code,
                            unitId: $scope.selectedUnit == null ? null : $scope.selectedUnit.id,
                            startDate: $scope.startDate,
                            endDate: appUtil.formatDate(appUtil.calculatedDate(0, $scope.endDate), 'yyyy-MM-dd'),
                            vendorId: $scope.selectedVendor == null ? null : $scope.selectedVendor.id,
                            prId: $scope.prId,
                            invoiceNumber: $scope.invoiceNumber,
                            status: $scope.selectedPRStatus == null ? null : $scope.selectedPRStatus.name,
                            companyId: $scope.selectedCompany.id,
                            dateType : $scope.selectedDateType,
                            paymentDate: $scope.paymentDate == null || $scope.paymentDate.length < 2 ? null : $scope.paymentDate
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.prs = response.data;
                            $scope.applySpecialFilters();
                        } else {
                            $scope.prs = [];
                            $scope.vendorPRs = [];
                            $scope.showNoPR = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.backToSelectView = function () {
                $scope.selectView = true;
                $scope.expireSoPo=[];
            };

            $scope.applySpecialFilters = function () {
                $scope.filteredPrs = [];
                if ($scope.blockFilter != true && $scope.validFilter != true) {
                    $scope.filteredPrs = angular.copy($scope.prs);
                } else {
                    if ($scope.blockFilter == true) {
                        $scope.filteredPrs = $scope.filteredPrs.concat($scope.prs.filter(function (a) {
                            return a.blocked == true;
                        }));
                    }
                    if ($scope.validFilter == true) {
                        $scope.filteredPrs = $scope.filteredPrs.concat($scope.prs.filter(function (a) {
                            return a.blocked != true;
                        }));
                    }
                }
                $scope.formatPRToVendorView();
            };

            $scope.formatPRToVendorView = function () {
                var vendorMap = {};
                $scope.filteredPrs.map(function (pr) {
                    var vendorObj = vendorMap[pr.vendorId.id];
                    if (vendorObj == null || vendorObj == undefined) {
                        vendorObj = {};
                        vendorObj.vendorId = pr.vendorId;
                        vendorObj.vendorDebitBalanceVOS = pr.vendorDebitBalanceVOS;
                        vendorObj.vendorDebitBalance = pr.vendorDebitBalance;
                        vendorObj.vendorCreditPeriod = pr.vendorCreditPeriod;
                    }
                    var prList = vendorObj.prList;
                    if (prList == null || prList == undefined) {
                        prList = [];
                    }
                    prList.push(pr);
                    vendorObj.prList = prList;
                    vendorMap[pr.vendorId.id] = vendorObj;
                });
                $scope.vendorPRs = Object.values(vendorMap);
                $scope.vendorPRs.map(function (vpr) {
                    var amount = 0;
                    vpr.prList.map(function (pr) {
                        if (['CANCELLED', 'REJECTED', 'PAID'].indexOf(pr.currentStatus) < 0 && pr.blocked != true) {
                            amount = amount + pr.paidAmount;
                        }
                    });
                    vpr.toBePaid = amount;
                });
            };

            $scope.getVendorPRSummary = function (item) {
                if(item.showPRList == true) {
                    item.showPRList = false;
                }else {
                    if(item.vendorSummary != null){
                        item.showPRList = true;
                    } else {
                        $http({
                            method: 'POST',
                            url: apiJson.urls.paymentRequestManagement.getVendorPRSummary,
                            data: item.vendorId.id
                        }).then(function (response) {
                            if (!appUtil.isEmptyObject(response.data)) {
                                item.vendorSummary = response.data;
                                $scope.calculateVendorSummary(item);
                            } else {
                                $toastService.create("Could not load vendor payment summary.");
                            }
                            item.showPRList = true;
                        }, function (error) {
                            console.log(error);
                            $toastService.create("Could not load vendor payment summary.");
                        });
                    }
                }
            };

            $scope.selectAllPaymentRequests = function (item) {
                /*$scope.selectAllPrs = !$scope.selectAllPrs;
                $scope.filteredPrs.map(function (pr) {
                    if (!pr.blocked) {
                        pr.selected = $scope.selectAllPrs;
                    }
                });*/
                item.prList.map(function (pr) {
                    pr.selected = item.selectAllPrs;
                });
                $scope.calculateVendorSummary(item);
            };

            $scope.selectPR = function (pr, item) {
                $scope.calculateVendorSummary(item);
            };

            $scope.calculateVendorSummary = function (item) {
                var count = 0;
                var sum = 0;
                item.prList.map(function (pr) {
                    if (pr.selected && pr.blocked != true && pr.currentStatus == "APPROVED") {
                        count++;
                        sum = sum + pr.paidAmount;
                    }
                });
                item.selectedForPayment = count;
                item.selectedForPaymentSum = sum;
                item.balance = item.vendorSummary.paymentSentAmount+item.vendorSummary.totalDebitBalance + sum;
            };

            $scope.setPaidAmount = function (amount) {
                if (!appUtil.isEmptyObject($scope.viewPr.vendorAdvancePayments)) {
                    var refundInitiatedAmount = 0;
                    for (var i = 0; i < $scope.viewPr.vendorAdvancePayments.length; i++) {
                        if ($scope.viewPr.vendorAdvancePayments[i].advanceStatus == "REFUND_INITIATED") {
                            refundInitiatedAmount = refundInitiatedAmount + $scope.viewPr.vendorAdvancePayments[i].availableAmount;
                        }
                    }
                    if (refundInitiatedAmount > 0) {
                        $alertService.alert("Can not add Debit Note", "Vendor Advance Related to this payment has refund amount of " + $scope.viewPr.advancePayment.availableAmount +
                            "<br><b>Please Contact Finance Team..!</b>", function () {
                        }, true);
                        $scope.viewPr.paidAmount = $scope.viewPr.proposedAmount;
                        return false;
                    } else {
                        $scope.viewPr.paidAmount = amount;
                    }
                } else {
                    $scope.viewPr.paidAmount = amount;
                }
            };

            $scope.viewPaymentRequest = function (pr, actionType) {
                $scope.enteredAmountPercent = null;
                $rootScope.showFullScreenLoader = true;
                $scope.advancePayment = null;
                $scope.extraChargeType = null;
                $scope.useAdvance = false;
                $scope.useByPercentage = false;
                $scope.advanceUsageType = null;
                $scope.extraChargeTaxes = [{"type": "SGST", "taxAmount": null},
                    {"type": "CGST", "taxAmount": null},
                    {"type": "IGST", "taxAmount": null}
                ];
                $scope.freightageWithoutTax = 0;
                $scope.selectView = false;
                $scope.actionType = actionType;
                $scope.uploadedDocData = null;
                $scope.selectedPaymentDate = null;
                $scope.uploadedDebitDoc = null;
                $scope.debitNoteDocId= null;
                $scope.viewPr = null;
                $scope.srType = null;
                $http({
                    url: apiJson.urls.paymentRequestManagement.paymentRequest,
                    method: 'GET',
                    params: {
                        paymentRequestId: pr.paymentRequestId
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.viewPr = response;
                        if (actionType == 'APPROVE' && $scope.viewPr.type != 'ADVANCE_PAYMENT') {
                            $scope.getProposedPaymentDates();
                        }
                        if ($scope.viewPr.vendorAdvancePayments != null) {
                            $scope.calculateAdvanceDetails($scope.viewPr.vendorAdvancePayments);
                        }
                        $scope.printBoth($scope.viewPr);
                        $scope.loadMandatoryDoc();
                        if (actionType == "ACKNOWLEDGED" || actionType == "APPROVE") {
                            if ($scope.viewPr.section206 != undefined && $scope.viewPr.section206 != null) {
                                $alertService.alert("RETURNS NOT FILED", "<b>TDS will be deducted at twice of rate prescribed in section or 5% whichever is higher</b>", null, false);
                            }
                        }
                    } else {
                        $toastService.create("Could not find payment request.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.calculateAdvanceDetails = function (advances) {
                $scope.amount = null;
                $scope.lastStatus = null;
                $scope.isAdjusted = null;
                $scope.selectedSoPo = null;
                $scope.refundSelectedDate = null;
                $scope.involvedAdvanceIds = [];
                var amount = 0;
                for (var i = 0; i < advances.length; i++) {
                    amount += advances[i].availableAmount;
                    if (advances[i].selectedSoPo != null) {
                        $scope.selectedSoPo = advances[i].selectedSoPo;
                    }
                    if (advances[i].lastPoSoStatus != null) {
                        $scope.lastStatus = advances[i].lastPoSoStatus;
                    }
                    if (advances[i].selectedSoPo != null) {
                        $scope.isAdjusted = true;
                    }
                    if (advances[i].refundDate != null) {
                        $scope.refundSelectedDate = advances[i].refundDate;
                    }
                    $scope.involvedAdvanceIds.push(advances[i].advancePaymentId);
                }
                $scope.amount = amount;
            };

            $scope.setUseByPercentage = function () {
                $scope.useByPercentage = !$scope.useByPercentage;
                $scope.setEnteredAmountPercent(null);
            };

            $scope.openAdvancePaymentModal = function () {
                $scope.tempAdvanceAmount = null;
                $scope.tempAdvanceType = null;
                if ($scope.advanceUsageType != null) {
                    $scope.tempAdvanceType = angular.copy($scope.advanceUsageType);
                }
                if ($scope.viewPr.advanceAmount != null) {
                    $scope.tempAdvanceAmount = angular.copy($scope.viewPr.advanceAmount);
                }
            };

            $scope.cancelAdvancePayment = function () {
                $scope.advanceUsageType =  $scope.tempAdvanceType;
                $('#advanceUsageType').val($scope.viewPr.advanceUsageType);
                $scope.viewPr.advanceAmount = $scope.tempAdvanceAmount;
                $('#advance').val($scope.viewPr.advanceAmount);
            };

            $scope.setAdvanceUsageType = function (type) {
                $scope.advanceUsageType = type;
                $scope.viewPr.percentage = null;
                $('#percentage').val(null);
                if (type == "Complete") {
                    $scope.setEnteredAmountPercent(0);
                } else {
                    $scope.setEnteredAmountPercent(null);
                }
            };

            $scope.setEnteredPercentage = function (percent) {
                if (appUtil.isFloat(percent)) {
                    $toastService.create("No decimal Value allowed..!");
                    $scope.viewPr.percentage = null;
                    $('#percentage').val(null);
                    return;
                }
                if (percent < 0) {
                    $toastService.create("Percentage Should be Greater than 0 ..!");
                    $scope.viewPr.percentage = null;
                    $('#percentage').val(null);
                    return;
                }
                if (percent > 100) {
                    $toastService.create("Percentage Should not be Greater than 100 ..!");
                    $scope.viewPr.percentage = null;
                    $('#percentage').val(null);
                    $scope.viewPr.advanceAmount = null;
                    $('#advance').val(null);
                    return;
                }
                $scope.viewPr.percentage = percent;
                var amount = (percent / 100 ) * $scope.advancePayment.availableAmount;
                $scope.setEnteredAmountPercent(parseFloat(amount.toFixed(6)));
            };

            $scope.setEnteredAmountPercent = function (amount) {
                if (appUtil.isFloat(amount) && $scope.advanceUsageType != "Percentage") {
                    $toastService.create("No decimal Value allowed..!");
                    $scope.viewPr.advanceAmount = null;
                    $('#advance').val(null);
                    return;
                }
                if (amount != null) {
                    if (amount < 0) {
                        $toastService.create("Amount Should be Greater than 0 ..!");
                        $scope.viewPr.advanceAmount = null;
                        $('#advance').val(null);
                        return;
                    }
                    if ($scope.advanceUsageType == "Amount" || $scope.advanceUsageType == "Percentage") {
                        $scope.viewPr.advanceAmount = parseFloat(amount.toFixed(6));
                    }  else {
                        var totalAmount = $scope.viewPr.proposedAmount;
                        var availableAmount = $scope.advancePayment.availableAmount;
                        if (availableAmount >= totalAmount) {
                            $scope.viewPr.advanceAmount = parseFloat(totalAmount.toFixed(6));
                        } else {
                            $scope.viewPr.advanceAmount = parseFloat(availableAmount.toFixed(6));
                        }
                        $('#advance').val($scope.viewPr.advanceAmount);
                    }
                    if ($scope.viewPr.advanceAmount > $scope.advancePayment.availableAmount) {
                        $toastService.create("Please Check Amount is Exceeding the available amount ..!");
                        $scope.viewPr.advanceAmount = null;
                        $('#advance').val(null);
                        if ($scope.advanceUsageType == "Percentage") {
                            $scope.viewPr.percentage = null;
                            $('#percentage').val(null);
                        }
                    }
                    if ($scope.viewPr.advanceAmount > $scope.viewPr.paidAmount) {
                        $toastService.create("Please Check Amount is Exceeding the Required Amount ..!");
                        $scope.viewPr.advanceAmount = null;
                        $('#advance').val(null);
                        if ($scope.advanceUsageType == "Percentage") {
                            $scope.viewPr.percentage = null;
                            $('#percentage').val(null);
                        }
                    }
                } else {
                    $scope.viewPr.advanceAmount = amount;
                    $('#advance').val(null);
                }
            };

            $scope.clearUseAdvance = function () {
                $scope.advanceUsageType =  null;
                $('#advanceUsageType').val(null);
                $scope.viewPr.advanceAmount = null;
                $('#advance').val(null);
                $scope.useAdvance = false;
                $scope.viewPr.paidAmount = $scope.viewPr.proposedAmount;
            };

            $scope.submitAdvance = function () {
                if ($scope.viewPr.advanceAmount != undefined && $scope.viewPr.advanceAmount != null) {
                    if ($scope.viewPr.advanceAmount <= 0) {
                        $toastService.create("Amount Should be Greater than 0 ..!");
                        return;
                    }
                    $scope.useAdvance = true;
                    $scope.viewPr.paidAmount = getPaidAmountAfterAdvance();
                }
            };

            $scope.changeProposedAmount = function () {
                var items = $scope.viewPr.paymentInvoice.paymentInvoiceItems;
                $scope.viewPr.proposedAmount = parseFloat(calculateProposedAmount(items)).toFixed(2);
                $timeout(function(){
                    $scope.viewPr.paidAmount = parseFloat($scope.viewPr.proposedAmount).toFixed(2);
                });
                $scope.viewPr.paymentInvoice.paymentAmount = parseFloat($scope.viewPr.proposedAmount).toFixed(2);
                $scope.viewPr.paymentInvoice.calculatedInvoiceAmount = parseFloat($scope.viewPr.proposedAmount).toFixed(2);
            };

            function calculateProposedAmount(items) {
                var amount = 0;
                var totalTds = 0;
                for(var i in items){
                    var item = items[i];
                    var tdsValue= parseFloat(getValueOf(item.totalPrice, item.tdsRate));
                    var itemCost = parseFloat(item.totalPrice);
                    var totalTax = parseFloat(calculateTax(itemCost, item.taxes)).toFixed(2);
                    var itemAmount = parseFloat(itemCost) + parseFloat(totalTax);
                    amount = parseFloat(amount) + parseFloat(itemAmount);
                    totalTds = parseFloat(totalTds) + parseFloat(tdsValue);
                }
                amount = parseFloat(amount) - parseFloat(totalTds);
                return amount;
            }

            function calculateTax(cost, taxes) {
                cost = parseFloat(cost);
                var totalTax = 0;
                for(var i in taxes){
                    var tax = taxes[i];
                    tax.taxValue = parseFloat(getValueOf(cost,tax.taxPercentage));
                    totalTax = parseFloat(totalTax) + parseFloat(tax.taxValue);
                }
                return totalTax;
            }

            function getValueOf(totalCost, tdsRate) {
                var tds = appUtil.isEmptyObject(tdsRate) ? 0 : parseFloat(tdsRate);
                if(tds > 0){
                    return parseFloat(parseFloat(totalCost) * (tds/100)).toFixed(2);
                }else{
                    return 0;
                }
            }

            $scope.setAvailableDeviations = function (item, type) {
                $scope.availableDevs = [];
                $scope.selectedItemForDev = item;
                $scope.selectedItemForDevType = type;
                var devList;
                if (type === "INVOICE") {
                    devList = $scope.invoiceDeviations;
                } else {
                    devList = $scope.itemDeviations;
                }
                devList.map(function (dev) {
                    var found = false;
                    item.deviations.map(function (dev1) {
                        if (dev.paymentDeviationId == dev1.paymentDeviation.paymentDeviationId) {
                            found = true;
                        }
                    });
                    if (!found) {
                        dev.checked = false;
                        dev.deviationRemark = null;
                        $scope.availableDevs.push({data: dev});
                    }
                });
            };

            $scope.addDeviations = function (item) {
                var unchecked = [];
                $scope.availableDevs.map(function (dev) {
                    if (dev.checked) {
                        if ($scope.actionType === "UPDATE_INVOICE") {
                            item.deviations.push({
                                mappingId: null,
                                paymentDeviation: dev.data,
                                deviationRemark: dev.remark,
                                currentStatus: "CREATED",
                                createdBy: appUtil.createGeneratedBy(),
                            });
                        } else {
                            item.deviations.push({
                                mappingId: null,
                                paymentDeviation: dev.data,
                                deviationRemark: dev.remark,
                                currentStatus: "ACCEPTED",
                                createdBy: appUtil.createGeneratedBy(),
                                acceptedBy: appUtil.createGeneratedBy(),
                                addType: "NEW"
                            });
                        }
                    } else {
                        unchecked.push(dev);
                    }
                });
                $scope.availableDevs = unchecked;
            };

            $scope.deleteDeviation = function (list, index) {
                var items = list.splice(index, 1);
                $scope.availableDevs.push({data: items[0].paymentDeviation});
            };

//            $scope.setAvailableRejections = function () {
//                $scope.availableRejections = [];
//                $scope.invoiceRejections.map(function (dev) {
//                    var found = false;
//                    $scope.viewPr.paymentInvoice.rejections.map(function (dev1) {
//                        if (dev.paymentDeviationId == dev1.paymentDeviation.paymentDeviationId) {
//                            found = true;
//                        }
//                    });
//                    if (!found) {
//                        dev.checked = false;
//                        $scope.availableRejections.push({data: dev});
//                    }
//                });
//            };

//            $scope.addRejections = function () {
//                $scope.availableRejections.map(function (dev) {
//                    if (dev.checked) {
//                        if(dev.data.paymentDeviationId===34 && dev.data.deviationRemark==undefined ) {
//                            $toastService.create("Select Deviation.");
////                            $('#addRejectionModal').modal('show');
//                            $modal.open('#addRejectionModal');
////                            return;
//                        }
//                        $scope.viewPr.paymentInvoice.rejections.push(   {
//                            mappingId: null,
//                            paymentDeviation: dev.data,
//                            deviationRemark: dev.data.deviationRemark==undefined ? null : dev.data.deviationRemark,
//                            currentStatus: "ACCEPTED",
//                            createdBy: appUtil.createGeneratedBy(),
//                            acceptedBy: appUtil.createGeneratedBy()
//                        });
//                    }
//                });
//            };
//
//            $scope.deleteRejection = function (list, index) {
//                var items = list.splice(index, 1);
//                $scope.availableRejections.push({data: items[0].paymentDeviation});
//            };
//
//            $scope.addToDeviationRemark=function(availableRejections, mandatoryDocuments){
//                if(availableRejections.checked && mandatoryDocuments.checked){
//                    if(availableRejections.data.deviationRemark!==undefined) {
//                        availableRejections.data.deviationRemark= availableRejections.data.deviationRemark +","+mandatoryDocuments.name;
//                    } else {
//                        availableRejections.data.deviationRemark = mandatoryDocuments.name;
//                    }
//                } else {
//                    var temp = availableRejections.data.deviationRemark.split(",");
//                    availableRejections.data.deviationRemark = null;
//                    for(var remark in temp) {
//                        if (mandatoryDocuments.name != temp[remark]) {
//                            if (availableRejections.data.deviationRemark !=null) {
//                                availableRejections.data.deviationRemark= availableRejections.data.deviationRemark +","+temp[remark];
//                            } else {
//                                availableRejections.data.deviationRemark = temp[remark];
//                            }
//                        }
//                    }
//                }
//            }
            $scope.deviationAction = function (item, type) {
                if (type == "APPROVE") {
                    item.currentStatus = "ACCEPTED";
                    item.acceptedBy = appUtil.createGeneratedBy();
                } else if (type == "REJECT") {
                    item.currentStatus = "REJECTED";
                    item.rejectedBy = appUtil.createGeneratedBy();
                    item.drejectedBy = appUtil.createGeneratedBy();
                } else if (type == "REMOVE") {
                    item.currentStatus = "REMOVED";
                    item.removedBy = appUtil.createGeneratedBy();
                }
            };

            $scope.debitNoteAdvanceCheck = function () {
                if ($scope.useAdvance) {
                    if ($scope.viewPr.proposedAmount != $scope.viewPr.paidAmount) {
                        if ($scope.viewPr.debitNote == null) {
                            $toastService.create("Please Upload debit note or Clear the use of advance..!");
                            return false;
                        }
                    }
                } else {
                    if ($scope.viewPr.proposedAmount != $scope.viewPr.paidAmount) {
                        if ($scope.viewPr.debitNote == null) {
                            $toastService.create("Please Upload debit note or Reset the paid amount ..!");
                            return false;
                        }
                    }
                }
                return true;
            };

            $scope.changeStatus = function (pr, status) {
                $rootScope.showFullScreenLoader = true;
                if ($scope.debitNoteAdvanceCheck()) {
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentRequestStatusChange,
                        method: 'POST',
                        data: {
                            paymentRequestId: pr.paymentRequestId,
                            updatedBy: appUtil.createGeneratedBy().id,
                            newStatus: status,
                            paymentType: $scope.prRequestType.code,
                            paymentRequestQueries: appUtil.isEmptyObject(pr.paymentRequestQueries) ? [] : pr.paymentRequestQueries
                        }
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null && response.updated == true) {
                            pr.currentStatus = status;
                            $toastService.create("Status updated successfully.");
                            $scope.backToSelectView();
                            $scope.findPrs();
                        } else {
                            $toastService.create("Could not update payment request status.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                        } else {
                            $toastService.create("Status update failed");
                        }
                    });
                }
            };

            $scope.setExtraChargeType = function (type) {
                $scope.extraChargeType = type;
                if (type === 'FREIGHTAGE_WITH_TAX') {
                    $scope.freightageWithoutTax = $scope.viewPr.paymentInvoice.extraCharges;
                    angular.forEach($scope.extraChargeTaxes, function (entry) {
                        entry.taxAmount = null;
                    });
                }
            };

            function getExtraChargesGST(taxType) {
                for (var i in $scope.extraChargeTaxes) {
                    if (taxType === $scope.extraChargeTaxes[i].type) {
                        return $scope.extraChargeTaxes[i].taxAmount;
                    }
                }
            }

            $scope.approvePaymentRequest = function () {
                if ($scope.viewPr.type  != 'ADVANCE_PAYMENT' ) {
                    if ($scope.viewPr.paymentInvoice.extraCharges > 0) {
                        if ($scope.extraChargeType == null) {
                            $toastService.create("Please Select the Extra Charges Type..!");
                            return false;
                        }
                        if ($scope.extraChargeType === 'FREIGHTAGE_WITH_TAX') {
                            if ($scope.viewPr.paymentInvoice.extraCharges == $scope.freightageWithoutTax) {
                                $toastService.create("Please Enter the Tax as you selected FREIGHTAGE_WITH_TAX .. Other wise change it to FREIGHTAGE");
                                return false;
                            }
                        }
                    }
                }

                if($scope.viewPr.type=='SERVICE_RECEIVED' && $scope.viewPr.tds!=null) {
                    if ($scope.viewPr.tds == true && $scope.checkBoxModal.checkAllTdsRate == false) {
                        $toastService.create("Please enter tds rate.");
                        return;
                    }
                }
                $scope.viewPr.updatedBy = appUtil.createGeneratedBy();
                $scope.viewPr.extraChargesType = $scope.extraChargeType == 'FREIGHTAGE_WITH_TAX' ? 'FREIGHTAGE' : $scope.extraChargeType;
                $scope.viewPr.extraChargesSgst = $scope.extraChargeType == 'FREIGHTAGE_WITH_TAX' ? getExtraChargesGST("SGST") : null;
                $scope.viewPr.extraChargesCgst = $scope.extraChargeType == 'FREIGHTAGE_WITH_TAX' ? getExtraChargesGST("CGST") : null;
                $scope.viewPr.extraChargesIgst = $scope.extraChargeType == 'FREIGHTAGE_WITH_TAX' ? getExtraChargesGST("IGST") : null;
                $scope.viewPr.extraChargesWithOutTax = $scope.extraChargeType == 'FREIGHTAGE_WITH_TAX' ? $scope.freightageWithoutTax : null;
                $scope.viewPr.paymentCycle = $scope.selectedPaymentDate;
                $scope.viewPr.paymentInvoice.paymentAmount = $scope.viewPr.paidAmount;
                var devs = $scope.viewPr.paymentInvoice.deviations;
                $scope.viewPr.paymentInvoice.paymentInvoiceItems.map(function (item) {
                    if (item.deviations.length > 0) {
                        devs.concat(item.deviations);
                    }
                });
                var found = false;
                devs.map(function (dev) {
                    if (!found && dev.currentStatus === "CREATED") {
                        found = true;
                    }
                });
                if (found) {
                    $toastService.create("Please take action on all invoice and item deviations.");
                }/* else if ($scope.viewPr.proposedAmount < $scope.viewPr.paidAmount) {
                    $toastService.create("Paid amount should not be greater than proposed amount.");
                }*/ else if ($scope.viewPr.paymentDate == null) {
                    $toastService.create("Please select payment date.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    if ($scope.debitNoteAdvanceCheck()) {
                        $http({
                            url: apiJson.urls.paymentRequestManagement.approve,
                            method: 'POST',
                            data: $scope.viewPr
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response != null && response == true) {
                                $toastService.create("Payment request approved successfully.");
                                $scope.backToSelectView();
                                $scope.findPrs();
                            } else {
                                $toastService.create("Could not update payment request status.");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response.errorMsg != null) {
                                $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                            } else {
                                $toastService.create("Failed to approve request.");
                            }
                        });
                    }
                }
            };

            $scope.rejectPaymentRequest = function () {
                if ($scope.viewPr.paymentInvoice.rejections.length == 0) {
                    $toastService.create("Please add reason for rejection.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $scope.viewPr.updatedBy = appUtil.createGeneratedBy();
                    $http({
                        url: apiJson.urls.paymentRequestManagement.reject,
                        method: 'POST',
                        data: $scope.viewPr
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null && response == true) {
                            $toastService.create("Payment request rejected successfully.");
                            $scope.backToSelectView();
                            $scope.findPrs();
                        } else {
                            $toastService.create("Could not update payment request status.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                        } else {
                            $toastService.create("Failed to reject request.");
                        }
                    });
                }
            };

            $scope.addDebitNoteModal = function (pr) {
                var addDebitModal = Popeye.openModal({
                    templateUrl: "addDebitNoteModal.html",
                    controller: "addDebitNoteModalCtrl",
                    resolve: {
                        viewPr: function(){
                            return $scope.viewPr;
                        },debitNoteDocId: function(){
                            return $scope.debitNoteDocId;
                        },useAdvance: function(){
                            return $scope.useAdvance;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });

                addDebitModal.closed.then(function (result) {
                    if (!appUtil.isEmptyObject(result)) {
                        if( result.resetAdvance != undefined && result.resetAdvance != null && result.resetAdvance) {
                            $scope.resetAdvance();
                        }
                    }
                });
            };

            $scope.viewSRs = function (pr) {
                var viewDetailModal = Popeye.openModal({
                    templateUrl: "viewSRsDetail.html",
                    controller: "viewSRsDetailCtrl",
                    resolve: {
                        pr: function(){
                            return pr;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });
            };

            $scope.viewMultipleSoPos = function (list) {
                var viewPoSoModal = Popeye.openModal({
                    templateUrl: "viewPoSo.html",
                    controller: "viewPoSoCtrl",
                    resolve: {
                        poSo: function(){
                            return list;
                        },
                        typeReceived: function () {
                            return $scope.viewPr;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });
            };

            /*$scope.updatePaymentRequest = function () {
                if ($scope.viewPr.paymentInvoice.invoiceNumber == null) {
                    $toastService.create("Please provide invoice number.");
                } else if ($scope.viewPr.paymentInvoice.invoiceDate == null) {
                    $toastService.create("Please provide invoice date.");
                } else if ($scope.uploadedDocData == null) {
                    $toastService.create("Please attach invoice document.");
                } else {
                    $scope.viewPr.deviationCount = $scope.viewPr.paymentInvoice.deviations.length;
                    $scope.viewPr.paymentInvoice.paymentInvoiceItems.map(function (item) {
                        $scope.viewPr.deviationCount += item.deviations.length;
                    });
                    $scope.viewPr.paymentInvoice.invoiceDocumentHandle = $scope.uploadedDocData.documentId;
                    $scope.viewPr.updatedBy = appUtil.createGeneratedBy();
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentRequestUpdateInvoice,
                        method: 'POST',
                        data: $scope.viewPr,
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null) {
                            $alertService.alert("Payment Request updated successfully",
                                "Payment request updated successfully with request id " + response.paymentRequestId, function () {
                                    $scope.backToSelectView();
                                    $scope.findPrs();
                                })
                            //$toastService.create("Payment Request creation successful.");
                        } else {
                            $toastService.create("Payment Request updation failed.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $alertService.alert("Payment Request updation failed", response.errorMsg, function () {
                        }, true)
                    });
                }
            };*/

            $scope.resetAdvance = function () {
                $scope.clearUseAdvance();
                metaDataService.getVendorAdvancePayment($scope.viewPr.vendorId.id, "CREATED", function (advance) {
                    $scope.advancePayment = advance;
                });
            };

            $scope.getProposedPaymentDates = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.paymentRequestManagement.paymentDates,
                    method: 'POST',
                    data: $scope.viewPr.paymentRequestId
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.proposedPaymentDates = response;
                        if ($scope.selectedPaymentDate == null && response.length > 0) {
                            $scope.selectedPaymentDate = response[0];
                        }
                    } else {
                        $toastService.create("Could not get payment dates.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error getting payment dates.");
                    }
                });
            };

            $scope.loadInvoiceDoc = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.paymentRequestManagement.getInvoice,
                    method: 'POST',
                    data: $scope.viewPr.paymentInvoice.invoiceDocumentHandle
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Could not get document detail.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error getting document detail.");
                    }
                });
            };

            $scope.changeBank = function () {
                $scope.bankName = $scope.selectedBank.code;
                if($scope.paymentDetail != null){
                    $scope.paymentDetail.debitAccount = $scope.selectedBank.accountNumber;
                    $scope.paymentDetail.paymentType = $scope.selectedPaymentType == null ? null : $scope.selectedPaymentType.name;
                    $scope.paymentDetail.debitBank = $scope.selectedBank.name;
                    console.log($scope.selectedBank);
                    //console.log($scope.bankList);

                }
            };


            $scope.exportCsv =function(viewPr){
                console.log(viewPr);
                var PrType=viewPr.type;
                var invoiceData=viewPr.paymentInvoice.paymentInvoiceItems;
                console.log(invoiceData);
                $scope.downloadData1(invoiceData,PrType);
            };

            $scope.downloadData1 =function(data,PrType) {
                var filename = 'data'
                var arrHeader =[];
                if(PrType=='SERVICE_RECEIVED') {
                    arrHeader=["skuId", "skuName", "businessCostCenterId","bccCode", "businessCostCenterName", "category", "budgetCategory", "costDescription", "packagingName", "quantity", "totalPrice", "taxes", "totalTax", "totalAmount"];
                }
                else
                {
                    arrHeader=["skuId", "skuName", "category", "subCategory", "packagingName", "quantity", "totalPrice", "taxes", "totalTax", "totalAmount"];

                }
                var csvData = $scope.ConvertToCSV(data, arrHeader,PrType);
                var blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });
                var dwldLink = document.createElement("a");
                var url = URL.createObjectURL(blob);
                var isSafariBrowser = navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1;
                if (isSafariBrowser) {  //if Safari open in new window to save file with random filename.
                    dwldLink.setAttribute("target", "_blank");
                }
                dwldLink.setAttribute("href", url);
                dwldLink.setAttribute("download", "PaymentGridDataSheet.csv");
                dwldLink.style.visibility = "hidden";
                document.body.appendChild(dwldLink);
                dwldLink.click();
                document.body.removeChild(dwldLink);
            }
            $scope.ConvertToCSV=function(objArray, headerList,PrType) {

                var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
                var str = '';
                var row = 'S.No,';

               var newHeaders =[];
                if(PrType=='SERVICE_RECEIVED')
                {
                    newHeaders = ["Sku Id", "Sku Name","Bcc Id","Unit Id","Unit Name","Category","BudgetCategory","CostDescription","Packaging Name","Quantity", "Total Price","Tax","Total Tax","Total Amount"];
                }
                else
                {
                    newHeaders = ["Sku Id", "Sku Name","Category","Sub Category","Packaging Name","Quantity", "Total Price","Tax","Total Tax","Total Amount"];

                }
                for (var head in newHeaders) {
                    row += newHeaders[head] + ',';
                }
                console.log(array);
                row = row.slice(0, -1);
                str += row + '\r\n';
                for (var i = 0; i < array.length; i++) {
                    var line = (i + 1) + '';
                    for (var index in headerList) {
                        var head = headerList[index];
                        if(head!="taxes") {
                            line += ',' + array[i][head];
                        }
                        else
                        {
                            var tax='';
                            for(var index1 in array[i][head])
                            {
                                tax += array[i][head][index1]['taxType']+"@"+array[i][head][index1]['taxPercentage']+"%";
                            }
                            line += ','+tax;
                        }
                    }

                    str += line + '\r\n';
                }
                return str;
            };


            $scope.downloadPaymentSheet = function () {
                var error = false;
                var reqObj = {
                    paymentRequestIds: [],
                    bankName: $scope.bankName,
                    updatedBy: appUtil.createGeneratedBy().id,
                    companyId: $scope.selectedCompany.id
                };
                var prsSentInEmail = [];
                var prsAutoSettled = [];
                $scope.filteredPrs.map(function (pr) {
                    if (pr.selected === true) {
                        if (!error) {
                            if (pr.currentStatus !== "APPROVED" || pr.blocked == true) {
                                $toastService.create("Payment request id: " + pr.paymentRequestId + " is not authorized for payment.");
                                error = true;
                                prsSentInEmail = [];
                                prsAutoSettled = [];
                            } else {
                                reqObj.paymentRequestIds.push(pr.paymentRequestId);
                                if (pr.paidAmount != null && pr.paidAmount == 0 && pr.vendorAdvancePayments != null && pr.vendorAdvancePayments.length > 0) {
                                    prsAutoSettled.push(pr.paymentRequestId);
                                } else {
                                    prsSentInEmail.push(pr.paymentRequestId);
                                }
                            }
                        }
                    }
                });
                if (reqObj.paymentRequestIds.length == 0) {
                    $toastService.create("No valid payment requests selected for payment initiation.");
                } else if (!error) {
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentSheet,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: reqObj
                    }).success(function (data) {
                        var msg = "";
                        if (prsSentInEmail.length > 0) {
                            msg += "<b> PR's Sent In Email " + "</b> : " + prsSentInEmail.join(",") + "<br>";
                        }
                        if (prsAutoSettled.length > 0) {
                            msg += " <b> PR's Auto Settled " + "</b> : " + prsAutoSettled.join(",") + "<br>";
                        }
                        $alertService.alert("Sent For Payment.", msg, function () {
                            $scope.findPrs();
                            $scope.selectAllPrs = false;
                        }, false)
                    }).error(function (err) {
                        $toastService.create("Error Occurred while creating payment sheet. Please try again later..!");
                    });
                }
            };

            $scope.downloadPaymentSheetAdhoc = function () {
                var error = false;
                var reqObj = {
                    paymentRequestIds: $scope.adhocPrIds.split(","),
                    bankName: $scope.bankNameAdhoc,
                    updatedBy: appUtil.createGeneratedBy().id,
                    companyId: $scope.selectedCompany.id
                };
                if (reqObj.paymentRequestIds.length == 0) {
                    $toastService.create("No valid payment requests selected for payment initiation.");
                } else if (!error) {
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentSheetAdhoc,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: reqObj,
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    }).success(function (data) {
                        if (appUtil.checkEmpty(data)) {
                            var fileName = "PAYMENT_REQUEST_" + $scope.bankNameAdhoc + "_Sheet_" + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss") + ".xls";
                            var blob = new Blob([data], {
                                type: 'c'
                            }, fileName);
                            saveAs(blob, fileName);
                            $scope.findPrs();
                            $scope.selectAllPrs = false;
                        } else {
                            $toastService.create("Could not fetch payment sheet. Please try again later");
                        }
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                    });
                }
            };

            $scope.blockPaymentRequests = function (type) {
                var error = false;
                var reqObj = {
                    paymentRequestIds: [],
                    bankName: $scope.bankName,
                    updatedBy: appUtil.createGeneratedBy().id,
                    reason: $scope.actionReason
                };
                $scope.filteredPrs.map(function (pr) {
                    if (pr.selected === true) {
                        if (!error) {
                            if (pr.blocked == type) {
                                $toastService.create("Payment request id: " + pr.paymentRequestId + " is " +
                                    (type ? "already" : "not" + " blocked."));
                                error = true;
                            } else {
                                reqObj.paymentRequestIds.push(pr.paymentRequestId)
                            }
                        }
                    }
                });
                if (reqObj.paymentRequestIds.length == 0) {
                    $toastService.create("No valid payment requests selected for " + (type ? "blocking" : "unblocking"));
                } else if (!error) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: type ? apiJson.urls.paymentRequestManagement.blockPaymentRequests :
                            apiJson.urls.paymentRequestManagement.unblockPaymentRequests,
                        method: 'POST',
                        data: reqObj
                    }).success(function (data) {
                        if (appUtil.checkEmpty(data)) {
                            $scope.findPrs();
                            $scope.selectAllPrs = false;
                        } else {
                            $toastService.create("Could not update payments. Please try again later");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.resetPaymentDetail = function (pr) {
                $scope.paymentDetail = {
                    vendorId: pr.vendorId.id,
                    vendorName: pr.vendorId.name,
                    beneficiaryAccountNumber: null,
                    beneficiaryIfscCode: null,
                    debitAccount: $scope.selectedBank==null ? null : $scope.selectedBank.accountNumber,
                    debitBank: null,
                    paymentType: null,
                    paidAmount: null,
                    paymentDate: null,
                    remarks: null,
                    proposedAmount: null,
                    createdBy: appUtil.createGeneratedBy(),
                    utrNumber: null,
                    paymentRequests: []
                };
                $scope.selectedPaymentType = null;
                $scope.paymentTypeList = [{id: 0, name: "NEFT"}, {id: 1, name: "IFT"}, {id: 2, name: "RTGS"}, {
                    id: 3,
                    name: "IMPS"
                }];
                $scope.viewPr = pr;

                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.vendorManagement.getAccount + "?vendorId=" + pr.vendorId.id,
                    method: 'GET'
                }).success(function (data) {
                    if (data != null) {
                        $scope.paymentDetail.beneficiaryAccountNumber = data.accountNumber;
                        $scope.paymentDetail.beneficiaryIfscCode = data.ifsc;
                    } else {
                        $toastService.create("Could not fetch vendor account details.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }).error(function (err) {
                    $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.settlePaymentRequestSingle = function () {
                console.log("prs are : ",$scope.viewPr);
                $scope.paymentDetail.vendorPaymentDate = $scope.viewPr.vendorPaymentDate;
                console.log("payment detail is  are : ",$scope.paymentDetail);
                $scope.paymentDetail.paymentType = $scope.selectedPaymentType == null ? null : $scope.selectedPaymentType.name;
                if ($scope.paymentDetail.vendorId == null) {
                    $toastService.create("No vendor selected.");
                } else if ($scope.paymentDetail.beneficiaryAccountNumber == null) {
                    $toastService.create("Fill beneficiary account number.");
                } else if ($scope.paymentDetail.beneficiaryIfscCode == null) {
                    $toastService.create("Fill beneficiary IFSC.");
                } else if ($scope.paymentDetail.debitAccount == null) {
                    $toastService.create("Fill debit account number.");
                } else if ($scope.paymentDetail.debitBank == null) {
                    $toastService.create("Select debit bank.");
                } else if ($scope.paymentDetail.paymentType == null) {
                    $toastService.create("Select Payment type.");
                } else if ($scope.paymentDetail.paidAmount == null) {
                    $toastService.create("Fill paid amount.");
                } else if ($scope.paymentDetail.paymentDate == null) {
                    $toastService.create("Fill payment date.");
                } else if ($scope.paymentDetail.utrNumber == null) {
                    $toastService.create("Fill UTR number.");
                } else if ($scope.viewPr.blocked == true || $scope.viewPr.currentStatus != "SENT_FOR_PAYMENT") {
                    $toastService.create("Payment not allowed.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $scope.paymentDetail.paymentRequests.push({paymentRequestId: $scope.viewPr.paymentRequestId});
                    $scope.paymentDetail.proposedAmount = $scope.viewPr.paidAmount;
                    $http({
                        url: apiJson.urls.paymentRequestManagement.settlePaymentRequestSingle,
                        method: 'POST',
                        data: $scope.paymentDetail
                    }).success(function (data) {
                        if (data != null && data == true) {
                            $scope.findPrs();
                            $toastService.create("Payment request settled.");
                        } else {
                            $toastService.create("Could not settle payments. Please try again later");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            function getPaidAmountAfterAdvance() {
                var result = 0;
                if ($scope.viewPr.advanceAmount < $scope.viewPr.proposedAmount){
                    result = parseFloat($scope.viewPr.proposedAmount - $scope.viewPr.advanceAmount).toFixed(6);
                }
                return result;
            }

            $scope.resetPaidAmount = function () {
                if ($scope.useAdvance) {
                    $scope.viewPr.paidAmount = getPaidAmountAfterAdvance();
                } else {
                    $scope.viewPr.paidAmount = $scope.viewPr.proposedAmount;
                }
            };

            $scope.setAction = function (pr, action) {
                if (pr != null) {
                    $scope.viewPr = pr;
                }
                $scope.action = action;
                $scope.actionReason = null;
            };

            $scope.submitAction = function () {
                if ($scope.actionReason == null || $scope.actionReason.trim().length == 0) {
                    $toastService.create("Please fill reason.");
                } else {
                    if ($scope.action == "ADHOC") {
                        $scope.payAdhoc();
                    } else if ($scope.action == "UNBLOCK") {
                        $scope.blockPaymentRequests(false);
                    } else if ($scope.action == "BLOCK") {
                        $scope.blockPaymentRequests(true);
                    } else if ($scope.action == "FORCE_CLOSE") {
                        $scope.forceClosePaymentRequests(true);
                    }
                }
            };

            $scope.getAssetsForGr = function (grIds) {
                $http({
                    method: 'GET',
                    url: apiJson.urls.goodsReceivedManagement.findAssetsForPayment,
                    params: {
                        grIds: grIds,
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $scope.selectedAssets = response.data;
                        if(!appUtil.isEmptyObject($scope.viewPr) && !appUtil.isEmptyObject($scope.viewPr.paymentInvoice) && !appUtil.isEmptyObject($scope.viewPr.paymentInvoice.paymentInvoiceItems)){
                            $scope.viewPr.paymentInvoice.paymentInvoiceItems.map(function (prItem,index){
                                $scope.viewPr.paymentInvoice.paymentInvoiceItems[index].assetTags = [];
                                if(!appUtil.isEmptyObject($scope.selectedAssets)){
                                    $scope.selectedAssets.map(function (asset){
                                        if(asset.skuId == prItem.skuId){
                                            $scope.viewPr.paymentInvoice.paymentInvoiceItems[index].assetTags.push(asset.assetId);
                                        }
                                    })
                                }
                            })
                        }
                        console.log("assets    ",response.data);
                    }
                }, function (error) {
                    console.log(error);
                });
            };

            $scope.showAssetTags = function (item,tags) {

                var showAssetsModal = Popeye.openModal({
                    templateUrl: "showAssetTagsModal.html",
                    controller: "showAssetTagsModalCtrl",
                    resolve: {
                        tagIds : function () {
                            return angular.copy(tags);
                        },
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });
            }

            $scope.payAdhoc = function () {
                if ($scope.viewPr.currentStatus != "APPROVED") {
                    $toastService.create("Payment request cannot be paid adhoc.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: apiJson.urls.paymentRequestManagement.payAdhoc,
                        method: 'POST',
                        data: {
                            paymentRequestId: $scope.viewPr.paymentRequestId,
                            currentStatus: $scope.viewPr.currentStatus,
                            newStatus: "SENT_FOR_PAYMENT",
                            reason: $scope.actionReason,
                            updatedBy: appUtil.createGeneratedBy().id
                        }
                    }).success(function (data) {
                        if (data != null && data == true) {
                            $scope.findPrs();
                            $toastService.create("Payment request status updated.");
                        } else {
                            $toastService.create("Could not update request. Please try again later");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.forceClosePaymentRequests = function () {
                var error = false;
                var reqObj = {
                    paymentRequestIds: [],
                    bankName: null,
                    updatedBy: appUtil.createGeneratedBy().id,
                    reason: $scope.actionReason
                };
                $scope.filteredPrs.map(function (pr) {
                    if (pr.selected === true) {
                        if (!error) {
                            if (pr.currentStatus != "APPROVED") {
                                $toastService.create("Payment request id: " + pr.paymentRequestId + " is not in APPROVED state");
                                error = true;
                            } else {
                                reqObj.paymentRequestIds.push(pr.paymentRequestId)
                            }
                        }
                    }
                });
                if (reqObj.paymentRequestIds.length == 0) {
                    $toastService.create("No valid payment requests selected for force closure.");
                } else if (!error) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentRequestForceSettle,
                        method: 'POST',
                        data: reqObj
                    }).success(function (response) {
                        if (response == true) {
                            $scope.findPrs();
                            $scope.selectAllPrs = false;
                            $toastService.create("Payments closed successfully. Please try again.");
                        } else {
                            $toastService.create("Could not update payments. Please try again.");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.downloadPRInvoice = function (prInvoice) {
                metaDataService.downloadDocument(prInvoice);
            };

            $scope.validateAndSaveId = function(id){
                $scope.showValidFilingText = false;
                if (!appUtil.isEmptyObject(id) && alphaFilingNumeric(id)){
                    $scope.showValidFilingText = true;
                }
            };

            $scope.filingPaymentRequest = function (pr) {
                $scope.filingPr = pr;
            };

            $scope.updateFileNumber = function(filingNumber){
                $scope.validateAndSaveId(filingNumber);
                if($scope.showValidFilingText == false){
                    $toastService.create("Please... Add Filing Number in Alpha Numeric Format!");
                    return;
                }
                if (appUtil.isEmptyObject(filingNumber) || (filingNumber == null || filingNumber == undefined)) {
                    $toastService.create("Please... Add Filing Number!");
                    return;
                }
                else {
                    var obj = {
                        prId: $scope.filingPr.paymentRequestId,
                        filingNo: filingNumber
                    }
                    $http({
                        method: "POST",
                        url: apiJson.urls.paymentRequestManagement.updateFilingNumber,
                        params: obj,
                    }).then(function (response) {
                        $scope.filingPr.currentStatus = "CLOSED";
                        $toastService.create("Filing Number updated successfully.");
                        $scope.backToSelectView();
                        $scope.findPrs();
                    }, function (error) {
                        $toastService.create("Could not save filing number");
                    });
                }
            }

            $scope.previewPRInvoice = function (prInvoice) {
                if (!appUtil.isEmptyObject(prInvoice.documentLink)) {
                    $http({
                        method: "POST",
                        url: apiJson.urls.vendorManagement.downloadDocument,
                        data: prInvoice,
                        responseType: 'arraybuffer'
                    }).then(function (response) {
                        var arrayBufferView = new Uint8Array(response.data);
                        var blob = new Blob([arrayBufferView], {type: appUtil.mimeTypes[prInvoice.mimeType]});
                        var urlCreator = window.URL || window.webkitURL;
                        var imageUrl = urlCreator.createObjectURL(blob);
                        var preview = document.getElementById("invoicePreview");
                        preview.innerHTML = "";
                        var img = new Image();
                        img.src = imageUrl;
                        preview.appendChild(img);
                    }, function (error) {
                        $toastService.create("Could not download the document... Please try again");
                    });
                } else {
                    $toastService.create("Not a valid document... Please check");
                }
            };
            $scope.printBoth = function (selectedPr) {
                if (selectedPr.type == "GOODS_RECEIVED") {
                    $scope.getGRs(selectedPr.paymentRequestId);
                } else if (selectedPr.type == "SERVICE_RECEIVED") {
                    $scope.getSRs(selectedPr.paymentRequestId);
                }
            }

            $scope.uploadDebitNote = function () {
                $fileUploadService.openFileModal("Upload Debit Note Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    if(file.size > 2048000){
                        $toastService.create('File size should not be greater than 2 MB');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('docType', "DEBIT_NOTE");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadDebitNoteDoc,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedDebitDoc = response;
                                $scope.debitNoteDocId= response.documentId;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                    /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                        $scope.uploadedDocData = doc;
                    });*/
                });
            };

            $scope.downloadDebitNote = function (debitaNote) {
                metaDataService.downloadDocument(debitaNote);
            };

            $scope.printDebitNote = function (debitNoteDocumentDetail) {
                metaDataService.downloadDocument(debitNoteDocumentDetail);
            }


            $scope.printSelected = function (printType) {
                $scope.grFlag = false;
                $scope.srFlag = false;
                    for (var i = 0; i < $scope.selectedGr.length; i++) {
                        if ($scope.selectedGr[i].paymentRequestId == printType.paymentRequestId && printType.type == "GOODS_RECEIVED") {
                            $scope.printPOforGR($scope.selectedGr[i],$scope.selectedGr.length,i);
                        }
                        else {
                            $scope.printSOforSR($scope.selectedGr[i],$scope.selectedGr.length,i);
                        }
                    }
            }

            function contains(a, obj) {
                for (var i = 0; i < a.length; i++) {
                    if (a[i] === obj) {
                        return true;
                    }
                }
                return false;
            }

            $scope.getSRs = function (paymentId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.serviceReceivedManagement.getLinkedSrForSo,
                    params: {
                        prId: paymentId,
                    }
                }).then(function (response) {
                    if (appUtil.isEmptyObject(response)) {
                        $toastService.create("No Receiving found!");
                    } else {
                        var soIds = []
                        response.data.forEach(function(el){
                         var temp = el.serviceOrderList.map(function(e){return e.id});
                         temp.forEach(function(el){ soIds.push(el); });
                         });

                        $scope.checkSoPoCapexStatus(soIds,"SO_ID");
                        $scope.requestingUnitsForView = "";
                        var bcc = new Map();
                        var data = [];
                        for(var i in response.data[0].serviceReceiveItems){
                                if(!contains(data,response.data[0].serviceReceiveItems[i].businessCostCenterName)){
                                    data.push(response.data[0].serviceReceiveItems[i].businessCostCenterName
                                    + " (" + response.data[0].serviceReceiveItems[i].businessCostCenterCode + ")" );
                                }
                                bcc.set(response.data[0].serviceReceiveItems[i].businessCostCenterName,"");
                        }
                        $scope.requestingUnitsForView = data.join(",");
                        $scope.selectedGr = response.data;
                        $scope.srType = $scope.selectedGr[0].serviceOrderList[0].type;
                        $scope.viewPoSrDetails(response.data);
                    }
                }, function (err) {
                    console.log("Encountered error at backend", err);
                });
            };

            $scope.checkSoPoCapexStatus = function(soPoIds,type){
                $http({method:"POST",
                 url : apiJson.urls.capexManagement.getCapexValidationBySoPo,
                 data:{
                    soPos : soPoIds,
                    type :  type
                }}).then(function(res){
                    $scope.expireSoPo = Object.keys(res.data);
                },function(err){
                    alert("Error while fetching SO capex status.")
                    console.log("Encountered error at backend", err);
                });
            }

            $scope.getGRs = function (paymentId) {
                $http({
                    method: 'GET',
                    url: apiJson.urls.goodsReceivedManagement.findVendorGrsForPo,
                    params: {
                        prId: paymentId,
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $scope.selectedGr = response.data;
                        var poIds = [];
                        response.data.forEach(function(el){
                          var temp = el.purchaseOrderList.map(function(e){return e.id});
                          temp.forEach(function(el){ poIds.push(el); });
                        });
                        $scope.checkSoPoCapexStatus(poIds,"PO_ID");
                        $scope.viewPoSrDetails(response.data);
                    } else {
                        $scope.gr = [];
                    }
                }, function (error) {
                    console.log(error);
                });
            };

            $scope.viewPoSrDetails = function (list) {
                $scope.poSrDetails = [];
                var obj = {};
                if ($scope.viewPr.type == "GOODS_RECEIVED") {
                    var grIds = [];
                    list.map(function (gr) {
                        if (gr.assetOrder || gr.assetOrder == 'true') {
                            grIds.push(gr.id);
                        }
                    })
                    if (grIds.length > 0) {
                        $scope.getAssetsForGr(grIds);
                    }
                }
                $scope.poSrDetails = [];
                var obj = {};
                angular.forEach(list, function (item) {
                    console.log("item is : ", item);
                        if ($scope.viewPr.type == "GOODS_RECEIVED") {
                            obj.grId = item.id;
                            obj.grGenerationTime = item.generationTime;
                            for (var j=0;j<$scope.viewPr.requestItemMappings.length;j++) {
                                if ($scope.viewPr.requestItemMappings[j].paymentRequestItemId == item.id) {
                                    obj.status = $scope.viewPr.requestItemMappings[j].status;
                                    break;
                                }
                            }
                            obj.multiplePo = [];
                            if (item.purchaseOrderList.length > 1) {
                                var poObj = {};
                                angular.forEach(item.purchaseOrderList,function (poItem) {
                                    poObj.poId = poItem.id;
                                    poObj.poGenerationTime = poItem.generationTime;
                                    obj.multiplePo.push(angular.copy(poObj));
                                });
                            }
                            else {
                                angular.forEach(item.purchaseOrderList, function (poItem) {
                                    obj.poId =poItem.id;
                                    obj.poGenerationTime = poItem.generationTime;
                                });
                            }
                            $scope.poSrDetails.push(angular.copy(obj));
                            console.log("multiple po is ",obj);
                        }
                        else {
                            obj.srId = item.id;
                            obj.srGenerationTime = item.creationTime;
                            for (var j=0;j<$scope.viewPr.requestItemMappings.length;j++) {
                                if ($scope.viewPr.requestItemMappings[j].paymentRequestItemId == item.id) {
                                    obj.status = $scope.viewPr.requestItemMappings[j].status;
                                    break;
                                }
                            }
                            obj.multipleSo = [];
                            if (item.serviceOrderList.length > 1) {
                                var soObj = {};
                                angular.forEach(item.serviceOrderList,function (soItem) {
                                    soObj.soId = soItem.id;
                                    soObj.soGenerationTime = soItem.generationTime;
                                    obj.multipleSo.push(angular.copy(soObj));
                                });
                            }
                            else {
                                angular.forEach(item.serviceOrderList, function (soItem) {
                                    obj.soId = soItem.id;
                                    obj.soGenerationTime = soItem.generationTime;
                                    console.log("obj is ", obj);
                                });
                            }
                            $scope.poSrDetails.push(angular.copy(obj));
                        }
                  });
                  console.log("final obj is : ",$scope.poSrDetails);
            };

            $scope.printPOforGR = function (gr,length,count) {
                $scope.grFlag = true;
                $scope.srFlag = false;
                $scope.currentPrintGR = gr;
                $scope.currentPrintGR.total = parseFloat(parseFloat($scope.currentPrintGR.billAmount) +
                    parseFloat($scope.currentPrintGR.extraCharges != null ? $scope.currentPrintGR.extraCharges : 0)).toFixed(2);
                document.title= 'PR-'+$scope.currentPrintGR.paymentRequestId+'-GR-'+$scope.currentPrintGR.id;
                $timeout(function () {
                    $scope.grFlag = true;
                    $scope.srFlag = false;
                    $scope.currentPrintGR = gr;
                    $scope.currentPrintGR.total = parseFloat(parseFloat($scope.currentPrintGR.billAmount) +
                        parseFloat($scope.currentPrintGR.extraCharges != null ? $scope.currentPrintGR.extraCharges : 0)).toFixed(2);
                    console.log("After timeout");
                    angular.element('#printDiv').trigger('click');
                    for (var i = 0; i < gr.purchaseOrderList.length; i++) {
                        metaDataService.downloadDocument(gr.purchaseOrderList[i].poInvoice);
                    }
                    document.title= 'PR-'+$scope.currentPrintGR.paymentRequestId+'-GR-'+$scope.currentPrintGR.id;
                    if( length-1 == count){
                        document.title = "CHAAYOS - SUMO";
                    }
                },0);
            }

            $scope.printSOforSR = function (sr, length, count) {
                $scope.grFlag = false;
                $scope.srFlag = true;
                $scope.currentPrintSR = sr;
                $scope.currentPrintSR["companyAddress"] = $scope.companyMap[sr.company.id].registeredAddress;
                   document.title= 'PR-'+$scope.currentPrintSR.paymentRequestId+'-SR-'+$scope.currentPrintSR.id;
                $timeout(function () {
                    $scope.grFlag = false;
                    $scope.srFlag = true;
                    $scope.currentPrintSR = sr;
                    $scope.currentPrintSR["companyAddress"] = $scope.companyMap[sr.company.id].registeredAddress;
                    angular.element('#printDiv').trigger('click');
                    for (var i = 0; i < sr.serviceOrderList.length; i++) {
                        metaDataService.downloadDocument(sr.serviceOrderList[i].soInvoiceDocument);
                    }
                    document.title= 'PR-'+$scope.currentPrintSR.paymentRequestId+'-SR-'+$scope.currentPrintSR.id;
                    if( length-1 == count){
                        document.title = "CHAAYOS - SUMO";
                    }
                },0);
            };


            /////////////////////document upload methods/////////////////////////////////

            /*$scope.resetScanModal = function () {
                $scope.imagesScanned = [];
                document.getElementById('images').innerHTML = "";
                var canvas = document.createElement('canvas');
                canvas.id = "scaleCanvas";
                document.getElementById('images').appendChild(canvas);
                $scope.uploadedDocData = null;
            };

            $scope.resetSnapModal = function () {
                $scope.snapRunning = false;
                if ($scope.localstream != null) {
                    $scope.localstream.getTracks()[0].stop();
                }
                $scope.uploadedDocData = null;
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                context.clearRect(0, 0, 640, 480);
            };

            $scope.startSnap = function () {
                var video = document.getElementById('video');
                // Get access to the camera!
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // Not adding `{ audio: true }` since we only want video now
                    navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                        video.src = window.URL.createObjectURL(stream);
                        $scope.localstream = stream;
                        video.play();
                    });
                }
                $scope.snapRunning = true;
            };

            $scope.snapPicture = function () {
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                var video = document.getElementById('video');
                context.drawImage(video, 0, 0, 640, 480);
                video.pause();
                video.src = "";
                $scope.localstream.getTracks()[0].stop();
                $scope.snapRunning = false;
            };

            function dataURItoBlob(dataURI) {
                var byteString = atob(dataURI.split(',')[1]);
                var ab = new ArrayBuffer(byteString.length);
                var ia = new Uint8Array(ab);
                for (var i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            }

            $scope.uploadFile = function () {
                var canvas = document.getElementById('canvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedDocData = response;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                    /!*metaDataService.uploadFile("OTHERS", "PAYMENT_REQUEST_INVOICE", file, function (doc) {
                        $scope.uploadedDocData = doc;
                    });*!/
                });
            };

            $scope.scanToPng = function () {
                scanner.scan($scope.displayImagesOnPage,
                    {
                        "output_settings": [
                            {
                                "type": "return-base64",
                                "format": "png"
                            }
                        ]
                    }
                );
            };

            $scope.displayImagesOnPage = function (successful, mesg, response) {
                if (!successful) { // On error
                    console.error('Failed: ' + mesg);
                    return;
                }
                if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                    console.info('User cancelled');
                    return;
                }
                var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
                $scope.imagesScanned = [];
                $scope.processScannedImage(scannedImages[0]);
                /!*for(var i = 0; (scannedImages instanceof Array) && i < scannedImages.length; i++) {
                    var scannedImage = scannedImages[i];
                    $scope.processScannedImage(scannedImage);
                }*!/
            };

            $scope.processScannedImage = function (scannedImage) {
                $scope.imagesScanned.push(scannedImage);
                scaleImage(scannedImage.src);
            };

            function scaleImage(src) {
                var MAX_WIDTH = 1000;
                var image = new Image();
                var canvas = document.getElementById("scaleCanvas");
                image.onload = function () {
                    //var canvas = document.getElementById("scaleCanvas");
                    if (image.width > MAX_WIDTH) {
                        image.height *= MAX_WIDTH / image.width;
                        image.width = MAX_WIDTH;
                    }
                    var ctx = canvas.getContext("2d");
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    canvas.width = image.width;
                    canvas.height = image.height;
                    ctx.drawImage(image, 0, 0, image.width, image.height);
                };
                image.src = src;
            }

            $scope.uploadScannedFile = function () {
                var canvas = document.getElementById('scaleCanvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.goodsReceivedManagement.uploadGR,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };*/

$scope.deleteRejection = function (list, index) {
var items = [];

if(list.length > 1) {
 list.splice(index, 1);
 items = list ;
}
console.log($scope.availableRejections);
$scope.viewPr.paymentInvoice.rejections[index].deviationRemark = null;
//availableRejectionsDeviation.
$scope.viewPr.paymentInvoice.rejections = items;
};

            $scope.openAddRejectionModal = function(addRejections) {
            var invoiceRejections = $scope.invoiceRejections;
            var viewPr = $scope.viewPr;
            var mandatoryDocuments = $scope.mandatoryDocuments;
            var instanceModal = Popeye.openModal({
                               templateUrl: "addRejectionModal.html",
                               controller: "addRejectionModalCtrl",
                               resolve: {
                                   addRejections: function(){
                                       return addRejections;
                                   },
                                   invoiceRejections: function(){
                                       return invoiceRejections;
                                   },
                                   viewPr: function(){
                                        return viewPr;
                                   },
                                   mandatoryDocuments: function(){
                                        return mandatoryDocuments;
                                   }

                               },
                               click: false,
                               keyboard: false
                           });
            }

            $scope.openQueryModalView =function(edit) {
                var queryModal = Popeye.openModal({
                    templateUrl: "prQueryModal.html",
                    controller: "prQueryModalCtrl",
                    resolve: {
                        queryReasons: function () {
                            return angular.copy($scope.queryReasons);
                        },
                        pr: function () {
                            return angular.copy($scope.viewPr);
                        },
                        isEdit: function () {
                            return edit;
                        }
                    },
                    click: false,
                    keyboard: false
                });

                queryModal.closed.then(function (result) {
                    if (!appUtil.isEmptyObject(result) && result.isSubmitted && result.prData != null) {
                        $scope.changeStatus(result.prData, "QUERIED");
                    }
                });
            };


            $scope.openQueryModal = function(isEdit) {
                $http({
                    url: apiJson.urls.paymentRequestManagement.validatePrForQuery,
                    method: 'GET',
                    params: {
                        "prId": $scope.viewPr.paymentRequestId
                    }

                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response ){
                        if ($scope.viewPr.paymentRequestQueries != null && $scope.viewPr.paymentRequestQueries.length > 0) {
                            $alertService.confirm("Are you sure?","Do you want to Raise the query again..? <br> " +
                                "If You want to see the Raised query Click On <b>No</b>",function(result) {
                                if (result) {
                                    $scope.openQueryModalView(isEdit);
                                } else {
                                    $scope.openQueryModalView(false);
                                }
                            });
                        } else {
                            $scope.openQueryModalView(isEdit);
                        }
                    } else {
                        $toastService.create("Query on this PR is not allowed..!");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Status update failed");
                    }
                });
            }


            $scope.clubAmountModal = function (viewData) {
            	$scope.getClubbedDataModal(viewData);

            }

            $scope.getClubbedDataModal = function (viewData) {
                var modalInstance = Popeye.openModal({
                    templateUrl: 'clubAmountModal.html',
                    controller: 'clubAmountModalCtrl',
                    size: 'lg',
                    windowClass: 'my-modal-popup',
                    modalClass:'my-modal-popup',
                    resolve: {
                        viewData: function () {
                            return viewData;
                        }
                    },
                    click: true,
                    keyboard: false
                });
            };


        }]
    ).controller('clubAmountModalCtrl', ['$scope', 'appUtil', 'Popeye', 'viewData',
        function ($scope, appUtil, Popeye, viewData) {


    	$scope.init = function (){
    		$scope.summaryInvoicesList = [];
            $scope.summaryInvoicesList.type=viewData.type;
    		$scope.list = viewData.paymentInvoice.paymentInvoiceItems;
    		$scope.summaryList();
    	}

    	$scope.summaryList = function () {
    		var map = new Map();
            for(var x = 0; x < $scope.list.length; x++){
                if(x == 0 || !map.has($scope.list[x].subCategory)){
                    $scope.list[x].packagingPrice = $scope.list[x].totalAmount;
                    $scope.list[x].tdsRate = $scope.list[x].totalTax;
                    $scope.list[x].totalPrice = $scope.list[x].packagingPrice - $scope.list[x].tdsRate;
                    map.set($scope.list[x].subCategory,$scope.list[x]);
                }
                else if(map.has($scope.list[x].subCategory)){
                    var listData = map.get($scope.list[x].subCategory);
                    $scope.list[x].packagingPrice = listData.packagingPrice + $scope.list[x].totalAmount;
                    $scope.list[x].tdsRate = listData.tdsRate + $scope.list[x].totalTax;
                    $scope.list[x].totalPrice = $scope.list[x].packagingPrice - $scope.list[x].tdsRate;
                    map.delete($scope.list[x].subCategory);
                    map.set($scope.list[x].subCategory,$scope.list[x]);
                }
                // else{
                // 	$scope.list[x].packagingPrice = $scope.list[x].totalAmount;
                // 	$scope.list[x].tdsRate = $scope.list[x].totalTax;
                // $scope.list[x].totalPrice = $scope.list[x].packagingPrice - $scope.list[x].tdsRate;
                // 	map.set($scope.list.subCategory,$scope.list[x]);
                // }
            }
            angular.forEach(map, function (value, key) {
                $scope.summaryInvoicesList.push(value);
            });
            //console.log($scope.summaryInvoicesList);
            //console.log($scope.summaryInvoicesList.type);
           }

            $scope.closeModal = function closeModal() {
                Popeye.closeCurrentModal();
            }

        }
    ]
).controller('viewPoSoCtrl', ['$scope', 'appUtil', 'Popeye', 'poSo','typeReceived',
        function ($scope, appUtil, Popeye, poSo,typeReceived) {
            $scope.init = function () {
                console.log("poso",poSo);
                console.log("type",typeReceived);
                $scope.poSo = poSo;
                $scope.typeReceived = typeReceived;
            };

            $scope.closeModal = function closeModal() {
                Popeye.closeCurrentModal();
            }
        }
    ]

 ).controller('addRejectionModalCtrl',['$scope','appUtil','Popeye','addRejections','invoiceRejections','viewPr','mandatoryDocuments','$toastService',
           function($scope, appUtil, Popeye,addRejections,invoiceRejections,viewPr,mandatoryDocuments,$toastService){

           $scope.init = function () {
           $scope.mandatoryDocuments = mandatoryDocuments;
           $scope.availableRejections = [];
               $scope.setAvailableRejections();

//                $rootScope.availableRejectionsDeviation = $scope.availableRejections;
           }
           $scope.addRejections=addRejections;

           $scope.setAvailableRejections = function () {
                $scope.availableRejections = [];
                               invoiceRejections.map(function (dev) {
                                   var found = false;
                                   viewPr.paymentInvoice.rejections.map(function (dev1) {
                                       if (dev.paymentDeviationId == dev1.paymentDeviation.paymentDeviationId) {
                                           found = true;
                                       }
                                   });
                                   if (!found) {
                                       dev.checked = false;

                                       if(dev.paymentDeviationId === 34) {
                                       dev.deviationRemark = null;
                                       $scope.availableRejections.push({data: dev});
                                       } else {
                                       $scope.availableRejections.push({data: dev});
                                       }

                                       $scope.addToDeviationRemark($scope.availableRejections, $scope.mandatoryDocuments);
                                   }
                               });
                           };


                           $scope.addRejections = function () {
                            var isDeviationSelected = true;
                               $scope.availableRejections.map(function (dev) {
                                   if (dev.checked) {
                                       if(dev.data.paymentDeviationId === 34 && dev.data.deviationRemark==undefined ) {
                                          $toastService.create("Select Deviation.");
                                          isDeviationSelected = false;
                                           return;
                                       }else if(dev.data.paymentDeviationId === 34 && dev.data.deviationRemark!=undefined ){
                                       for(var i in $scope.mandatoryDocuments){
                                            $scope.mandatoryDocuments[i].checked = false;
                                            $scope.addToDeviationRemark(dev, $scope.mandatoryDocuments[i]);

                                       }

                                       }

                                       viewPr.paymentInvoice.rejections.push(   {
                                           mappingId: null,
                                           paymentDeviation: dev.data,
                                           deviationRemark: dev.data.deviationRemark==undefined ? null : dev.data.deviationRemark,
                                           currentStatus: "ACCEPTED",
                                           createdBy: appUtil.createGeneratedBy(),
                                           acceptedBy: appUtil.createGeneratedBy()
                                       });
                                   }
                               });
                               if(isDeviationSelected){
                               $scope.closeModal();
                               }

                           };


                                       $scope.addToDeviationRemark=function(availableRejections, mandatoryDocuments){
                                       if(availableRejections.checked != undefined && availableRejections.checked != null  && availableRejections.checked
                                       && mandatoryDocuments.checked != undefined && mandatoryDocuments.checked != null && mandatoryDocuments.checked) {
                                           if(availableRejections.checked && mandatoryDocuments.checked){
                                               if(availableRejections.data.deviationRemark!==undefined && availableRejections.data.deviationRemark!==null) {
                                                   availableRejections.data.deviationRemark= availableRejections.data.deviationRemark +","+mandatoryDocuments.name;
                                               } else {
                                                   availableRejections.data.deviationRemark = mandatoryDocuments.name;
                                               }
                                           } else {
                                               var temp = availableRejections.data.deviationRemark.split(",");
                                               availableRejections.data.deviationRemark = null;
                                               for(var remark in temp) {
                                                   if (mandatoryDocuments.name != temp[remark]) {
                                                       if (availableRejections.data.deviationRemark !=null) {
                                                           availableRejections.data.deviationRemark= availableRejections.data.deviationRemark +","+temp[remark];
                                                       } else {
                                                           availableRejections.data.deviationRemark = temp[remark];
                                                       }
                                                   }
                                               }
                                           }
                                           }
                                       }

//                                       $scope.deviationAction = function (item, type) {
//                                           if (type == "APPROVE") {
//                                               item.currentStatus = "ACCEPTED";
//                                               item.acceptedBy = appUtil.createGeneratedBy();
//                                           } else if (type == "REJECT") {
//                                               item.currentStatus = "REJECTED";
//                                               item.rejectedBy = appUtil.createGeneratedBy();
//                                           } else if (type == "REMOVE") {
//                                               item.currentStatus = "REMOVED";
//                                               item.removedBy = appUtil.createGeneratedBy();
//                                           }
//                                       };


                $scope.closeModal = function closeModal() {
                   Popeye.closeCurrentModal();
               }
           }]
).controller('prQueryModalCtrl',['$scope','appUtil','Popeye','queryReasons','pr','isEdit','$toastService','$alertService','metaDataService',
           function($scope, appUtil, Popeye,queryReasons,pr,isEdit,$toastService, $alertService, metaDataService){

               function setQueries(queries) {
                    angular.forEach(queries, function (query) {
                        query.checked = false;
                    });
                    return queries;
               }

               $scope.init = function () {
                   $scope.queryReasons = setQueries(queryReasons);
                   $scope.pr = pr;
                   $scope.isEdit = isEdit;
               };

               $scope.downloadQueryDoc = function (doc) {
                   metaDataService.downloadDocument(doc);
               };

               $scope.setSelectedQuery = function (query, flag) {
                    query.checked = flag;
                    query.raiseByComment = null;
               };

               function getQueryObj(query) {
                   return {
                       "prId": $scope.pr.paymentRequestId,
                       "paymentDeviationId": query.paymentDeviationId,
                       "paymentDeviationDetail": query.deviationDetail,
                       "queryRaisedBy": appUtil.getCurrentUser().userId,
                       "raisedByComment": query.raisedByComment
                   };
               }

               $scope.raiseQuery = function () {
                   $scope.pr.paymentRequestQueries = [];
                   var queryCheck = false;
                   for (var i = 0; i < $scope.queryReasons.length; i++) {
                       if ($scope.queryReasons[i].checked) {
                           queryCheck = true;
                           break;
                       }
                   }
                   if (!queryCheck) {
                       $toastService.create("Please Select at least one Reason to Query..!");
                       return false;
                   } else {
                       var selectedReasons = [];
                       angular.forEach($scope.queryReasons, function (query) {
                            if (query.checked) {
                                selectedReasons.push(getQueryObj(query));
                            }
                       });
                       pr.paymentRequestQueries = selectedReasons;
                       if (selectedReasons.length > 0) {
                           $scope.closeModal({
                               "isSubmitted" : true,
                               "prData" : angular.copy($scope.pr)
                           });
                       } else {
                           $toastService.create("Please Select at least one Reason to Query..!");
                           return false;
                       }
                   }
               };

               $scope.close = function () {
                    $scope.closeModal({
                        "isSubmitted" : false,
                        "prData" : null
                    });
               };

               $scope.closeModal = function closeModal(obj) {
                   Popeye.closeCurrentModal(obj);
               }
           }]
).controller('showAssetTagsModalCtrl', ['$scope', 'appUtil', 'Popeye','tagIds',
         function ($scope, appUtil, Popeye,tagIds) {

             $scope.init = function () {
                 $scope.tagIds = tagIds;
             };

             $scope.closeAssetModal = function closeModal() {
                 Popeye.closeCurrentModal();
             }

         }
     ]
 ).controller('addDebitNoteModalCtrl',['$http','$rootScope','$scope','viewPr','debitNoteDocId','useAdvance','appUtil','apiJson','$toastService','metaDataService','$alertService','Popeye',
    function ($http, $rootScope, $scope, viewPr,debitNoteDocId,useAdvance, appUtil, apiJson, $toastService, metaDataService, $alertService,Popeye) {

    $scope.init = function () {
        $scope.addedCategorySubCategory = {};
        $scope.isAutoDistributed = false;
        $scope.viewPr = viewPr;
        $scope.debitNoteDocId = debitNoteDocId;
        $scope.useAdvance = useAdvance;
        $scope.categorySubCategoryMap = {};
        $scope.categorySubCategoryList = [];
        $scope.debitedFrom = null;
        $scope.addedItems = Object.values($scope.addedCategorySubCategory);

        if ($scope.viewPr.type == 'GOODS_RECEIVED') {
            $scope.categorySubCategoryMap = {};
            $scope.categorySubCategoryList = [];
            angular.forEach($scope.viewPr.paymentInvoice.paymentInvoiceItems, function (entry) {
                var key = entry.categoryId + "_" + entry.subCategoryId;
                if ($scope.categorySubCategoryMap[key] === undefined || $scope.categorySubCategoryMap[key] == null) {
                    $scope.categorySubCategoryMap[key] = {
                        "categoryId": entry.categoryId,
                        "categoryName": entry.category,
                        "subCategoryId": entry.subCategoryId,
                        "subCategoryIdName": entry.subCategory,
                        "key": key,
                        "cat_subCat": entry.category + "_" + entry.subCategory
                    };
                }
            });
            $scope.categorySubCategoryList = Object.values($scope.categorySubCategoryMap);
        }
        $scope.createDebitNote();
    }

        $scope.setDebitedFrom = function (debitedFrom) {
            $scope.debitedFrom = debitedFrom;
        };

        $scope.createDebitNote = function () {
            if($scope.debitNoteDocId == (null || undefined)){
                $toastService.create("Please Upload  Debit Note First");
                return;
            }
            var debitNoteAmount = parseFloat((parseFloat($scope.viewPr.proposedAmount) - parseFloat($scope.viewPr.paidAmount)).toFixed(6));
            $scope.debitNote = {
                debitNoteId: null,
                paymentRequestId: $scope.viewPr.paymentRequestId,
                invoiceNumber: $scope.viewPr.paymentInvoice.invoiceNumber,
                amount: debitNoteAmount,
                totalTaxes: 0,
                totalAmount: null,
                generatedBy: appUtil.createGeneratedBy(),
                lastUpdatedBy: appUtil.createGeneratedBy(),
                busyReferenceNumber: null,
                debitNoteDocId : $scope.debitNoteDocId,
                advancePaymentId: $scope.advancePayment != null ? $scope.advancePayment.advancePaymentId : null,
                paidAmount : $scope.viewPr.paidAmount
            };
            $scope.calculateTotalAmount();
        };

        $scope.calculateTotalAmount = function () {
            $scope.debitNote.totalAmount = parseFloat((parseFloat($scope.debitNote.amount > 0 ? $scope.debitNote.amount : 0) +
                parseFloat($scope.debitNote.totalTaxes > 0 ? $scope.debitNote.totalTaxes : 0)).toFixed(6));
            $scope.viewPr.paidAmount = parseFloat((parseFloat($scope.viewPr.proposedAmount) - parseFloat($scope.debitNote.totalAmount)).toFixed(6));
            $scope.remainingAmount = $scope.debitNote.totalAmount;
            if ($scope.categorySubCategoryList.length == 1) {
                $scope.isAutoDistributed = true;
                $toastService.create("Auto Setting Category And SubCategory...!");
                $scope.addCategorySubCategory($scope.categorySubCategoryList[0], $scope.debitNote.totalAmount);
            }
        };

        $scope.submitDebitNote = function () {
            if ($scope.useAdvance) {
                $scope.debitNote.advanceAmount = $scope.viewPr.advanceAmount;
            }
            if ($scope.viewPr.type === 'GOODS_RECEIVED') {
                if ($scope.remainingAmount !== 0) {
                    $toastService.create("Please Add the Remaining Amount with categories and Sub Categories..!");
                    return;
                }
                var finalList = [];
                if ($scope.remainingAmount === 0) {
                    angular.forEach(Object.values($scope.addedCategorySubCategory), function (catSubCategory) {
                         finalList.push({
                             "categoryId": catSubCategory.categoryId,
                             "subCategoryId": catSubCategory.subCategoryId,
                             "amount" : catSubCategory.amount
                         });
                    });
                    $scope.debitNote.categorySubCategoryDebitNotes = finalList;
                }
            }
            if (!appUtil.isEmptyObject($scope.debitNote.busyReferenceNumber)) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.paymentRequestManagement.addDebitNote,
                    method: 'POST',
                    data: $scope.debitNote
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null && response.debitNoteId != null) {
                        $scope.viewPr.debitNote = response;
                        $toastService.create("Debit note added successfully.");
                        $scope.closeModal({
                            "isSubmitted" : true
                        })
                    } else {
                        $toastService.create("Could not Add Debit Note...!");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        if (response.errorTitle == "Exceeding the available Advance ..!") {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                            $scope.resetAdvance();
                        }
                        else {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                        }
                    } else {
                        $toastService.create("Add Debit Note failed");
                    }
                });
            } else {
                $toastService.create("Please add busy reference number.");
            }
        };

        $scope.addCategorySubCategory = function (debitedFrom, enteredAmount) {
            if ($scope.remainingAmount == 0) {
                $toastService.create("Amount Completely divided amongst categories and subcategories..!");
                return;
            }
            if (appUtil.isEmptyObject(debitedFrom)) {
                $toastService.create("Please Select Category and Sub Category ..!");
                return;
            }
            $scope.debitedFrom = debitedFrom;
            if (appUtil.isEmptyObject(enteredAmount)) {
                $toastService.create("Please Enter Amount..!");
                return;
            }
            if (enteredAmount < 0) {
                $toastService.create("Please Enter Amount Greater than 0..!");
                $scope.enteredAmount = null;
                return;
            }
            if (enteredAmount > $scope.debitNote.totalAmount) {
                $toastService.create("Amount Cannot be Greater than the total debit Amount..!");
                $scope.enteredAmount = null;
                return;
            }
            if ($scope.remainingAmount < enteredAmount) {
                $toastService.create("Remaining Amount is : " + $scope.remainingAmount + ". Please Enter Amount Less than the Remaining Amount..!");
                $scope.enteredAmount = null;
                return;
            }
            $scope.enteredAmount = enteredAmount;
            if (appUtil.isEmptyObject($scope.addedCategorySubCategory[debitedFrom.key])) {
                $scope.debitedFrom["amount"] = enteredAmount;
                $scope.categorySubCategoryMap[debitedFrom.key] = debitedFrom;
                $scope.categorySubCategoryList = Object.values($scope.categorySubCategoryMap);
                $scope.addedCategorySubCategory[debitedFrom.key] = debitedFrom;
                $scope.addedItems = Object.values($scope.addedCategorySubCategory);
                $scope.calculateRemainingAmount(enteredAmount, true);
            } else {
                $toastService.create("Category Sub Category Already Added ...!");
            }
        };

        $scope.removeItem = function (item) {
            delete $scope.addedCategorySubCategory[item.key];
            $scope.addedItems = Object.values($scope.addedCategorySubCategory);
            $scope.calculateRemainingAmount($scope.categorySubCategoryMap[item.key]["amount"], false);
            delete $scope.categorySubCategoryMap[item.key]["amount"];
            $scope.categorySubCategoryList = Object.values($scope.categorySubCategoryMap);
        };


        $scope.calculateRemainingAmount = function (amount, isAdded) {
            if (isAdded) {
                $scope.remainingAmount = parseFloat((parseFloat($scope.remainingAmount.toFixed(6)) -  parseFloat(amount.toFixed(6))).toFixed(6));
            } else {
                $scope.remainingAmount = parseFloat((parseFloat($scope.remainingAmount.toFixed(6)) +  parseFloat(amount.toFixed(6))).toFixed(6));
            }
        };

        $scope.resetAdvance = function () {
            $scope.closeModal({
                "isSubmitted" : false,
                "resetAdvance" : true
            });
        };

        $scope.closeModal = function closeModal(obj) {
            Popeye.closeCurrentModal(obj);
        }
    }]);
