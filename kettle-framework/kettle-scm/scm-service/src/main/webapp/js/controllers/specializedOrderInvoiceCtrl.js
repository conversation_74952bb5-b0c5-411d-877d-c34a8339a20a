/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('specializedOrderInvoiceCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService','$timeout','$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService, previewModalService,$timeout,$window) {

            $scope.init = function () {
                $scope.isMilkPayment = true;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.selectView = true;
                $scope.selectedGr = null;
                $scope.itemDeviations = [];
                $scope.invoiceDeviations = [];
                metaDataService.getSCMMetaData(function (metadata) {
                    $scope.prRequestTypes = metadata.paymentRequestTypes.filter(function (prType) {
                        return prType.shortCode == "GR";
                    });
                    $scope.prRequestType = $scope.prRequestTypes == null ? null : $scope.prRequestTypes[0];
                    metadata.paymentDeviations.map(function (deviation) {
                        if (deviation.deviationType == "DEVIATION") {
                            if (deviation.deviationLevel == "INVOICE_ITEM") {
                                $scope.itemDeviations.push(deviation);
                            } else if (deviation.deviationLevel == "INVOICE") {
                                $scope.invoiceDeviations.push(deviation);
                            }
                        }
                    });
                });
                $scope.unitList = [];
                $scope.unitList.push({
                    id: null,
                    name: ""
                });
                $scope.selectedUnit = $scope.unitList[0];
                $scope.vendorList = [];
                $scope.vendorList.push({
                    id: null,
                    name: ""
                });
                $scope.selectedVendor = $scope.vendorList[0];
                $scope.getEmployeeMappedUnits();
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.showPreview = previewModalService.showPreview;
            };


            $scope.generateInvoice = function (){
                       var selectedGrs = [];
                       for(var i =0;i<$scope.grs.length;i++){
                           if($scope.grs[i].checked == true){
                               selectedGrs.push($scope.grs[i]);
                           }
                       }
                       if(selectedGrs.length ==0){
                           $toastService.create("Please Select Atleast One Gr For Invoice !!");
                           return;
                       }
                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.generateAndSaveSpecializedOrderInvoice,
                    data : selectedGrs,
                    params: {
                        userId: $scope.currentUser.userId,
                        vendorId : $scope.selectedVendor.id
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $toastService.create("Invoice uploaded");
                        $window.open(response.data.key, '_blank');
                        $scope.findGrs();
                    }
                }, function (err) {
                    if (err.data.errorMsg != null) {
                        $toastService.create(err.data.errorMsg);
                    }
                    console.log("Encountered error at backend", err);
                });
            }


            $scope.getEmployeeMappedUnits = function () {
                appUtil.getUnitList().map(function (unit) {
                    if ($rootScope.mappedUnits.indexOf(unit.id) >= 0) {
                        $scope.unitList.push(unit);
                    }
                });
                if ($scope.unitList != null && $scope.unitList.length > 0) {
                    $scope.selectedUnit = $scope.unitList[0];
                }
            };

            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                $scope.getMappedVendors();
            };

            $scope.getMappedVendors = function () {
                console.log($scope.selectedUnit);
                if ($scope.selectedUnit.id !== null) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.skuMapping.getVendorsForUnitTrimmed + "?unitId=" + $scope.selectedUnit.id
                    }).then(function success(response) {
                        $scope.vendorList = [];
                        response.data.map(function (item) {
                            $scope.vendorList.push(item);
                        });
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $scope.vendorList = [{
                        id: null,
                        name: ""
                    }];
                    $scope.selectedVendor = $scope.vendorList[0];
                }

            };



            $scope.findGrs = function () {
                var url = apiJson.urls.goodsReceivedManagement.getMilkGrsForPayment;

                $scope.selectedGr = null;
                $scope.showNoGR = false;
                if ($scope.selectedUnit.id == null || $scope.selectedUnit.id == "") {
                    $toastService.create("Please select unit");
                } else if ($scope.startDate == null || $scope.startDate == "") {
                    $toastService.create("Please select start date");
                } else if ($scope.endDate == null || $scope.endDate == "") {
                    $toastService.create("Please select end date");
                } else if ($scope.selectedVendor.id == null || $scope.selectedVendor.id == "") {
                    $toastService.create("Please select vendor");
                }
                else {
                    $scope.grs = [];
                    $http({
                        method: 'GET',
                        url: url,
                        params: {
                            deliveryUnitId: $scope.selectedUnit.id,
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            vendorId: $scope.selectedVendor.id
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            if($scope.isMilkPayment == false){
                                $scope.getFilteredGrsByType(response.data);
                            }else{
                                $scope.grs = response.data;
                            }
                        } else {
                            $scope.showNoGR = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.selectAll = function (){
                var currentValue = $scope.grs[0].checked == null ? false : $scope.grs[0].checked;
                for(var i =0 ;i<$scope.grs.length;i++){
                    $scope.grs[i].checked = !currentValue;
                }
            }

            $scope.getFilteredGrsByType = function (data) {
                $scope.grs = [];
                data.map(function (gr) {
                    if (gr.receiptType === $scope.grType) {
                        $scope.grs.push(gr);
                    }
                });
                if ($scope.grs.length === 0) {
                    $scope.showNoGR = true;
                }
            };

            $scope.backToSelectView = function () {
                $scope.selectView = true;
            };





        }]
    );
