'use strict';

angular.module('scmApp')
    .controller('trGrOrderCreateCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', '$alertService', 'metaDataService', 'previewModalService', 'Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, previewModalService, Popeye) {


            $scope.init = function () {
                $scope.requestOrderList = [];
                $scope.pendingList = [];
                $scope.requestOrderDetail = null;
                $scope.getAvailableSKUs();
                $scope.getUnitSkuMappings();
                $scope.packagingMap = appUtil.getPackagingMap();
                $scope.isWarehouse = appUtil.isWarehouse();
                $scope.activeProducts = appUtil.getActiveScmProducts();
                $scope.fulfillmentDate = appUtil.getDate(0);
                $scope.minDate = appUtil.getDate(0);
                $scope.maxDate = appUtil.getDate(0);
                $scope.productPackaging = null;
                metaDataService.getAllPackagingMappings(function (packagingMap) {
                    $scope.productPackaging = packagingMap;
                }, false);
                $scope.showPendingRequest = true;
                $scope.grData =null;
                $scope.getPendingRequestOrders(appUtil.getDate(0));
            }

            $scope.getAvailableSKUs = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableSKUs,
                    params: {
                        unitId: appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.availableSKUList = response.data;
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.bySKUs = function (sku) {
                return sku != null && sku != undefined && $scope.availableSKUList != undefined && $scope.availableSKUList != null
                    && $scope.availableSKUList.indexOf(sku.skuId) > -1;
            };

            $scope.selectOpenRo = function (ro, type) {
                $http({
                    method: 'GET',
                    url: apiJson.urls.requestOrderManagement.requestOrder,
                    params: {requestOrderId: ro.id}
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $scope.getSelectedRo = response.data;
                        if (!type) {
                            $scope.selectRequest(ro);
                        } else {
                            $scope.fillTransferOrder($scope.getSelectedRo);
                        }
                    } else {
                        $toastService.create("Could Not Find Items for the Selected Ro!!");
                    }
                }, function (error) {
                    $scope.isInvoiceRo = false;
                    console.log(error);
                });
            }

            $scope.getUnitSkuMappings = function () {
                $scope.unitSkuProductPackagingMap = [];
                $http({
                    method: "GET",
                    url: apiJson.urls.productManagement.getUnitSkuPackagingMappings,
                    params: {unitId: appUtil.getUnitData().id}
                }).then(function success(response) {
                    if (response.data !== null && response.status == 200) {
                        $scope.unitSkuProductPackagingMap = response.data;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.getPendingRequestOrders = function (fulfilmentDate) {
                fulfilmentDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                if (fulfilmentDate == null) {
                    $toastService.create("Please select a fulfilment date before ");
                    return false;
                }
                $http({
                    method: "GET",
                    url: apiJson.urls.requestOrderManagement.pendingRequestOrderShort
                        + "?unitId=" + appUtil.getCurrentUser().unitId + "&date=" + fulfilmentDate
                }).then(function success(response) {
                    $scope.requestOrderList = response.data.filter(function(reqOrder){
                        return (reqOrder.specialOrder==true);
                    })
                    $scope.requestOrderList.length == 0 ? $scope.showMessage = true : $scope.showMessage = false;
                    $scope.requestOrderList = $scope.requestOrderList.sort(sortByUnit);
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function sortByUnit(x, y) {
                if (x.requestUnit.name < y.requestUnit.name) return -1;
                if (x.requestUnit.name > y.requestUnit.name) return 1;
                return 0;
            }

            $scope.fillTransferOrder = function (requestOrder) {
                $scope.specialOrder = requestOrder.specialOrder;
                if (!appUtil.isEmptyObject(requestOrder)) {
                    $scope.requestOrderDetail = requestOrder;
                    $scope.showPendingRequest = false;
                    if (!requestOrder.assetOrder) {
                        setSkuToProducts($scope.requestOrderDetail.requestOrderItems);
                    }
                    $scope.comment = null;
                }
            };

            $scope.getImageS3Links = function (callback) {
                var filesArr=[];
                $scope.requestOrderDetail.requestOrderItems.map(function (item) {
                    var name = "multi-por-images_" + item.productId;
                    var htmlEle = document.getElementById(name);
                    var currentFileArr = [];
                    currentFileArr = htmlEle.files;
                    for (var index = 0; index < currentFileArr.length; index++) {
                        filesArr.push(currentFileArr[index]);
                    }
                })
                if (filesArr.length <= 0) {
                    callback();
                }
                if (filesArr.length > $scope.requestOrderDetail.requestOrderItems.length*3) {
                    $toastService.create("Please Upload less than "+$scope.requestOrderDetail.requestOrderItems.length*3 +" Images");
                    return;
                }
                var count = 0;
                var fd = [];
                $scope.porImagesUrl = [];
                for (var index = 0; index < filesArr.length; index++) {
                    $scope.uploadImage = function (fd, i, filesArr) {
                        if(filesArr[i].size > 5120000){
                            $toastService.create('Image size should not be greater than 5 MB.');
                            return;
                        }
                        else {
                            var fileExt = metaDataService.getFileExtension(filesArr[i].name);
                            fd[i] = new FormData();
                            fd[i].append('type', "OTHERS");
                            fd[i].append('docType', "PROOF_OF_REJECTION");
                            fd[i].append('mimeType', fileExt.toUpperCase());
                            fd[i].append('userId', appUtil.getCurrentUser().userId);
                            fd[i].append('file', filesArr[i]);
                            fd[i].append('grId', $scope.requestOrderDetail.id);
                            fd[i].append('fileName', filesArr[i].name);
                            if (metaDataService.isImage(fileExt.toLowerCase())) {
                                $http({
                                    url: apiJson.urls.goodsReceivedManagement.uploadProofOfRejection,
                                    method: 'POST',
                                    data: fd[i],
                                    headers: {'Content-Type': undefined},
                                    transformRequest: angular.identity
                                }).success(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    if (response.fileUrl !== null) {
                                        $scope.previewDocument = true;
                                        $scope.porImagesUrl.push(response.documentId);
                                        if(filesArr.length-1 ===count){
                                            callback();
                                        }
                                        count +=1;
                                    } else {
                                        $toastService.create("Image Upload failed");
                                    }
                                }).error(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    $toastService.create("Image Upload failed");
                                    return;
                                });
                            } else {
                                $toastService.create('Upload Failed , File Format not Supported, Please upload images only');
                                return;
                            }
                        }

                    }
                    $scope.uploadImage(fd, index, filesArr);
                }
            }

            function setSkuToProducts(requestOrderItems) {
                var productIds = [];
                requestOrderItems.map(function (item) {
                    productIds.push(item.productId);
                })
                $http({
                    method: "POST",
                    url: apiJson.urls.productManagement.skuDetailsByUnitAndProduct + "?unitId=" + appUtil.getCurrentUser().unitId,
                    data: productIds
                }).then(function success(response) {
                    $scope.skuProductMapNew = response.data;
                    $scope.initializeSkus(requestOrderItems);
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.enterNumberOfRejectedUnits = function (item,val,pgd) {
                if (pgd.numberOfUnitsRejected < 0) {
                    $toastService.create("Units rejected Cannot Be Negative ");
                    pgd.numberOfUnitsRejected = 0;
                    pgd.numberOfUnitsReceived =pgd.numberOfUnitsPacked;
                    pgd.receivedQuantity =pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                    return;
                }
                if (pgd.numberOfUnitsRejected > pgd.numberOfUnitsPacked) {
                    $toastService.create("Units rejected Cannot Be Greater than Units Packed ");
                    pgd.numberOfUnitsRejected = 0;
                    pgd.numberOfUnitsReceived =pgd.numberOfUnitsPacked;
                    pgd.receivedQuantity =pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                    return;
                }
                if (pgd.numberOfUnitsPacked == undefined) {
                    clearTimeout($scope.timeout);
                    return;
                }
               pgd.numberOfUnitsRejected = val;
                pgd.numberOfUnitsReceived =pgd.numberOfUnitsPacked-pgd.numberOfUnitsRejected;
                pgd.receivedQuantity =(pgd.numberOfUnitsPacked-pgd.numberOfUnitsRejected) * pgd.packagingDefinitionData.conversionRatio;
            }

            $scope.onReject = function (trItem, index, item,pgd) {
                pgd.numberOfUnitsRejected = 0;
                pgd.numberOfUnitsReceived =pgd.numberOfUnitsPacked;
                pgd.receivedQuantity =pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                $scope.requestOrderDetail.requestOrderItems.map(function (product) {
                    if (item.productId == product.productId) {
                        product.rejectedQuantity = 0;
                        product.enableRejectionInput = !product.enableRejectionInput;
                    }
                })
            };

            $scope.initializeSkus = function (requestOrderItems) {
                if (requestOrderItems != null && requestOrderItems.length > 0) {
                    requestOrderItems.forEach(function (roItem) {
                        roItem.skuList = $scope.skuProductMapNew[roItem.productId].filter(function (sku) {
                            return sku.skuStatus == 'ACTIVE';
                        });
                        roItem.skuList.forEach(function (sku) {
                            sku.skuPackagings = sku.skuPackagings.filter(function (mapping) {
                                var mappedPackagingId;
                                if ($scope.unitSkuProductPackagingMap[roItem.productId] != null && $scope.unitSkuProductPackagingMap[roItem.productId] != undefined) {
                                    mappedPackagingId = $scope.unitSkuProductPackagingMap[roItem.productId];
                                }
                                return (mapping.mappingStatus == "ACTIVE" && mapping.packagingId == mappedPackagingId);
                            });
                            sku.skuPackagings.forEach(function (packaging) {
                                packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                            });
                            sku.skuPackagings = $scope.filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);

                        });
                        roItem.selectedSku = $scope.initializeSelectedSku(roItem.skuList);
                        roItem.trPackaging = $scope.setDefaultPackagings(roItem, roItem.selectedSku);
                        var qty = 0;
                        roItem.trPackaging.forEach(function (trp) {
                            var pkgqty = 0;
                            trp.packagingDetails.forEach(function (pgd) {
                                pkgqty += pgd.transferredQuantity;
                            });
                            trp.transferredQuantity = parseFloat(pkgqty).toFixed(6);
                            qty += trp.transferredQuantity;
                        });
                        roItem.transferredQuantity = parseFloat(qty).toFixed(6);
                        roItem.receivedQuantity = parseFloat(qty).toFixed(6);
                        roItem.enableRejectionInput = false;
                    })
                }
            }

            $scope.filterLoosePackaging = function (pkgList, looseOrdering) {
                var ret = pkgList;
                if (!looseOrdering) {
                    var pkgs = [];
                    pkgList.forEach(function (pkg) {
                        if (pkg.packagingDefinition.packagingType != "LOOSE") {
                            pkgs.push(pkg);
                        }
                    });
                    ret = pkgs;
                }
                return ret;
            }

            $scope.initializeSelectedSku = function (skuList) {
                var ret = null;
                if (skuList.length == 1) {
                    ret = skuList[0];
                } else {
                    skuList.forEach(function (item) {
                        if (item.isDefault) {
                            ret = item;
                        }
                    });
                }
                return ret;
            }

            $scope.setDefaultPackagings = function (roItem, sku) {
                var pgkDetails = [];
                var qty = roItem.requestedQuantity;
                qty = getCalculatedNumbers(sku, "CASE", qty, pgkDetails);
                if (qty > 0) {
                    qty = getCalculatedNumbers(sku, "INNER", qty, pgkDetails);
                }
                if (qty > 0) {
                    getCalculatedNumbers(sku, "LOOSE", qty, pgkDetails);
                }
                var trPackaging = [];
                var pkg = null;
                if (pgkDetails.length > 0) {
                    pkg = {
                        id: null,
                        skuId: sku.skuId,
                        skuName: sku.skuName,
                        packagingDetails: [],
                        requestedQuantity: roItem.requestedQuantity,
                        requestedAbsoluteQuantity: roItem.requestedAbsoluteQuantity,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: sku.unitOfMeasure,
                        unitPrice: (roItem.vendor != null && roItem.vendor.id == 22) ? null : sku.unitPrice,
                        negotiatedUnitPrice: (roItem.vendor != null && roItem.vendor.id == 22) ? null : sku.negotiatedUnitPrice,
                        requestOrderItemId: roItem.id,
                        purchaseOrderItemId: null,
                        goodReceivedItemId: null,
                    };
                    pkg.packagingDetails = pgkDetails;
                    trPackaging.push(pkg);
                }
                return trPackaging;
            }

            function getCalculatedNumbers(sku, type, qty, pgkDetails) {
                var ret = qty;
                var packagingList = sortPackaging(sku.skuPackagings, type);
                if (sku.skuId == 205) {
                    console.log(packagingList);
                }
                packagingList.forEach(function (item) {
                    var unitsPacked = 0;
                    if (type == item.packagingDefinition.packagingType) {
                        if (type == "LOOSE") {
                            if ((ret / item.packagingDefinition.conversionRatio) > 0) {
                                unitsPacked = (ret / item.packagingDefinition.conversionRatio);
                                ret = ret - (unitsPacked * item.packagingDefinition.conversionRatio);
                            }
                        } else {
                            if (parseInt(ret / item.packagingDefinition.conversionRatio) > 0) {
                                unitsPacked = parseInt(ret / item.packagingDefinition.conversionRatio);
                                ret = Math.round((ret - (unitsPacked * item.packagingDefinition.conversionRatio)) * 1000000) / 1000000;
                            }
                        }
                        if (unitsPacked > 0) {
                            pgkDetails.push({
                                id: null,
                                packagingDefinitionData: item.packagingDefinition,
                                numberOfUnitsPacked: unitsPacked,
                                numberOfUnitsReceived: unitsPacked,
                                transferredQuantity: unitsPacked * item.packagingDefinition.conversionRatio,
                                receivedQuantity: unitsPacked * item.packagingDefinition.conversionRatio,
                                numberOfUnitsRejected:0,
                                rejectionReason:null
                            });
                        }
                    }
                });
                return ret;
            }

            function sortPackaging(packagings, type) {
                var pkgList = [];
                packagings.forEach(function (pkg) {
                    if (pkg.packagingDefinition.packagingType == type) {
                        pkgList.push(pkg);
                    }
                });
                pkgList.sort(function (a, b) {
                    return b.packagingDefinition.conversionRatio - a.packagingDefinition.conversionRatio;
                });
                return pkgList;
            }

            function generateTOItems(requestOrderDetail) {
                var trItems = [];
                var requestOrderItems = typeof requestOrderDetail.requestOrderItems == "object" ?
                    Object.values(requestOrderDetail.requestOrderItems) : requestOrderDetail.requestOrderItems;
                requestOrderItems.forEach(function (roi) {
                    roi.trPackaging.forEach(function (item) {
                        if (item.transferredQuantity != null) {
                            item.excessQuantity = roi.excessQuantity;
                            item.productId = roi.productId;
                            if (roi.expiryDate != null && roi.expiryDate != undefined) {
                                item.roItemExpiryDate = roi.expiryDate;
                            }
                            var rcvQty = 0;
                            item.packagingDetails.map(function (pkg) {
                                rcvQty += pkg.numberOfUnitsReceived * pkg.packagingDefinitionData.conversionRatio;
                            })
                            item.receivedQuantity = rcvQty;
                            trItems.push(item);
                        }
                    })
                });
                return trItems;
            }

            function getToType() {
                return 'REGULAR_TRANSFER';
            }

            $scope.updatePackagingQty = function (pgd, trItem, roItem) {
                if (pgd.numberOfUnitsPacked < 0) {
                    $toastService.create("Units packed Cannot Be Negative ");
                    pgd.numberOfUnitsPacked = 0;
                    pgd.transferredQuantity = 0;
                    pgd.numberOfUnitsRejected=0;
                    pgd.numberOfUnitsReceived=0;
                    pgd.receivedQuantity =0;
                }
                if (pgd.numberOfUnitsPacked == undefined) {
                    clearTimeout($scope.timeout);
                    return;
                }
                var totalQty = 0;
                for (var i = 0; i < trItem.packagingDetails.length; i++) {
                    if (!appUtil.isEmptyObject(trItem.packagingDetails[i].numberOfUnitsPacked)) {
                        totalQty += (trItem.packagingDetails[i].numberOfUnitsPacked * trItem.packagingDetails[i].packagingDefinitionData.conversionRatio);
                    }
                }
                updatePkg(trItem, pgd, roItem);
            };

            function updatePkg(trItem, pgd, roItem) {
                if (pgd.packagingDefinitionData.packagingType != "LOOSE") {
                    pgd.numberOfUnitsPacked = parseInt(pgd.numberOfUnitsPacked);
                }
                pgd.transferredQuantity = pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                pgd.numberOfUnitsReceived =pgd.numberOfUnitsPacked -pgd.numberOfUnitsRejected;
                pgd.receivedQuantity =(pgd.numberOfUnitsPacked -pgd.numberOfUnitsRejected)  * pgd.packagingDefinitionData.conversionRatio;
                pgd.transferredQuantity = parseFloat(pgd.transferredQuantity.toFixed(6));
                if (roItem.freshQuantity != undefined || roItem.shortExpiry != undefined) {
                    var totalQty = roItem.freshQuantity != undefined ? roItem.freshQuantity : 0;
                    totalQty = totalQty + (roItem.shortExpiry != undefined ? roItem.shortExpiry : 0);
                    var qty = null;
                    trItem.packagingDetails.forEach(function (item) {
                        qty += item.transferredQuantity;
                    });
                    qty = parseFloat(qty.toFixed(6));
                    if (totalQty < qty) {
                        $toastService.create("Total Quantity cannot be greater than " + totalQty);
                        pgd.numberOfUnitsPacked = 0;
                        pgd.transferredQuantity = 0;
                        pgd.numberOfUnitsReceived=0;
                        pgd.receivedQuantity=0;
                    }
                }
                $scope.updateTrItemQty(trItem, roItem);
            }

            $scope.updateTrItemQty = function (trItem, roItem) {
                var qty = null;
                var rcvQty =null;
                trItem.packagingDetails.forEach(function (item) {
                    qty += item.transferredQuantity;
                    rcvQty += item.receivedQuantity;
                });
                trItem.transferredQuantity = qty;
                trItem.receivedQuantity =rcvQty;
                $scope.updateRoItemQty(roItem);
            };

            $scope.updateRoItemQty = function (roItem) {
                var qty = null;
                var rcvQty =null;
                roItem.trPackaging.forEach(function (item) {
                    qty += item.transferredQuantity;
                    rcvQty += item.receivedQuantity;
                });
                roItem.transferredQuantity = qty;
                roItem.receivedQuantity =rcvQty;
            };

            $scope.createTransferOrderObject = function () {
                $scope.getImageS3Links(function(){
                    $scope.submitted = true;
                    var to = {
                        id: null,
                        generationTime: null,
                        initiationTime: null,
                        lastUpdateTime: null,
                        generationUnitId: appUtil.createRequestUnit(),
                        generatedForUnitId: $scope.requestOrderDetail.requestUnit,
                        generatedBy: appUtil.createGeneratedBy(),
                        lastUpdatedBy: appUtil.createGeneratedBy(),
                        status: "CREATED",
                        comment: $scope.comment,
                        requestOrderId: $scope.requestOrderDetail.id,
                        sourceCompany: $scope.fulfillmentCompany,
                        receivingCompany: $scope.requestCompany,
                        transferOrderItems: generateTOItems($scope.requestOrderDetail),
                        toType: getToType()
                    };
                    if (to.transferOrderItems.length == 0) {
                        if (window.confirm("This transfer order contains no items. Are you sure you want to transfer?")) {
                            $scope.sendTOForCreation(to);
                        }
                    } else {
                        $scope.sendTOForCreation(to);
                    }
                    $scope.submitted = false;
                })

            };

            $scope.sendTOForCreation = function (to) {
                var url = apiJson.urls.transferOrderManagement.transferOrderAndGr;
                var data = to;
                data.docIdsPorImages ="";
                if($scope.porImagesUrl !=null)
                data.docIdsPorImages = $scope.porImagesUrl.join(",");
                $http({
                    method: "POST",
                    url: url,
                    data: data
                }).then(function success(response) {
                    if (response.data != null) {
                        if (response.data != null && response.data > 0) {
                            if (response.data == 1) {
                                $toastService.create("Production Booking for Invoice created successfully!");
                            } else {
                                $toastService.create("Transfer order with id " + response.data + " created successfully!");
                            }
                            $scope.init($scope.fulfillmentDate);
                        } else {
                            if (response.errorCode != 108) {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }
                    }
                }, function error(response) {
                    if (response.data.errorCode != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () {
                        }, true);
                    } else {
                        $toastService.create("Could not create transfer. Please try again later");
                        console.log("error:" + response);
                    }
                });
            };


        }]).controller('toQtyValidationCtrl', ['$scope', 'toItem', 'pkg', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window',
        function ($scope, toItem, pkg, appUtil, $toastService, apiJson, $http, Popeye, $window) {
            $scope.init = function () {
                $scope.toItem = toItem;
                $scope.pkg = pkg;
                $scope.pkgClone = angular.copy($scope.pkg);
                $scope.pkgClone.numberOfUnitsPacked = 0;
                $scope.showClubROBtn = false;
            }

            $scope.submit = function () {
                if ($scope.pkgClone.numberOfUnitsPacked != $scope.pkg.numberOfUnitsPacked) {
                    Popeye.closeCurrentModal();
                } else {
                    Popeye.closeCurrentModal({success: true});
                }
            }

            $scope.closeModal = function () {
                Popeye.closeCurrentModal();
            };

        }
    ]
).controller('scanViewModalCtrl', ['$scope', 'diffs', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', function ($scope, diffs, appUtil, $toastService, apiJson, $http, Popeye) {
    $scope.diffs = diffs;
    $scope.cancel = function () {
        Popeye.closeCurrentModal(true);
    };
    $scope.submit = function () {
        Popeye.closeCurrentModal(true);
    }
}]);
