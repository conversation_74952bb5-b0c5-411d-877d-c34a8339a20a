/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('trOrderCreateBulkCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService', '$alertService','metaDataService','previewModalService','Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $alertService,metaDataService, previewModalService, Popeye) {
            var manualBookProductId=100217;

            initializeBillBookParams();


            $scope.init = function (date) {
                $scope.availableSKUList = [];
                $scope.getAvailableSKUs();
                $scope.isEmpty = appUtil.isEmptyObject;
                $scope.clubbedRoIds = [];
                $scope. showClubROBtn = false;
                $scope.showPendingRequest = true;
                $scope.isInvoiceRo = false;
                $scope.requestOrderDetail = null;
                $scope.isWarehouse = appUtil.isWarehouse();
                $scope.skuProductMap = appUtil.getSkuProductMap();
                $scope.activeProducts = appUtil.getActiveScmProducts();
                $scope.packagingMap = appUtil.getPackagingMap();
                $scope.selectedDate = appUtil.isEmptyObject(date)
                    ?  appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd") : date;
                $scope.minDate = appUtil.formatDate(appUtil.getDate(-30), "yyyy-MM-dd");
                $scope.maxDate = appUtil.formatDate(appUtil.getDate(7), "yyyy-MM-dd");
                $scope.getPendingRequestOrders($scope.selectedDate);
                $scope.showPreview = previewModalService.showPreview;
                $scope.toTypeList = [
                    "FIXED_ASSET_TRANSFER",
                    "BROKEN_ASSET_TRANSFER",
                    "RENOVATION_ASSET_TRANSFER"
                ],
                $scope.productToSkuMapping = {};
                $scope.skuToPackagingMapping = new Map();
                $scope.productSkuObjects = [];
                $scope.showPreviewScreen = false;
                $scope.allSelected = false;
                $scope.isNotDefaultSkuFound = false;
                $scope.selectedUnitsCount = new Map();
                $scope.selectedROCount = 0;
                $scope.requestOrderList = [];

            };


            $scope.showNextScreen = function(){
                $scope.showPreviewScreen = true;
                var productSkus = {};
                for(var productId in $scope.productToSkuMapping){
                    productSkus[productId] = $scope.productToSkuMapping[productId].skuId;
                }
                getPackagingDistribution(productSkus);
                //$scope.calculateTransferDistribution();
            };

            $scope.setROCount = function(ro,isSelected) {
                if(isSelected){
                    $scope.selectedROCount++;
                    if(!$scope.selectedUnitsCount.has(ro.requestUnit.id)){
                        $scope.selectedUnitsCount.set(ro.requestUnit.id,1);
                    }else{
                        $scope.selectedUnitsCount.set(ro.requestUnit.id,$scope.selectedUnitsCount.get(ro.requestUnit.id)+1);
                    }

                }else{
                    $scope.selectedROCount--;
                    $scope.selectedUnitsCount.set(ro.requestUnit.id,$scope.selectedUnitsCount.get(ro.requestUnit.id)-1);
                    if($scope.selectedUnitsCount.get(ro.requestUnit.id) == 0 ){
                       $scope.selectedUnitsCount.delete(ro.requestUnit.id);
                    }
                }
            }

            $scope.getAvailableSKUs = function() {
                $http({
                    method : "GET",
                    url : apiJson.urls.filter.availableSKUs,
                    params : {
                        unitId : appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.availableSKUList = response.data;
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.bySKUs = function(sku) {
                return sku != null && sku != undefined && $scope.availableSKUList != undefined && $scope.availableSKUList != null
                    && $scope.availableSKUList.indexOf(sku.skuId) > -1;
            };

            $scope.selectOpenRo = function(ro, type){
                if(ro.transferType == 'INVOICE'){
                    $scope.isInvoiceRo = true;
                }
                $scope.selectRequest(ro);
            }
            $scope.selectRequest = function(request){
                if(request.transferType == 'INVOICE'){
                    request.checked= false;
                    $scope.isInvoiceRo = false;
                    $toastService.create("INVOICE RO's cannot be clubbed together!");
                    return;
                }

                ///$scope.setROType(request , request.checked);
                //in case you need to un-club the RO
                if(!request.checked){
                    if(!appUtil.isEmptyObject($scope.clubbedRoIds)){
                        var index = $scope.clubbedRoIds.indexOf(request.id);
                        if(index!=-1){
                            $scope.clubbedRoIds.splice(index,1);
                            $scope.setROCount(request,request.checked);
                            request.firstChecked = false;
                        }

                    }
                }else{
                    if(!checkInRoArray($scope.clubbedRoIds,request)){
                        $scope.clubbedRoIds.push(request.id);
                        $scope.setROCount(request,request.checked);
                    }
                }
                $scope.showClubROBtn =   $scope.clubbedRoIds.length > 1;

            };

            function checkInRoArray(roList,ro) {
                var found = false;
                for(var i=0; i < roList.length; i++){
                    var reqId = roList[i];
                    if(reqId === ro.id){
                        found = true; // found RO at i
                        break;
                    }
                }
                return found;
            }

            $scope.selectAllROs = function (requestOrderList ){

                if(!appUtil.isEmptyObject(requestOrderList)){
                    for(var request in requestOrderList){
                        requestOrderList[request].checked = !$scope.allSelected;
                        $scope.selectOpenRo(requestOrderList[request],false);
                    }
                    $scope.allSelected = !$scope.allSelected;
                }
            }

            $scope.selectAllUnitRos = function (unitId ,  index){
                var isAllSelected = $scope.requestOrderList[index].checked;
                for(var i = index ; i <$scope.requestOrderList.length;i++){
                    $scope.requestOrderList[i].checked = !isAllSelected;
                    $scope.selectOpenRo($scope.requestOrderList[i],false);
                    if(i!= $scope.requestOrderList.length - 1 && $scope.requestOrderList[i+1].requestUnit.id != $scope.requestOrderList[i].requestUnit.id){
                        break;
                    }
                }
            }


            $scope.getSelectedUnitCount = function (){
                return $scope.selectedUnitsCount.size;
            }

            $scope.initSKU = function (productList){
                $scope.skuShow = false;
                if(!appUtil.isEmptyObject(productList)){
                    for(var i =0;i<productList.length;i++){
                        if(productList[i].skuList.length>1){
                            $scope.skuShow = true;
                            break;
                        }
                    }
                }
            }

            $scope.getAggregateQuantity = function (productId){
                var requestOrderItems = Object.values($scope.requestOrderItems[productId]);
                var result = 0;
                for(var i =0;i<requestOrderItems.length;i++){
                    result = result + requestOrderItems[i].requestedQuantity;
                    result.toPrecision(4);
                }
                return result.toPrecision(4);

            }

            $scope.getAggregateAbsoluteQuantity = function (productId){
                var requestOrderItems = Object.values($scope.requestOrderItems[productId]);
                var result = 0;
                for(var i =0;i<requestOrderItems.length;i++){
                    result = result + requestOrderItems[i].requestedAbsoluteQuantity;
                    result.toPrecision(4);
                }
                return result.toPrecision(4);
            }

            $scope.getUnitsCount = function (productId){
                return Object.values($scope.requestOrderItems[productId]).length;

            }

            $scope.clubAllROs = function(){
                var clubbedROItems = {};
                $http({
                    method: "POST",
                    url: apiJson.urls.transferOrderManagement.getSkuToProducts,
                    data : {
                        unitId : appUtil.getCurrentUser().unitId,
                        roIds : $scope.clubbedRoIds
                    },
                    params : {
                        isPackagingRequired : false
                    }
                }).then(function success(response) {
                    console.log("response : ",response );
                    $scope.requestOrderItems = response.data.orderItems;
                    var productToSkus = response.data.productToSkus;
                    var productList = Object.keys(productToSkus)
                    setSkuToProducts(productList ,productToSkus );
                }, function error(response) {
                    console.log("error:" + response);
                });

                $scope.showPendingRequest = false;
                $scope.comment = null;

            };

            function getPackagingDistribution(productToSkus){
                $http({
                    method: "POST",
                    url: apiJson.urls.transferOrderManagement.getSkuToProducts,
                    data : {
                        unitId : appUtil.getCurrentUser().unitId,
                        roIds : $scope.clubbedRoIds,
                        productToSkuMap : productToSkus
                    },
                    params : {
                        isPackagingRequired : true
                    }
                }).then(function success(response) {
                    console.log("response : ",response );
                    $scope.productUnitPackaging = response.data.productUnitPackaging;
                    $scope.calculateTransferDistribution();
                }, function error(response) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg,function () {}, true);
                    $scope.init();
                    console.log("error:" + response);
                });
            }

            $scope.getPendingRequestOrders = function(fulfilmentDate){
                fulfilmentDate = $scope.isEmpty(fulfilmentDate) ? $scope.selectedDate : fulfilmentDate;
                if(fulfilmentDate==null){
                    $toastService.create("Please select a fulfilment date before ");
                    return false;
                }

                $http({
                    method: "GET",
                    url: apiJson.urls.requestOrderManagement.pendingRequestOrderShort
                        + "?unitId=" + appUtil.getCurrentUser().unitId + "&date="+fulfilmentDate
                }).then(function success(response) {
                    $scope.requestOrderList = response.data;
                    if($scope.requestOrderList.length>0){
                        $scope.fulfillmentDate = $scope.requestOrderList[0].fulfillmentDate;
                    }
                    $scope.requestOrderList = $scope.requestOrderList.sort(sortByFulfillmentDate);
                    $scope.requestOrderList = filterByAssetAndCapexOrder($scope.requestOrderList);
                    $scope.requestOrderList.length==0?$scope.showMessage = true:$scope.showMessage = false;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };



            function filterByAssetAndCapexOrder(requestOrderList){
                var nonAssetAndNonCapexRequestOrders = []
                if(!appUtil.isEmptyObject(requestOrderList)){
                    for(var i =0;i<requestOrderList.length;i++){
                        if(!requestOrderList[i].assetOrder && requestOrderList[i].type != 'CAPEX'){
                            nonAssetAndNonCapexRequestOrders.push(requestOrderList[i]);
                        }
                        if(i===0){
                            if(i!= requestOrderList.length-1 && requestOrderList[i+1].requestUnit.id === requestOrderList[i].requestUnit.id ){
                                requestOrderList[i].isFirst = true;
                            }else{
                                requestOrderList[i].isFirst = false;
                            }

                        }else if(requestOrderList[i-1].requestUnit.id != requestOrderList[i].requestUnit.id){
                            if(i!= requestOrderList.length-1 && requestOrderList[i+1].requestUnit.id === requestOrderList[i].requestUnit.id ){
                                requestOrderList[i].isFirst = true;
                            }else {
                                requestOrderList[i].isFirst = false;
                            }
                        }else {
                            requestOrderList[i].isFirst = false;
                        }
                    }

                }
                return nonAssetAndNonCapexRequestOrders;
            }

            function sortByFulfillmentDate(a,b){
                var diff = b.fulfillmentDate - a.fulfillmentDate;
                if(diff==0){
                    return sortByUnit(a,b);
                }
                return diff>0 ? 1 : -1;
            }


            function sortByUnit(x,y){

                if(x.requestUnit.name < y.requestUnit.name) return -1;
                if(x.requestUnit.name > y.requestUnit.name) return 1;
                return 0;
            }


            //EDIT
            $scope.checkSemiFinshedProducts = function(requestOrderItems){
                $scope.semiFinishedProducts = [];
                var products = appUtil.getActiveScmProducts();
                requestOrderItems.forEach(function(roItem){
                    var i = 0;
                    var p = null;
                    for(i in products){
                        if(products[i].productId == roItem.productId){
                            p = products[i];
                            break;
                        }
                    }
                    if(p != null && p.categoryDefinition.code == 'SEMI_FINISHED'){
                        $scope.semiFinishedProducts.push(roItem);
                    }
                });
            };

            $scope.openShortExpiryModal = function(){
                if(!appUtil.isEmptyObject($scope.semiFinishedProducts)){
                    var mappingModal = Popeye.openModal({
                        templateUrl: "views/trOrderShortExpiry.html",
                        controller: "trOrderShortExpiryCtrl",
                        modalClass:"semifinishedItemsModal",
                        resolve:{
                            data:function(){
                                return $scope.semiFinishedProducts;
                            }
                        },
                        click:false,
                        keyboard:false
                    });

                    mappingModal.closed.then(function(data){
                        if(data== null || data.dataStatus == 'CANCEL'){
                            $scope.backTransfer();
                        } else {
                            $scope.semiFinishedProducts = data.semiFinishedProducts;
                            $scope.updateTOwithShortExpiry($scope.semiFinishedProducts, data.expiryData);
                        }
                    });
                }else{
                    $toastService.create("Please select a default mapping first!");
                }
            };


            // SEMI-FINISHED EXPIRY ADDED HERE
            $scope.updateTOwithShortExpiry = function(semiFinishedProducts, expiryData){

                if(semiFinishedProducts == null || semiFinishedProducts.length == 0 ){
                    return;
                }

                var i=0;
                var j=0;

                for(i in semiFinishedProducts){
                    var item = semiFinishedProducts[i];
                    for(j in $scope.requestOrderDetail.requestOrderItems){
                        var roItem = $scope.requestOrderDetail.requestOrderItems[j];

                        if(item.productId == roItem.productId){
                            //add packaging
                            var rOrderItem = $scope.requestOrderDetail.requestOrderItems[j];
                            var trItem = $scope.requestOrderDetail.requestOrderItems[j].trPackaging[0];
                            var pkg = trItem.packagingDetails[0];
                            var value = 0;
                            if(item.shortExpiry == null || item.shortExpiry == undefined){
                                value = item.freshQuantity;
                            } else {
                                value = item.freshQuantity + item.shortExpiry
                            }
                            //pkg.numberOfUnitsPacked = value;
                            pkg.numberOfUnitsPacked = value/pkg.packagingDefinitionData.conversionRatio;
                            var e = 0;
                            for(e in expiryData.inventoryItems){
                                var exp = expiryData.inventoryItems[e];
                                if(trItem.skuId == exp.keyId){
                                    if(trItem.drillDowns == null){
                                        trItem.drillDowns =[];
                                    }
                                    trItem.drillDowns = trItem.drillDowns.concat(exp.drillDowns);
                                }
                            }
                            $scope.updatePackagingQty(pkg,trItem,roItem)
                        }
                    }
                }

            };


            $scope.toBeHidden = function (isPreviewScreen){
                var result = isPreviewScreen ? $scope.showPreviewScreen ? true : false  : $scope.showPendingRequest && !$scope.showPreviewScreen ? true : false;
                return result;

            }

            function mapToObj(tempMap) {
                var obj = {};
                Array.from(tempMap.keys()).forEach(function (key){

                   obj[key] = Object.fromEntries(tempMap.get(key));
                });
                return obj;
            }




            $scope.sendClubbedROs = function (){
                if($scope.skuToPackagingMapping.size>0){
                    createBulkTo();
                }else{
                    if(window.confirm("This transfer order contains no items. Are you sure you want to transfer?")){
                     createBulkTo();
                    }
                }
            }

            function createBulkTo(){
                var url = apiJson.urls.transferOrderManagement.bulkClubbedTransferOrder;
                console.log("fulfillemnt date",$scope.fulfillmentDate)
                if(!appUtil.isEmptyObject($scope.clubbedRoIds)){
                    console.log($scope.requestCompany);
                    var data = {
                        clubRoIds:$scope.clubbedRoIds,
                        skuToPackagingMapping : mapToObj($scope.skuToPackagingMapping),
                        fulfilmentDate:appUtil.formatDate($scope.fulfillmentDate, "yyyy-MM-dd"),
                        generationUnit : {
                            id : appUtil.getUnitData().id,
                            code : null,
                            name : appUtil.getUnitData().name
                        },
                        generatedBy: appUtil.createGeneratedBy(),
                        lastUpdatedBy: appUtil.createGeneratedBy(),
                    }
                }
                $http({
                    method: "POST",
                    url: url,
                    data: data
                }).then(function success(response) {
                    console.log(response);
                    var data = response.data;
                    if(data!=null) {
                        if(!data.invoiceSet){
                            $alertService.alert("Invoices Not Set While Creating TO's", "Transfer Order with Ids : " + data.toIds + "Created Successfully , On Clicking Ok Invoices Will be Set "
                            ,function (){
                                    $scope.setInvoices(data.toIds,data.eventId);
                            });
                        }else{
                            $alertService.alert("Successfully Created TO's", "Transfer Order with Ids : " + data.toIds + "Created Successfully"
                                ,function (){},false);
                        }
                        $toastService.create("Transfer order with id " + response.data + " created successfully!");
                        $scope.init($scope.selectedDate);
                    }else{
                        if(response.errorCode != 108){
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    }

                }, function error(response) {
                    if(response.data.errorCode!=null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg,function () {}, true);
                    }else{
                        $toastService.create("Could not create transfer. Please try again later");
                        console.log("error:" + response);
                    }
                });

            }


            function setSkuToProducts(productList , productToSkus){
                productList.forEach(function(productId){
                    var product = {};
                    product.productId = productId;
                    product.skuList = productToSkus[productId];
                    product.skuList.forEach(function(sku){
                        sku.skuPackagings = sku.skuPackagings.filter(function (mapping) {
                            return mapping.mappingStatus == "ACTIVE";
                        });
                        sku.skuPackagings.forEach(function(packaging){
                            packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                        });
                        sku.skuPackagings = $scope.filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);
                    });
                    //roItem.skuList = $scope.removeInactive(roItem.skuList);  //TODO add this check back when everything is stable
                    product.selectedSku = $scope.initializeSelectedSku(product.skuList);
                    product.isDefaultSet = true;
                    if(appUtil.isEmptyObject(product.selectedSku)){
                        product.isDefaultSet = false;
                        $scope.isNotDefaultSkuFound = true;
                        product.name = product.skuList[0].linkedProduct.name;
                        product.unitOfMeasure = product.skuList[0].unitOfMeasure;
                    }
                    $scope.productToSkuMapping[product.productId] = product.selectedSku;
                    $scope.productSkuObjects.push(product);
                });
            }

            $scope.setInvoices = function (toIds , eventId) {
                if (toIds.length> 0) {
                    $http({
                        url: apiJson.urls.transferOrderManagement.setTransferInvoices,
                        method: "POST",
                        data : toIds,
                        params : {
                            eventId : eventId
                        }
                    }).then(function (response) {
                        if (!appUtil.checkEmpty(response)) {
                            if(response.data){
                                $alertService.alert("Success ","Successfully inserted Invoices ids for All Transfer Orders");
                            }
                        }
                    }, function (error) {
                        $alertService.alert("error while inserting invoices ids " , "Please Try Again in Bulk Transfer Management Screen With Event ID : " + eventId , function (){},true);
                        console.log(error);
                    });
                } else {
                    $toastService.create("Select a bulk Event First");
                }
            };

            $scope.removeInactive = function(skuList){
                var skus = [];
                skuList.forEach(function(sku){
                    if(sku.skuStatus=='ACTIVE'){
                        var pkgs = [];
                        sku.skuPackagings.forEach(function(packaging){
                            if(packaging.mappingStatus=='ACTIVE' && packaging.packagingDefinition.packagingStatus=='ACTIVE'){
                                pkgs.push(packaging);
                            }
                        });
                        sku.skuPackagings = pkgs;
                        skus.push(sku);
                    }
                });
                return skus;
            };

            $scope.initializeSelectedSku = function(skuList){
                var ret = null;
                if(skuList.length==1){
                    ret = skuList[0];
                }else{
                    skuList.forEach(function (item) {
                        if(item.isDefault){
                            ret = item;
                        }
                    });
                }
                return ret;
            }

            $scope.filterLoosePackaging = function(pkgList, looseOrdering){
                var ret = pkgList;
                if(!looseOrdering){
                    var pkgs = [];
                    pkgList.forEach(function (pkg) {
                        if(pkg.packagingDefinition.packagingType!="LOOSE"){
                            pkgs.push(pkg);
                        }
                    });
                    ret = pkgs;
                }
                return ret;
            }

            $scope.setDefaultPackagings  = function (roItem, sku) {
                var pgkDetails = [];
                var qty = roItem.requestedQuantity;
                qty = getCalculatedNumbers(sku, "CASE", qty, pgkDetails);
                if(qty>0){
                    qty = getCalculatedNumbers(sku, "INNER", qty, pgkDetails);
                }
                if(qty>0){
                    getCalculatedNumbers(sku, "LOOSE", qty, pgkDetails);
                }
                var trPackaging = [];
                var pkg = null;
                if(pgkDetails.length>0){
                    pkg = {
                        id: null,
                        skuId: sku.skuId,
                        skuName: sku.skuName,
                        packagingDetails: [],
                        requestedQuantity: roItem.requestedQuantity,
                        requestedAbsoluteQuantity: roItem.requestedAbsoluteQuantity,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: sku.unitOfMeasure,
                        unitPrice: (roItem.vendor!=null && roItem.vendor.id==22)?null:sku.unitPrice,
                        negotiatedUnitPrice: (roItem.vendor!=null && roItem.vendor.id==22)?null:sku.negotiatedUnitPrice,
                        requestOrderItemId: roItem.id,
                        purchaseOrderItemId: null,
                        goodReceivedItemId: null
                    };
                    pkg.packagingDetails = pgkDetails;
                    trPackaging.push(pkg);
                }
                return trPackaging;
            }

            function sortPackaging(packagings, type){
                var pkgList = [];
                packagings.forEach(function (pkg) {
                    if(pkg.packagingDefinition.packagingType==type){
                        pkgList.push(pkg);
                    }
                });
                pkgList.sort(function (a,b) {
                    return b.packagingDefinition.conversionRatio - a.packagingDefinition.conversionRatio;
                });
                return pkgList;
            }

            $scope.updateBillBookDetailsObject=function(billBookDetails){
                $scope.manualBillBookDetails=billBookDetails;
                $scope.isBillBooksDetailFilled=false;
            }

            $scope.toggleBillBookDetailsView=function(){
                $scope.addBillBookDetails[manualBookProductId]=!$scope.addBillBookDetails[manualBookProductId];
            }

            $scope.fillManualBBDetails=function(trItem,$index, item){
                $scope.toggleBillBookDetailsView();
            };
            $scope.manualDetailsRequired=function(){
                if($scope.isBillBooksDetailFilled===undefined){
                    $scope.isBillBooksDetailFilled=true;
                }
            };

            function initializeBillBookParams(){
                $scope.manualBillBookDetails=undefined;
                $scope.isBillBooksDetailFilled=undefined;
                $scope.addBillBookDetails={};
                $scope.addBillBookDetails[manualBookProductId]=false;
            }

            $scope.resetValues = function (){
                $scope.selectedROCount = 0;
                $scope.selectedUnitsCount = new Map();
                $scope.showPendingRequest=true;
                $scope.isInvoiceRo = false;
                $scope.requestOrderDetail = null;
                $scope.semiFishedProducts = null;
                $scope.clubbedROs = {};
                $scope.arrayAfterClubbing = {};
                $scope.clubbedRoIds = [];
                $scope.productSkuObjects = [];
                $scope.productToSkuMapping = {};
                $scope.skuToPackagingMapping = new Map();
                $scope.showPreviewScreen = false;
                $scope.allSelected = false;
                $scope.isNotDefaultSkuFound = false;


                $scope.getPendingRequestOrders($scope.selectedDate);
            }

            $scope.backTransfer = function(){
                if($scope.showPreviewScreen){
                    $scope.showPreviewScreen = false;
                    return;
                }
                $scope.showPendingRequest=true;
                $scope.productSkuObjects = [];
                $scope.productToSkuMapping = {};
                $scope.skuToPackagingMapping = new Map();
                $scope.showPreviewScreen = false;
                $scope.isNotDefaultSkuFound = false;


                //$scope.getPendingRequestOrders($scope.selectedDate);
            };

            $scope.hasDrillDown = function (trItem){
                return trItem.drillDowns != null && trItem.drillDowns != undefined && trItem.drillDowns.length > 0;
            };

            $scope.isDefault = function (isDefault){
                if(isDefault){
                    return " (default)";

                }
                return ;

            }

            $scope.onSkuChanged = function(item ){
                $scope.productToSkuMapping[item.productId] = item.selectedSku;
            };


            $scope.showDetailsTO = function (productId) {
                var viewDetailModal = Popeye.openModal({
                    templateUrl: "showPreviewToModal.html",
                    controller: "showPreviewToModalCtrl",
                    resolve: {
                        productId: function () {
                            return productId;
                        },
                        requestOrderItems: function () {
                            return $scope.requestOrderItems[productId];
                        },
                        productToSkuMapping : function (){
                            return $scope.productToSkuMapping;
                        }

                    },
                    modalClass: 'custom-modal',
                    click: false,
                    keyboard: false,
                });
                viewDetailModal.closed.then(function (check) {
                });
            }

            function getCalculatedNumbers(sku, type, qty, pgkDetails){
                var ret = qty;
                var packagingList =  sortPackaging(sku.skuPackagings, type);
                if(sku.skuId==205){
                    console.log(packagingList);
                }
                packagingList.forEach(function (item) {
                    var unitsPacked = 0;
                    if(type==item.packagingDefinition.packagingType){
                        if(type=="LOOSE"){
                            if((ret/item.packagingDefinition.conversionRatio)>0){
                                unitsPacked = (ret/item.packagingDefinition.conversionRatio);
                                ret = ret - (unitsPacked*item.packagingDefinition.conversionRatio);
                            }
                        }else{
                            if(parseInt(ret/item.packagingDefinition.conversionRatio)>0){
                                unitsPacked = parseInt(ret/item.packagingDefinition.conversionRatio);
                                ret = Math.round((ret - (unitsPacked*item.packagingDefinition.conversionRatio))*1000000)/1000000;
                            }
                        }
                        if(unitsPacked>0){
                            pgkDetails.push({
                                id: null,
                                packagingDefinitionData: item.packagingDefinition,
                                numberOfUnitsPacked: unitsPacked,
                                numberOfUnitsReceived: null,
                                transferredQuantity: unitsPacked*item.packagingDefinition.conversionRatio,
                                receivedQuantity:null
                            });
                        }
                    }
                });
                return ret;
            }

            function sortPackaging(packagings, type){
                var pkgList = [];
                packagings.forEach(function (pkg) {
                    if(pkg.packagingDefinition.packagingType==type){
                        pkgList.push(pkg);
                    }
                });
                pkgList.sort(function (a,b) {
                    return b.packagingDefinition.conversionRatio - a.packagingDefinition.conversionRatio;
                });
                return pkgList;
            }

            function setDefaultPackagings(roItem , sku , unitId){
                var pgkDetails = [];
                /*var qty = roItem.requestedQuantity;
                qty = getCalculatedNumbers(sku, "CASE", qty, pgkDetails);
                if(qty>0){
                    qty = getCalculatedNumbers(sku, "INNER", qty, pgkDetails);
                }
                if(qty>0){
                    getCalculatedNumbers(sku, "LOOSE", qty, pgkDetails);
                }*/
                if(!appUtil.isEmptyObject($scope.productUnitPackaging[roItem.productId][unitId])){
                    pgkDetails = $scope.productUnitPackaging[roItem.productId][unitId];
                }
                var trPackaging = [];
                var pkg = null;
                if(pgkDetails.length>0){
                    pkg = {
                        id: null,
                        skuId: sku.skuId,
                        skuName: sku.skuName,
                        packagingDetails: [],
                        requestedQuantity: roItem.requestedQuantity,
                        requestedAbsoluteQuantity: roItem.requestedAbsoluteQuantity,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: sku.unitOfMeasure,
                        unitPrice: (roItem.vendor!=null && roItem.vendor.id==22)?null:sku.unitPrice,
                        negotiatedUnitPrice: (roItem.vendor!=null && roItem.vendor.id==22)?null:sku.negotiatedUnitPrice,
                        requestOrderItemId: roItem.id,
                        purchaseOrderItemId: null,
                        goodReceivedItemId: null
                    };
                    pkg.packagingDetails = pgkDetails;
                    trPackaging.push(pkg);
                }
                return trPackaging;
            }

            function handlesZeroQuantity(orderItem){
                var tempSku = $scope.skuProductMap[orderItem.productId][0];
                var tempPackaging = []
                var tempSkuMapping = $scope.packagingMap[tempSku.skuPackagings[0].packagingId];
                var tempPackagingDetail = [];

                tempPackagingDetail.push({
                    id: null,
                    packagingDefinitionData: tempSkuMapping,
                    numberOfUnitsPacked: 0,
                    numberOfUnitsReceived: null,
                    transferredQuantity: 0,
                    receivedQuantity:null
                });
                tempPackaging.push({
                    id: null,
                    skuId: tempSku.skuId,
                    skuName: tempSku.skuName,
                    packagingDetails: tempPackagingDetail,
                    requestedQuantity: orderItem.requestedQuantity,
                    requestedAbsoluteQuantity: orderItem.requestedAbsoluteQuantity,
                    transferredQuantity: 0,
                    receivedQuantity: null,
                    unitOfMeasure: tempSku.unitOfMeasure,
                    unitPrice: (orderItem.vendor!=null && orderItem.vendor.id==22)?null:tempSku.unitPrice,
                    negotiatedUnitPrice: (orderItem.vendor!=null && orderItem.vendor.id==22)?null:tempSku.negotiatedUnitPrice,
                    requestOrderItemId: orderItem.id,
                    purchaseOrderItemId: null,
                    goodReceivedItemId: null
                });
                orderItem.trPackaging = tempPackaging;
                orderItem.transferredQuantity = 0;
                return tempPackaging[0];
            }

            $scope.calculateTransferDistribution = function(){
                var packagingNotAvailable = [];
                Object.keys($scope.requestOrderItems).forEach(function (productId){
                    Object.keys($scope.requestOrderItems[productId]).forEach(function (unitId){
                        var orderItem = $scope.requestOrderItems[productId][unitId];
                        if(orderItem.requestedQuantity == 0 ){
                            try{
                                if (!$scope.skuToPackagingMapping.has(unitId)) {
                                    $scope.skuToPackagingMapping.set(unitId, new Map());
                                }
                                $scope.skuToPackagingMapping.get(unitId).set(productId, handlesZeroQuantity(orderItem));
                            }catch (e){
                                $alertService.create("something Went  Wrong With " + orderItem.productName)
                            }
                            return;

                        }
                        if(appUtil.isEmptyObject($scope.productToSkuMapping[orderItem.productId].skuPackagings)){
                            if(packagingNotAvailable.indexOf($scope.productToSkuMapping[orderItem.productId].skuName)== -1){
                                packagingNotAvailable.push($scope.productToSkuMapping[orderItem.productId].skuName);
                            }
                        }else {
                            orderItem.trPackaging = setDefaultPackagings(orderItem, $scope.productToSkuMapping[orderItem.productId] , unitId);
                            if(orderItem.trPackaging.length>0){
                                orderItem.trPackaging[0].productId = productId;
                                orderItem.trPackaging[0].excessQuantity = orderItem.excessQuantity;
                                if (orderItem.expiryDate != null && orderItem.expiryDate != undefined) {
                                    orderItem.trPackaging.roItemExpiryDate = orderItem.expiryDate;
                                }
                            }

                            var qty = 0;
                            orderItem.trPackaging.forEach(function (trp) {
                                var pkgqty = 0;
                                trp.packagingDetails.forEach(function (pgd) {
                                    pkgqty += pgd.transferredQuantity;
                                });
                                trp.transferredQuantity = parseFloat(pkgqty).toFixed(6);
                                qty += trp.transferredQuantity;
                            });
                            orderItem.transferredQuantity = parseFloat(qty).toFixed(6);
                            if (appUtil.isEmptyObject(orderItem.trPackaging)) {
                                orderItem.trPackaging = [];
                                var selectedSku = $scope.productToSkuMapping[orderItem.productId];
                                orderItem.trPackaging.push({
                                    id: null,
                                    skuId: selectedSku.skuId,
                                    skuName: selectedSku.skuName,
                                    packagingDetails: [],
                                    requestedQuantity: orderItem.requestedQuantity,
                                    requestedAbsoluteQuantity: orderItem.requestedAbsoluteQuantity,
                                    transferredQuantity: 0,
                                    receivedQuantity: null,
                                    unitOfMeasure: selectedSku.unitOfMeasure,
                                    unitPrice: (orderItem.vendor!=null && orderItem.vendor.id==22)?null:selectedSku.unitPrice,
                                    negotiatedUnitPrice: (orderItem.vendor!=null && orderItem.vendor.id==22)?null:selectedSku.negotiatedUnitPrice,
                                    requestOrderItemId: orderItem.id,
                                    purchaseOrderItemId: null,
                                    goodReceivedItemId: null
                                });
                            }
                            if (!$scope.skuToPackagingMapping.has(unitId)) {
                                $scope.skuToPackagingMapping.set(unitId, new Map());
                            }
                            $scope.skuToPackagingMapping.get(unitId).set(productId, orderItem.trPackaging[0]);
                        }
                    });
                });
                if(packagingNotAvailable.length>0){
                    $alertService.alert("Packaging Not Found For Some  Skus","packaging not found for Sku's : " + packagingNotAvailable ,
                        function (){},true);
                    $scope.backTransfer();
                }
            }

        }]).controller('showPreviewToModalCtrl', ['$scope', 'appUtil','apiJson','$http', 'Popeye', '$toastService','$alertService','productId','requestOrderItems','productToSkuMapping',
    function ($scope, appUtil,apiJson,$http, Popeye,$toastService,$alertService,productId,requestOrderItems,productToSkuMapping){


        $scope.init = function (){
            $scope.productId = productId;
            $scope.requestOrderItemsList = requestOrderItems;
            $scope.productToSkuMapping = productToSkuMapping;
            $scope.unitIds = Object.keys($scope.requestOrderItemsList);
        }

        $scope.getLength = function (){
            return Object.keys($scope.requestOrderItemsList).length;
        }

        $scope.getKeys = function (){
            return Object.keys($scope.requestOrderItemsList);
        }

        $scope.getUnitName = function (unitId){
            console.log("unitId :",unitId);
            return appUtil.findUnitDetail(unitId).name;
        }



    }]);



