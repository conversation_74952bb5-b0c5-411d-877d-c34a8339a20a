/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('trOrderCreateCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', '$alertService', 'metaDataService', 'previewModalService', 'Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, previewModalService, Popeye) {
            var manualBookProductId = 100217;

            initializeBillBookParams();

            $scope.selectedRO = {
                roType: null,
                count: 0
            }

            $scope.init = function (date) {
                $scope.togglePending = true;
                $scope.faEventPresent = false;
                $scope.faEventRoId = null;
                $scope.specialOrder = false;
                $scope.timeout = null
                $scope.scannedAssetTagValue = "";
                $scope.availableSKUList = [];
                $scope.currentFetchEvent = null ;
            	$scope.getAvailableSKUs();
                $scope.isEmpty = appUtil.isEmptyObject;
                $scope.clubbedROs = {};
                $scope.arrayAfterClubbing = {};
                $scope.showClubROBtn = false;
                $scope.showPendingRequest = true;
                $scope.isCreate = true ;
                $scope.isInvoiceRo = false;
                $scope.requestOrderDetail = null;
                $scope.currentTransferDetails = null;
                $scope.isWarehouse = appUtil.isWarehouse();
                $scope.dayClosePending = false;
                $scope.checkPendingDayClose(appUtil.getCurrentUser().unitId);
                $scope.skuProductMap = appUtil.getSkuProductMap();
                $scope.activeProducts = appUtil.getActiveScmProducts();
                $scope.isScanned = {};
                $scope.pendingList = [];
                 $scope.skuScannedList = {} ;

                $scope.packagingMap = appUtil.getPackagingMap();
                $scope.productIdAndProductMap = {};
                $scope.scmUnitList = appUtil.filterCurrentUnit(appUtil.getUnitList());
                $scope.currentstockEventDefinition = {};
                $scope.selectedDate = appUtil.isEmptyObject(date)
                    ? appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd") : date;
                $scope.minDate = appUtil.formatDate(appUtil.getDate(-30), "yyyy-MM-dd");
                $scope.maxDate = appUtil.formatDate(appUtil.getDate(70), "yyyy-MM-dd");
                $scope.getPendingRequestOrders($scope.selectedDate);
                $scope.showPreview = previewModalService.showPreview;
                $scope.productSKuAssetMap = {};
                $scope.productInventorySize = {};
                $scope.toggleSkuList = -1;
                $scope.diffs = {};
                $scope.scannedAssetProductMap = {};
                $scope.allowInput = true;
                $scope.scmProductDetails = appUtil.getActiveScmProducts();
                $scope.availableAssets = [];
                $scope.toTypeList = [
                    "FIXED_ASSET_TRANSFER",
                    "BROKEN_ASSET_TRANSFER",
                    "RENOVATION_ASSET_TRANSFER"
                ]
                $scope.productFaInventory = {};
                $scope.fetchInitiatedFa(null,true);
            };

            $scope.checkPendingDayClose = function(unitId, callback){
                var URL="";
                if($scope.isWarehouse == true){
                    URL  = apiJson.urls.warehouseClosing.checkFixedAssetDayClose
                }else if($scope.isWarehouse == false){
                    URL  = apiJson.urls.stockManagement.checkFixedAssetDayClose
                }
                $http({
                    method: 'GET',
                    url: URL,
                    params: {
                        id:unitId
                    }
                }).then(function success(response) {
                    if(response.data != null){
                        $scope.blockDaily = response.data.blockDaily;
                        $scope.blockWeekly = response.data.blockWeekly;
                        $scope.blockMonthly = response.data.blockMonthly;
                        if($scope.blockDaily || $scope.blockWeekly || $scope.blockMonthly){
                            $scope.dayClosePending = true;
                            if($scope.blockMonthly){
                                $scope.blockDaily = false;
                                $scope.blockWeekly = false;
                            }
                            else if($scope.blockWeekly){
                               $scope.blockDaily = false;
                            }
                        }
                        var today = new Date();
                        if(today.getDay() == 0 || today.getDay() == 6){
                            $scope.dayClosePending = false;
                        }
                }})
            }


            $scope.showAssetGrid= function(){
                $scope.showGrid = !$scope.showGrid;
            }


            $scope.setROType = function (ro) {
                if ($scope.selectedRO.roType == null || $scope.selectedRO.count == 0) {
                    $scope.selectedRO.roType = ro.assetOrder ? 'FIXED_ASSET' : 'REGULAR';
                    $scope.selectedRO.count = 1;
                } else if ($scope.selectedRO.roType == 'FIXED_ASSET' && ro.assetOrder && ro.checked) {
                    $scope.selectedRO.count = $scope.selectedRO.count + 1;
                } else if ($scope.selectedRO.roType == 'REGULAR' && !ro.assetOrder && ro.checked) {
                    $scope.selectedRO.count = $scope.selectedRO.count + 1;
                } else {
                    $scope.selectedRO.count = $scope.selectedRO.count - 1;
                }
            }

            $scope.getAvailableSKUs = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableSKUs,
                    params: {
                        unitId: appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.availableSKUList = response.data;
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.onScan = function () {
                console.log($scope.scannedAssetTagValue);
                if ($scope.scannedAssetTagValue == null || $scope.scannedAssetTagValue.length != 6) {
                    return;
                }
                console.log($scope.scannedAssetTagValue);
                if ($scope.requestOrderDetail.requestOrderItems == null || $scope.requestOrderDetail.requestOrderItems.length == 0) {
                    $toastService.create("No request orderItem present ");
                    resetScannedAssetTagValue();
                    return;
                }
                /*
                    Search for asset in available assets
                 */
                var assetId = null;
                var associatedSKUId = null;
                for (var index in $scope.availableAssets) {
                    if ($scope.availableAssets[index].tagValue == $scope.scannedAssetTagValue) {
                        assetId = $scope.availableAssets[index].assetId;
                        associatedSKUId = $scope.availableAssets[index].skuId;
                        break;
                    }
                }
                if (assetId == null) {
                    $toastService.create("Asset tag value is invalid");
                    resetScannedAssetTagValue();
                    return;
                }

                for (var i in $scope.requestOrderDetail.requestOrderItems) {
                    var item = $scope.requestOrderDetail.requestOrderItems[i];
                    if (item.selectedSku.skuId == associatedSKUId) {
                        if (item.associatedAssetId == assetId) {
                            $toastService.create("Asset already added ");
                            resetScannedAssetTagValue();
                            return;
                        }
                    }
                }
                var foundSku = false;
                var added = false;
                for (var i in $scope.requestOrderDetail.requestOrderItems) {
                    var item = $scope.requestOrderDetail.requestOrderItems[i];

                    console.log(item);
                    if (item.selectedSku.skuId == associatedSKUId) {
                        foundSku = true;
                        if (item.associatedAssetId == null || item.assetValidated == false) {
                            item.associatedAssetId = assetId;
                            item.assetValidated = true;
                            item.associatedAssetTagValue = $scope.scannedAssetTagValue;
                            // item.unitPrice = ;
                            // item.negotiatedUnitPrice = ;
                            added = true;
                            $toastService.create("Asset successfully added ");
                            resetScannedAssetTagValue();
                            return;
                        }
                    }

                }
                if (foundSku == false) {
                    $toastService.create("Could not found appropriate sku");
                    resetScannedAssetTagValue();
                }
                if (added == false) {
                    $toastService.create("Assets are already associated for sku " + item.selectedSku.skuName);
                    resetScannedAssetTagValue();
                    return;
                }
                /*
                    After performing operation set it back to empty string
                */
                resetScannedAssetTagValue();
            }

            function resetScannedAssetTagValue() {
                $scope.scannedAssetTagValue = "";
            }


            $scope.bySKUs = function (sku) {
                return sku != null && sku != undefined && $scope.availableSKUList != undefined && $scope.availableSKUList != null
                    && $scope.availableSKUList.indexOf(sku.skuId) > -1;
            };

            $scope.selectOpenRo = function (ro, type) {


                if (ro.transferType == 'INVOICE') {
                    $scope.isInvoiceRo = true;
                }
                $http({
                    method: 'GET',
                    url: apiJson.urls.requestOrderManagement.requestOrder,
                    params: { requestOrderId: ro.id }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $scope.getSelectedRo = response.data;
                        if (!type) {
                            if($scope.getSelectedRo.assetOrder && $scope.dayClosePending){
                                $alertService.alert("Can't make Transfer !!", "Fixed Asset Day Close Pending. Fixed Asset Transfers and Receiving are Disabled Until Fixed Asset Day Close is Completed.",
                                 function () { }, true);
                                return;
                            }
                            else {
                                $scope.selectRequest(ro);
                            }
                        } else {
                            if ($scope.getSelectedRo.assetOrder) {
                                if ($scope.faEventPresent == true && ro.id != $scope.faEventRoId) {
                                    $alertService.alert("Can't make Transfer !!", "There Is Already Fa Transfer Event Initiated Please Check For Pending Event",
                                     function () { }, true);
                                    return;
                                }
                                if($scope.dayClosePending){
                                    var msg = "Fixed Asset Day Close Pending. Fixed Asset Transfers and Receiving are Disabled Until Fixed Asset Day Close is Completed.";
                                    if($scope.blockMonthly){
                                        msg = "Monthly " + msg;
                                    }else if($scope.blockWeekly){
                                        msg = "Weekly " + msg;
                                    }else if($scope.blockDaily){
                                        msg = "Daily " + msg;
                                    }
                                    $alertService.alert("Can't make Transfer !!", msg,
                                     function () { }, true);
                                    return;
                                }
                                $scope.fetchInitiatedFa($scope.getSelectedRo, false);
                            } else {
                                $scope.fillTransferOrder($scope.getSelectedRo);
                            }

                        }
                    } else {
                        $toastService.create("Could Not Find Items for the Selected Ro!!");
                    }
                }, function (error) {
                    $scope.isInvoiceRo = false;
                    console.log(error);
                });
            }
            $scope.selectRequest = function (request) {
                if (request.transferType == 'INVOICE') {
                    request.checked = false;
                    $scope.isInvoiceRo = false;
                    $toastService.create("INVOICE RO's cannot be clubbed together!");
                    return;
                }
                if (request.assetOrder == true) {
                    request.checked = false;
                    $toastService.create("Fixed Asset RO's cannot be clubbed together!");
                    return;
                }

                $scope.setROType(request);
                var flag = false;
                //in case you need to un-club the RO
                if (!request.checked) {
                    var unitWiseList = $scope.clubbedROs[request.requestUnit.id];
                    if (!appUtil.isEmptyObject(unitWiseList)) {
                        var index = -1;
                        for (var i = 0; i < unitWiseList.length; i++) {
                            var req = unitWiseList[i];
                            if (req.id == request.id) {
                                index = i; // found RO at i
                                break;
                            }
                        }
                        // remove the RO if not checked and still in the list
                        if (index != -1) {
                            unitWiseList.splice(index, 1);
                            $scope.arrayAfterClubbing[request.requestUnit.id].splice(index, 1);
                        }
                    }
                } else {
                    if (appUtil.isEmptyObject($scope.clubbedROs)) {
                        $scope.clubbedROs[request.requestUnit.id] = [];
                        $scope.arrayAfterClubbing[request.requestUnit.id] = [];
                        $scope.clubbedROs.unitName = request.requestUnit.name;
                        $scope.arrayAfterClubbing.unitName = request.requestUnit.name;
                        flag = true;
                    } else {
                        var keys = Object.keys($scope.clubbedROs);
                        var foundAt = keys.indexOf(request.requestUnit.id.toString());
                        flag = foundAt != -1;
                    }

                    if (flag) {
                        if (!checkInRoArray($scope.clubbedROs[request.requestUnit.id], request)) {
                            if (checkForCapex(request, $scope.clubbedROs[request.requestUnit.id]) == false) {
                                request.checked = false;
                                $scope.selectedRO.count -= 1;
                                if ($scope.selectedRO.count < 0) {
                                    $scope.selectedRO.count = 0;
                                }
                                $toastService.create("Ro's Of Capex Can't be Clubbed With Opex RO");
                                return;
                            }
                            $scope.clubbedROs[request.requestUnit.id].push(request);
                            $scope.arrayAfterClubbing[request.requestUnit.id].push($scope.getSelectedRo);
                        } else {
                            $toastService.create("RO already added in the list");
                        }
                    } else {
                        request.checked = false;
                        $scope.selectedRO.count -= 1;
                        if ($scope.selectedRO.count < 0) {
                            $scope.selectedRO.count = 0;
                        }
                        $toastService.create("ROs with same REQUESTING UNIT can be clubbed together!");
                    }
                }

                function checkForCapex(request, unitWiseList) {
                    var budgetTypeToCheck = appUtil.isEmptyObject(request.type) ? "OPEX" : request.type;
                    var valid = true;
                    for (var i = 0; i < unitWiseList.length; i++) {
                        var req = unitWiseList[i];
                        var budgetType = appUtil.isEmptyObject(req.type) ? "OPEX" : req.type;
                        if (budgetType != budgetTypeToCheck) {
                            valid = false;
                            break;
                        }
                    }
                    return valid;
                }

                $scope.showClubROBtn = appUtil.isEmptyObject($scope.clubbedROs[request.requestUnit.id])
                    ? false : $scope.clubbedROs[request.requestUnit.id].length > 1;
                if ($scope.selectedRO.count == 0) {
                    $scope.selectedRO.roType = "";
                    $scope.clubbedROs = {};
                    $scope.arrayAfterClubbing = {};
                }

            };

            function checkInRoArray(roList, ro) {
                var found = false;
                for (var i = 0; i < roList.length; i++) {
                    var req = roList[i];
                    if (req.id === ro.id) {
                        found = true; // found RO at i
                        break;
                    }
                }
                return found;
            }

            $scope.clubAllROs = function (clubbedROsByUnit) {
                var clubbedROItems = {};
                $scope.clubbedROs = $scope.arrayAfterClubbing;
                var unitId = Object.keys(clubbedROsByUnit)[0];
                var clubbedROs = clubbedROsByUnit[unitId];
                var productIds = [];

                clubbedROs.forEach(function (request) {
                    // request = $scope.selectOpenRo(request.id);
                    console.log("Size of clubbedRO item ::::::", request.requestOrderItems.length);
                    if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0) {
                        request.requestOrderItems.forEach(function (item) {
                            var productId = item.productId;
                            if (Object.keys(clubbedROItems).indexOf(productId.toString()) == -1) {
                                clubbedROItems[productId] = item;
                                productIds.push(productId);
                            } else {
                                console.log("Found a duplicate object for product Id :::::: ", productId);
                                clubbedROItems[productId].requestedQuantity += item.requestedQuantity;
                                clubbedROItems[productId].requestedAbsoluteQuantity += item.requestedAbsoluteQuantity;
                            }
                            if (item.requestedQuantity > 1) {
                                var quantity = item.requestedQuantity;
                                var roItem = angular.copy(item);
                                roItem.requestedQuantity = 1; roItem.requestedAbsoluteQuantity = 1;
                                for (var i = 0; i < quantity; i++) {
                                    clubbedROItems
                                }
                            }
                        });
                    } else {
                        request.requestOrderItems.forEach(function (item) {
                            var productId = item.productId;
                            if (Object.keys(clubbedROItems).indexOf(productId.toString()) == -1) {
                                clubbedROItems[productId] = item;
                            } else {
                                console.log("Found a duplicate object for product Id :::::: ", productId);
                                clubbedROItems[productId].requestedQuantity += item.requestedQuantity;
                                clubbedROItems[productId].excessQuantity += item.excessQuantity;
                                clubbedROItems[productId].requestedAbsoluteQuantity += item.requestedAbsoluteQuantity;
                            }
                        });
                    }

                });
                if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0 && !appUtil.isEmptyObject(productIds)) {
                    getAssetFromUnitByProducts(unitId, productIds);
                }
                if (!appUtil.isEmptyObject(clubbedROItems) && Object.keys(clubbedROItems).length >= 250) {
                    $toastService.create("Clubbed Transfer cannot have more than 249 items. Please select lesser transfers to club!");
                    return false;
                }

                $scope.showPendingRequest = false;
                $scope.comment = null;
                $scope.requestOrderDetail = {};
                $scope.requestOrderDetail.fulfillmentDate = $scope.selectedDate;
                $scope.requestOrderDetail.requestUnit = {};
                $scope.requestOrderDetail.requestUnit.name = $scope.clubbedROs.unitName;
                $scope.requestOrderDetail.requestUnit.id = parseInt(unitId);
                $scope.requestOrderDetail.requestOrderId = null;
                $scope.fulfillmentCompany = clubbedROs[0].fulfillmentCompany;
                $scope.requestCompany = clubbedROs[0].requestCompany;


                $scope.requestOrderDetail.requestOrderItems = Object.values(clubbedROItems);
                setSkuToProducts(Object.values($scope.requestOrderDetail.requestOrderItems));
                if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0) {
                    var requestOrderItemArray = [];
                    for (var i = 0; i < $scope.requestOrderDetail.requestOrderItems.length; i++) {
                        var quantity = $scope.requestOrderDetail.requestOrderItems[i].requestedQuantity;

                        for (var l = 0; l < quantity; l++) {

                            var requestOrderItem = getModifiedROItemForFixedAsset(angular.copy($scope.requestOrderDetail.requestOrderItems[i]));
                            requestOrderItemArray.push(requestOrderItem);
                        }
                    }
                    $scope.requestOrderDetail.requestOrderItems = requestOrderItemArray;
                }

                var unit = appUtil.getUnitData();
                //if(appUtil.isKitchen(unit)){
                //$alertService.alert("Running for kitchen","Update Semifinished Products");
                $scope.checkSemiFinshedProducts($scope.requestOrderDetail.requestOrderItems);
                if ($scope.semiFinishedProducts != null) {
                    // $scope.openShortExpiryModal();
                }
                //}
            };

            $scope.getPendingRequestOrders = function (fulfilmentDate) {

                $scope.clubbedROs = {};
                fulfilmentDate = $scope.isEmpty(fulfilmentDate) ? $scope.selectedDate : fulfilmentDate;

                if (fulfilmentDate == null) {
                    $toastService.create("Please select a fulfilment date before ");
                    return false;
                }

                $http({
                    method: "GET",
                    url: apiJson.urls.requestOrderManagement.pendingRequestOrderShort
                        + "?unitId=" + appUtil.getCurrentUser().unitId + "&date=" + fulfilmentDate
                }).then(function success(response) {
                    $scope.requestOrderList = response.data;
                    $scope.requestOrderList.length == 0 ? $scope.showMessage = true : $scope.showMessage = false;
                    $scope.requestOrderList = $scope.requestOrderList.sort(sortByFulfillmentDate);
                    $scope.fetchInitiatedFa(null,true);
                   // console.log("RequestOrder=",$scope.requestOrderList);
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function sortByFulfillmentDate(a, b) {
                var diff = b.fulfillmentDate - a.fulfillmentDate;
                if (diff == 0) {
                    return sortByUnit(a, b);
                }
                return diff > 0 ? 1 : -1;
            }

            function sortByUnit(x, y) {

                if (x.requestUnit.name < y.requestUnit.name) return -1;
                if (x.requestUnit.name > y.requestUnit.name) return 1;
                return 0;
            }
            function prefillData(transferEvent, requestOrder) {


                if (requestOrder.assetOrder) {
                    var productIds = [];
                     $scope.scmProductDetails.forEach(function (product) {
                                        $scope.productIdAndProductMap[product.productId] = product;
                                    });

                    for (var item in transferEvent[0].productRequestQtyMap) {
                        productIds.push(item);

                    }
                    $scope.scanAndtransferQty = transferEvent[0].productRequestQtyMap;
                    getAssetFromUnitByProducts(requestOrder.fulfillmentUnit.id, productIds);
                    $scope.selectedRO.roType = "FIXED_ASSET"
                    $scope.selectedRO.count = 1;
                    var requestOrderItemArray = [];
                    for (var i = 0; i < requestOrder.requestOrderItems.length; i++) {
                        if (transferEvent[0].productRequestQtyMap[requestOrder.requestOrderItems[i].productId] == null) {
                            continue;
                        }

                        var quantity = transferEvent[0].productRequestQtyMap[requestOrder.requestOrderItems[i].productId].key;
                        requestOrder.requestOrderItems[i].transferredQuantity = quantity;
                        requestOrder.requestOrderItems[i].scanQty = transferEvent[0].productRequestQtyMap[requestOrder.requestOrderItems[i].productId].value;

                        var requestOrderItem = requestOrder.requestOrderItems[i];
                        requestOrderItem.transferredQuantity = quantity;

                        var requestOrderItems = getModifiedROItemForFixedAsset(requestOrder.requestOrderItems[i]);
                        requestOrderItemArray.push(requestOrderItems);

                    }
                    requestOrder.requestOrderItems = requestOrderItemArray;
                    $scope.requestOrderDetail = requestOrder;
                }
                if (!appUtil.isEmptyObject(requestOrder)) {
                    initializeBillBookParams();
                    $scope.requestOrderDetail = requestOrder;
                    console.log($scope.requestOrderDetail)
                    $scope.showPendingRequest = false;
                    //setSkuToProducts($scope.requestOrderDetail.requestOrderItems);
                    $scope.comment = null;


                    $scope.checkSemiFinshedProducts($scope.requestOrderDetail.requestOrderItems);
                    if ($scope.semiFinishedProducts != null) {
                        // $scope.openShortExpiryModal();
                    }
                    //}
                }

                $scope.scmProductDetails.forEach(function (product) {
                    $scope.productIdAndProductMap[product.productId] = product;
                });

                (!appUtil.isEmptyObject(transferEvent[0].scannedAssets.length) && transferEvent[0].scannedAssets.forEach(function (asset) {


                    $scope.isScanned[asset.assetId] = true;
                    //$scope.scannedProductAsset[transferEvent[0]].push(asset)
                }))


            }

            $scope.changeFaTransferQty = function (item) {
                var productInventory = 0;
                for (var skuId in Object.keys(productSKuAssetMap[item.productId])) {
                    productInventory += (productSKuAssetMap[item.productId][skuId].length);
                }
                if (item.transferredQuantity > productInventory) {
                    item.transferredQuantity = 0;
                }
            }

            $scope.showPending = function () {
                $scope.togglePending = !$scope.togglePending;
            }
            $scope.fetchInitiatedFa = function (requestOrder, flg) {
                $scope.scannedAssetProductMap = {};

                var url = apiJson.urls.assetManagement.stockEventUnit;
                var params = {
                    unitId: appUtil.getCurrentUser().unitId,
                    eventStatus: 'INITIATED'
                }
                if (!flg) {
                    params['roId'] = requestOrder.id
                }
                $http({
                    url: url,
                    method: 'GET',
                    params: params
                }).success(function (response) {
                    if (response != null && response.stockEventDefinition.length > 0) {
                        $scope.allowInput = false;
                        $scope.currentstockEventDefinition = response.stockEventDefinition[0];
                        if (flg) {
                            $scope.faEventPresent = true;
                            if ($scope.currentstockEventDefinition.roId != null) {
                                $scope.faEventRoId = $scope.currentstockEventDefinition.roId;
                            }

        
                            if($scope.faEventRoId!=null){
                                $http({
                                    url : apiJson.urls.requestOrderManagement.requestOrder,
                                    method:'GET',
                                    params:{requestOrderId: $scope.faEventRoId} 
                                }).success(function(res){
                                    if(res!=null){
                                        $scope.pendingList = [];
                                        $scope.pendingList.push(res);
                                    }else{
                                        $alertService.alert("RO Not Found", "Ro not found with ro_id ="+$scope.faEventRoId, function () { }, true);
                                    }
                                }).error(function(res){
                                    if (response.errorCode != null) {
                                        $alertService.alert(response.errorTitle, response.errorMsg, function () { }, true);
                                    } else {
                                        console.log("error:" + response);
                                    }
                                });
                            }
                           

                            $scope.requestOrderList = $scope.requestOrderList.filter(function (ro) {
                                return ro.id != $scope.faEventRoId;
                            });
                        }

                        $scope.currentState = $scope.currentstockEventDefinition.productRequestQtyMap;
                        $scope.checkState();
                        if (!flg)
                            prefillData(response.stockEventDefinition, requestOrder);
                    }
                    else {
                        $scope.allowInput = true;
                        if (!flg) {
                            $scope.fillTransferOrder(requestOrder);
                        }

                    }
                }).error(function (response) {
                    if (response.errorCode != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, function () { }, true);
                    } else {

                        console.log("error:" + response);
                    }
                });
            }
            $scope.initiateFATransfer = function () {
                var url = apiJson.urls.transferOrderManagement.initiateFaTransfer;
                var productAndQty = {};
                var isValid = true;
                for (var product in $scope.requestOrderDetail.requestOrderItems) {
                    var availableInventory = $scope.productInventorySize[$scope.requestOrderDetail.requestOrderItems[product].productId];
                    var transferQty = parseInt($scope.requestOrderDetail.requestOrderItems[product].transferredQuantity);
                    if( availableInventory == null || !Number.isInteger(transferQty) || availableInventory < transferQty){
                        $toastService.create("Can't initiate As One Of Product Available Inventory Is Invalid !!");
                        isValid = false;
                        return;
                    }

                    productAndQty[$scope.requestOrderDetail.requestOrderItems[product].productId] = $scope.requestOrderDetail.requestOrderItems[product].transferredQuantity;

                }
                if(isValid == false){
                    return;
                }

                console.log(productAndQty)
                $http({
                    url: url,
                    method: 'POST',
                    data: productAndQty,
                    params: {
                        userId: appUtil.getCurrentUser().user.id,
                        receivingUnitId: $scope.requestOrderDetail.requestUnit.id,
                        fullfillingUnitId: $scope.requestOrderDetail.fulfillmentUnit.id,
                        budgetType: $scope.getSelectedRo.type == null ? "OPEX" : $scope.getSelectedRo.type,
                        toType: "FIXED_ASSET_TRANSFER",
                        roId: $scope.getSelectedRo.id,
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response) {
                        var title = "Transfer Out Event Successfully Initiated!!";
                        var msg = "Please Proceed With Scanning of Assets!!";
                        $scope.allowInput = false;
                        $alertService.alert(title, msg, function () {

                        }, false);
                        $scope.fetchInitiatedFa(response, false);
                    } else {
                        $toastService.create("Something Went Wrong");
                    }
                }).error(function (response) {
                    if (response.errorCode != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, function () { }, true);
                    } else {

                        console.log("error:" + response);
                    }
                });
            }
            function removeAddedProduct(productId) {
                $http({
                    method: "POST",
                    url: apiJson.urls.transferOrderManagement.changeFaTranferProduct,
                    params: {
                        eventId: $scope.currentstockEventDefinition.eventId,
                        productId: productId,
                        action: false,
                        transferQty: 0,
                        toAdd: false

                    }

                }).then(function success(response) {
                    if(response.data==true){
                        $toastService.create("product is Removed !");
                    }else{
                        $alertService.alert("Error while Removing product","Please contact scm team.", function () { }, true);
                    }
                        
                        

                    }, function error(response) {
                        console.log("error:" + response);
                        if (response.errorCode != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, function () { }, true);
                        } 
                    });
}

            $scope.checkState = function () {
             $scope.scmProductDetails.forEach(function (product) {
                                $scope.productIdAndProductMap[product.productId] = product;
                            });
            $scope.scannedAssetProductMap = {} ;

            for (var item in $scope.currentstockEventDefinition.scannedAssets)
            {
                 $scope.isScanned[$scope.currentstockEventDefinition.scannedAssets[item].assetId] = true;
                if (appUtil.isEmptyObject($scope.scannedAssetProductMap[$scope.currentstockEventDefinition.scannedAssets[item].productId])) {
                     $scope.scannedAssetProductMap[$scope.currentstockEventDefinition.scannedAssets[item].productId] = [];
                }
            
            $scope.scannedAssetProductMap[$scope.currentstockEventDefinition.scannedAssets[item].productId].push($scope.currentstockEventDefinition.scannedAssets[item].assetTagValue);
                            }

                for (var item in $scope.currentstockEventDefinition.availableAssets) {
                    if (appUtil.isEmptyObject($scope.scannedAssetProductMap[$scope.currentstockEventDefinition.availableAssets[item].productId])) {
                        $scope.scannedAssetProductMap[$scope.currentstockEventDefinition.availableAssets[item].productId] = [];
                    }
                    if ($scope.isScanned[$scope.currentstockEventDefinition.availableAssets[item].assetId]) {
                        $scope.scannedAssetProductMap[$scope.currentstockEventDefinition.availableAssets[item].productId].push($scope.currentstockEventDefinition.availableAssets[item].assetTagValue);
                    }
                }
                $scope.skuScannedList = {};
                for (var k in $scope.currentState) {
                for(var item in $scope.productSKuAssetMap[k])
                                {
                                for(var item2 in $scope.productSKuAssetMap[k][item])
                                {
                                if($scope.isScanned[$scope.productSKuAssetMap[k][item][item2].assetId])
                                {
                                if (appUtil.isEmptyObject($scope.skuScannedList[item]))
                                $scope.skuScannedList[item] = 0 ;
                                $scope.skuScannedList[item]++ ;
                                }
                                }
                                }
                var assetTagList = [] ;
                for(var tag in $scope.scannedAssetProductMap[k])
                {
                assetTagList.push($scope.scannedAssetProductMap[k][tag])
                }

                    var obj = {
                        'transferredQty': $scope.currentState[k].value,
                        'scannedAssetTag': assetTagList,
                        'notScannedQty': ($scope.currentState[k].key - $scope.currentState[k].value)
                    };
                    $scope.diffs[$scope.productIdAndProductMap[k].productName] = obj;
                }




            }

            $scope.showScanQty = function () {
            //$scope.onSubmitFaTransfer();
               var budgetModal = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'scanViewModal.html',
                    controller: 'scanViewModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    resolve: {
                        diffs: function () {
                            return $scope.diffs;
                        },

                    },
                    size: 'md',
                    click: false,
                    keyboard: false
                });

                budgetModal.closed
                    .then(function (data) {
                        if (data) {
                            $scope.onSubmitFaTransfer();
                            //$scope.createTransfer();
                        }
                    });
            }

            $scope.onSubmitFaTransfer = function () {

                    if ($scope.currentstockEventDefinition.scannedAssets.length == 0) {
                                    $toastService.create("Can't submit As No Asset Is Scanned !!");
                                    return;
                                }


                $http({
                    method: "POST",
                    url: apiJson.urls.assetManagement.faSubmitTransferOut,
                    params: {
                        eventId: $scope.currentstockEventDefinition.eventId
                    }
                }).then(function success(response) {
                    $alertService.alert("Successfully Submitted", " Fixed Assets TO's are successfully submitted");
                    $scope.init();
                }, function error(response) {
                    $alertService.alert("Can;t Create Transfer", response.errorMessage, function () { }, true);
                    console.log("error:" + response);
                });

            }


            $scope.fillTransferOrder = function (requestOrder) {
                $scope.specialOrder = requestOrder.specialOrder;
                console.log(requestOrder.assetOrder)
                if (requestOrder.assetOrder) {
                    var productIds = [];
                    for (var i = 0; i < requestOrder.requestOrderItems.length; i++) {
                        productIds.push(requestOrder.requestOrderItems[i].productId);
                    }
                    getAssetFromUnitByProducts(requestOrder.fulfillmentUnit.id, productIds);
                    $scope.selectedRO.roType = "FIXED_ASSET"
                    $scope.selectedRO.count = 1;
                    var requestOrderItemArray = [];
                    for (var i = 0; i < requestOrder.requestOrderItems.length; i++) {
                        var quantity = requestOrder.requestOrderItems[i].requestedQuantity;
                        requestOrder.requestOrderItems[i].transferredQuantity = quantity;

                        var requestOrderItem = angular.copy(requestOrder.requestOrderItems[i]);
                        requestOrderItem.requestedQuantity = quantity; requestOrderItem.requestedAbsoluteQuantity = quantity;
                        requestOrderItem.transferredQuantity = quantity;

                        var requestOrderItems = getModifiedROItemForFixedAsset(angular.copy(requestOrder.requestOrderItems[i]));
                        requestOrderItemArray.push(requestOrderItems);
                    }
                    requestOrder.requestOrderItems = requestOrderItemArray;
                }
                if (!appUtil.isEmptyObject(requestOrder)) {
                    initializeBillBookParams();
                    $scope.requestOrderDetail = requestOrder;
                    console.log($scope.requestOrderDetail)
                    $scope.showPendingRequest = false;
                    if(!requestOrder.assetOrder){
                        setSkuToProducts($scope.requestOrderDetail.requestOrderItems);
                    }
                    $scope.comment = null;

                    //var unit = appUtil.getUnitData();
                    //if(appUtil.isKitchen(unit)){
                    //$alertService.alert("Running for kitchen","Update Semifinished Products");
                    $scope.checkSemiFinshedProducts($scope.requestOrderDetail.requestOrderItems);
                    if ($scope.semiFinishedProducts != null) {
                        // $scope.openShortExpiryModal();
                    }
                    //}
                }
            };
            $scope.toggle = function (idx) {
                if (idx != $scope.toggleSkuList)
                    $scope.toggleSkuList = idx;
                else
                    $scope.toggleSkuList = -1;
            }

            function getModifiedROItemForFixedAsset(requestOrderItem) {
                // requestOrderItem.requestedQuantity = 1; requestOrderItem.requestedAbsoluteQuantity = 1;
                // requestOrderItem.transferredQuantity = 1;
                // var trPackaging = angular.copy(requestOrderItem.trPackaging)

                if (requestOrderItem.trPackaging != undefined) {
                    var trPackagingArray = [];
                    for (var j = 0; j < requestOrderItem.trPackaging.length; j++) {
                        var trpac = angular.copy(requestOrderItem.trPackaging[j]);
                        trpac.requestedQuantity = 1;
                        trpac.requestedAbsoluteQuantity = 1;
                        trpac.transferredQuantity = 1;
                        var packagingDetailsArray = [];
                        for (var k = 0; k < trpac.packagingDetails.length; k++) {
                            var pgd = angular.copy(trpac.packagingDetails[k]);
                            pgd.requestedQuantity = 1;
                            pgd.requestedAbsoluteQuantity = 1;
                            pgd.transferredQuantity = 1;
                            pgd.numberOfUnitsPacked = 1;
                            packagingDetailsArray.push(pgd);
                        }
                        trpac.packagingDetails = packagingDetailsArray;
                        trPackagingArray.push(trpac);
                    }
                    requestOrderItem.trPackaging = trPackagingArray;
                }

                return requestOrderItem;
            }

            function getAssetFromUnit(unitId) {
                $http({
                    url: apiJson.urls.assetManagement.getTransferableAssetsFromUnit,
                    method: 'GET',
                    params: {
                        unitId: unitId
                    },
                    headers: { "Content-Type": "application/json" }
                }).then(function success(response) {
                    $scope.availableAssets = response.data;
                }, function error(response) {
                    $scope.availableAssets = [];
                    console.log("error:" + response);
                });
            }
            function getAssetFromUnitByProducts(unitId, productIds) {
                $http({
                    url: apiJson.urls.assetManagement.getTransferableAssetsFromUnitByProducts,
                    method: 'POST',
                    params: {
                        unitId: unitId
                    },
                    data: productIds,
                    headers: { "Content-Type": "application/json" }
                }).then(function success(response) {
                    if (response.data != null) {
                        var skuAssetMap = {};
                        $scope.availableAssets = [];
                         $scope.productSKuAssetMap = {} ;

                        $scope.productInventorySize={};

                        response.data.forEach(function (asset) {

                            $scope.availableAssets.push(asset);
                            if (appUtil.isEmptyObject($scope.productSKuAssetMap[asset.productId])) {
                                $scope.productSKuAssetMap[asset.productId] = {};
                            }
                            skuAssetMap = $scope.productSKuAssetMap[asset.productId];

                            if (appUtil.isEmptyObject(skuAssetMap[asset.skuId]))
                                skuAssetMap[asset.skuId] = [];
                            // skuAssetMap.set(asset.skuId , asset) ;

                            skuAssetMap[asset.skuId].push(asset);
                            if ($scope.productInventorySize[asset.productId] == null) {
                                $scope.productInventorySize[asset.productId] = 0;
                            }

                            $scope.productInventorySize[asset.productId] += 1;
                            $scope.productSKuAssetMap[asset.productId] = skuAssetMap;
                        });
                        // $scope.productSKuAssetMap.set(productIds[0] ,skuAssetMap);

                        //$scope.productSKuAssetMap[productIds[0]] =skuAssetMap;
                        console.log("::::::::skuAssetMap ", $scope.productSKuAssetMap);
                        $scope.availableAssets = response.data;
                        console.log("available assets : ", $scope.availableAssets);
                    }
                }, function error(response) {
                    $scope.availableAssets = [];
                    console.log("error:" + response);
                });

            }

            //EDIT
            $scope.checkSemiFinshedProducts = function (requestOrderItems) {
                $scope.semiFinishedProducts = [];
                var products = appUtil.getActiveScmProducts();
                if (requestOrderItems != null)
                    requestOrderItems.forEach(function (roItem) {
                        var i = 0;
                        var p = null;
                        for (i in products) {
                            if (products[i].productId == roItem.productId) {
                                p = products[i];
                                break;
                            }
                        }
                        if (p != null && p.categoryDefinition.code == 'SEMI_FINISHED') {
                            $scope.semiFinishedProducts.push(roItem);
                        }
                    });
            };

            $scope.openShortExpiryModal = function () {
                if (!appUtil.isEmptyObject($scope.semiFinishedProducts)) {
                    var mappingModal = Popeye.openModal({
                        templateUrl: "views/trOrderShortExpiry.html",
                        controller: "trOrderShortExpiryCtrl",
                        modalClass: "semifinishedItemsModal",
                        resolve: {
                            data: function () {
                                return $scope.semiFinishedProducts;
                            }
                        },
                        click: false,
                        keyboard: false
                    });

                    mappingModal.closed.then(function (data) {
                        if (data == null || data.dataStatus == 'CANCEL') {
                            $scope.backTransfer();
                        } else {
                            $scope.semiFinishedProducts = data.semiFinishedProducts;
                            $scope.updateTOwithShortExpiry($scope.semiFinishedProducts, data.expiryData);
                        }
                    });
                } else {
                    $toastService.create("Please select a default mapping first!");
                }
            };
            $scope.onCancelTo = function () {
                $scope.currentstockEventDefinition.eventStatus = "ABANDONED"
                $scope.currentstockEventDefinition.productRequestQtyMap = null;
                $scope.currentstockEventDefinition.availableAssets = null;



                $http({
                    method: "PUT",
                    url: apiJson.urls.assetManagement.stockEvent,
                    data: $scope.currentstockEventDefinition

                }).then(function success(response) {
                    $scope.allowInput = true;
                    $alertService.alert("Successfully Cancelled", "Your current TO is cancelled!!");
                    $scope.init();
                }, function error(response) {
                    console.log("error:" + response);
                });

            }


            // SEMI-FINISHED EXPIRY ADDED HERE
            $scope.updateTOwithShortExpiry = function (semiFinishedProducts, expiryData) {

                if (semiFinishedProducts == null || semiFinishedProducts.length == 0) {
                    return;
                }

                var i = 0;
                var j = 0;

                for (i in semiFinishedProducts) {
                    var item = semiFinishedProducts[i];
                    for (j in $scope.requestOrderDetail.requestOrderItems) {
                        var roItem = $scope.requestOrderDetail.requestOrderItems[j];

                        if (item.productId == roItem.productId) {
                            //add packaging
                            var rOrderItem = $scope.requestOrderDetail.requestOrderItems[j];
                            var trItem = $scope.requestOrderDetail.requestOrderItems[j].trPackaging[0];
                            var pkg = trItem.packagingDetails[0];
                            var value = 0;
                            if (item.shortExpiry == null || item.shortExpiry == undefined) {
                                value = item.freshQuantity;
                            } else {
                                value = item.freshQuantity + item.shortExpiry
                            }
                            //pkg.numberOfUnitsPacked = value;
                            pkg.numberOfUnitsPacked = value / pkg.packagingDefinitionData.conversionRatio;
                            var e = 0;
                            for (e in expiryData.inventoryItems) {
                                var exp = expiryData.inventoryItems[e];
                                if (trItem.skuId == exp.keyId) {
                                    if (trItem.drillDowns == null) {
                                        trItem.drillDowns = [];
                                    }
                                    trItem.drillDowns = trItem.drillDowns.concat(exp.drillDowns);
                                }
                            }
                            $scope.updatePackagingQty(pkg, trItem, roItem)
                        }
                    }
                }

            };

            function getTorqusTO(toID) {
                $http({
                    url: apiJson.urls.transferOrderManagement.getTorqusTO + "/" + toID,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(function (data) {
                    var fileName = "Torqus_Transfer_Order_Sheet_" + toID + "_" + Date.now() + ".xls";
                    var blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }, fileName);
                    saveAs(blob, fileName);
                }).error(function (err) {
                    console.log("Error during getting data", err);
                });
            }

            function getToType() {
                if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0) {
                    return 'FIXED_ASSET_TRANSFER';
                } else {
                    return 'REGULAR_TRANSFER';
                }
            }

            $scope.createTransferOrderObject = function () {
                if ($scope.isBillBooksDetailFilled) {
                    $toastService.create("Fill Manual bill book details to create Transfer order!");
                    return false;
                }
                if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0 && $scope.requestOrderDetail.assetOrder) {
                    for (var i = 0; i < $scope.requestOrderDetail.requestOrderItems.length; i++) {
                        var requestOrderItem = $scope.requestOrderDetail.requestOrderItems[i];
                        if (requestOrderItem.trPackaging.length > 0) {
                            if (requestOrderItem.assetValidated && requestOrderItem.associatedAssetId != null) {

                            } else {
                                $toastService.create("Please enter valid Asset Tag values");
                                return;
                            }
                        }

                    }

                }
                $scope.submitted = true;
                var to = {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    generationUnitId: appUtil.createRequestUnit(),
                    generatedForUnitId: $scope.requestOrderDetail.requestUnit,
                    generatedBy: appUtil.createGeneratedBy(),
                    lastUpdatedBy: appUtil.createGeneratedBy(),
                    status: "CREATED",
                    comment: $scope.comment,
                    requestOrderId: $scope.requestOrderDetail.id,
                    sourceCompany: $scope.fulfillmentCompany,
                    receivingCompany: $scope.requestCompany,
                    transferOrderItems: generateTOItems($scope.requestOrderDetail),
                    toType: getToType()
                };
                if (to.transferOrderItems.length == 0) {
                    if (window.confirm("This transfer order contains no items. Are you sure you want to transfer?")) {
                        $scope.sendTOForCreation(to);
                    }
                } else {
                    $scope.sendTOForCreation(to);
                }
                $scope.submitted = false;
            };

            $scope.sendTOForCreation = function (to) {
                var url = apiJson.urls.transferOrderManagement.transferOrder;
                var data = to;
                var generatedForUnitId = to.generatedForUnitId;
                if (!appUtil.isEmptyObject($scope.clubbedROs)
                    && $scope.clubbedROs[Object.keys($scope.clubbedROs)[0]].length > 1) {
                    url = apiJson.urls.transferOrderManagement.clubbedTransferOrder;
                    data = {
                        clubRoIds: Object.values($scope.clubbedROs[Object.keys($scope.clubbedROs)[0]]).map(function (ro) {
                            return ro.id;
                        }),
                        requestCompany: $scope.requestCompany,
                        fulfillmentCompany: $scope.fulfillmentCompany,
                        transferOrder: to,
                        fulfilmentDate: $scope.selectedDate
                    }
                }

                $http({
                    method: "POST",
                    url: url,
                    data: data
                }).then(function success(response) {
                    if (response.data != null) {
                        if (response.data != null && response.data > 0) {
                            //if(!appUtil.isCafe()){
                            //getTorqusTO(response.data);
                            //}
                            $scope.selectedRO.count = 0;
                            $scope.selectedRO.roType = null;
                            if (response.data == 1) {
                                $toastService.create("Production Booking for Invoice created successfully!");
                            } else {
                                $toastService.create("Transfer order with id " + response.data + " created successfully!");
                            }
                            if ($scope.manualBillBookDetails != undefined) {
                                $scope.manualBillBookDetails.transferOrderId = response.data;
                                $scope.manualBillBookDetails.generatedForUnitId = generatedForUnitId;
                                var url = apiJson.urls.manualBillBookManagement.createManualBillBookEntry;
                                metaDataService.createManualBillBookEntry(url, $scope.manualBillBookDetails).then(function (data) {
                                    if (data) {
                                        $toastService.create("Manual bill book with start no: " + $scope.manualBillBookDetails.startNo + " and end no : "
                                            + $scope.manualBillBookDetails.endNo + " created successfully!");
                                        initializeBillBookParams();
                                    } else {
                                        $toastService.create("Manual bill book with given start no and end no already exist!");
                                    }
                                });
                            }
                            $scope.init($scope.selectedDate);
                        } else {
                            if (response.errorCode != 108) {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }
                    }
                }, function error(response) {
                    if (response.data.errorCode != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () { }, true);
                    } else {
                        $toastService.create("Could not create transfer. Please try again later");
                        console.log("error:" + response);
                    }
                });

            };

            $scope.addPackaging = function (item) {
                var found = false;
                item.trPackaging.forEach(function (trp) {
                    if (trp.skuId == item.selectedSku.skuId) {
                        var pfound = false;
                        trp.packagingDetails.forEach(function (pkgd) {
                            if (pkgd.packagingDefinitionData.packagingId == item.selectedPackaging.packagingDefinition.packagingId) {
                                alert("Packaging already added!");
                                pfound = true;
                                return false;
                            }
                        });
                        if (!pfound) {
                            trp.packagingDetails.push({
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity: null
                            });
                        }
                        found = true;
                    }
                });
                if (!found) {
                    if (item.productId == 100217) {
                        $scope.isBillBooksDetailFilled = true;
                    }
                    item.trPackaging = [];
                    item.trPackaging.push({
                        id: null,
                        skuId: item.selectedSku.skuId,
                        skuName: item.selectedSku.skuName,
                        packagingDetails: [
                            {
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity: null
                            }
                        ],
                        requestedQuantity: item.requestedQuantity,
                        requestedAbsoluteQuantity: item.requestedAbsoluteQuantity,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: item.selectedSku.unitOfMeasure,
                        unitPrice: item.selectedSku.unitPrice,
                        negotiatedUnitPrice: item.selectedSku.negotiatedUnitPrice,
                        requestOrderItemId: item.id,
                        purchaseOrderItemId: null,
                        goodReceivedItemId: null
                    });
                }
                //console.log(item);
                if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0) {
                    var trItem = item.trPackaging[0];
                    var pgd = trItem.packagingDetails[0]
                    pgd.numberOfUnitsPacked = 1;
                    $scope.updatePackagingQty(pgd, trItem, item)
                }
            };

            $scope.removePackaging = function (trItem, index, item) {
                if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0) {
                    $scope.requestOrderDetail.requestOrderItems.splice(index, 1);
                    delete $scope.diffs[$scope.productIdAndProductMap[item.productId].productName]
                    removeAddedProduct(item.productId);
                    /*item.associatedAssetTagValue = "";
                    $scope.validateAssetTagValue(item.associatedAssetTagValue, item,-1);*/
                } else {
                    if (item.productId == 100217) {
                        initializeBillBookParams();
                    }
                    trItem.packagingDetails.splice(index, 1);
                    if (trItem.packagingDetails.length == 0) {
                        item.trPackaging.forEach(function (pkg, i) {
                            if (pkg.skuId == trItem.skuId) {
                                item.trPackaging.splice(i, 1);
                            }
                        });
                    }
                    $scope.updateTrItemQty(trItem, item);
                }
            };

            function openValidateInputModal(trItem, pgd, roItem) {
                var enterdUnits = pgd.numberOfUnitsPacked;
                var qtyViewModal = Popeye.openModal({
                    templateUrl: "toQtyValidation.html",
                    controller: "toQtyValidationCtrl",
                    resolve: {
                        toItem: function () {
                            return angular.copy(trItem);
                        },
                        pkg: function () {
                            return angular.copy(pgd);
                        }
                    },
                    modalClass: 'custom-modal',
                    click: false,
                    keyboard: false
                });

                qtyViewModal.closed.then(function (data) {
                    if (appUtil.isEmptyObject(data)) {
                        pgd.numberOfUnitsPacked = 0;
                        updatePkg(trItem, pgd, roItem);
                        pgd.numberOfUnitsPacked = null;
                        $toastService.create("Re-entered Qty Doesn't Matched !!");
                    } else {
                        updatePkg(trItem, pgd, roItem);
                    }
                })
            }

            $scope.updatePackagingQty = function (pgd, trItem, roItem) {




                if (pgd.numberOfUnitsPacked < 0) {
                    $toastService.create("Units packed Cannot Be Negative ");
                    pgd.numberOfUnitsPacked = 0;
                    pgd.transferredQuantity = 0;
                }

                if (roItem.productId == 100217) {
                    pgd.numberOfUnitsPacked = 1;
                }
                if ($scope.specialOrder == true) {
                    if (pgd.numberOfUnitsPacked == undefined) {
                        clearTimeout($scope.timeout);
                        return;
                    }
                    var totalQty = 0;
                    var validPercentageValue = (trItem.requestedQuantity * 20) / 100;
                    var maxRange = trItem.requestedQuantity + validPercentageValue;
                    var minRange = trItem.requestedQuantity - validPercentageValue;
                    for (var i = 0; i < trItem.packagingDetails.length; i++) {
                        if (!appUtil.isEmptyObject(trItem.packagingDetails[i].numberOfUnitsPacked)) {
                            totalQty += (trItem.packagingDetails[i].numberOfUnitsPacked * trItem.packagingDetails[i].packagingDefinitionData.conversionRatio);
                        }
                    }

                    if (totalQty <= minRange || totalQty >= maxRange) {
                        clearTimeout($scope.timeout);
                        $scope.timeout = setTimeout(function () {
                            openValidateInputModal(trItem, pgd, roItem);
                        }, 1000);

                    } else {
                        clearTimeout($scope.timeout);
                        updatePkg(trItem, pgd, roItem);
                    }
                } else {
                    updatePkg(trItem, pgd, roItem);
                }

            };

            function updatePkg(trItem, pgd, roItem) {
                if (pgd.packagingDefinitionData.packagingType != "LOOSE") {
                    pgd.numberOfUnitsPacked = parseInt(pgd.numberOfUnitsPacked);
                }
                pgd.transferredQuantity = pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                pgd.transferredQuantity = parseFloat(pgd.transferredQuantity.toFixed(6));
                if (roItem.freshQuantity != undefined || roItem.shortExpiry != undefined) {
                    var totalQty = roItem.freshQuantity != undefined ? roItem.freshQuantity : 0;
                    totalQty = totalQty + (roItem.shortExpiry != undefined ? roItem.shortExpiry : 0);
                    var qty = null;
                    trItem.packagingDetails.forEach(function (item) {
                        qty += item.transferredQuantity;
                    });
                    qty = parseFloat(qty.toFixed(6));
                    if (totalQty < qty) {
                        $toastService.create("Total Quantity cannot be greater than " + totalQty);
                        pgd.numberOfUnitsPacked = 0;
                        pgd.transferredQuantity = 0;
                    }
                }


                $scope.updateTrItemQty(trItem, roItem);
            }

            $scope.updateTrItemQty = function (trItem, roItem) {
                var qty = null;
                trItem.packagingDetails.forEach(function (item) {
                    qty += item.transferredQuantity;
                });
                trItem.transferredQuantity = qty;
                $scope.updateRoItemQty(roItem);
            };

            $scope.updateRoItemQty = function (roItem) {
                var qty = null;
                roItem.trPackaging.forEach(function (item) {
                    qty += item.transferredQuantity;
                });
                roItem.transferredQuantity = qty;
            };

            function generateTOItems(requestOrderDetail) {
                var trItems = [];
                var requestOrderItems = typeof requestOrderDetail.requestOrderItems == "object" ?
                    Object.values(requestOrderDetail.requestOrderItems) : requestOrderDetail.requestOrderItems;
                requestOrderItems.forEach(function (roi) {
                    roi.trPackaging.forEach(function (item) {
                        if (item.transferredQuantity != null) {
                            if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0 && requestOrderDetail.assetOrder) {
                                item.associatedAssetId = roi.associatedAssetId;
                                item.associatedAssetTagValue = roi.associatedAssetTagValue;
                                var currentValue
                                for (var index in $scope.availableAssets) {
                                    if ($scope.availableAssets[index].assetId == roi.associatedAssetId) {
                                        currentValue = $scope.availableAssets[index].currentValueWithoutTax;
                                        //currentValue = 10;
                                    }

                                }
                                item.unitPrice = currentValue;
                                item.negotiatedUnitPrice = currentValue;
                            }
                            item.excessQuantity = roi.excessQuantity;
                            item.productId = roi.productId;
                            if (roi.expiryDate != null && roi.expiryDate != undefined) {
                                item.roItemExpiryDate = roi.expiryDate;
                            }
                            trItems.push(item);
                        }
                    })
                });
                return trItems;
            }

            function setSkuToProducts(requestOrderItems) {
                if (requestOrderItems != null && requestOrderItems.length > 0)
                    requestOrderItems.forEach(function (roItem) {
                        //roItem.trPackaging = [];
                        roItem.skuList = $scope.skuProductMap[roItem.productId].filter(function (sku) {
                            return sku.skuStatus == 'ACTIVE';
                        });
                        roItem.skuList.forEach(function (sku) {
                            sku.skuPackagings = sku.skuPackagings.filter(function (mapping) {
                                return mapping.mappingStatus == "ACTIVE";
                            });
                            sku.skuPackagings.forEach(function (packaging) {
                                packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                            });
                            sku.skuPackagings = $scope.filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);
                        });
                        //roItem.skuList = $scope.removeInactive(roItem.skuList);  //TODO add this check back when everything is stable
                        roItem.selectedSku = $scope.initializeSelectedSku(roItem.skuList);
                        roItem.trPackaging = $scope.setDefaultPackagings(roItem, roItem.selectedSku);
                        var qty = 0;
                        roItem.trPackaging.forEach(function (trp) {
                            var pkgqty = 0;
                            trp.packagingDetails.forEach(function (pgd) {
                                pkgqty += pgd.transferredQuantity;
                            });
                            trp.transferredQuantity = parseFloat(pkgqty).toFixed(6);
                            qty += trp.transferredQuantity;
                        });
                        roItem.transferredQuantity = parseFloat(qty).toFixed(6);
                    });
            }

            $scope.removeInactive = function (skuList) {
                var skus = [];
                skuList.forEach(function (sku) {
                    if (sku.skuStatus == 'ACTIVE') {
                        var pkgs = [];
                        sku.skuPackagings.forEach(function (packaging) {
                            if (packaging.mappingStatus == 'ACTIVE' && packaging.packagingDefinition.packagingStatus == 'ACTIVE') {
                                pkgs.push(packaging);
                            }
                        });
                        sku.skuPackagings = pkgs;
                        skus.push(sku);
                    }
                });
                return skus;
            };

            $scope.initializeSelectedSku = function (skuList) {
                var ret = null;
                if (skuList.length == 1) {
                    ret = skuList[0];
                } else {
                    skuList.forEach(function (item) {
                        if (item.isDefault) {
                            ret = item;
                        }
                    });
                }
                return ret;
            }

            $scope.filterLoosePackaging = function (pkgList, looseOrdering) {
                var ret = pkgList;
                if (!looseOrdering) {
                    var pkgs = [];
                    pkgList.forEach(function (pkg) {
                        if (pkg.packagingDefinition.packagingType != "LOOSE") {
                            pkgs.push(pkg);
                        }
                    });
                    ret = pkgs;
                }
                return ret;
            }


            $scope.validTransferQty = function (item, transferQty) {
                if (item.transferredQuantity > $scope.productInventorySize[item.productId] || item.transferredQuantity < 0) {
                    item.transferredQuantity = 0;
                }else{
                    item.transferredQuantity = transferQty;
                }
            }

            $scope.setDefaultPackagings = function (roItem, sku) {
                var pgkDetails = [];
                var qty = roItem.requestedQuantity;
                qty = getCalculatedNumbers(sku, "CASE", qty, pgkDetails);
                if (qty > 0) {
                    qty = getCalculatedNumbers(sku, "INNER", qty, pgkDetails);
                }
                if (qty > 0) {
                    getCalculatedNumbers(sku, "LOOSE", qty, pgkDetails);
                }
                var trPackaging = [];
                var pkg = null;
                if (pgkDetails.length > 0) {
                    pkg = {
                        id: null,
                        skuId: sku.skuId,
                        skuName: sku.skuName,
                        packagingDetails: [],
                        requestedQuantity: roItem.requestedQuantity,
                        requestedAbsoluteQuantity: roItem.requestedAbsoluteQuantity,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: sku.unitOfMeasure,
                        unitPrice: (roItem.vendor != null && roItem.vendor.id == 22) ? null : sku.unitPrice,
                        negotiatedUnitPrice: (roItem.vendor != null && roItem.vendor.id == 22) ? null : sku.negotiatedUnitPrice,
                        requestOrderItemId: roItem.id,
                        purchaseOrderItemId: null,
                        goodReceivedItemId: null
                    };
                    pkg.packagingDetails = pgkDetails;
                    trPackaging.push(pkg);
                }
                return trPackaging;
            }

            function getCalculatedNumbers(sku, type, qty, pgkDetails) {
                var ret = qty;
                var packagingList = sortPackaging(sku.skuPackagings, type);
                if (sku.skuId == 205) {
                    console.log(packagingList);
                }
                packagingList.forEach(function (item) {
                    var unitsPacked = 0;
                    if (type == item.packagingDefinition.packagingType) {
                        if (type == "LOOSE") {
                            if ((ret / item.packagingDefinition.conversionRatio) > 0) {
                                unitsPacked = (ret / item.packagingDefinition.conversionRatio);
                                ret = ret - (unitsPacked * item.packagingDefinition.conversionRatio);
                            }
                        } else {
                            if (parseInt(ret / item.packagingDefinition.conversionRatio) > 0) {
                                unitsPacked = parseInt(ret / item.packagingDefinition.conversionRatio);
                                ret = Math.round((ret - (unitsPacked * item.packagingDefinition.conversionRatio)) * 1000000) / 1000000;
                            }
                        }
                        if (unitsPacked > 0) {
                            pgkDetails.push({
                                id: null,
                                packagingDefinitionData: item.packagingDefinition,
                                numberOfUnitsPacked: unitsPacked,
                                numberOfUnitsReceived: null,
                                transferredQuantity: unitsPacked * item.packagingDefinition.conversionRatio,
                                receivedQuantity: null
                            });
                        }
                    }
                });
                return ret;
            }

            function sortPackaging(packagings, type) {
                var pkgList = [];
                packagings.forEach(function (pkg) {
                    if (pkg.packagingDefinition.packagingType == type) {
                        pkgList.push(pkg);
                    }
                });
                pkgList.sort(function (a, b) {
                    return b.packagingDefinition.conversionRatio - a.packagingDefinition.conversionRatio;
                });
                return pkgList;
            }

            $scope.updateBillBookDetailsObject = function (billBookDetails) {
                $scope.manualBillBookDetails = billBookDetails;
                $scope.isBillBooksDetailFilled = false;
            }

            $scope.toggleBillBookDetailsView = function () {
                $scope.addBillBookDetails[manualBookProductId] = !$scope.addBillBookDetails[manualBookProductId];
            }

            $scope.fillManualBBDetails = function (trItem, $index, item) {
                $scope.toggleBillBookDetailsView();
            };
            $scope.manualDetailsRequired = function () {
                if ($scope.isBillBooksDetailFilled === undefined) {
                    $scope.isBillBooksDetailFilled = true;
                }
            };

            function initializeBillBookParams() {
                $scope.manualBillBookDetails = undefined;
                $scope.isBillBooksDetailFilled = undefined;
                $scope.addBillBookDetails = {};
                $scope.addBillBookDetails[manualBookProductId] = false;
            }

            $scope.backTransfer = function () {
                $scope.selectedRO.roType = null;
                $scope.selectedRO.count = 0;
                $scope.showPendingRequest = true;
                $scope.isInvoiceRo = false;
                $scope.requestOrderDetail = null;
                $scope.semiFishedProducts = null;
                $scope.clubbedROs = {};
                $scope.arrayAfterClubbing = {};
                $scope.getPendingRequestOrders($scope.selectedDate);
            };

            $scope.hasDrillDown = function (trItem) {
                return trItem.drillDowns != null && trItem.drillDowns != undefined && trItem.drillDowns.length > 0;
            };

            $scope.onSkuChanged = function (item) {
                item.associatedAssetTagValue = null;
                item.associatedAssetId = null;
                item.assetValidated = false;
                if ($scope.selectedRO.roType == 'FIXED_ASSET' && $scope.selectedRO.count > 0) {
                    $scope.addPackaging(item);
                }
            };

            $scope.validateAssetTagValue = function (associatedAssetTagValue, item, index) {
                if (associatedAssetTagValue == null || associatedAssetTagValue.length != 6) {
                    item.assetValidated = false;
                    item.associatedAssetId = null;
                    return
                }
                console.log(item.selectedSku);
                if (item.selectedSku == null) {
                    $toastService.create("Please Select SKU first");
                    return;
                }
                if (index != -1) {
                    var counter = 0;
                    for (var i in $scope.requestOrderDetail.requestOrderItems) {
                        var item2 = $scope.requestOrderDetail.requestOrderItems[i];
                        if (item2.associatedAssetTagValue == associatedAssetTagValue) {
                            counter++;
                        }
                    }
                    if (counter > 1) {
                        $toastService.create("Asset already added ");
                        item.associatedAssetTagValue = null;
                        return;
                    }
                }

                for (var index in $scope.availableAssets) {
                    if ($scope.availableAssets[index].tagValue == associatedAssetTagValue) {

                        if ($scope.availableAssets[index].skuId == item.selectedSku.skuId) {
                            item.associatedAssetId = $scope.availableAssets[index].assetId;
                            item.assetValidated = true;
                            return;
                        }
                        break;
                    }
                }
                item.assetValidated = false;
                item.associatedAssetId = null;
                $toastService.create("Asset Tag Value Entered is not associated with selected SKU");
            }

        }]).controller('toQtyValidationCtrl', ['$scope', 'toItem', 'pkg', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window',
            function ($scope, toItem, pkg, appUtil, $toastService, apiJson, $http, Popeye, $window) {
                $scope.init = function () {
                    $scope.toItem = toItem;
                    $scope.pkg = pkg;
                    $scope.pkgClone = angular.copy($scope.pkg);
                    $scope.pkgClone.numberOfUnitsPacked = 0;
                }

                $scope.submit = function () {
                    if ($scope.pkgClone.numberOfUnitsPacked != $scope.pkg.numberOfUnitsPacked) {
                        Popeye.closeCurrentModal();
                    } else {
                        Popeye.closeCurrentModal({ success: true });
                    }
                }


                $scope.closeModal = function () {
                    Popeye.closeCurrentModal();
                };

            }
        ]).controller('scanViewModalCtrl', ['$scope', 'diffs', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', function ($scope, diffs, appUtil, $toastService, apiJson, $http, Popeye) {
            $scope.diffs = diffs;
            $scope.openList = {} ;
            $scope.toggleOpen = function(id){
            $scope.openList[id] = !$scope.openList[id] ;
            }
            $scope.cancel = function () {
                Popeye.closeCurrentModal(false);
            };
            $scope.submit = function () {
                Popeye.closeCurrentModal(true);
            }
        }]);;
