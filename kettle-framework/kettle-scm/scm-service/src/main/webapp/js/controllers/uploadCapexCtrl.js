angular.module('scmApp').controller(
    'uploadCapexCtrl',
    ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService, previewModalService, Popeye) {


            $scope.init = function () {
                $scope.capexList = null;
                $scope.unitList = appUtil.getUnitList();
                $scope.statusLists = ["All", "CREATED",
                    "APPROVED", "PENDING_APPROVAL_L1",
                    "PENDING_APPROVAL_L2",
                    "PENDING_APPROVAL_L3", "REJECTED_L1", "CLOSED_L1", "CLOSED_L2", "CLOSED_L3",
                    "REJECTED_L2", "REJECTED_L3", "INITIATE_CLOSURE", "DOCUMENT_UPLOADED",
                    "CANCELLED", "EDITED", "CLOSED"]
                $scope.capexUnit = null;
                $scope.capexStatus = null;
                $scope.capexVersion = null;
                $scope.objOfCurrentAndLastApprovedOriginal = {};
                $scope.getCapexVersionList();
                $scope.summaryItem = [];
                $scope.gridOptions = appUtil.getGridOptions($scope);
            }

            $scope.getCapexVersionList = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.getVersionList,
                }).then(function success(response) {
                    $scope.versionList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.getCapexRequestList = function (capexUnit, capexStatus, capexVersion) {
                if (capexUnit == null && capexStatus == null && capexVersion == null) {
                    $toastService.create("Please select atleast one option.");
                    return;
                }
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.getAllCapexData,
                    params: {
                        unitId: capexUnit == null ? null : capexUnit.id,
                        status: capexStatus,
                        version: capexVersion
                    }
                }).then(function success(response) {
                    $scope.capexList = response.data;
                }, function error(response) {
                    $toastService.create("No Capex Request is found for this Unit.");
                    $scope.capexList = [];
                    console.log("error:" + response);
                });
            }

            $scope.copyCapexId = function(){
                var copyText = document.getElementById("capexIdCopy");
                copyText.select();
                copyText.setSelectionRange(0, 99999); 
                if(navigator.clipboard){
                     navigator.clipboard.writeText(copyText.value);
                     $toastService.create("Copied Id : " + copyText.value);
                }else{
                    var textArea = document.createElement("textarea");
                    textArea.value = copyText.value;
                    // Avoid scrolling to bottom
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? 'successful' : 'unsuccessful';
                        console.log('Fallback: Copying text command was ' + msg);
                    } catch (err) {
                        console.error('Fallback: Oops, unable to copy', err);
                    }
                    document.body.removeChild(textArea);
                    $toastService.create("Copied Id : " + copyText.value);
                }
                
            }

            $scope.downloadfile = function (item) {
                $http({
                    method: "POST",
                    url: apiJson.urls.capexManagement.getCapexFile,
                    data: item,
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(function (response, status, headers) {
                    $rootScope.showFullScreenLoader = false;
                    var fileName = headers("Content-Disposition");
                    var blob = new Blob([response],
                        {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'}, fileName);
                    saveAs(blob, fileName);
                }, function (error) {
                    $toastService.create("Could not download the document... Please try again");
                });
            }

            $scope.uploadDoc = function (item) {
                $fileUploadService.openFileModal("Upload Capex Sheet", "Find", function (file) {
                    $scope.validateFile(file, item);
                });
            };

            $scope.validateFile = function (file, item) {
                $rootScope.showFullScreenLoader = true;
                var fd = new FormData(document.forms[0]);
                fd.append("file", file);
                $http({
                    url: apiJson.urls.capexManagement.validateCapex,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    var obj = response;
                    if (obj.id == item.capexRequestId && obj.accessKey == item.accessKey) {
                        $scope.getDepartmentList(file);
                    } else {
                        $toastService.create("Capex sheet is not valid.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error in uploaded sheet.");
                    }
                });
            };

            $scope.getDepartmentList = function (file) {
                $rootScope.showFullScreenLoader = true;
                var fd = new FormData(document.forms[0]);
                fd.append("file", file);
                $http({
                    url: apiJson.urls.capexManagement.getDepartmentList,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $scope.deptList = response;
                    $scope.showDepartmentView(file, $scope.deptList);
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error in parsing sheet.");
                    }
                });
            }

            $scope.showDepartmentView = function (file, deptList) {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'departmentView.html',
                    controller: 'departmentViewCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        departmentList: function () {
                            return deptList;
                        }
                    },
                });
                modalInstance.closed
                    .then(function (isSuccessful) {
                        if (isSuccessful) {
                            $scope.uploadCapexSheet(file);
                        } else {
                            $toastService.create("Uploading is cancelled");
                        }
                    });
            }


            $scope.uploadCapexSheet = function (file) {
                $rootScope.showFullScreenLoader = true;
                var fd = new FormData(document.forms[0]);
                fd.append("file", file);
                fd.append("uploadedBy", appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.capexManagement.uploadCapexFile,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    if (response) {
                        $toastService.create("File uploaded successfully.");
                        $scope.getCapexRequestList($scope.capexUnit, $scope.capexStatus, $scope.capexVersion);
                    } else {
                        $toastService.create("Error in Approving Capex Budget. Uploaded Budget is not valid.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error in uploaded sheet.");
                    }
                });
            };

            $scope.changeStatus = function (id, status, comment) {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.changeCapexStatus,
                    params: {
                        capexRequestId: id,
                        status: status,
                        userId: appUtil.getCurrentUser().userId,
                        comment: comment
                    },
                }).then(function success(response) {
                    if (response) {
                        $toastService.create("Status updated successfully.");
                        $scope.getCapexRequestList($scope.capexUnit, $scope.capexStatus, $scope.capexVersion);
                    } else {
                        $toastService.create("Error in updating Capex Status.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.initiateClosureState = function (id, status, comment) {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.initiateClosureState,
                    params: {
                        capexRequestId: id,
                        status: status,
                        userId: appUtil.getCurrentUser().userId,
                        comment: comment
                    },
                }).then(function success(response) {
                    if (response.data == "") {
                        $toastService.create("Status updated successfully.");
                        $scope.getCapexRequestList($scope.capexUnit, $scope.capexStatus, $scope.capexVersion);
                    } else {
                        $alertService.alert(response.data);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.editRequest = function (item) {
                item.uploadedBy = appUtil.getCurrentUser().userId;
                $http({
                    method: "POST",
                    url: apiJson.urls.capexManagement.editCapexRequest,
                    data: item,
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(function (response, status, headers) {
                    $scope.getCapexRequestList($scope.capexUnit, $scope.capexStatus, $scope.capexVersion);
                    $rootScope.showFullScreenLoader = false;
                    var fileName = headers("Content-Disposition");
                    var blob = new Blob([response],
                        {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'}, fileName);
                    saveAs(blob, fileName);
                }, function (error) {
                    $toastService.create("Could not download the document... Please try again");
                });
            }

            $scope.approveCapex = function (item) {
                $alertService.confirm("Are you sure you want to approve Budget?", "", function (result) {
                    if (result) {
                        $scope.showBudgetForApproval(item);
                    }
                });
            }

            $scope.showBudgetForApproval = function (item) {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.getDeptBudgetForApproval,
                    params: {
                        capexAuditId: item.id
                    },
                }).then(function success(response) {
                    $scope.deptListForApproval = response.data;
                    $scope.getBudget(item);
                    $scope.showDepartmentViewForApproval(item, $scope.deptListForApproval);
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.showDepartmentViewForApproval = function (item, deptListForApproval) {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'departmentView.html',
                    controller: 'departmentViewCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        departmentList: function () {
                            return deptListForApproval;
                        }
                    },
                });
                modalInstance.closed
                    .then(function (isSuccessful) {
                        if (isSuccessful) {
                            $scope.approveCapexBudget(item);
                        } else {
                            $toastService.create("Approval is cancelled");
                        }
                    });
            }

            $scope.getBudget = function (item, callback) {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.getBudgetComaparision,
                    params: {
                        unitId: item.unitId,
                        capexRequestId: item.capexRequestId,
                        currentCapexId: item.id
                    }
                }).then(function success(response) {
                    if (response.status == 200) {
                        var emptyCheck = appUtil.isEmptyObject(response.data);
                        $scope.result = response.data;
                        if (emptyCheck) {
                            $toastService.create("No Budget comparision details found.");
                        } else {
                            var sumOfLastApprovedOriginalAmount = 0;
                            var sumOfCurrentOriginalAmount = 0;
                            for (var i = 0; i < $scope.result.length; i++) {
                                sumOfCurrentOriginalAmount += $scope.result[i].currentOriginalAmount != null ? $scope.result[i].currentOriginalAmount : 0;
                                sumOfLastApprovedOriginalAmount += $scope.result[i].lastApprovedOriginalAmount != null ? $scope.result[i].lastApprovedOriginalAmount : 0;
                            }

                            $scope.objOfCurrentAndLastApprovedOriginal = {
                                sumOfLastApprovedOriginalAmountForApproval: sumOfLastApprovedOriginalAmount,
                                sumOfCurrentOriginalAmountForApproval: sumOfCurrentOriginalAmount
                            };
                            console.log("obj is ", $scope.objOfCurrentAndLastApprovedOriginal);
                            if (callback != null && callback != undefined) {
                                callback();
                            }
                        }
                    }
                }, function error(response) {
                    $toastService.create("No Budget comparision details found.");
                    console.log("error:" + response);
                });
            };

            $scope.showSO = function (budgetItem, departmentItem) {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.getSOByDepartment,
                    params: {
                        unitId: budgetItem.unitId,
                        capexRequestId: budgetItem.capexRequestId,
                        departmentId: departmentItem.departmentId
                    }
                }).then(function (response) {
                    if (response.data != null) {
                        $scope.soList = response.data;
                        $scope.selectedItem = departmentItem.departmentName;
                        if (departmentItem.expanded != null) {
                            departmentItem.expanded = !departmentItem.expanded;
                        } else {
                            departmentItem.expanded = true;
                        }
                        var gridItemaArr = [];
                        $scope.soList.forEach(function (so) {
                            so.orderItems.forEach(function (soItem) {
                                var receivingPrice = soItem.receivedQuantity * soItem.unitPrice;
                                soItem.receivingAmount = (receivingPrice + (receivingPrice * soItem.taxRate) / 100);
                                soItem.vendorName = so.vendorName;
                                soItem.status = so.status;
                                gridItemaArr.push(soItem);
                            })
                        });
                        if (departmentItem.expanded === true) {
                            $scope.gridOptions.data = gridItemaArr;
                            $scope.gridOptions.columnDefs = $scope.showSOs();
                        }
                        if (departmentItem.expanded === false) {
                            $scope.selectedItem = null;
                        }
                    }
                });

            }

            $scope.showSOs = function () {
                return [
                    {
                        field: 'serviceOrderId',
                        name: 'serviceOrderId',
                        enableCellEdit: false,
                        displayName: 'SO Id'
                    }, {
                        field: 'id',
                        name: 'serviceOrderItemId',
                        displayName: 'Service Order Item id'
                    }
                    , {
                        field: 'costElementName',
                        name: 'costElementName',
                        enableCellEdit: false,
                        displayName: 'Cost Element Name'
                    }, {
                        field: 'vendorName',
                        name: 'vendorName',
                        enableCellEdit: false,
                        displayName: 'Vendor Name'
                    }, {
                        field: 'serviceDescription',
                        name: 'serviceDescription',
                        enableCellEdit: false,
                        displayName: 'Service Description',
                    }, {
                        field: 'totalCost',
                        name: 'totalCost',
                        enableCellEdit: false,
                        displayName: 'Cost'
                    }, {
                        field: 'totalTax',
                        name: 'totalTax',
                        enableCellEdit: false,
                        displayName: 'Tax'
                    }, {
                        field: 'amountPaid',
                        name: 'amountPaid',
                        enableCellEdit: false,
                        displayName: 'Total Amount'
                    }, {
                        field: 'receivingAmount',
                        name: 'receivingAmount',
                        enableCellEdit: false,
                        displayName: 'Receiving Amount'
                    }, {
                        field: 'paidAmount',
                        name: 'paidAmount',
                        enableCellEdit: false,
                        displayName: 'Paid Amount',
                    },
                    , {
                        field: 'status',
                        name: 'status',
                        enableCellEdit: false,
                        displayName: 'Status',
                    }
                ];
            };

            $scope.isSelectedItem = function (srItem) {
                return srItem.expanded == true;
            }

            $scope.approveCapexBudget = function (item) {
                var checkForApprovalL2L3 = function () {
                    if ($scope.objOfCurrentAndLastApprovedOriginal.sumOfCurrentOriginalAmountForApproval != null && $scope.objOfCurrentAndLastApprovedOriginal.sumOfLastApprovedOriginalAmountForApproval != null) {
                        if ($scope.objOfCurrentAndLastApprovedOriginal.sumOfCurrentOriginalAmountForApproval > $scope.objOfCurrentAndLastApprovedOriginal.sumOfLastApprovedOriginalAmountForApproval) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                };
                var isRequiredForL2L3Approval = checkForApprovalL2L3();
                console.log("is required for L2 L3 approval : ", isRequiredForL2L3Approval);
                if (isRequiredForL2L3Approval) {
                    if (item.status != 'PENDING_APPROVAL_L3') {
                        $scope.approvalStatusChange(item.id, 'APPROVED', item.status.substr(item.status.length - 2), item, isRequiredForL2L3Approval);
                    } else {
                        $scope.approveBudget(item);
                    }
                } else {
                    $scope.approveBudget(item);
                }

            };

            $scope.approvalStatusChangeCall = function (auditId, status, type, item, L2L3Approval) {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.approvalStatusChange,
                    params: {
                        capexAuditId: auditId,
                        status: status,
                        type: type,
                        userId: appUtil.getCurrentUser().userId,
                        isRequiredForL2L3Approval: L2L3Approval
                    },
                }).then(function success(response) {
                    if (response) {
                        $toastService.create("Capex Budget " + status + " Successfully.");
                        $scope.getCapexRequestList($scope.capexUnit, $scope.capexStatus, $scope.capexVersion);
                    } else {
                        $toastService.create("Error in " + status + " Capex Budget.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.approvalStatusChange = function (auditId, status, type, item, L2L3Approval) {
                var L2L3ApprovalCheck = L2L3Approval;
                if (L2L3ApprovalCheck == null || L2L3ApprovalCheck == undefined) {
                    $scope.getBudget(item, function () {
                        if ($scope.objOfCurrentAndLastApprovedOriginal.sumOfCurrentOriginalAmountForApproval != null && $scope.objOfCurrentAndLastApprovedOriginal.sumOfLastApprovedOriginalAmountForApproval != null) {
                            if ($scope.objOfCurrentAndLastApprovedOriginal.sumOfCurrentOriginalAmountForApproval > $scope.objOfCurrentAndLastApprovedOriginal.sumOfLastApprovedOriginalAmountForApproval) {
                                L2L3ApprovalCheck = true;
                            } else {
                                L2L3ApprovalCheck = false;
                            }
                        }
                        console.log("obj is : ", $scope.objOfCurrentAndLastApprovedOriginal);
                        $scope.approvalStatusChangeCall(auditId, status, type, item, L2L3ApprovalCheck);
                    });

                } else {
                    $scope.approvalStatusChangeCall(auditId, status, type, item, L2L3ApprovalCheck);
                }
            };

            $scope.approveBudget = function (item) {
                item.uploadedBy = appUtil.getCurrentUser().userId;
                $http({
                    method: "POST",
                    url: apiJson.urls.capexManagement.approveCapexBudget,
                    params: {
                        "userId": appUtil.getCurrentUser().userId,
                    },
                    data: item
                }).then(function success(response) {
                    if (response.data) {
                        $toastService.create("Capex Budget Approved Successfully.");
                        $scope.getCapexRequestList($scope.capexUnit, $scope.capexStatus, $scope.capexVersion);
                    } else {
                        $toastService.create("Error in Approving Capex Budget. Budget Upload is not valid.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.capexModal = function () {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'capexModal.html',
                    controller: 'capexModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {}
                });
                modalInstance.closed
                    .then(function (isSuccessful) {
                        if (isSuccessful) {
                            $scope.getCapexRequestList($scope.capexUnit, $scope.capexStatus, $scope.capexVersion);
                        }
                    });
            }

            $scope.fetchBudgetForUnit = function (item, status) {
                $scope.check = false;
                if (status == "VIEW") {
                    $scope.check = true;
                }
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.fetchBudgetDetails,
                    params: {
                        unitId: item.unitId,
                        capexRequestId: item.capexRequestId
                    }
                }).then(function success(response) {
                    if (response) {
                        $scope.showBudgetView(response.data, item, $scope.check, status, $scope.capexList);
                    } else {
                        $toastService.create("Error in showing Capex Budget.");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.showBudgetView = function (itemData, item, check, status, capexList) {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'budgetModal.html',
                    controller: 'budgetModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        budgetList: function () {
                            return itemData;
                        },
                        closureCheck: function () {
                            return check;
                        },
                        item: function () {
                            return item;
                        },
                        capexList: function () {
                            return capexList;
                        }
                    },
                });
                modalInstance.closed
                    .then(function (isSuccessful) {
                        if (isSuccessful && isSuccessful != undefined) {
                            $scope.changeStatus(item.id, status, '');
                        }
                    });
            }

            $scope.showBudgetComparisonView = function (item) {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'budgetComparisionModal.html',
                    controller: 'budgetComparisionModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        item: function () {
                            return item;
                        }
                    }
                });
            }

            $scope.openDocumentModel = function (item) {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'documentModal.html',
                    controller: 'documentModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        item: function () {
                            return item;
                        }
                    },
                });
                modalInstance.closed
                    .then(function (comment) {
                        if (comment != '' && comment != undefined) {
                            $scope.changeStatus(item.id, 'DOCUMENT_UPLOADED', comment);
                        }
                    });
            }


        }]).controller("capexModalCtrl",
    ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService, previewModalService, Popeye) {


            $scope.init = function () {
                $scope.unitList = [];
                $scope.selectedUnit = null;
                $scope.type = null;
                $scope.types = [];
                $scope.types = ["New Cafe", "Renovation"];
                $scope.unitList = appUtil.getUnitList();
            }

            $scope.createCapex = function (type, selectedUnit) {
                $http({
                    method: "GET",
                    url: apiJson.urls.capexManagement.validateUnit,
                    params: {
                        unitId: selectedUnit.id,
                        type: type
                    },
                }).success(function (response) {
                    if (!response) {
                        $scope.createCapexRequest(type, selectedUnit);
                    } else {
                        $toastService.create("Capex Already Exist of type " + type + " for this unit");
                    }
                }, function (error) {
                    $toastService.create("Could not validate Capex");
                });
            }

            $scope.createCapexRequest = function (type, selectedUnit) {
                var currentUser = appUtil.getCurrentUser();
                var reqObj = {
                    type: type,
                    unitName: selectedUnit.name,
                    unitId: selectedUnit.id,
                    generatedBy: currentUser.userId,
                }
                $http({
                    method: "POST",
                    url: apiJson.urls.capexManagement.createCapex,
                    data: reqObj,
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(function (response, status, headers) {
                    $rootScope.showFullScreenLoader = false;
                    var fileName = headers("Content-Disposition");
                    var blob = new Blob([response],
                        {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'}, fileName);
                    saveAs(blob, fileName);
                    $toastService.create("Capex Request Created Successfully.");
                    Popeye.closeCurrentModal(true);
                }, function (error) {
                    $toastService.create("Could not download the document... Please try again");
                });
            };

            $scope.cancel = function () {
                Popeye.closeCurrentModal(false);
            };

        }]).controller('departmentViewCtrl', ['$scope', 'appUtil', 'Popeye', 'departmentList',
    function ($scope, appUtil, Popeye, departmentList) {


        $scope.init = function () {
            $scope.amountTotal = 0;
            $scope.summaryDepartmentList = departmentList;
            $scope.getTotalAmount();
        }

        $scope.getTotalAmount = function () {
            for (var x = 0; $scope.summaryDepartmentList.length; x++) {
                $scope.amountTotal = parseFloat($scope.amountTotal) + parseFloat($scope.summaryDepartmentList[x].amount);
            }
        }


        $scope.submit = function () {
            Popeye.closeCurrentModal(true);
        }

        $scope.cancelUpload = function () {
            Popeye.closeCurrentModal(false);
        }


    }]).controller('budgetModalCtrl', ['$rootScope', '$scope', 'appUtil', 'apiJson', '$http', 'Popeye', 'budgetList', 'closureCheck', 'item',
    function ($rootScope, $scope, appUtil, apiJson, $http, Popeye, budgetList, closureCheck, item) {


        $scope.init = function () {
            $scope.amountTotal = 0;
            $scope.showExpandedView = false;
            $scope.unitId = item.unitId;
            $scope.capexRequestId = item.capexRequestId;
            $scope.closureCheck = closureCheck;
            $scope.summaryDepartmentList = budgetList;
            $scope.displayItem = item;
            //$scope.getTotalAmount();
        }

        $scope.getTotalAmount = function () {
            for (var x = 0; $scope.summaryDepartmentList.length; x++) {
                $scope.amountTotal = parseFloat($scope.amountTotal) + parseFloat($scope.summaryDepartmentList[x].amount);
            }
        }

        $scope.viewClosureView = function () {
            $scope.showExpandedView = true;
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getClosureComment,
                params: {
                    capexRequestId: item.capexRequestId
                }
            }).then(function success(response) {
                $scope.comment = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });

        }

        $scope.downloadDocSheet = function (sheetName) {
            $rootScope.showFullScreenLoader = true;
            var name = sheetName + '-' + $scope.unitId + '-' + $scope.capexRequestId + '.pdf';
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getCapexClosureFile,
                params: {
                    sheetName: sheetName,
                    unitId: $scope.unitId,
                    capexRequestId: $scope.capexRequestId
                },
                responseType: 'arraybuffer',
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                var blob = new Blob([response], {type: appUtil.mimeTypes["PDF"]}, name);
                saveAs(blob, name);
            }, function (error) {
                $toastService.create("Could not download the document... Please try again");
            });
        }

        $scope.cancel = function () {
            Popeye.closeCurrentModal(false);
        }

        $scope.submit = function () {
            Popeye.closeCurrentModal(true);
        }


    }]).controller('documentModalCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
    '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', 'item',
    function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService, previewModalService, Popeye, item) {


        $scope.init = function () {
            $scope.item = item;
            $scope.comment = null;
            /*$scope.list = [ 'Feasibility_Report',
               'Projected_P&L', 'Possession_Letter',
               'Drawing_Set', 'Capex_Closure_Sheet',
               'Accepted_Work_Order_Copy&BOQ',
               'Invoice_for_Advance',
               'Civil_Final_Invoice',
               'Warranty_Paper',
               'Site_Completion_Certificate',
               'Time_Spent_Report' ];*/
            $scope.sheetMap = [{
                name: "Feasibility_Report",
                status: false,
            }, {
                name: "Projected_P&L",
                status: false,
            }, {
                name: "Possession_Letter",
                status: false,
            }, {
                name: "Drawing_Set",
                status: false,
            }, {
                name: "Capex_Closure_Sheet",
                status: false,
            }, {
                name: "Accepted_Work_Order_Copy&BOQ",
                status: false,
            }, {
                name: "Invoice_for_Advance",
                status: false,
            }, {
                name: "Civil_Final_Invoice",
                status: false,
            }, {
                name: "Warranty_Paper",
                status: false,
            }, {
                name: "Site_Completion_Certificate",
                status: false,
            }, {
                name: "Time_Spent_Report",
                status: false,
            }];
        }

        $scope.uploadDocSheet = function (sheetName) {
            $fileUploadService.openFileModal("Upload " + sheetName, "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                if (file.size > 2048000) {
                    $toastService.create('File size should not be greater than 2MB.');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf') {
                    $scope.uploadClosureSheets(file, sheetName);
                } else {
                    $toastService.create('File is not Valid');
                }
            });
        };

        $scope.uploadClosureSheets = function (file, sheetName) {
            $rootScope.showFullScreenLoader = true;
            var fd = new FormData(document.forms[0]);
            fd.append("file", file);
            fd.append("sheetName", sheetName);
            fd.append("unitId", $scope.item.unitId);
            fd.append("capexRequestId", $scope.item.capexRequestId);
            $http({
                url: apiJson.urls.capexManagement.uploadClosureSheet,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (response) {
                    $toastService.create(sheetName + " Uploaded Successfully");
                    for (var x = 0; x < $scope.sheetMap.length; x++) {
                        if (sheetName == $scope.sheetMap[x].name) {
                            $scope.sheetMap[x].status = true;
                            break;
                        }
                    }
                } else {
                    $toastService.create("Error in uploading sheet.");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (response.errorMsg != null) {
                    $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                } else {
                    $toastService.create("Error in uploaded sheet.");
                }
            });
        };


        $scope.cancel = function () {
            Popeye.closeCurrentModal('');
        }

        $scope.submit = function () {
            if ($scope.comment == null || $scope.comment == undefined || $scope.comment.trim() == '') {
                $toastService.create("Please Input Comment.");
                return;
            }
            var check = true;
            for (var x = 0; x < $scope.sheetMap.length; x++) {
                if (!$scope.sheetMap[x].status) {
                    check = false;
                    $scope.comment = '';
                    $toastService.create($scope.sheetMap[x].name + " sheet is not uploaded. Please upload all sheets before submitting.");
                    break;
                }
            }
            if (check) {
                Popeye.closeCurrentModal($scope.comment);
            }
        }

    }]).controller('budgetComparisionModalCtrl', ['$rootScope', '$scope', 'appUtil', 'apiJson', '$http', '$toastService', 'Popeye', 'item',
    function ($rootScope, $scope, appUtil, apiJson, $http, $toastService, Popeye, item) {

        $scope.init = function () {
            $scope.currentItem = item;
            $scope.emptyResult = false;
            $scope.getBudgetComparision();
                 
        };

        $scope.getBudgetComparision = function () {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getBudgetComaparision,
                params: {
                    unitId: $scope.currentItem.unitId,
                    capexRequestId: $scope.currentItem.capexRequestId,
                    currentCapexId: $scope.currentItem.id
                }
            }).then(function success(response) {
                if (response.status == 200) {
                    var emptyCheck = appUtil.isEmptyObject(response.data);
                    $scope.result = response.data;
                    if (emptyCheck) {
                        $scope.emptyResult = true;
                        $toastService.create("No Budget comparision details found.");
                    } else {
                        if ($scope.result.length > 0) {
                            $scope.emptyResult = false;
                            var sumOfLastApprovedOriginalAmount = 0;
                            var sumOfCurrentOriginalAmount = 0;
                            for (var i = 0; i < $scope.result.length; i++) {
                                sumOfCurrentOriginalAmount += $scope.result[i].currentOriginalAmount != null ? $scope.result[i].currentOriginalAmount : 0;
                                sumOfLastApprovedOriginalAmount += $scope.result[i].lastApprovedOriginalAmount != null ? $scope.result[i].lastApprovedOriginalAmount : 0;
                            }

                            $scope.currentAndLastApprovedOriginalAmount = {
                                sumOfLastApprovedOriginalAmountForApproval: sumOfLastApprovedOriginalAmount,
                                sumOfCurrentOriginalAmountForApproval: sumOfCurrentOriginalAmount
                            };
                        }
                    }
                }
            }, function error(response) {
                $toastService.create("No Budget comparision details found.");
                console.log("error:" + response);
            });
        };

        $scope.cancel = function () {
            Popeye.closeCurrentModal();
        };

    }]);