angular.module('scmApp').controller(
    'vendorToCostElementMapCtrl',
    ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
                  previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
            	$scope.mappingTable = [];
            	$scope.vendors = [];
            	$scope.hideModalGrid = true;
            	$scope.costElements =[];
            	$scope.getCostElement();
            	metaDataService.getServiceVendors(function(serviceVendors){
                    $scope.vendors = serviceVendors;
                });
            }


            $scope.getCostElement = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getCostElementData,
				}).then(function success(response) {
					if(response.data != null){
						$scope.costElements = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }

            $scope.searchMappings = function () {
                if ($scope.selectedVendor == undefined || $scope.selectedVendor == null
                    || $scope.selectedVendor.id == 0) {
                    return;
                }
                $scope.getVendorCostElementMappingsGrid();
            }

            $scope.getVendorCostElementMappingsGrid = function () {
                $scope.gridOptions = $scope.vendorGridOptions();
                $scope.getAllVendorCostMappings();
            }

            $scope.getAllVendorCostMappings = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getVendorToCostElementMapping,
					params : {
						vendorId : $scope.selectedVendor.id
					}
				}).then(function success(response) {
					if(response.data != null){
						$scope.gridOptions.data = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }


            $scope.checkIsInGrid=function (item){
                for(var i in $scope.newMappingList){
                    if($scope.newMappingList[i].id==item.id){
                        return false;
                    }
                }
                return true;
            }

            $scope.getAllVendorCostMappingsToClone = function(){
                if($scope.selectedVendor.id == $scope.selectedVendorToClone.id){
                    $toastService.create("Can't not clone from same vendor");
                    return;
                }
                $http({
                    method : 'GET',
                    url : apiJson.urls.serviceMappingManagement.getVendorToCostElementMapping,
                    params : {
                        vendorId : $scope.selectedVendorToClone.id
                    }
                }).then(function success(response) {
                    if(response.data != null){
                        console.log(response.data);
                        for(var i in response.data){
                            var item= response.data[i];
                            if(item.status==='ACTIVE' && item.mappingStatus==='ACTIVE' && $scope.checkIsInGrid(item)){
                                item.mappingStatus='NA';
                                $scope.newMappingList.push(item);
                            }
                        }
                        $scope.modalGridOptions.data = $scope.newMappingList;
                        console.log($scope.modalGridOptions.data);
                        $scope.hideModalGrid=false;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                })
            }

            $scope.openVendorToCostElementMapModal = function () {
            	 $scope.newMappingList = [];
                if ($scope.selectedVendor != undefined && $scope.selectedVendor.id > 0) {
                	 $scope.getModalSkuMappGrid();
                }
            }

            $scope.getModalSkuMappGrid = function () {
                $scope.modalGridOptions = $scope.vendorGridOptions();
                $scope.modalGridOptions.data = [];
            }

            $scope.vendorGridOptions = function () {
                return {
                    enableFiltering: true,
                    enableColumnResizing: true,
                    enableColumnMenus: false,
                    columnDefs: [{
                        field: 'id',
                        displayName: 'Cost Element Id',
                        //cellTemplate: 'skuIdTemplate.html'
                    }, {
                        field: 'name',
                        displayName: 'Cost Element Name'
                    }, {
                        field: 'status',
                        displayName: 'Cost Element Status'
                    }, {
                        field: 'mappingStatus',
                        displayName: 'Mapping Status'
                    }, {
                        field: 'code',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeRemoveButton.html'
                    }]
                };
            }

            $scope.removeRow = function (value) {
                var index = $scope.modalGridOptions.data.indexOf(value);
                $scope.modalGridOptions.data.splice(index, 1);
            }

            $scope.cancelModal = function () {
                $scope.hideModalGrid = true;
                $timeout(function () {
                    $('#modalValueDataId').val('').trigger('change');
                    $('#modalValueDataId2').val('').trigger('change');
                });
            }

            $scope.addToModalGridData = function () {
                $scope.hideModalGrid = false;
                data = $scope.selectedCostElement;
                if (data == undefined || data == null) {
                    return;
                }
                if(!$scope.checkIsInGrid(data)){
                    $toastService.create('Duplicate Mapping');
                    return;
                }
                data.mappingStatus = "NA";
                var index = $scope.newMappingList.indexOf(data);
                if (index > -1) {
                    $toastService.create('Duplicate Mapping');
                } else {
                    $scope.newMappingList.push(data);
                    $scope.modalGridOptions.data = $scope.newMappingList;
                }
            }

            $scope.submitModalGridData = function () {
                var list = [];
                var x;
                for (x in $scope.newMappingList) {
                    list.push($scope.newMappingList[x].id);
                }
                var currentUser = appUtil.getCurrentUser();
                payload = {
                    id: $scope.selectedVendor.id,
                    mappingIds: list,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }
                $http({
                    url: apiJson.urls.serviceMappingManagement.addVendorToCostElementMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response) {
                        $toastService.create('Mappings Added Successfully');
                        $scope.cancelModal();
                        $scope.searchMappings();
                    }
                }, function (response) {
                    console.log("error", response);
                });
            }

            $scope.changeStatus = function (value) {
                var vendorId = null;
                var costElementId = null;
                var status = value.mappingStatus == 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
                costElementId = value.id;
                    vendorId = $scope.selectedVendor.id;
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                    vendorId: vendorId,
                    costElementId: costElementId,
                    status: status,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }

                $http({
                    url: apiJson.urls.serviceMappingManagement.updateCostelementVendorMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data == true) {
                        $scope.updateGridRowStatus(value, status);
                        var msg = 'Mapping '
                        msg = msg + (status == 'ACTIVE' ? 'Activated' : 'Deactivated')
                        msg = msg + ' Successfully'
                        $toastService.create(msg);
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.updateGridRowStatus = function (value, status) {
                var x = null;
                var id = null;
                for (x in $scope.gridOptions.data) {
                    if ($scope.gridOptions.data[x].id == value.id) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.gridOptions.data[id].mappingStatus = status;
                }
            }



    }
]
);
