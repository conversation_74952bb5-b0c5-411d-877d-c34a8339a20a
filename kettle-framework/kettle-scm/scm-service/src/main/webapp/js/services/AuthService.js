/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 27-04-2016.
 */
angular.module('scmApp').service('authService', ['appUtil', '$cookieStore', function (appUtil, $cookieStore) {
    var service = this;
    service.authorization = null;
    service.getAuthorization = getAuthorization;
    service.setAuthorization = setAuthorization;
    service.canView = canView;
    service.setMenu = setMenu;

    function getAuthorization() {
        if (this.authorization == null || angular.isUndefined(this.authorization)) {
            service.authorization = $cookieStore.get("scmToken");
        }
        return service.authorization;
    };

    function setAuthorization(authorization) {
        service.authorization = authorization;
        $cookieStore.put('scmToken', authorization);
    };

    function canView(permission) {
        console.log("inside canView function");
        var flag = false;
        if (service.authorization != undefined) {
            do {
                if (appUtil.checkPermissions(permission, service.authorization.permissions, "view")) {
                    return true;
                } else {
                    permission = appUtil.stripPermissions(permission);
                }
            } while (permission.length > 0 && permission.lastIndexOf(".") != -1);
        }
        return flag;
    };

    function setMenu(stateName) {
        var index = stateName.search('menu');
        if (index != -1 && index == 0) {

        }
    };
    return service;
}]);