#!/bin/sh

source_env_name=$1
source_schema_name=$2
target_env_name=$3
target_schema_name=$4
current_date=$(date "+%Y.%m.%d")

function failure_email {
failure_message="Failed to copy data from schema $source_env_name : $source_schema_name to schema $target_env_name : $target_schema_name"
aws sns publish --topic-arn arn:aws:sns:eu-west-1:427994371464:DATA_COPY_FAILURE_EMAIL --subject "DB Dump Failure for $current_date" --message "$failure_message"
exit
}

function success_email {
success_message="Data copies successfully from schema $source_env_name : $source_schema_name to schema $target_env_name : $target_schema_name"
aws sns publish --topic-arn arn:aws:sns:eu-west-1:427994371464:DATA_COPY_SUCCESS_EMAIL --subject "DB Dump Successful for $current_date" --message "$success_message"
exit
}

echo "Copying data from schema $source_env_name : $source_schema_name to schema $target_env_name : $target_schema_name"
dump_dir=/data/db/dump/$source_env_name/$source_schema_name
echo "Dump Dir :" "$dump_dir"
mkdir -p $dump_dir
file_name=$source_schema_name

current_time=$(date "+%Y.%m.%d-%H.%M.%S")
echo "Current Time : $current_time"

new_fileName=$file_name.$current_time.sql
echo "New FileName: " "$new_fileName"

dump_file_name=$dump_dir/$new_fileName

echo "Dumping data to file $dump_file_name"

mysqldump -h$source_env_name.kettle.chaayos.com -udbdump -pdump $source_schema_name > $dump_file_name
if mysqldump -h$source_env_name.kettle.chaayos.com -udbdump -pdump $source_schema_name 2>$dump_file_name.log > $dump_file_name
then
    echo "Data dump completed successfully from $source_env_name : $source_schema_name and the file is created at $dump_file_name"

    echo "Now Copying the data to the target database  $target_env_name : $target_schema_name"

if mysql -udbcopy -pdbcopy -h$target_env_name.kettle.chaayos.com $target_schema_name < $dump_file_name
then
    echo "Data copied successfully to  $target_env_name : $target_schema_name"
success_email
else
    echo "Failed to copy data to  $target_env_name : $target_schema_name"
fi
else
    echo -e "mysqldump failed at $(date +'%d-%m-%Y %H:%M:%S')"$'\r' >> $dump_file_name.log
fi
failure_email
