#!/bin/sh

cd /home/<USER>/application/chaayos/kettle-admin
#echo "############Stashing Any Local Changes############"
#git stash
echo "############Pulling out new changes############"
git pull
echo "############Running Build Process############"
mvn clean install -Dmaven.test.skip=true
STATUS=$?
if [ $STATUS -eq 0 ]; then
echo "############Deployment Successful############"
cd /usr/share/tomcat8/webapps
echo "############Stopping Tomcat Server############"
service tomcat8 stop
echo "############Copying The war file############"
cp /home/<USER>/application/chaayos/kettle-admin/target/kettle-admin-0.0.1.war ./kettle-admin.war
echo "############Starting Tomcat Server############"
service tomcat8 start
else
echo "############Deployment Failed############"
fi
