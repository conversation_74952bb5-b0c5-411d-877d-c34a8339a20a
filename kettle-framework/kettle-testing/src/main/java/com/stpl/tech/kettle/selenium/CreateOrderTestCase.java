package com.stpl.tech.kettle.selenium;

import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.master.core.WebServiceHelper;

public class CreateOrderTestCase {
	
	String token;
	
	public CreateOrderTestCase(String token) {
		super();
		this.token = token;
	}

	public boolean createOrder(String token, String emailId,String contactNumber,int unitId, int terminalId, String customerName, int numberOfItems) {
		
		CustomerResponse response =  signInCustomer(unitId, contactNumber);
		if(response.isNewCustomer()) {
				//signUpCustomer(unitId, contactNumber, emailId);
		}
		
		return false;
		
	}

	private CustomerResponse signInCustomer(int unitId, String contactNumber) {
		CustomerResponse request = new CustomerResponse();
		request.setUnitId(unitId);
		request.setUnitId(unitId);
		CustomerResponse response = WebServiceHelper.postWithAuth(
				"http://dev.kettle.chaayos.com:9595/kettle-inventory/rest/v1/inventory-data/cafe-inventory", token,
				request, CustomerResponse.class);
		return response;
	}
}
