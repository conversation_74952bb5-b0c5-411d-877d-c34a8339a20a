/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.data.dao.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;
import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;

import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderItemMetaDataDetail;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.PartnerOrderRiderStatesDetail;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionRequest;
import org.hibernate.query.internal.NativeQueryImpl;
import org.hibernate.transform.AliasToEntityMapResultTransformer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.core.data.vo.OrderStatusData;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.FeedbackManagementDao;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.OrderSearchDao;
import com.stpl.tech.kettle.data.dao.PaidEmployeeMealDao;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.EmployeeMealData;
import com.stpl.tech.kettle.data.model.HouseCostEvent;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.OrderNPSDetail;
import com.stpl.tech.kettle.data.model.OrderSettlement;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.ClosureState;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderNPS;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.PrintType;
import com.stpl.tech.util.TemplateRenderingException;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Repository
public class OrderSearchDaoImpl extends AbstractDaoImpl implements OrderSearchDao {

    private static final Logger LOG = LoggerFactory.getLogger(OrderSearchDaoImpl.class);
    @Autowired
    public FeedbackManagementDao feedbackManagementDao;
    @Autowired
    public PaymentGatewayDao payment;
    @Autowired
    private CustomerDao customeDao;
    @Autowired
    private LoyaltyDao loyaltyDao;
    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private RecipeCache recipeCache;
    @Autowired
    private MetadataCache metadataCache;
    @Autowired
    private PaidEmployeeMealDao paidEmployeeMealDao;

    @Autowired
    private PartnerOrderRiderStatesDetailDao partnerOrderRiderStatesDetailDao;

    @Autowired
    private EnvironmentProperties props;
    private MessageProducer producer;
    private SQSSession session;

    @PostConstruct
    public void setQueueSessions() throws JMSException {
		LOG.info("POST-CONSTRUCT OrderSearchDaoImpl - STARTED");
		Regions region = AppUtils.getRegion(props.getEnvironmentType());
        session = SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE);
        producer = SQSNotification.getInstance().getProducer(session, props.getEnvironmentType().name(),
                "_ORDER_STATUS_EVENTS");
    }

    public Order getOrderDetail(int orderId) throws DataNotFoundException {
        try {
            OrderDetail data = manager.find(OrderDetail.class, orderId);
            SubscriptionPlan plan = null;
            if(data.getCustomerId()!=5)
            {
                plan = getActiveSubscription(data.getCustomerId());
            }
//            SubscriptionPlan plan = getActiveSubscription(data.getCustomerId());
            Order order = convertOrder(data, null);
            if (Objects.nonNull(plan) && Objects.nonNull(plan.getOverAllSaving())){
                getSubscriptionObject(plan, order);
            }
            return order;
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Order Details with ID : %d", orderId), e);
        }
    }

    private void getSubscriptionObject(SubscriptionPlan plan, Order order) {
        Subscription subscription = new Subscription();
        subscription.setOverAllSavings(plan.getOverAllSaving());
        order.setSubscriptionDetail(subscription);
    }

    public SubscriptionPlan getActiveSubscription(Integer customerId) {
        try {
            Query query = manager.createQuery("From SubscriptionPlan l where l.customerId = :customerId and l.status = :active");
            query.setParameter("customerId", customerId);
            query.setParameter("active", AppConstants.ACTIVE);
            SubscriptionPlan score = (SubscriptionPlan) query.getSingleResult();
            return score;
        } catch (NoResultException e) {
            LOG.info(String.format("No Subscription Found For Customer with ID : %d", customerId));
            return null;
        }
    }

    public int getLastOrderDetail(int unitId) throws DataNotFoundException {
        Query query = manager.createQuery("select max(E.orderId) FROM OrderDetail E where E.unitId = :unitId");
        query.setParameter("unitId", unitId);
        Object o = query.getSingleResult();
        if (o == null) {
            LOG.info(String.format("Did not find any order on unit %d", unitId));
            return 0;
        }
        Integer lastOrderId = (Integer) o;
        LOG.info("The last order productId before day close for the unit is " + lastOrderId);
        return lastOrderId;
    }

    @SuppressWarnings("unchecked")
    public List<Order> getOrderDetails(int unitId, Date startTime, Date endTime, OrderFetchStrategy strategy) {
        List<Order> orders = new ArrayList<Order>();
        Query query = manager.createQuery(
                "FROM OrderDetail E where E.unitId = :unitId and E.billingServerTime >= :startTime and E.billingServerTime <= :endTime");
        query.setParameter("unitId", unitId);
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        List<OrderDetail> list = query.getResultList();
        if (list == null) {
            LOG.info(String.format("Did not find any order for unit %d between %s and %s", unitId, startTime, endTime));
            return orders;
        }
        for (OrderDetail order : list) {
            orders.add(convertOrder(order, strategy));
        }
        return orders;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<String> getOrdersForUnitFromStatus(Integer unitId, List<String> statusList) {
        StringBuilder queryBuilder = new StringBuilder("SELECT od.generatedOrderId FROM OrderDetail od " +
                "where od.unitId = :unitId ");
        if(!CollectionUtils.isEmpty(statusList)) {
            queryBuilder.append(" and od.orderStatus in (:statusList)");
        }

        Query query = manager.createQuery(queryBuilder.toString());
        query.setParameter("unitId", unitId);
        if(!CollectionUtils.isEmpty(statusList)) {
            query.setParameter("statusList", statusList);
        }

        return query.getResultList();
    }


    private Map<Integer,String> getCODItemNames(Integer orderId){
        Map<Integer,String> itemNameMap = new HashMap<>();
        StringBuffer bufferedQuery = new StringBuffer("From OrderItemMetaDataDetail where orderId = :orderId ");
        try {
            Query query = manager.createQuery(bufferedQuery.toString());
            query.setParameter("orderId",orderId);
            itemNameMap = (Map<Integer, String>) ((List<OrderItemMetaDataDetail>)query.getResultList()).stream().filter(item ->
                            Objects.nonNull(item.getItemName()))
                    .collect(Collectors.toMap(OrderItemMetaDataDetail::getOrderItemId,
                            OrderItemMetaDataDetail::getItemName));
        }catch (Exception e){
            LOG.error("Error While Finding COD Menu Name For Order Item");
        }
        return  itemNameMap;

    }

    @SuppressWarnings("unchecked")
    public List<Order> getOrderDetails(int unitId, int startOrderId, int endOrderId, OrderFetchStrategy strategy)
            throws DataNotFoundException {
        LOG.info(String.format("Finding orders for unit %d between %d and %d", unitId, startOrderId, endOrderId));
        List<Order> orders = new ArrayList<Order>();

        StringBuffer bufferedQuery = new StringBuffer("FROM OrderDetail E ");
        bufferedQuery.append(" where E.unitId = :unitId and E.orderId > :startOrderId and E.orderId <= :endOrderId ");
        if (strategy.getOrderStatuses() != null && strategy.getOrderStatuses().size() > 0) {
            bufferedQuery.append(" and E.orderStatus in (:orderStatuses) ");
        }
        if (strategy.getTerminalId() >= 0) {
            bufferedQuery.append(" and E.terminalId = :terminalId ");
        }
        if (strategy.isOnlyDelivery()) {
            bufferedQuery.append(" and E.orderSource IN (:deliverySources) ");
        }
        bufferedQuery.append("order by orderId desc");
        Query query = manager.createQuery(bufferedQuery.toString());
        query.setParameter("unitId", unitId);
        query.setParameter("startOrderId", startOrderId);
        query.setParameter("endOrderId", endOrderId);
        if (strategy.getOrderStatuses() != null && strategy.getOrderStatuses().size() > 0) {
            query.setParameter("orderStatuses", toString(strategy.getOrderStatuses()));
        }
        if (strategy.getTerminalId() >= 0) {
            query.setParameter("terminalId", strategy.getTerminalId());
        }
        if (strategy.isOnlyDelivery()) {
            query.setParameter("deliverySources",
                    Arrays.asList(UnitCategory.COD.name(), UnitCategory.TAKE_AWAY.name()));
        }
        if(strategy.isPageable()){
            query.setFirstResult((strategy.getStart()-1)*strategy.getBatchSize());
            query.setMaxResults(strategy.getBatchSize());
        }
        List<OrderDetail> list = query.getResultList();

        if (list == null) {
            LOG.info(String.format("Did not find any order for unit %d between %d and %d", unitId, startOrderId,
                    endOrderId));
            return orders;
        }
        for (OrderDetail order : list) {
            Order temp = convertOrder(order, strategy);
            Query closure = manager.createQuery("SELECT L.transactionPoints FROM LoyaltyEvents L where L.orderId = :orderId and L.transactionStatus = 'SUCCESS' and L.transactionCodeType = 'Addition'");
            closure.setParameter("orderId", temp.getOrderId());
            List<Object> o = closure.getResultList();
            if (o != null && o.size() > 0) {
                temp.setPointsAcquired((Integer) o.get(0));
            }
            orders.add(temp);
        }
        LOG.info(String.format("Found %d orders for unit %d between %d and %d", orders.size(), unitId, startOrderId,
                endOrderId));
        return orders;
    }

    @Override
    public List<Order> getAllOrdersOfTheDays(DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest, OrderFetchStrategy strategy) {
        LOG.info(String.format("Finding All Orders Of Days for unit %d ", dayWiseOrderConsumptionRequest.getUnitId()));
        List<Order> orders = new ArrayList<>();

        StringBuffer bufferedQuery = new StringBuffer("FROM OrderDetail E where E.unitId = :unitId ");
        if (dayWiseOrderConsumptionRequest.isExcludeOnlyBulkOrders()) {
            bufferedQuery.append(" AND E.totalAmount >=: bulkOrderValue ");
            bufferedQuery.append("AND E.orderSource <>:codOrderSource ");
        }
        if (strategy.getOrderStatuses() != null && strategy.getOrderStatuses().size() > 0) {
            bufferedQuery.append(" and E.orderStatus in (:orderStatuses) ");
        }
        if (strategy.getTerminalId() >= 0) {
            bufferedQuery.append(" and E.terminalId = :terminalId ");
        }
        bufferedQuery.append("AND (");
        for (int i = 0; i < dayWiseOrderConsumptionRequest.getDayWiseOrderConsumptionItems().size(); i++) {
            bufferedQuery.append("(E.orderId > :start" + i + " AND E.orderId <= :end" + i + ") OR ");
        }
        String queryString = bufferedQuery.toString().substring(0, bufferedQuery.toString().length() - 4) + ")";
        Query query = manager.createQuery(queryString);
        query.setParameter("unitId", dayWiseOrderConsumptionRequest.getUnitId());
        if (dayWiseOrderConsumptionRequest.isExcludeOnlyBulkOrders()) {
            query.setParameter("bulkOrderValue", dayWiseOrderConsumptionRequest.getBulkOrderMinimLimit());
            query.setParameter("codOrderSource", UnitCategory.COD.name());
        }
        for (int i = 0; i < dayWiseOrderConsumptionRequest.getDayWiseOrderConsumptionItems().size(); i++) {
            query.setParameter("start" + i, dayWiseOrderConsumptionRequest.getDayWiseOrderConsumptionItems().get(i).getKettleOrderStartId());
            query.setParameter("end" + i, dayWiseOrderConsumptionRequest.getDayWiseOrderConsumptionItems().get(i).getKettleOrderEndId());
        }
        if (strategy.getOrderStatuses() != null && strategy.getOrderStatuses().size() > 0) {
            query.setParameter("orderStatuses", toString(strategy.getOrderStatuses()));
        }
        if (strategy.getTerminalId() >= 0) {
            query.setParameter("terminalId", strategy.getTerminalId());
        }
        if(strategy.isPageable()){
            query.setFirstResult((strategy.getStart()-1)*strategy.getBatchSize());
            query.setMaxResults(strategy.getBatchSize());
        }
        List<OrderDetail> list = query.getResultList();

        if (list == null || list.isEmpty()) {
            LOG.info(String.format("Did not find any order for unit %d ", dayWiseOrderConsumptionRequest.getUnitId()));
            return orders;
        }
        for (OrderDetail order : list) {
            orders.add(convertOrder(order, strategy));
        }
        return orders;
    }

    @SuppressWarnings("unchecked")
    public List<OrderStatusData> getOrderStatus(int unitId, int startOrderId, int endOrderId,
                                                List<OrderStatus> orderStatuses, List<String> orderSource,List<String> currentOrderStatus,String generatedOrderId) {
        List<OrderStatusData> orders = new ArrayList<OrderStatusData>();
        StringBuffer bufferedQuery = new StringBuffer(
                "select orderId,unitId, orderStatus, orderSource FROM OrderDetail E ");
        bufferedQuery.append(
                " where E.orderId > :startOrderId and E.orderId <= :endOrderId and E.orderSource IN (:orderSource)");
        if (unitId > 0) {
            bufferedQuery.append(" and E.unitId = :unitId");
        }
        if (orderStatuses != null) {
            bufferedQuery.append(" and E.orderStatus in (:orderStatuses)");
        }
        if(generatedOrderId != null) {
           bufferedQuery.append(" and E.generatedOrderId = :generatedOrderId");
        }
        else if(currentOrderStatus != null){
            bufferedQuery.append(" and E.orderStatus IN (:orderStatus)");
        }

        bufferedQuery.append(" order by orderId");
        Query query = manager.createQuery(bufferedQuery.toString());
        if (unitId > 0) {
            query.setParameter("unitId", unitId);
        }
        query.setParameter("startOrderId", startOrderId);
        query.setParameter("endOrderId", endOrderId);
        query.setParameter("orderSource", orderSource);
        if (orderStatuses != null) {
            query.setParameter("orderStatuses", toString(orderStatuses));
        }
        if(generatedOrderId != null ) {
            query.setParameter("generatedOrderId",generatedOrderId);
        }
        else if(currentOrderStatus !=null){
            query.setParameter("orderStatus",currentOrderStatus);
        }
        List<Object[]> list = query.getResultList();
        if (list == null) {
            LOG.info(String.format("Did not find any order for unit %d between %d and %d", unitId, startOrderId,
                    endOrderId));
            return orders;
        } else {
            for (Object[] array : list) {
                orders.add(new OrderStatusData((Integer) array[0], (Integer) array[1],
                        OrderStatus.valueOf((String) array[2]), UnitCategory.valueOf((String) array[3])));
            }
        }
        /*LOG.info(String.format("Found %d orders for unit %d between %d and %d", orders.size(), unitId, startOrderId,
                endOrderId));*/
        return orders;
    }

    private List<String> toString(List<OrderStatus> list) {
        List<String> strList = new ArrayList<String>();
        for (OrderStatus status : list) {
            strList.add(status.name());
        }
        return strList;
    }

    public List<OrderStatusData> getOrderStatusForDay(int unitId, List<OrderStatus> orderStatus,
                                                      List<String> orderSource,List<String> currentOrderStatus,String generatedOrderId) throws DataNotFoundException {
        List<OrderStatusData> allOrders = getOrderStatus(unitId, getLastDayCloseOrderId(unitId), Integer.MAX_VALUE,
                orderStatus, orderSource,currentOrderStatus,generatedOrderId);
        return allOrders;
    }

    public int getLastDayCloseOrderId(int unitId) {
        if (unitId <= 0) {
            return 0;
        }
        Query closure = manager.createQuery(
                "select max(lastOrderId) FROM UnitClosureDetails E where E.unitId = :unitId and E.currentStatus <> :cancelledState");
        closure.setParameter("unitId", unitId);
        closure.setParameter("cancelledState", ClosureState.CANCELLED.name());
        Object o = closure.getSingleResult();
        return o == null ? Integer.MIN_VALUE : (Integer) o;
    }

    public Date getLastBusinessDate(int unitId) {
        Query closure = manager.createQuery(
                "select max(businessDate) FROM UnitClosureDetails E where E.unitId = :unitId and E.currentStatus <> :cancelledState");
        closure.setParameter("unitId", unitId);
        closure.setParameter("cancelledState", ClosureState.CANCELLED.name());
        Object o = closure.getSingleResult();
        return o == null ? AppUtils.getPreviousDate() : (Date) o;
    }

    public List<UnitToDeliveryPartnerMappings> getUnitToDeliveryPartnerMappings(int unitId) {
        Query query = manager.createQuery("FROM UnitToDeliveryPartnerMappings U WHERE U.unitId = :unitId");
        query.setParameter("unitId", unitId);
        return query.getResultList();
    }

    @SuppressWarnings("unchecked")
    public List<Settlement> getSettlementDetails(int unitId, Date startTime, Date endTime) {
        List<Settlement> settlements = new ArrayList<Settlement>();
        Query query = manager.createQuery(
                "FROM OrderSettlement E where E.orderDetail.unitId = :unitId and E.orderDetail.billGenerationTime >= :startTime and E.orderDetail.billGenerationTime <= :endTime");
        query.setParameter("unitId", unitId);
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        List<OrderSettlement> list = query.getResultList();
        if (list == null) {
            LOG.info(String.format("Did not find any settlements for unit %d betmween %s and %s", unitId, startTime,
                    endTime));
            return settlements;
        }
        for (OrderSettlement settlement : list) {
            settlements.add(DataConverter.convert(masterCache, settlement));
        }
        return settlements;
    }

    public Order getPartnerOrderDetail(String externalOrderId, int channerPartnerId) throws DataNotFoundException {
        try {
            Query query = manager.createQuery("FROM OrderDetail E where E.orderSourceId = :externalOrderId");
            query.setParameter("externalOrderId", externalOrderId);
            // query.setParameter("channerPartnerId", channerPartnerId);
            OrderDetail order = (OrderDetail) query.getSingleResult();
            return convertOrder(order, null);
        } catch (NoResultException e) {
            throw new DataNotFoundException(
                    String.format("Did not find Partner Order Details with Extrenal Order ID : %s", externalOrderId),
                    e);
        }
    }

    public OrderStatus getPartnerOrderStatus(String externalOrderId, int channelPartnerId) throws DataNotFoundException {
        try {
            Query query = manager.createQuery("FROM OrderDetail E where E.orderSourceId = :externalOrderId ");
            query.setParameter("externalOrderId", externalOrderId);
            OrderDetail order = (OrderDetail) query.getSingleResult();
            return OrderStatus.valueOf(order.getOrderStatus());
        } catch (NoResultException e) {
            throw new DataNotFoundException(
                    String.format("Did not find Partner Order Details with Extrenal Order ID : %s", externalOrderId),
                    e);
        }
    }

    private OrderDetail getOrderDetailObject(String generatedOrderId, int lastOrderId, int unitId) {
        Query query = manager.createQuery(
                "FROM OrderDetail E where E.generatedOrderId = :generatedOrderId and E.orderId > :lastOrderId and E.unitId = :unitId");
        query.setParameter("generatedOrderId", generatedOrderId);
        query.setParameter("lastOrderId", lastOrderId);
        query.setParameter("unitId", unitId);
        Object o = query.getSingleResult();
        return o == null ? null : (OrderDetail) o;
    }

    private OrderDetail getOrderDetailObject(String generatedOrderId) {
        Query query = manager.createQuery("FROM OrderDetail E where E.generatedOrderId = :generatedOrderId");
        query.setParameter("generatedOrderId", generatedOrderId);
        OrderDetail order = (OrderDetail) query.getSingleResult();
        return order;
    }

	public List<OrderEmailNotification> getEmailEvents() {
		try {
			Query query = manager.createQuery(
					"from OrderEmailNotification where isEmailDelivered = :isEmailDeivered AND retryCount <= :retryCount AND executionTime IS NULL"
							+ " AND requestTime >= :lastHour");
			query.setParameter("isEmailDeivered", "N");
			query.setParameter("retryCount", props.getRetryCount());
			query.setParameter("lastHour",
					AppUtils.getDateBeforeOrAfterInSeconds(AppUtils.getCurrentTimestamp(), -3600));
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not fetch events ::::", e);
		}
		return null;
	}

	/*
	 * public List<OrderEmailNotification> getEmailEvents(List<String> emails,
	 * OrderEmailEntryType entryType) { Query query =
	 * manager.createNativeQuery("select * from OrderEmailNotification " +
	 * " where isEmailDelivered = :isEmailDeivered AND retryCount <= :retryCount AND executionTime IS NULL"
	 * + " AND requestTime >= :lastHour", OrderEmailNotification.class);
	 * query.setParameter("isEmailDeivered", "N"); query.setParameter("retryCount",
	 * props.getRetryCount()); query.setParameter("lastHour",
	 * AppUtils.getDateBeforeOrAfterInSeconds(AppUtils.getCurrentTimestamp(),
	 * -3600)); return query.getResultList(); }
	 */

    public OrderInfo getOrderReceipt(int orderId, boolean includeReceipts, String customerName)
            throws DataNotFoundException, TemplateRenderingException {
        Order order = getOrderDetail(orderId);
        return getOrderInfo(order, includeReceipts, customerName);
    }

    public OrderInfo getOrderReceipt(String orderId, boolean includeReceipts)
            throws DataNotFoundException, TemplateRenderingException {
        Order order = getOrderDetail(orderId);
        return getOrderInfo(order, includeReceipts, order.getCustomerName());
    }

    private void setItemMenuNames(Map<Integer,String> itemMenuNameMap , Order order){
        order.getOrders().forEach(orderItem -> {
            orderItem.setItemName(itemMenuNameMap.get(orderItem.getItemId()));
        });
    }

    private OrderInfo getOrderInfo(Order order, boolean includeReceipts, String customerName)
            throws DataNotFoundException, TemplateRenderingException {
        Unit unit = masterCache.getUnit(order.getUnitId());
        Customer customer = customeDao.getCustomer(order.getCustomerId());
        LoyaltyEvents loyaltyEvents= loyaltyDao.getTransactionPointsByOrderId(order.getOrderId());
        if (customer.getEmailId() == null) {
            customer.setEmailId(props.getUndeliveredEmail());
        }
        // Customer Name addition
        if (customerName != null) {
            order.setCustomerName(customerName);
        } else {
            order.setCustomerName(customer.getId() > 5 ? customer.getFirstName() : null);
        }


        // Customer address clear out
        Address a = null;
        if (order.getDeliveryAddress() != null) {
            for (Address address : customer.getAddresses()) {
                if (order.getDeliveryAddress().equals(address.getId())) {
                    a = address;
                }
            }
        }
        customer.getAddresses().clear();
        if (a != null) {
            customer.getAddresses().add(a);
            if (a.getName() != null) {
                order.setCustomerName(a.getName());
            }
        }

        OrderInfo info = new OrderInfo(props.getEnvironmentType(), order, customer, unit,
                metadataCache.getDeliveryPartner(order.getDeliveryPartner()),
                masterCache.getChannelPartner(order.getChannelPartner()));
        if(Objects.nonNull(order.getSource()) && UnitCategory.COD.name().equals(order.getSource())){
            Optional<PartnerOrderRiderStatesDetail> partnerOrderRiderStatesDetailOptional = partnerOrderRiderStatesDetailDao.findBykettleOrderId(order.getOrderId());
            partnerOrderRiderStatesDetailOptional.ifPresent(partnerOrderRiderStatesDetail -> info.setPartnerOrderRiderStates(DataConverter.convert(partnerOrderRiderStatesDetail)));
            Map<Integer,String> itemMenuNameMap = getCODItemNames(order.getOrderId());
            if(!CollectionUtils.isEmpty(itemMenuNameMap)){
                setItemMenuNames(itemMenuNameMap,info.getOrder());
            }
            if(props.isPriortizationOfOrdersEnabled()){
                info.getOrder().setPrioritizedOrder(isPrioritizedOrder(info.getOrder().getOrderId()));
            }
        }
        info.setBrand(masterCache.getBrandMetaData().get(order.getBrandId()));
        info.getOrder().setEarnedLoyaltypoints(loyaltyEvents.getTransactionPoints());
        if(Objects.nonNull(order.getInvoice())){
            info.setOrderInvoice(order.getInvoice());
        }
        OrderStatusEvent orderStatusEvent = getLatestOrderTransitionDetail(order.getOrderId());

        if(Objects.nonNull(orderStatusEvent)){
            info.setLastOrderStatusEventTime(orderStatusEvent.getUpdateTime());
        }
        PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail = getRiderDelayReason(order.getOrderId());
        if(Objects.nonNull(partnerOrderRiderStatesDetail)
                && Objects.nonNull(partnerOrderRiderStatesDetail.getReasonsForDelay())
                && Objects.nonNull(info.getPartnerOrderRiderStates())){
            info.getPartnerOrderRiderStates().setDelayReason(partnerOrderRiderStatesDetail.getReasonsForDelay());
        }

        if (includeReceipts) {
            /*
             * boolean needsCafeOrderPrint = info.getOrder().getChannelPartner() ==
             * AppConstants.CHANNEL_PARTNER_WEB_APP || info.getUnit().isTableService() ||
             * info.getCustomer() == null || info.getUnit().isTokenEnabled() ||
             * info.getCustomer().getEmailId() == null ||
             * info.getCustomer().getEmailId().trim().length() == 0 ||
             * props.getUndeliveredEmail().equals(info.getCustomer().getEmailId( ));
             */
            boolean needsCafeOrderPrint = !TransactionUtils.isSpecialOrder(order)
                    || TransactionUtils.isPaidEmployeeMeal(order);


            Map<ReceiptType, String> androidReceipts = TransactionUtils.getReceipts(props.getChaayosBaseUrl(), masterCache, needsCafeOrderPrint, info, props.getBasePath(),
                    props.getRawPrintingSatus(),props);
            info.setAndroidReceipts(androidReceipts);
            List<String> receipts = new ArrayList<>();
            for (ReceiptType r : androidReceipts.keySet()) {
                receipts.add(androidReceipts.get(r));
            }
            info.setReceipts(receipts);
            if (props.getRawPrintingSatus()) {
                info.setPrintType(PrintType.RAW);
            } else {
                info.setPrintType(PrintType.HTML);
            }
        }
        return info;
    }


    @Override
    public Boolean isPrioritizedOrder(Integer kettleOrderId){
        try {
            Query query = manager.createNativeQuery("select IS_PRIORTIZED FROM PARTNER_ORDER_DETAIL  WHERE  KETTLE_ORDER_ID = :kettleOrderId");
            query.setParameter("kettleOrderId",kettleOrderId);
            return AppUtils.getStatus((String) query.getSingleResult());
        }catch (Exception e){
            LOG.error("Error while Getting Partner Order Detail for kettle order id : {} ",kettleOrderId);
            return false;
        }

    }

    @Override
    public int getLastOrderOfLastBusinessDate() {
        Query query = manager.createQuery("FROM UnitClosureDetails u order by u.closureId DESC");
        query.setMaxResults(1);
        List<UnitClosureDetails> unitClosureDetails = query.getResultList();
        return unitClosureDetails.get(0).getLastOrderId();
    }

    @Override
    public List<EmployeeMealData> getEmployeeMealData(int employeeId) {
        Query query = manager.createQuery(
                "FROM  EmployeeMealData where orderDetail.orderStatus <> :cancelledStatus AND employeeId = :employeeId AND businessDate = :businessDate ");
        query.setParameter("cancelledStatus", OrderStatus.CANCELLED.name());
        query.setParameter("employeeId", employeeId);
        query.setParameter("businessDate", AppUtils.getCurrentBusinessDate());
        return query.getResultList();
    }

    @Override
    public CashCardDetail findCashCardByCardNumber(String cashCardNumber, Integer empId, Integer unitId) {
        try {
            Query query = manager.createQuery("FROM CashCardDetail c WHERE c.cardNumber = :cardNumber");
            query.setParameter("cardNumber", cashCardNumber);
            return (CashCardDetail) query.getSingleResult();
        } catch (NoResultException e) {
            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.SYSTEM_ERRORS,
                    "Cash card id: " + cashCardNumber + " employee id: " + empId + " unit name: "
                            + masterCache.getUnit(unitId).getName() + " not found in database.");
            return null;
        } catch (NonUniqueResultException e) {
            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.SYSTEM_ERRORS,
                    "Cash card id: " + cashCardNumber + " employee id: " + empId + " unit name: "
                            + masterCache.getUnit(unitId).getName() + " non unique result.");
            return null;
        }
    }

    @Override
    public int getCostEvent(int orderId) {
        Query query = manager
                .createQuery("FROM HouseCostEvent u where u.kettleOrderId = :orderId order by u.orderId desc");
        query.setMaxResults(1);
        query.setParameter("orderId", orderId);
        @SuppressWarnings("unchecked")
        List<HouseCostEvent> event = query.getResultList();
        return event.get(0).getOrderId();
    }

    @Override
    public List<Order> getEmployeeMealOrders(int userId) {
        Set<Integer> orders = paidEmployeeMealDao.getEmployeeMealOrders(userId);
        if (orders.isEmpty()) {
            return new ArrayList<>();
        }
        Query query = manager.createQuery("FROM OrderDetail E where E.orderId IN :orderSet");
        query.setParameter("orderSet", orders);
        @SuppressWarnings("unchecked")
        List<OrderDetail> list = query.getResultList();
        OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
        return Optional.ofNullable(list).map(Collection::stream).orElse(Stream.empty())
                .map(o -> convertOrder(o, strategy)).collect(Collectors.toList());
    }

    @Override
    public boolean publishFinalOrderStatus(List<Integer> orderIdList) {
        Set<Integer> set = getOrderStatusIds(orderIdList);
        if (set != null && !set.isEmpty()) {
            Query query = manager.createQuery(
                    "FROM OrderStatusEvent E  WHERE E.orderStatusId IN :orderSet AND E.transitionStatus = :status");
            query.setParameter("orderSet", set);
            query.setParameter("status", TransitionStatus.SUCCESS.name());
            @SuppressWarnings("unchecked")
            List<OrderStatusEvent> results = query.getResultList();
            results.forEach(p -> publishOrderStatusEvent(p));
        }
        return true;
    }

    public Order getOrderDetail(String generatedOrderId) throws DataNotFoundException {
        try {
            OrderDetail order = getOrderDetailObject(generatedOrderId);
            return convertOrder(order, null);
        } catch (NoResultException e) {
            throw new DataNotFoundException(
                    String.format("Did not find Order Details with Generated Order ID : %s", generatedOrderId), e);
        }
    }

    private Set<Integer> getOrderStatusIds(List<Integer> orderIdList) {
        Query query = manager.createQuery("SELECT E.orderId, MAX(E.orderStatusId)  FROM OrderStatusEvent E "
                + " WHERE E.orderId IN :orderSet AND E.transitionStatus = :status GROUP BY E.orderId");
        query.setParameter("orderSet", orderIdList);
        query.setParameter("status", TransitionStatus.SUCCESS.name());
        @SuppressWarnings("unchecked")
        List<Object[]> results = query.getResultList();
        return Optional.ofNullable(results).map(Collection::stream).orElse(Stream.empty()).map(o -> (Integer) o[1])
                .collect(Collectors.toSet());
    }

    private void publishOrderStatusEvent(OrderStatusEvent ose) {
        if (props.publishOrders() && ose != null) {
            try {
                producer.send(session.createTextMessage(JSONSerializer.toJSON(ose)));
            } catch (JMSException e) {
                LOG.error("Error while adding order to the message queue", e);
            }
        }
    }

    @Override
    public List<OrderInfo> getOrderToPublish(int unitId, Date businessDate)
            throws DataNotFoundException, TemplateRenderingException {
        List<OrderInfo> infos = new ArrayList<>();
        Date startTime = AppUtils.getStartOfBusinessDay(businessDate);
        Date endTime = AppUtils.getEndOfBusinessDay(businessDate);
        List<Order> orders = getOrderDetails(unitId, startTime, endTime, null);
        for (Order o : orders) {
            infos.add(getOrderInfo(o, false, o.getCustomerName()));
        }
        return infos;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.OrderSearchDao#getOrderDetails(java.util.
     * Date, com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy)
     */
    @Override
    public List<Order> getOrderDetails(Date businessDate, OrderFetchStrategy strategy) {
        LOG.info(String.format("Finding orders for all Units for %s ", businessDate));
        List<Order> orders = new ArrayList<Order>();

        Query query = manager.createQuery("FROM OrderDetail E where E.businessDate = :businessDate");
        query.setParameter("businessDate", businessDate);
        List<OrderDetail> list = query.getResultList();
        if (list == null) {
            LOG.info(String.format("Did not find any order all Units for %s ", businessDate));
            return orders;
        }
        for (OrderDetail order : list) {
            orders.add(convertOrder(order, strategy));
        }
        LOG.info(String.format("Found %d orders for all Units for %s", orders.size(), businessDate));
        return orders;
    }

    @Override
    public List<Order> getOrderDetails(Date businessDate, OrderFetchStrategy strategy, List<Integer> unitIds) {
        LOG.info(String.format("Finding orders for unit %s for %s ", unitIds, businessDate));
        List<Order> orders = new ArrayList<Order>();

        List<String> orderTypes = new ArrayList<>();
        orderTypes.add(AppConstants.ORDER_TYPE_REGULAR);
        orderTypes.add(AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL);

        Query query = manager
                .createQuery("FROM OrderDetail E where E.businessDate = :businessDate and E.unitId IN( :unitIds)"
                        + " AND E.orderStatus <> :orderStatus AND E.orderType IN :orderTypes");
        query.setParameter("businessDate", businessDate);
        query.setParameter("unitIds", unitIds);
        query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
        query.setParameter("orderTypes", orderTypes);

        List<OrderDetail> list = query.getResultList();
        if (list == null) {
            LOG.info(String.format("Did not find any order all Units for %s ", businessDate));
            return orders;
        }
        for (OrderDetail order : list) {
            orders.add(convertOrder(order, strategy));
        }
        LOG.info(String.format("Found %d orders for all Units for %s", orders.size(), businessDate));
        return orders;
    }

    @Override
    public List<Order> getOrderDetails(Date businessDate, OrderFetchStrategy strategy, int unitId, String source) {
        LOG.info("Finding orders for Unit {} and source {} for {} ", unitId, source, businessDate);
        List<Order> orders = new ArrayList<Order>();

        List<String> orderTypes = new ArrayList<>();
        orderTypes.add(AppConstants.ORDER_TYPE_REGULAR);
        orderTypes.add(AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL);

        Query query = manager
                .createQuery("FROM OrderDetail E where E.businessDate = :businessDate and E.unitId = :unitId"
                        + " AND E.orderStatus <> :orderStatus AND E.orderType IN :orderTypes AND E.orderSource = :source");
        query.setParameter("businessDate", businessDate);
        query.setParameter("unitId", unitId);
        query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
        query.setParameter("orderTypes", orderTypes);
        query.setParameter("source", source);

        @SuppressWarnings("unchecked")
        List<OrderDetail> list = query.getResultList();
        if (list == null) {
            LOG.info("Did not find any order for Unit {} and source {} for {}", unitId, source, businessDate);
            return orders;
        }
        for (OrderDetail order : list) {
            orders.add(convertOrder(order, strategy));
        }
        LOG.info("Found {} orders for Unit {} and source {} for {}", orders.size(), unitId, source, businessDate);
        return orders;
    }

    /**
     * Searched for last day close which is non cancelled, returns it after domain
     * conversion
     * <p>
     * returns null when nothing is found
     *
     * @param unitId
     * @return {@link UnitClosure}
     */
    @Override
    public UnitClosure getLastDayClose(int unitId) {
        if (unitId <= 0) {
            return null;
        }
        Query query = manager.createQuery(
                "FROM UnitClosureDetails E where E.unitId = :unitId and E.currentStatus <> :cancelledState ORDER BY closureId DESC");
        query.setParameter("unitId", unitId);
        query.setParameter("cancelledState", ClosureState.CANCELLED.name());
        query.setMaxResults(2);
        @SuppressWarnings("unchecked")
        List<UnitClosureDetails> list = query.getResultList();
        if (list != null && !list.isEmpty()) {
            return DataConverter.convert(list.get(0));
        }
        return null;
    }

    @Override
    public UnitClosure getUnitClosure(int closureId) {
        UnitClosureDetails c = manager.find(UnitClosureDetails.class, closureId);
        return DataConverter.convert(c);
    }

    /**
     * Collects orders based on start order Id and End Order Id, can skip orders
     * provided in skip list
     *
     * @return {@link OrderInfo}
     */
    @Override
    public List<OrderInfo> getOrderDetails(int unitId, int lastOrderId, OrderFetchStrategy strategy,
                                           List<Integer> skipOrders) {
        List<OrderInfo> orders = new ArrayList<>();
        List<String> orderTypes = new ArrayList<>();
        orderTypes.add(AppConstants.ORDER_TYPE_REGULAR);
        orderTypes.add(AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL);

        String queryString = "FROM OrderDetail E where E.unitId = :unitId "
                + " AND E.orderStatus <> :orderStatus AND E.orderType IN :orderTypes "
                + " AND E.orderId > :lastOrderId ";

        if (skipOrders != null && !skipOrders.isEmpty()) {
            queryString = queryString + " AND E.orderId NOT IN :skipOrders ";
        }

        Query query = manager.createQuery(queryString);

        query.setParameter("unitId", unitId);
        query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
        query.setParameter("orderTypes", orderTypes);
        query.setParameter("lastOrderId", lastOrderId);

        if (skipOrders != null && !skipOrders.isEmpty()) {
            query.setParameter("skipOrders", skipOrders);
        }

        List<OrderDetail> list = query.getResultList();
        if (list == null) {
            return orders;
        }
        try {
            for (OrderDetail order : list) {
                orders.add(getOrderInfo(convertOrder(order, strategy), false, null));
            }
        } catch (DataNotFoundException | TemplateRenderingException e) {
            LOG.error("Error", e);
        }
        LOG.info("Found {} orders for Unit {} and source {} for {}", orders.size(), unitId);
        return orders;
    }

    @Override
    public CustomerAddressInfo getOrderAddressInfo(Integer addressId) {
        LOG.info(String.format("Finding order address for addressId %s ", addressId));
        CustomerAddressInfo info = find(CustomerAddressInfo.class, addressId);
        return info;
    }

    @Override
    public List<Integer> getOrderIdsInBatch(Integer startOrderId, int batchSize) {
        Query query = manager.createQuery("select orderId from OrderDetail where orderType = :ordertype AND orderStatus = :orderstatus AND orderId > :orderid AND customerId >5 order by orderId");
        query.setParameter("ordertype", "order");
        query.setParameter("orderstatus", "SETTLED");
        query.setParameter("orderid", startOrderId);
        query.setMaxResults(batchSize);
        return query.getResultList();
    }

    //Did this to fix Zomato masking number customer name issue
    private Order convertOrder(OrderDetail order, OrderFetchStrategy strategy) {
		if ((strategy == null || (strategy != null && !strategy.isDonotFetchCustomerAddress()))
				&& order.getDeliveryAddress() != null && !order.getDeliveryAddress().equals(0)) {
			CustomerAddressInfo info = getOrderAddressInfo(order.getDeliveryAddress());
			if (info != null && info.getName() != null) {
				order.setCustomerName(
						info.getName().substring(0, info.getName().length() > 45 ? 45 : info.getName().length()));
			}
		}
//                     OrderDetail orderDetail = dao.find(OrderDetail.class, orderId);
        if(Objects.nonNull(order.getInvoiceId())){
            try {
                OrderInvoiceDetail orderInvoiceDetail = getInvoiceDetail(order.getGeneratedOrderId());
                order.setInvoiceDetail(orderInvoiceDetail);
            }
            catch(Exception e){
                LOG.info("Unable to fetch invoice Id for this order");
            }
        }
        return DataConverter.convert(masterCache, order, strategy,recipeCache,props);
    }

    private OrderInvoiceDetail getInvoiceDetail(String generatedOrderId){
        try {
            Query query = manager.createQuery("FROM OrderInvoiceDetail E where E.orderId = :orderId ");
            query.setParameter("orderId", generatedOrderId);
            OrderInvoiceDetail orderInvoiceDetail= (OrderInvoiceDetail) query.getSingleResult();
            LOG.info("***************************************{}",orderInvoiceDetail);
            return orderInvoiceDetail;
        } catch (Exception e) {
            LOG.info("Error while fetching order invoice detail for order id");
        }
        return null;
    }

    @Override
    public List<Order> getCustomerOrders(int customerId, Date fromDate, int maxSize, List<Integer> filteredOrderIds) {
        Query query = manager.createQuery(
                "Select E.orderId FROM OrderDetail E where E.customerId = :customerId and E.orderStatus != :orderStatus and (E.businessDate >= :businessDate or E.businessDate is null) "
                        + (filteredOrderIds != null && filteredOrderIds.size() > 0 ? " and E.orderId not in (:filteredOrderIds) " : "")
                        + "order by E.orderId desc ");
        query.setParameter("customerId", customerId);
        query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
        query.setParameter("businessDate", fromDate);
        if (filteredOrderIds != null && filteredOrderIds.size() > 0) {
            query.setParameter("filteredOrderIds", filteredOrderIds);
        }
        query.setMaxResults(maxSize);
        List<Integer> orderids = query.getResultList();
        List<Order> orderDetail = new ArrayList<>();
        if (orderids != null) {
            for (int orderId : orderids) {
                try {
                    LOG.info("ids are " + orderId);
                    Order order = getOrderDetail(orderId);
                    orderDetail.add(order);
                } catch (DataNotFoundException e) {
                    LOG.error("Error", e);
                }
            }
        }
        return orderDetail;
    }

    public OrderNPS getFeedbackDetail(int surveyResponseId) throws DataNotFoundException {
        try {
            OrderNPSDetail data = manager.find(OrderNPSDetail.class, surveyResponseId);
            return DataConverter.convertOrderNPS(data);
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Order Details with ID : %d", surveyResponseId), e);
        }
    }

    @Override
    public List<OrderNPS> getCustomerFeedbacks(int customerId, Date fromDate, int maxSize, List<Integer> filteredIds) {
        Query query = manager.createQuery(
                "Select E.surveryResponseId FROM OrderNPSDetail E where E.customerId = :customerId and (E.surveyCreationTime >= :surveyCreationTime or E.surveyCreationTime is null)"
                        + (filteredIds != null && filteredIds.size() > 0
                        ? " and E.orderId not in (:filteredIds) "
                        : "")
                        + "order by E.surveryResponseId desc ");
        query.setParameter("customerId", customerId);
        query.setParameter("surveyCreationTime", fromDate);
        if (filteredIds != null && filteredIds.size() > 0) {
            query.setParameter("filteredIds", filteredIds);
        }
        query.setMaxResults(maxSize);
        List<Integer> surveyResponseIds = query.getResultList();
        List<OrderNPS> order = new ArrayList<>();
        if (surveyResponseIds != null) {
            for (int surveyResponseId : surveyResponseIds) {
                try {
                    OrderNPS orderNPS = getFeedbackDetail(surveyResponseId);
                    order.add(orderNPS);
                } catch (DataNotFoundException e) {
                    LOG.error("Error", e);
                }
            }
        }
        return order;
    }

    public OrderNPS getCustomerFeedback(int orderId) {
        Query query = manager.createQuery("FROM OrderNPSDetail E where E.orderId = :orderId");
        query.setParameter("orderId", orderId);
        List<OrderNPSDetail> surveyResponses = query.getResultList();
        if (surveyResponses != null && surveyResponses.size() > 0) {
            for (OrderNPSDetail surveyResponse : surveyResponses) {
                return DataConverter.convertOrderNPS(surveyResponse);
            }
        }
        return null;
    }

    @Override
    public Integer getCustomerId(Integer orderId) {
        OrderDetail order = manager.find(OrderDetail.class, orderId);
        return order.getCustomerId();
    }

    @Override
    public Integer getChannelPartnerId(Integer orderId) {
        OrderDetail order = manager.find(OrderDetail.class, orderId);
        return order.getChannelPartnerId();
    }

    @Override
    public OrderStatusEvent getLatestOrderTransitionDetail(int orderId) {
        Query query = manager.createQuery("FROM OrderStatusEvent where  orderId = :orderId ORDER BY orderStatusId DESC");
        query.setParameter("orderId", orderId);
        query.setMaxResults(1);
        try {
            return (OrderStatusEvent) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public PartnerOrderRiderStatesDetail getRiderDelayReason(int orderId){
        Query query = manager.createQuery(" FROM PartnerOrderRiderStatesDetail where kettleOrderId =:orderId");
        query.setParameter("orderId",orderId);
        query.setMaxResults(1);
        try {
            return !query.getResultList().isEmpty() ? (PartnerOrderRiderStatesDetail) query.getResultList().get(0) : null;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public Map<String,Object> getPartnerOrderDetail(String partnerSourceId,String issueType){
        Query query = manager.createNativeQuery("CALL GET_PARTNER_ORDER_DETAILS(:partnerSourceId,:customerComplaint,:issueType)");
        NativeQueryImpl nativeQuery = (NativeQueryImpl) query;
        nativeQuery.setParameter("partnerSourceId", partnerSourceId);
        nativeQuery.setParameter("customerComplaint","NA");
        nativeQuery.setParameter("issueType",issueType);
        nativeQuery.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
        List<Map<String,Object>> result = nativeQuery.getResultList();
        Map<String,Object> map = result.get(0);
//        List<Object[]> objs = query.getResultList();
        return map;
    }

    @Override
    public List<OrderItem> getComboOrderItems(int comboItemId){
        Query query = manager.createQuery(" FROM OrderItem where parentItemId = :comboItemId");
        query.setParameter("comboItemId",comboItemId);

        try {
            return query.getResultList();
        }catch (Exception e){
            return new ArrayList<>();
        }
    }

    @Override
    public OrderRefundDetail getOrderRefundByOrderId(int orderId) {
        Query query = manager.createQuery("FROM OrderRefundDetail where orderId = :orderId");
        query.setParameter("orderId", orderId);
        try {
            return !query.getResultList().isEmpty() ? (OrderRefundDetail) query.getResultList().get(0) : null;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Order> getCancelledOrdersByDateRange(Integer unitId, Date startDate, Date endDate) throws DataNotFoundException {
        LOG.info("Finding cancelled orders for unitId: {} between {} and {}", unitId, startDate, endDate);
        List<Order> orders = new ArrayList<>();

        StringBuilder queryBuilder = new StringBuilder(
                "FROM OrderDetail E WHERE E.orderStatus = :orderStatus " +
                "AND E.billingServerTime >= :startDate AND E.billingServerTime <= :endDate"+
                " AND E.channelPartnerId = :channelPartnerId");

        if (unitId != null) {
            queryBuilder.append(" AND E.unitId = :unitId");
        }

        queryBuilder.append(" ORDER BY E.billingServerTime DESC");

        Query query = manager.createQuery(queryBuilder.toString());
        query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        query.setParameter("channelPartnerId", AppConstants.ONE);

        if (unitId != null) {
            query.setParameter("unitId", unitId);
        }

        List<OrderDetail> list = query.getResultList();
        if (list == null || list.isEmpty()) {
            LOG.info("No cancelled orders found for unitId: {} between {} and {}", unitId, startDate, endDate);
            return orders;
        }

        OrderFetchStrategy strategy = new OrderFetchStrategy(true, true, true, true, false, null, -1, false);
        for (OrderDetail orderDetail : list) {
            orders.add(convertOrder(orderDetail, strategy));
        }

        LOG.info("Found {} cancelled orders for unitId: {} between {} and {}", orders.size(), unitId, startDate, endDate);
        return orders;
    }
}
