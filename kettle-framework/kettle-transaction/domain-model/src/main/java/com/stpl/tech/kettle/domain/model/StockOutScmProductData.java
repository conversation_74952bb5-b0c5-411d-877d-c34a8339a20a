package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.inventory.model.InventoryData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

@Document
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StockOutScmProductData {
    @Id
    private ObjectId id;
    private String kettleProductKey;
    private int unitId;
    private int productId;

    private boolean stockOut;
    private List<InventoryData> inventoryData;
    private Date stockOutReportTime;
}
