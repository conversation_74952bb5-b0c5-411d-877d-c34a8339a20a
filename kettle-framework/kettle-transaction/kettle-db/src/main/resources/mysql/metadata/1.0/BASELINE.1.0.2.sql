update UNIT_PRODUCT_PRICING 
set PRICE = '109.00'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm
where PRODUCT_ID IN( 830) and UNIT_ID = 10004)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '19.00'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm
where PRODUCT_ID IN( 780,
790) and UNIT_ID = 10004)
;

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(28,
'ADDONS',
'ComboSandwich',
'Combo Sandwich');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2801,
28,
'Cutting<PERSON><PERSON>',
'Cutting Chai');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`R<PERSON>_CODE`,
`RL_NAME`)
VALUES
(2802,
28,
'Butter<PERSON><PERSON>kenWrap',
'Butter Chicken Wrap');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2803,
28,
'KadhaiyaPaneer',
'Kadhaiya Paneer');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2804,
28,
'SicilianChicken',
'Sicilian Chicken');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2805,
28,
'SpinachCornCheese',
'Spinach Corn Cheese');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2806,
28,
'Mayonnaise',
'Mayonnaise');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2807,
28,
'WrapToSandwich',
'Wrap To Sandwich');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2808,
28,
'Parcel',
'Parcel');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(29,
'ADDONS',
'ComboNashta',
'Combo Nashta');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2901,
29,
'CuttingChai',
'Cutting Chai');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2902,
29,
'2Minutes',
'2 Minutes');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2903,
29,
'HomestyleAloo',
'Homestyle Aloo');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2904,
29,
'BombaySpeacial',
'Bombay Speacial');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2905,
29,
'KeemaPav',
'Keema Pav');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2906,
29,
'EggBun',
'Egg Bun');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2907,
29,
'Poha',
'Poha');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2908,
29,
'VadaPav',
'Vada Pav');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2909,
29,
'BunBhujia',
'Bun Bhujia');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2910,
29,
'BunMaska',
'Bun Maska');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2911,
29,
'Parcel',
'Parcel');


update PRODUCT_DETAIL
SET ADDITIONAL_ITEM_TYPES = 29
WHERE PRODUCT_ID = 820;

update PRODUCT_DETAIL
SET ADDITIONAL_ITEM_TYPES = 28
WHERE PRODUCT_ID = 830;

ALTER TABLE UNIT_CLOSURE_DETAILS
ADD COLUMN CURRENT_STATUS VARCHAR(30) NOT NULL DEFAULT 'INITIATED';

