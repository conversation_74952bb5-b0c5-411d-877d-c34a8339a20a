update UNIT_PRODUCT_PRICING 
set PRICE = '109.00'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm
where PRODUCT_ID IN( 830) and UNIT_ID = 10002)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '19.00'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm
where PRODUCT_ID IN( 780,
790) and UNIT_ID = 10002)
;

update PRODUCT_DETAIL set PRODUCT_NAME = 'Desi Chai Kadak' where PRODUCT_NAME = 'Desi – Ka<PERSON>';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Desi Chai Cutting' where PRODUCT_NAME = 'Cutting';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Desi Chai Pani Kum' where PRODUCT_NAME = 'Desi - Paani Kam';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Mint N Lemon Green' where PRODUCT_NAME = 'Mint & Lemon Green';

update PROD<PERSON>T_DETAIL set PRODUCT_NAME = 'Earl Grey' where PRODUCT_NAME = '<PERSON>';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Aam Papad Chai' where PRODUCT_NAME = 'Aam Papad';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Pahadi Chai' where PRODUCT_NAME = 'Pahadi';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Green Tea' where PRODUCT_NAME = 'Green';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Muscatel' where PRODUCT_NAME = 'Darjeeling Muscatel';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Lemon Iced Tea' where PRODUCT_NAME = 'Lemon' AND PRODUCT_ID=300;

update PRODUCT_DETAIL set PRODUCT_NAME = 'Peach Iced Tea' where PRODUCT_NAME = 'Peach';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Passion Fruit Iced Tea' where PRODUCT_NAME = 'Passion Fruit';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Strawberry Iced Tea' where PRODUCT_NAME = 'Strawberry';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Kiwi Iced Tea' where PRODUCT_NAME = 'Kiwi';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Red Berry Iced Tea' where PRODUCT_NAME = 'Red Berry';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Rooh Afza' where PRODUCT_NAME = 'Rooh afza';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Home Made Chaach' where PRODUCT_NAME = 'Homemade Chaach';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Maggi Sandwich' where PRODUCT_NAME = '2 Minutes';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Blueberry Cake' where PRODUCT_NAME = 'Blueberry';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Banana Cake' where PRODUCT_NAME = 'Banana';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Carrot Cake' where PRODUCT_NAME = 'Carrot';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Lemon Cake' where PRODUCT_NAME = 'Lemon' AND PRODUCT_ID=770;

update PRODUCT_DETAIL set PRODUCT_NAME = 'Jeera Cookies' where PRODUCT_NAME = 'Jeera';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Badam Pista Cookies' where PRODUCT_NAME = 'Badam Pista';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Nashta Combo' where PRODUCT_NAME = 'Chai Nashta';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Lunch Combo' where PRODUCT_NAME = 'Chai Sandwich';

update PRODUCT_DETAIL set PRODUCT_NAME = 'Kadhai Paneer' where PRODUCT_NAME = 'Kadhaiya Paneer';

INSERT INTO PRODUCT_DETAIL (PRODUCT_ID, PRODUCT_NAME, PRODUCT_DESCRIPTION, VENDOR_ID, PRODUCT_TYPE, PRODUCT_SUB_TYPE, PRODUCT_STATUS, ATTRIBUTE, PRODUCT_START_DATE, PRODUCT_END_DATE, PRODUCT_SKU_CODE, DIMENSION_CODE, PRICE_TYPE, ADDITIONAL_ITEM_TYPES, IN_TMSTMP, OUT_TMSTMP)
VALUES 
(70,'Sab Kuch Chai','-----',100,5,501,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011017',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00');

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_START_DATE)
VALUES(10004,70,'2014-01-01'),
(10002,70,'2014-01-01');

INSERT INTO `UNIT_PRODUCT_PRICING`
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 70),30,'2014-01-01 00:00:00',40),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 70),31,'2014-01-01 00:00:00',60),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 70),30,'2014-01-01 00:00:00',40),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 70),31,'2014-01-01 00:00:00',60);

