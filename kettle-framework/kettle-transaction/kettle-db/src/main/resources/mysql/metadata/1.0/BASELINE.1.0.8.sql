delete from CLOSURE_STATUS;
delete from UNIT_CLOSURE_DETAILS;

/*
-- Query: SELECT * FROM KETTLE.UNIT_CLOSURE_DETAILS
LIMIT 0, 5000

-- Date: 2015-09-08 18:37
*/
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`C<PERSON>OSURE_COMMENT`,`CURRENT_STATUS`) VALUES (2,10004,'2015-08-27','2015-08-27 20:59:27',NULL,104,100009,NULL,'Closed','INITIATED');
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`C<PERSON><PERSON>URE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (3,10004,'2015-08-28','2015-08-28 20:00:00',NULL,183,100009,NULL,'Closed','INITIATED');
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (4,10004,'2015-08-31','2015-08-31 20:01:58',NULL,277,100009,NULL,'CLOSE','INITIATED');
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (5,10004,'2015-09-01','2015-09-01 19:32:23',NULL,351,100009,NULL,'close','INITIATED');
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (6,10004,'2015-09-02','2015-09-02 19:47:37',NULL,428,100009,NULL,'dayclose','INITIATED');
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (7,10004,'2015-09-03','2015-09-03 19:39:46',NULL,494,100009,NULL,'dayclose','INITIATED');
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (8,10004,'2015-09-04','2015-09-04 19:37:09',NULL,573,100009,NULL,'dayclose','INITIATED');
INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (9,10004,'2015-09-07','2015-09-07 19:38:45',NULL,659,100009,NULL,'close','INITIATED');
#INSERT INTO `UNIT_CLOSURE_DETAILS` (`CLOSURE_ID`,`UNIT_ID`,`BUSINESS_DATE`,`CLOSURE_START_TIME`,`CLOSURE_END_TIME`,`LAST_ORDER_ID`,`EMPLOYEE_ID`,`RECONCILIATION_STATUS`,`CLOSURE_COMMENT`,`CURRENT_STATUS`) VALUES (10,10004,'2015-09-07','2015-09-08 19:32:09',NULL,659,100009,NULL,'close','INITIATED');

/*
-- Query: SELECT * FROM KETTLE.CLOSURE_STATUS
LIMIT 0, 5000

-- Date: 2015-09-08 18:39
*/
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (2,2,'INITIATED','2015-08-27 20:59:28');
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (3,3,'INITIATED','2015-08-28 20:00:00');
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (4,4,'INITIATED','2015-08-31 20:01:58');
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (5,5,'INITIATED','2015-09-01 19:32:23');
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (6,6,'INITIATED','2015-09-02 19:47:37');
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (7,7,'INITIATED','2015-09-03 19:39:46');
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (8,8,'INITIATED','2015-09-04 19:37:09');
INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (9,9,'INITIATED','2015-09-07 19:38:45');
#INSERT INTO `CLOSURE_STATUS` (`CLOSURE_STATUS_ID`,`CLOSURE_ID`,`CLOSURE_STATUS`,`UPDATE_TMSTMP`) VALUES (10,10,'INITIATED','2015-09-08 19:32:09');
