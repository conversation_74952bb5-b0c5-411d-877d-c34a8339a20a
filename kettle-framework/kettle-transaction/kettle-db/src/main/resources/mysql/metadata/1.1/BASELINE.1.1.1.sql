update UNIT_PRODUCT_PRICING 
set PRICE = '188.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(700) and UNIT_ID = 10000)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '188.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(710) and UNIT_ID = 10000)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '283.3'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(720) and <PERSON>IT_ID = 10000)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '283.3'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(730) and UNIT_ID = 10000)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '99'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(740) and UNIT_ID = 10000)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '99'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(750) and UNIT_ID = 10000)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '119'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(760) and UNIT_ID = 10000)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '99'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(770) and UNIT_ID = 10000)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '16.82'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(860) and UNIT_ID = 10000)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '189.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(700) and UNIT_ID = 10002)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '189.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(710) and UNIT_ID = 10002)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '284.7'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(720) and UNIT_ID = 10002)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '284.7'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(730) and UNIT_ID = 10002)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '0'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(800) and UNIT_ID = 10002)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '0'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(810) and UNIT_ID = 10002)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '40'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(10) and UNIT_ID = 10003) and DIMENSION_CODE=30
;
update UNIT_PRODUCT_PRICING 
set PRICE = '60'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(10) and UNIT_ID = 10003) and DIMENSION_CODE=31
;
update UNIT_PRODUCT_PRICING 
set PRICE = '40'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(20) and UNIT_ID = 10003) and DIMENSION_CODE=30
;
update UNIT_PRODUCT_PRICING 
set PRICE = '60'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(20) and UNIT_ID = 10003) and DIMENSION_CODE=31
;
update UNIT_PRODUCT_PRICING 
set PRICE = '40'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(50) and UNIT_ID = 10003) and DIMENSION_CODE=30
;
update UNIT_PRODUCT_PRICING 
set PRICE = '60'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(50) and UNIT_ID = 10003) and DIMENSION_CODE=31
;
update UNIT_PRODUCT_PRICING 
set PRICE = '188.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(700) and UNIT_ID = 10003)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '188.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(710) and UNIT_ID = 10003)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '283.3'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(720) and UNIT_ID = 10003)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '283.3'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(730) and UNIT_ID = 10003)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '17'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(860) and UNIT_ID = 10003)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '189.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(700) and UNIT_ID = 10004)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '189.5'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(710) and UNIT_ID = 10004)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '284.7'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(720) and UNIT_ID = 10004)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '284.7'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(730) and UNIT_ID = 10004)
;

update UNIT_PRODUCT_PRICING 
set PRICE = '284.7'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(730) and UNIT_ID = 10008)
;
update UNIT_PRODUCT_PRICING 
set PRICE ='99'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(740) and UNIT_ID = 10008)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '99'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(750) and UNIT_ID = 10008)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '119'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(760) and UNIT_ID = 10008)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '99'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(770) and UNIT_ID = 10008)
;
update UNIT_PRODUCT_PRICING 
set PRICE = '17.3'
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm 
where PRODUCT_ID IN(860) and UNIT_ID = 10008)
;





