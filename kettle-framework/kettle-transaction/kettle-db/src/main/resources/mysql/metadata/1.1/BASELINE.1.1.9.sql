ALTER TABLE CUSTOMER_INFO
ADD ACQUISITION_SOURCE VARCHAR(100) NOT NULL DEFAULT 'SQUARE_COINS'
;

ALTER TABLE CUSTOMER_INFO
ADD ACQUISITION_TOKEN VARCHAR(50) NOT NULL DEFAULT 'SQUARE_COINS'
;

ALTER TABLE CUSTOMER_INFO
ADD REGISTRATION_UNIT_ID INTEGER NULL ;

ALTER TABLE ORDER_DETAIL
ADD POINTS_REDEEMED INTEGER	NULL;

ALTER TABLE LOYALTY_SCORE
ADD COLUMN CUMULATIVE_POINTS INTEGER NOT NULL 
;

update LOYALTY_SCORE
set CUMULATIVE_POINTS = ACQUIRED_POINTS;


CREATE TABLE MIGRATION_LOOKUP_DATA(
LOOKUP_DATA_ID INT NOT NULL AUTO_INCREMENT,
LOOKUP_TYPE VARCHAR(10) NOT NULL DEFAULT 'EMAIL',
LOOKUP_TEXT VARCHAR(50) NOT NULL,
CUM<PERSON>LATIVE_POINTS INTEGER NOT NULL,
ACQUIRED_POINTS INTEGER NOT NULL,
REGISTRATION_UNIT_ID INTEGER NOT NULL,
FIRST_NAME VARCHAR(50) NULL,
IS_ACTIVE VARCHAR(1) NOT NULL DEFAULT 'Y',
PRIMARY KEY(LOOKUP_DATA_ID)
);

update CUSTOMER_INFO
set IS_NUMBER_VERIFIED = 'N',IS_EMAIL_VERIFIED = 'N'
where ACQUISITION_SOURCE = 'SQUARE_COINS';

