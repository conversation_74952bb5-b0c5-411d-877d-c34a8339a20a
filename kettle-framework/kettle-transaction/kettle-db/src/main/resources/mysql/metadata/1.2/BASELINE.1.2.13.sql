DELETE FROM ADDON_PRODUCT_DATA;

INSERT INTO ADDON_PRODUCT_DATA(ADDON_ID, PRODUCT_ID)
select rl.RL_ID, pd.PRODUCT_ID from REF_LOOKUP_TYPE rtl, REF_LOOKUP rl, PRODUCT_DETAIL pd
where rtl.RTL_ID = rl.RTL_ID and rl.RL_NAME = pd.PRODUCT_NAME 
and rtl.RTL_GROUP = 'ADDONS';

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(10,
'CHAAYOS_APP',
'Chaayos (Mobile App)');

INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2104', '21', 'CODFirstOrder', 'COD First Order', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2007', '20', 'InauguralOffer', 'Inaugural Offer', 'ACTIVE');
