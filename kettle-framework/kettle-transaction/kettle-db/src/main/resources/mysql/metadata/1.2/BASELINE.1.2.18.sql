DELETE FROM UNIT_PRODUCT_PRICING
WHERE UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID IN (11001,12001,12002,12003,12004,12005,12006,12007,12008,12009));

DELETE FROM UNIT_PRODUCT_MAPPING where UNIT_ID IN (11001,12001,12002,12003,12004,12005,12006,12007,12008,12009);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 11001,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 10006
and PRODUCT_ID  in (10,60,80,90,100,110,130,140,150,160,170,180,190,210,220,240,270,280,290,300,310,320,370,390,400,410,420,430,440,460,470,480,500,510,520,530,540,550,560,620,640,650,660,670,680,681,682,683,684,690,740,750,760,770,780,790,800,810);

update PRODUCT_DETAIL set IS_INVENTORY_TRACKED = 'N' where IS_INVENTORY_TRACKED = 'Y';
update PRODUCT_DETAIL set IS_INVENTORY_TRACKED = 'Y' where PRODUCT_ID IN (10,60,80,90,100,110,130,140,150,160,170,180,190,210,220,240,270,280,290,300,310,320,370,390,400,410,420,430,440,460,470,480,500,510,520,530,540,550,560,620,640,650,660,670,680,681,682,683,684,690,740,750,760,770,780,790,800,810)
and PRODUCT_TYPE IN (7,10);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 10),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 10),24,'2015-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 60),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 80),1102,'2015-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 90),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 100),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 110),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 130),23,'2015-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 140),23,'2015-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 150),23,'2015-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 160),23,'2015-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 170),23,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 180),23,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 190),23,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 210),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 220),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 240),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 270),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 280),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 290),23,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 300),30,'2015-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 310),30,'2015-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 320),30,'2015-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 370),1,'2015-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 390),1,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 400),1,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 410),1,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 420),30,'2015-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 430),30,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 440),30,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 460),30,'2015-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 470),30,'2015-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 480),30,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 500),1,'2015-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 510),1,'2015-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 520),1,'2015-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 530),1,'2015-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 540),1,'2015-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 550),1,'2015-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 560),1,'2015-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 620),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 640),1,'2015-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 650),1,'2015-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 660),1,'2015-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 670),1,'2015-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 680),1,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 681),1,'2015-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 682),1,'2015-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 683),1,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 684),1,'2015-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 690),1,'2015-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 740),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 750),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 760),1,'2015-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 770),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 780),1,'2015-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 790),1,'2015-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 800),1,'2015-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 810),1,'2015-01-01 00:00:00',55);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12001,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE 
from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12001;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12002,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12002;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12003,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12003;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12004,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12004;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12005,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12005;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12006,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12006;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12007,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12007;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12008,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12008;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12009,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12009;


INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 11001,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 10006
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12001,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12001 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12001 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12002,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12002 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12002 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12003,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12003 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12003 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12004,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12004 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12004 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12005,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12005 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12005 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12006,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12006 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12006 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12007,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12007 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12007 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12008,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12008 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12008 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12009,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001
and PRODUCT_ID  in (50);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12009 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 12009 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',139);

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_CLOSURE_EVENT` (
  `REQUEST_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` int(11) NOT NULL,
  `CLOSURE_DATE` date DEFAULT NULL,
  `OPERATION_STOP_DATE` date NOT NULL,
  `CLOSURE_STATUS` varchar(255) DEFAULT NULL,
  `MESSAGE` varchar(255) NOT NULL,
  `CREATED_BY` varchar(255) NOT NULL,
  `CREATION_TIME` datetime NOT NULL,
  `UPDATED_BY` varchar(255) DEFAULT NULL,
  `LAST_UPDATION_TIME` datetime DEFAULT NULL,
  PRIMARY KEY (`REQUEST_ID`),
  KEY `IDX_UNIT_CLOSURE_EVENT_CLOSURE_STATUS` (`CLOSURE_STATUS`),
  KEY `IDX_UNIT_CLOSURE_EVENT_UNIT_ID` (`UNIT_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_CLOSURE_STATE` (
  `STATE_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `STATE_NAME` varchar(255) NOT NULL,
  `STATE_DESCRIPTION` varchar(255) NOT NULL,
  `STATE_STATUS` varchar(255) NOT NULL,
  `QUERY` varchar(255) DEFAULT NULL,
  `PARAMETER` varchar(255) DEFAULT NULL,
  `DESTINATION_SOURCE` varchar(255) NOT NULL,
  `STATE_OWNER` varchar(255) NOT NULL,
  PRIMARY KEY (`STATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_CLOSURE_STATE_EVENT` (
  `EVENT_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `REQUEST_ID` bigint(20) NOT NULL,
  `STATE_ID` bigint(20) NOT NULL,
  `EVENT_STATUS` varchar(255) NOT NULL,
  `UPDATED_BY` varchar(255) DEFAULT NULL,
  `LAST_UPDATION_TIME` datetime DEFAULT NULL,
  PRIMARY KEY (`EVENT_ID`),
  KEY `IDX_UNIT_CLOSURE_STATE_EVENT_REQUEST_ID` (`REQUEST_ID`),
  KEY `IDX_UNIT_CLOSURE_STATE_EVENT_STATE_ID` (`STATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

