ALTER TABLE UNIT_DETAIL
ADD COLUMN NO_OF_TERMINALS INTEGER NOT NULL DEFAULT 1;


ALTER TABLE ORDER_DETAIL
ADD COLUMN TERMINAL_ID INTEGER NULL DEFAULT 1;

ALTER TABLE EMPLOYEE_SESSION_DETAILS
ADD COLUMN TERMINAL_ID INTEGER NULL DEFAULT 1;

ALTER TABLE ORDER_DETAIL
ADD COLUMN DELIVERY_PARTNER_ID INTEGER NULL DEFAULT 1;

ALTER TABLE ORDER_DETAIL
ADD COLUMN ORDER_REMARK VARCHAR(1000) NULL;

CREATE TABLE DELIVERY_PARTNER(
PARTNER_ID INT NOT NULL AUTO_INCREMENT,
PARTNER_CODE VARCHAR(50) NOT NULL,
PARTNER_DISPLAY_NAME VARCHAR(100) NOT NULL,
PRIMARY KEY(PARTNER_ID)
);

INSERT INTO DELIVERY_PARTNER(PARTNER_ID,PARTNER_CODE, PARTNER_DISPLAY_NAME)
VALUES
(1, 'NONE','None'),
(2, 'ROAD_RUNNR','Roadrunnr'),
(3, 'SHADOW_FAX','Shadowfax'),
(4, 'OPINIO','Opinio');

UPDATE REF_LOOKUP SET `RL_STATUS`='INACTIVE' WHERE `RL_ID`='902';
UPDATE REF_LOOKUP SET `RL_STATUS`='INACTIVE' WHERE `RL_ID`='903';
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) 
VALUES ('904', '9', 'Gifts', 'Gifts', 'ACTIVE');

INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('691', 'Chai Patti', 'Chaayos Speacial Playing Cards', '100', '9', '904', 'ACTIVE', '2015-11-07', '9999-12-01', 'CH1011102', '1', 'NET_PRICE', '27');

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 691, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 90.76 from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud, ADDRESS_INFO ai where upm.UNIT_ID = ud.UNIT_ID and ud.UNIT_ADDR_ID = ai.ADDRESS_ID and  ai.STATE = 'Uttar Pradesh'
and upm.PRODUCT_ID = 691;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 92.29 from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud, ADDRESS_INFO ai where upm.UNIT_ID = ud.UNIT_ID and ud.UNIT_ADDR_ID = ai.ADDRESS_ID and  ai.STATE = 'New Delhi'
and upm.PRODUCT_ID = 691;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 91.80 from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud, ADDRESS_INFO ai where upm.UNIT_ID = ud.UNIT_ID and ud.UNIT_ADDR_ID = ai.ADDRESS_ID and  ai.STATE = 'Haryana'
and upm.PRODUCT_ID = 691;


UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-8655336621', `CONTACT_NUM_2`='+91-8655336621' WHERE `ADDRESS_ID`='109';

INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10046', '17/477', 'DDA flats,Madangir', 'New Delhi', 'New Delhi', 'India', '110062', '+91-9971360667', '+91-9560526034', 'RESIDENTIAL');

UPDATE EMPLOYEE_DETAIL SET `DESIGNATION_ID`='1003' WHERE `EMP_ID`='100037';
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100041', 'Sumit', 'M', '10046', '10046', '+91-9971360667', '+91-9560526034', '101', '1002', 'FULL_TIME', 'ACTIVE', '2015-10-05', '9999-12-01', '100036');

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100041,'0UxQYmqlaaaY1jDVAZWNQQ==' );

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100041,10006);

INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('692', 'Diwali Gift Box', 'Diwali Gift Box', '100', '9', '904', 'ACTIVE', '2015-11-07', '9999-12-01', 'CH1011103', '1', 'NET_PRICE', '27');

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 692, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 0 from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud, ADDRESS_INFO ai where upm.UNIT_ID = ud.UNIT_ID and ud.UNIT_ADDR_ID = ai.ADDRESS_ID and upm.PRODUCT_ID = 692;

