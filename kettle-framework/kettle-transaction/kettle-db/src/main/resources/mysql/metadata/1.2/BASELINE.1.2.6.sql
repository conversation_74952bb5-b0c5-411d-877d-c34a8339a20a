INSERT INTO LOYALTY_EVENTS (CUS<PERSON>MER_ID, TRANSACTION_TYPE, TRANSACTION_POINTS, TRANSACTION_CODE_TYPE, TRANSACTION_CODE, TRANSACTION_STATUS, TRANSACTION_TIME)
VALUES(10613, 'DEBIT',540, 'Addition', 'MIGRATION','SUCCESS', current_timestamp);

update LOY<PERSON>TY_SCORE
set ACQUIRED_POINTS = ACQUIRED_POINTS + 540,
CUMULATIVE_POINTS = CUMULATIVE_POINTS + 540
where CUSTOMER_ID = 10613;

INSERT INTO LOYALTY_EVENTS (CUSTOMER_ID, TRANSACTION_TYPE, TRANSACTION_POINTS, TRANSACTION_CODE_TYPE, TRANSACTION_CODE, TRANSACTION_STATUS, TRANSACTION_TIME)
VALUES(8978, 'CREDIT',-540,'Addition', '<PERSON><PERSON>ATION','SUCCESS', current_timestamp);

update <PERSON><PERSON><PERSON><PERSON><PERSON>_SCORE
set ACQUIRED_POINTS = 0
where <PERSON><PERSON><PERSON>MER_ID = 8978;


INSERT INTO LOYALTY_EVENTS (CUSTOMER_ID, TRANSACTION_TYPE, TRANSACTION_POINTS, TRANSACTION_CODE_TYPE, TRANSACTION_CODE, TRANSACTION_STATUS, TRANSACTION_TIME)
VALUES(4286, 'DEBIT',70, 'Addition', 'OUTLET_VISIT','SUCCESS', current_timestamp);

update LOYALTY_SCORE
set ACQUIRED_POINTS = ACQUIRED_POINTS + 70,
CUMULATIVE_POINTS = CUMULATIVE_POINTS + 70
where CUSTOMER_ID = 4286;
