ALTER TABLE TAX_PROFILE
ADD COLUMN TAX_PROFILE_STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE';


update TAX_PROFILE 
set TAX_PROFILE_STATUS = 'IN_ACTIVE'
where TAX_PROFILE_ID = 5;

update <PERSON><PERSON>_DETAIL
set UNIT_STATUS = 'ACTIVE';

INSERT INTO DELIVERY_PARTNER(PARTNER_ID,PARTNER_CODE, PARTNER_DISPLAY_NAME)
VALUES
(5, 'PICKUP','Pick Up');

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10011,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 10000
and PRODUCT_ID  in (610);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`<PERSON>IMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10011 AND PRODUCT_ID = 610),1,'2015-01-01 00:00:00',79.00);

