ALTER TABLE ORDER_DETAIL
ADD COLUMN SALE_AMOUNT DECIMAL(10,2) NULL;

ALTER TABLE ORDER_DETAIL
ADD COLUMN PROMOTIONAL_DISCOUNT DECIMAL(10,2) NULL;

ALTER TABLE ORDER_DETAIL
ADD COLUMN TOTAL_DISCOUNT DECIMAL(10,2) NULL;

ALTER TABLE ORDER_ITEM
ADD COLUMN AMOUNT_PAID DECIMAL(10,2) NULL;

UPDATE ORDER_ITEM 
SET 
    AMOUNT_PAID = CASE
        WHEN COMPLIMENTARY_TYPE_ID IS NULL THEN TOTAL_AMOUNT
        WHEN COMPLIMENTARY_TYPE_ID IN (2100 , 2102) THEN 0
        ELSE PRICE * QUANTITY
    END
;

UPDATE ORDER_ITEM 
SET 
    TOTAL_AMOUNT = PRICE * QUANTITY
;

UPDATE ORDER_DETAIL od
        LEFT OUTER JOIN
    (SELECT 
        ORDER_ID,
            SUM(CASE
                WHEN COMPLIMENTARY_TYPE_ID IN (2100 , 2102) THEN (PRICE * QUANTITY)
                ELSE 0
            END) NON_ACCOUNTABLE,
            SUM(CASE
                WHEN COMPLIMENTARY_TYPE_ID NOT IN (2100 , 2102) THEN (PRICE * QUANTITY)
                ELSE 0
            END) ACCOUNTABLE
    FROM
        ORDER_ITEM
    WHERE
        IS_COMPLIMENTARY = 'Y'
    GROUP BY ORDER_ID) oi ON od.ORDER_ID = oi.ORDER_ID 
SET 
    od.PROMOTIONAL_DISCOUNT = CASE
        WHEN oi.ORDER_ID IS NULL THEN null
        ELSE oi.ACCOUNTABLE
    END
;

UPDATE ORDER_DETAIL 
set SALE_AMOUNT= TOTAL_AMOUNT;

UPDATE ORDER_DETAIL 
set TOTAL_AMOUNT= TOTAL_AMOUNT + PROMOTIONAL_DISCOUNT
where PROMOTIONAL_DISCOUNT is not null ;

update ORDER_DETAIL
set TOTAL_DISCOUNT = PROMOTIONAL_DISCOUNT + DISCOUNT_AMOUNT;

UPDATE `PRODUCT_DETAIL` SET `PRODUCT_NAME`='Bun Maska - Mint Jalapeno' WHERE `PRODUCT_ID`='684';

UPDATE `COMPLIMENTARY_CODE` SET `IS_ACCOUNTABLE`='N' WHERE `COMP_ID`='2100';
UPDATE `COMPLIMENTARY_CODE` SET `IS_ACCOUNTABLE`='N' WHERE `COMP_ID`='2102';
INSERT INTO `COMPLIMENTARY_CODE` (`COMP_ID`, `COMP_CODE`, `NAME`, `DESCRIPTION`, `STATUS`, `IS_ACCOUNTABLE`) VALUES ('2105', 'Training', 'Training', 'Complimentary Codes', 'ACTIVE', 'N');
INSERT INTO `COMPLIMENTARY_CODE` (`COMP_ID`, `COMP_CODE`, `NAME`, `DESCRIPTION`, `STATUS`, `IS_ACCOUNTABLE`) VALUES ('2106', 'SamplingNMarketing', 'Sampling & Marketing activities', 'Complimentary Codes', 'ACTIVE', 'Y');
UPDATE `COMPLIMENTARY_CODE` SET `STATUS`='IN_ACTIVE' WHERE `COMP_ID`='2103';

INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2105', '21', 'Training', 'Training', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2106', '21', 'SamplingNMarketing', 'Sampling & Marketing activities', 'ACTIVE');

UPDATE `UNIT_PRODUCT_PRICING` SET `PRICE`='45.00' WHERE `UNIT_PROD_PRICE_ID`='994';
UPDATE `UNIT_PRODUCT_PRICING` SET `PRICE`='55.00' WHERE `UNIT_PROD_PRICE_ID`='995';

UPDATE `UNIT_PRODUCT_PRICING` SET `PRICE`='55.00' WHERE `UNIT_PROD_PRICE_ID`='1748';
UPDATE `UNIT_PRODUCT_PRICING` SET `PRICE`='55.00' WHERE `UNIT_PROD_PRICE_ID`='1763';

UPDATE `UNIT_PRODUCT_PRICING` SET `PRICE`='69.00' WHERE `UNIT_PROD_PRICE_ID`='1778';
UPDATE `UNIT_PRODUCT_PRICING` SET `PRICE`='69.00' WHERE `UNIT_PROD_PRICE_ID`='1793';

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(11,
'GROFERS',
'Grofers');

UPDATE `PRODUCT_DETAIL` SET `PRODUCT_NAME`='2 Minutes Sandwich' WHERE `PRODUCT_ID`='610';
UPDATE `PRODUCT_DETAIL` SET `PRODUCT_NAME`='Bombay Special' WHERE `PRODUCT_ID`='630';
UPDATE `PRODUCT_DETAIL` SET `PRODUCT_NAME`='Bun Maska - Mint Jalapeno' WHERE `PRODUCT_ID`='684';
UPDATE `PRODUCT_DETAIL` SET `PRODUCT_NAME`='Additional Kulhad' WHERE `PRODUCT_ID`='861';
UPDATE `PRODUCT_DETAIL` SET `PRODUCT_NAME`='Additional Honey' WHERE `PRODUCT_ID`='860';
