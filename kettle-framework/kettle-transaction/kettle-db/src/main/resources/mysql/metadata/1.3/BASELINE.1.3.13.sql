CREATE TABLE INVENTORY_UPDATE_EVENT (
INVENTORY_UPDATE_EVENT_ID INTEGER NOT NULL AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
RECORDS_COUNT INTEGER NOT NULL,
EVENT_TYPE VARCHAR(15) NOT NULL,
UPDATED_BY INTEGER NULL,
UPDATE_COMMENT VARCHAR(300) NULL,
UPDATE_TIME TIMESTAMP NOT NULL,
UPDATE_STATUS VARCHAR(15) NOT NULL,
BUSINESS_DATE TIMESTAMP NOT NULL,
PRIMARY KEY(INVENTORY_UPDATE_EVENT_ID)
);

CREATE TABLE INVENTORY_UPDATE_DATA (
INVENTORY_UPDATE_DATA_ID INTEGER NOT NULL AUTO_INCREMENT,
INVENTORY_UPDATE_EVENT_ID INTEGER NOT NULL,
UNIT_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
QUANTITY INTEGER NULL,
THRESHOLD_QUANTITY INTEGER NULL,
ADD_TIME TIMESTAMP NOT NULL,
BUSINESS_DATE TIMESTAMP NOT NULL,
PRIMARY KEY(INVENTORY_UPDATE_DATA_ID)
);

CREATE TABLE INVENTORY_THRESHOLD_DATA (
INVENTORY_THRESHOLD_DATA_ID INTEGER NOT NULL AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
DAY_OF_THE_WEEK INTEGER NOT NULL,
AVG_QUANTITY INTEGER NULL,
MIN_QUANTITY INTEGER NULL,
MAX_QUANTITY INTEGER NULL,
TOTAL_QUANTITY INTEGER NULL,
TOTAL_DAYS INTEGER NULL,
THRESHOLD_DATA_STATUS VARCHAR(15) NOT NULL DEFAULT 'ACTIVE',
LAST_UPDATE_TIME TIMESTAMP NOT NULL,
PRIMARY KEY(INVENTORY_THRESHOLD_DATA_ID)
);
INSERT INTO `INVENTORY_THRESHOLD_DATA`
(`UNIT_ID`,`PRODUCT_ID`,`DAY_OF_THE_WEEK`,`AVG_QUANTITY`,`MIN_QUANTITY`,
`MAX_QUANTITY`,`TOTAL_QUANTITY`,`TOTAL_DAYS`,`LAST_UPDATE_TIME`, `THRESHOLD_DATA_STATUS`)
SELECT 
    oi.UNIT_ID,
    oi.PRODUCT_ID,
    oi.DAY_OF_THE_WEEK,
    oi.AVG_QUANTITY,
    oi.MIN_QUANTITY,
    oi.MAX_QUANTITY,
    oi.TOTAL_QUANTITY,
    oi.TOTAL_DAYS,
    CURRENT_TIMESTAMP,
    'ACTIVE'
FROM
    (SELECT 
        b.UNIT_ID UNIT_ID,
            b.PRODUCT_ID PRODUCT_ID,
            b.DAY_OF_THE_WEEK DAY_OF_THE_WEEK,
            COUNT(DISTINCT b.BUSINESS_DATE) TOTAL_DAYS,
            SUM(b.TOTAL_QUANTITY) TOTAL_QUANTITY,
            MAX(b.TOTAL_QUANTITY) MAX_QUANTITY,
            MIN(b.TOTAL_QUANTITY) MIN_QUANTITY,
            CEIL(SUM(b.TOTAL_QUANTITY) / COUNT(DISTINCT b.BUSINESS_DATE)) AVG_QUANTITY
    FROM
        (SELECT 
        a.UNIT_ID,
            a.PRODUCT_ID,
            a.DAY_OF_THE_WEEK,
            a.BUSINESS_DATE,
            SUM(a.QUANTITY) TOTAL_QUANTITY
    FROM
        (SELECT 
        CASE
                WHEN HOUR(od.BILL_GENERATION_TIME) <= 5 THEN WEEKDAY(DATE_ADD(BILL_GENERATION_TIME, INTERVAL - 6 HOUR))
                ELSE WEEKDAY(od.BILL_GENERATION_TIME)
            END DAY_OF_THE_WEEK,
            CASE
                WHEN HOUR(od.BILL_GENERATION_TIME) <= 5 THEN DATE(DATE_ADD(BILL_GENERATION_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(od.BILL_GENERATION_TIME)
            END BUSINESS_DATE,
            od.UNIT_ID,
            oi.PRODUCT_ID,
            oi.QUANTITY
    FROM
        ORDER_DETAIL od, ORDER_ITEM oi
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND od.ORDER_STATUS = 'SETTLED' and BILL_GENERATION_TIME >= '2015-11-30 00:00:00') a
    GROUP BY a.UNIT_ID , a.PRODUCT_ID , a.DAY_OF_THE_WEEK , a.BUSINESS_DATE) b
    GROUP BY b.UNIT_ID , b.PRODUCT_ID , b.DAY_OF_THE_WEEK) oi 
