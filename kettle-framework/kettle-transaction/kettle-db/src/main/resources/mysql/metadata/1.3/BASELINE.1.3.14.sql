UPDATE `REF_LOOKUP` SET `RL_NAME`='2 Minutes Sandwich' WHERE `RL_ID`='2902';

ALTER TABLE REF_LOOKUP_TYPE MODIFY COLUMN RTL_CODE VARCHAR(30);

INSERT INTO `REF_LOOKUP_TYPE` (`RTL_ID`, `RTL_GROUP`, `RTL_CODE`, `RTL_NAME`) VALUES ('30', 'ADDONS', 'HolidayComboNashta', 'Holiday Combo Nashta');
INSERT INTO `REF_LOOKUP_TYPE` (`RTL_ID`, `RTL_GROUP`, `RTL_CODE`, `RTL_NAME`) VALUES ('31', 'ADDONS', 'HolidayComboSandwich', 'Holiday Combo Sandwich');

INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3002,30,'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>','2 Minute Sandwich','2M','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3003,30,'HomestyleAloo','Homestyle Aloo','ALO','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3004,30,'BombaySpecial','Bombay Special','BS','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3005,30,'KeemaPav','Keema Pav','KP','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3006,30,'EggBun','Egg Bun','EB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3007,30,'Poha','Poha','POH','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3008,30,'VadaPav','Vada Pav','VP','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3009,30,'BunBhujia','Bun Bhujia','BB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3010,30,'BunMaska','Bun Maska','BM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3011,30,'To-Go','To-Go','*TG','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3014,30,'HoneyGarlicMayo','Honey Garlic Mayo','HGM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3015,30,'RedChilliMayo','Red Chilli Mayo','RCM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3016,30,'GreenChilliMayo','Green Chilli Mayo','GCM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3017,30,'HariChutni','Hari Chutni','HC','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3018,30,'NoToast','No Toast','NT','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3019,30,'NoButter','No Butter','NB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3020,30,'ExtraSugar','Extra Sugar','ES','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3021,30,'SugarFree','Sugar Free','SF','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3022,30,'NoSugar','No Sugar','NS','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3026', '30', 'ChaayosSpecial', 'Chaayos Special', 'CSP', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3027', '30', 'DesiChaiChotiKetli', 'Desi Chai Chotiketli', 'DCCK', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3028', '30', 'BunMaskaKeema', 'Bun Maska Keema', 'BMK', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3029', '30', 'BunMaskaVada', 'Bun Maska Vada', 'BMV', 'ACTIVE');

INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3101,31,'Kulhad Chai','Kulhad Chai','KUL','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3104,31,'SicilianChicken','Sicilian Chicken','SIC','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3105,31,'SpinachCornCheese','Spinach Corn Cheese','SCC','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3106,31,'HoneyGarlicMayo','Honey Garlic Mayo','HGM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3107,31,'WrapToSandwich','Wrap To Sandwich','W2S','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3108,31,'To-Go','To-Go','*TG','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3109,31,'RedChilliMayo','Red Chilli Mayo','RCM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3110,31,'GreenChilliMayo','Green Chilli Mayo','GCM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3111,31,'HariChutni','Hari Chutni','HC','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3113,31,'NoToast','No Toast','NT','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3114,31,'NoButter','No Butter','NB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3115,31,'FocacciaBread','Focaccia Bread','FB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3116,31,'MultigrainBread','Multigrain Bread','MB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3117,31,'JumboBread','Jumbo Bread','JB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3118,31,'CroissantBread','Croissant Bread','CB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3119,31,'ExtraSugar','Extra Sugar','ES','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3120,31,'SugarFree','Sugar Free','SF','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3121,31,'NoSugar','No Sugar','NS','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3125', '31', 'GreenChicken', 'Green Chicken', 'GRC', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3126', '31', 'PepperChicken', 'Pepper Chicken', 'PC', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3127', '31', 'Napoli', 'Napoli', 'NS', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3128', '31', 'Balsamico', 'Balsamico', 'BS', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3130', '31', 'ChocolateBun', 'Chocolate Bun', 'CBS', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3131', '31', 'BlueberryCake', 'Blueberry Cake', 'BBC', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3132', '31', 'BananaCake', 'Banana Cake', 'BAC', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3133', '31', 'CarrotCake', 'Carrot Cake', 'CRC', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3134', '31', 'LemonCake', 'LemonCake', 'LMC', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3135,31,'MaggiSandwich','2 Minute Sandwich','2M','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3136,31,'HomestyleAloo','Homestyle Aloo','ALO','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3137,31,'BombaySpecial','Bombay Special','BS','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3138,31,'KeemaPav','Keema Pav','KP','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3139,31,'EggBun','Egg Bun','EB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3140,31,'Poha','Poha','POH','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3141,31,'VadaPav','Vada Pav','VP','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3142,31,'BunBhujia','Bun Bhujia','BB','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3143,31,'BunMaska','Bun Maska','BM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3144', '31', 'ChaayosSpecial', 'Chaayos Special', 'CSP', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3145', '31', 'BunMaskaKeema', 'Bun Maska Keema', 'BMK', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3146', '31', 'BunMaskaVada', 'Bun Maska Vada', 'BMV', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3147', '31', 'DesiChai', 'Desi Chai', 'DC', 'ACTIVE');

INSERT INTO `PRODUCT_DETAIL` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`) VALUES ('821', 'Holiday Kettle & Nashta Veg', 'Holiday Kettle & Nashta Combo Veg', '100', '8', '801', 'ACTIVE', 'VEG', '2013-01-01', '9999-12-01', 'CH1011501', '1', 'NET_PRICE', '30', '2015-12-22 18:00:00', '2030-12-01 00:00:00', 'N');
INSERT INTO `PRODUCT_DETAIL` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`) VALUES ('822', 'Holiday Kettle & Nashta Non-Veg', 'Holiday Kettle & Nashta Combo Non-Veg', '100', '8', '801', 'ACTIVE', 'NON_VEG', '2013-01-01', '9999-12-01', 'CH1011502', '1', 'NET_PRICE', '30', '2015-12-22 18:00:00', '2030-12-01 00:00:00', 'N');
INSERT INTO `PRODUCT_DETAIL` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`) VALUES ('831', 'Holiday Sandwich Veg', 'Holiday Sandwich Combo Veg', '100', '8', '801', 'ACTIVE', 'VEG', '2013-01-01', '9999-12-01', 'CH1011503', '1', 'NET_PRICE', '31', '2015-12-22 18:00:00', '2030-12-01 00:00:00', 'N');
INSERT INTO `PRODUCT_DETAIL` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`) VALUES ('832', 'Holiday Sandwich Non-Veg', 'Holiday Sandwich Combo Non-Veg', '100', '8', '801', 'ACTIVE', 'NON_VEG', '2013-01-01', '9999-12-01', 'CH1011504', '1', 'NET_PRICE', '31', '2015-12-22 18:00:00', '2030-12-01 00:00:00', 'N');

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select upm.UNIT_ID, 831, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud where upm.UNIT_ID = ud.UNIT_ID and PRODUCT_ID = 660 and ud.UNIT_CATEGORY = 'CAFE';

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 199.00 from UNIT_PRODUCT_MAPPING upm where upm.PRODUCT_ID = 831;

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select upm.UNIT_ID, 832, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud where upm.UNIT_ID = ud.UNIT_ID and PRODUCT_ID = 660 and ud.UNIT_CATEGORY = 'CAFE';

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 219.00 from UNIT_PRODUCT_MAPPING upm where upm.PRODUCT_ID = 832;

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select upm.UNIT_ID, 821, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud where upm.UNIT_ID = ud.UNIT_ID and PRODUCT_ID = 10 and ud.UNIT_CATEGORY IN( 'COD', 'DELIVERY');

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 89.00 from UNIT_PRODUCT_MAPPING upm where upm.PRODUCT_ID = 821;

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select upm.UNIT_ID, 822, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud where upm.UNIT_ID = ud.UNIT_ID and PRODUCT_ID = 10 and ud.UNIT_CATEGORY IN( 'COD', 'DELIVERY');

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,1,current_timestamp, 99.00 from UNIT_PRODUCT_MAPPING upm where upm.PRODUCT_ID = 822;


insert into ADDON_PRODUCT_DATA(ADDON_ID, PRODUCT_ID)
select rl.RL_ID, pd.PRODUCT_ID from REF_LOOKUP rl, PRODUCT_DETAIL pd where  RTL_ID IN (30,31) and pd.PRODUCT_NAME = rl.RL_NAME;


INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3030,30,'Tulsi','Tulsi','T','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3031,30,'Adrak','Adrak','A','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3032,30,'Eliachi','Eliachi','E','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3033,30,'Laung','Laung','L','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3034,30,'Cinnamon','Cinnamon','C','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3035,30,'Masala','Masala','M','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3036,30,'Kali Mirch','Kali Mirch','KM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3037,30,'Mint','Mint','MNT','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3038,30,'Ajwain','Ajwain','AJ','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3039,30,'Moti Eliachi','Moti Eliachi','ME','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3040,30,'Hari Mirch','Hari Mirch','HM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3041,30,'Kadak','Kadak','KDK','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3042,30,'Extra Milk','Extra Milk',NULL,'INACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3043,30,'Honey','Honey','HNY','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3044,30,'Mulethi','Mulethi','ML','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3045,30,'Kesar','Kesar','KES','ACTIVE');

INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3150,31,'Tulsi','Tulsi','T','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3151,31,'Adrak','Adrak','A','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3152,31,'Eliachi','Eliachi','E','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3153,31,'Laung','Laung','L','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3154,31,'Cinnamon','Cinnamon','C','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3155,31,'Masala','Masala','M','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3156,31,'Kali Mirch','Kali Mirch','KM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3157,31,'Mint','Mint','MNT','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3158,31,'Ajwain','Ajwain','AJ','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3159,31,'Moti Eliachi','Moti Eliachi','ME','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3160,31,'Hari Mirch','Hari Mirch','HM','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3161,31,'Kadak','Kadak','KDK','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3162,31,'Extra Milk','Extra Milk',NULL,'INACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3163,31,'Honey','Honey','HNY','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3164,31,'Mulethi','Mulethi','ML','ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`,`RTL_ID`,`RL_CODE`,`RL_NAME`,`RL_SHORT_CODE`,`RL_STATUS`) VALUES (3165,31,'Kesar','Kesar','KES','ACTIVE');


