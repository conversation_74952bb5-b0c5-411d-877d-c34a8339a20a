ALTER TABLE DELIVERY_PARTNER
ADD COLUMN PARTNER_STATUS VARCHAR (15) NOT NULL DEFAULT 'ACTIVE';

ALTER TABLE DELIVERY_PARTNER
ADD COLUMN PARTNER_TYPE VARCHAR (15) NOT NULL DEFAULT 'EXTERNAL';

 update DELIVERY_PARTNER
 set PARTNER_TYPE = 'INTERNAL'
 where PARTNER_ID  IN (1,5);
 
INSERT INTO `KETTLE_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('20', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON> Davidson', 'ACTIVE');
INSERT INTO `KETTLE_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('20', 'CitibankCard', 'Citibank Card', 'ACTIVE');
INSERT INTO `<PERSON>ETTLE_DEV`.`REF_LOOKUP` (`RTL_ID`, `<PERSON>L_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('20', 'SlonkitCard', 'Slonkit Card', 'ACTIVE');
