

DROP TABLE IF EXISTS REPORT_STATUS_EVENT;

CREATE TABLE REPORT_STATUS_EVENT_DATA(
	EVENT_ID INT NOT NULL AUTO_INCREMENT,
	UNIT_ID INT,
	USER_ID INT,
	REPORT_START_TIME TIMESTAMP NULL,
	START_ORDER INT,
	END_ORDER INT,
	TENANT_ID VARCHAR(20),
	TERMINAL_ID INT,
	FILE_NO INT,
	BUSINESS_DATE TIMESTAMP NULL,
	EVENT_STATUS VARCHAR(10),
	DESCRIPTION VARCHAR(100),
	PRIMARY KEY (EVENT_ID)
);

#!! FIX THE UNIT ID
#INSERT INTO PARTNER_ATTRIBUTES (PARTNER_ID,MAPPING_TYPE,MAPPING_VALUE,PARTNER_TYPE)
#VALUES ( UNITID ,'TENANT_ID','DUMMY123456789','SALES_REPORT');

#INSERT INTO PARTNER_ATTRIBUTES (PARTNER_ID,MAPPING_TYPE,MAPPING_VALUE,PARTNER_TYPE)
#VALUES ( UNITID,'FTP_SERVER','ftp://[<user>[:<password>]@]<host>[:<port>]/<url-path>','SALES_REPORT');


ALTER TABLE REF_LOOKUP_TYPE
ADD COLUMN STATUS VARCHAR(10) DEFAULT 'ACTIVE';