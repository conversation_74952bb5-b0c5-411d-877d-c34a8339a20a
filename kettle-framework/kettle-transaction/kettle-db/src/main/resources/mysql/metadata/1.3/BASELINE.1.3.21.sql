
UPDATE ORDER_ITEM
SET DIMENSION = 'Regular'
WHERE DIMENSION = 'REGULAR';

UPDATE ORDER_ITEM
SET DIMENSION = 'Full'
WHERE DIMENSION = 'LARGE';


#CHANGE DIMENTIONS FOR MANGO_SHAKE TO NONE
UPDATE ORDER_ITEM
SET DIMENSION = 'None'
WHERE PRODUCT_ID = 400;

UPDATE PRODUCT_DETAIL
SET PRODUCT_STATUS = 'IN_ACTIVE'
WHERE PRODUCT_STATUS =  'INACTIVE';

#REMOVE CURRENT_TIMESTAMP ON_UPDATE PARAMETER
#-------------------------------------------------------------------------------------------
ALTER TABLE AUTHORIZATION_REQUEST
CHANGE ADD_TIME ADD_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE CLOSURE_STATUS
CHANGE UPDATE_TMSTMP UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE DELIVERY_DETAIL
CHANGE STATUS_UPDATE_TMSTMP STATUS_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE DELIVERY_STATUS_EVENT
CHANGE STATUS_UPDATE_TMSTMP STATUS_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE EMPLOYEE_PASS_CODE
CHANGE LAST_UPDATE_TMSTMP LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE EMPLOYEE_PASS_CODE
CHANGE LAST_UPDATE_TMSTMP LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE EMPLOYEE_UNIT_MAPPING
CHANGE LAST_UPDATE_TIME LAST_UPDATE_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE INVENTORY_THRESHOLD_DATA
CHANGE LAST_UPDATE_TIME LAST_UPDATE_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE INVENTORY_UPDATE_DATA
CHANGE ADD_TIME ADD_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE INVENTORY_UPDATE_EVENT
CHANGE UPDATE_TIME UPDATE_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE ORDER_DETAIL
CHANGE BILL_START_TIME BILL_START_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE ORDER_STATUS_EVENT
CHANGE UPDATE_TIME UPDATE_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE UNIT_DETAIL
CHANGE START_DATE START_DATE TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE UNIT_PRODUCT_INVENTORY
CHANGE LAST_UPDATE_TMSTMP LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE UNIT_PRODUCT_MAPPING
CHANGE LAST_UPDATE_TMSTMP LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE UNIT_PRODUCT_PRICING
CHANGE LAST_UPDATE_TMSTMP LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
#-------------------------------------------------------------------------------------------

#ADDING NEW_COLUMN TO ORDER_DETAIL
ALTER TABLE ORDER_DETAIL ADD COLUMN BILL_CREATION_SECONDS INT;

#ADDING NEW_COLUMN TO ORDER_DETAIL WITH_DEFAULT_NULL
ALTER TABLE ORDER_DETAIL ADD COLUMN BILLING_SERVER_TIME TIMESTAMP NULL;


#UPDATING REQUEST_TIME TO IST
UPDATE ORDER_EMAIL_NOTIFICATION
SET REQUEST_TIME =  ADDTIME(REQUEST_TIME, '05:30:00')
WHERE ORDER_ID <= 736;


#UPDATING BILLING_SERVER_TIME TO MIN_VALUE_OF REQUEST_TIME
UPDATE ORDER_DETAIL OD, ORDER_EMAIL_NOTIFICATION OEN
SET OD.BILLING_SERVER_TIME = OEN.REQUEST_TIME
WHERE OD.ORDER_ID = OEN.ORDER_ID AND OEN.RETRY_COUNT = 1; 


#MODIFYING BILLING_SERVER_TIME BACK TO NOT_NULL_VALUES
ALTER TABLE ORDER_DETAIL
MODIFY COLUMN BILLING_SERVER_TIME TIMESTAMP NOT NULL;


#ADDING COMPANY_ID TO VENDOR DETAILS
ALTER TABLE VENDOR_DETAIL
ADD COLUMN COMPANY_ID INT NOT NULL DEFAULT 1000;

INSERT INTO DESIGNATION (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`) VALUES ('1501', 'Business Analyst', 'Business Analyst');
INSERT INTO DEPARTMENT_DESIGNATION_MAPPING (`DEPT_ID`, `DESIGNATION_ID`) VALUES ('102', '1501');
