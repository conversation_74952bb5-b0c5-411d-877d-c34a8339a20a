ALTER TABLE CHANNEL_PARTNER
ADD COLUMN SERVICE_TYPE VARCHAR(15) NOT NULL DEFAULT 'THIRD_PARTY';

UPDATE `CHANNEL_PARTNER` SET `SERVICE_TYPE`='DINE_IN' WHERE `PARTNER_ID`='1';
UPDATE `CHANNEL_PARTNER` SET `SERVICE_TYPE`='DELIVERY' WHERE `PARTNER_ID`='2';
UPDATE `CHANNEL_PARTNER` SET `SERVICE_TYPE`='TAKE_AWAY' WHERE `PARTNER_ID`='9';
UPDATE `CHANNEL_PARTNER` SET `SERVICE_TYPE`='DELIVERY' WHERE `PARTNER_ID`='10';


ALTER TABLE EMPLOYEE_UNIT_MAPPING
ADD COLUMN MAPPING_STATUS VARCHAR(15) NOT NULL DEFAULT 'ACTIVE';

ALTER TABLE EMPLOYEE_UNIT_MAPPING
ADD COLUMN LAST_UPDATE_TIME TIMESTAMP NOT NULL ;

update <PERSON><PERSON>LOYEE_UNIT_MAPPING
set LAST_UPDATE_TIME  = CURRENT_TIMESTAMP;

ALTER TABLE EMPLOYEE_UNIT_MAPPING
DROP FOREIGN KEY EMPLOYEE_UNIT_MAPPING_ibfk_1;

ALTER TABLE EMPLOYEE_UNIT_MAPPING
DROP INDEX EMP_ID;

ALTER TABLE EMPLOYEE_UNIT_MAPPING
ADD FOREIGN KEY FK_EMPLOYEE_PASS_CODE_EMP_DETAIL(EMP_ID)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE;

ALTER TABLE EMPLOYEE_UNIT_MAPPING
ADD UNIQUE INDEX (EMP_ID, UNIT_ID, MAPPING_STATUS);  

