
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VAL<PERSON>ATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('3', 'BILL', 'PERCENTAGE_BILL_STRATEGY', 'ZOMATO REVIEWER: Chai Drinkers', '<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>', '2016-02-11', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '5', '5', '0', '100');
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('4', 'BILL', 'PERCENTAGE_BILL_STRATEGY', 'ZOMATO REVIEWER: Non-Chai Drinkers', 'Filter Kaafi, Aam Papad Chai, Shake- Kiwi, Iced Chai- Passion Fruit, Modinagar Shikanji', '2016-02-11', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '5', '5', '0', '100');
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('5', 'BILL', 'PERCENTAGE_BILL_STRATEGY', 'ZOMATO REVIEWER: Vegetarian', 'Bun Maska Achari, Paneer Sambossa, Vada Pav', '2016-02-11', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '3', '3', '0', '100');
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('6', 'BILL', 'PERCENTAGE_BILL_STRATEGY', 'ZOMATO REVIEWER: Non-Vegetarian', 'Bun Maska Achari, Chicken Sambossa, Keema Pav', '2016-02-11', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '3', '3', '0', '100');



INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('3', 'ZOMCHAI', '2016-02-11', '2016-03-31', 'Y', 'Y', '50', 'ACTIVE', '0', 'Y');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('4', 'ZOMNONCHAI', '2016-02-11', '2016-03-31', 'Y', 'Y', '50', 'ACTIVE', '0', 'Y');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('5', 'ZOMVEG', '2016-02-11', '2016-03-31', 'Y', 'Y', '50', 'ACTIVE', '0', 'Y');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('6', 'ZOMNONVEG', '2016-02-11', '2016-03-31', 'Y', 'Y', '50', 'ACTIVE', '0', 'Y');




#UBER COUPON
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('7', 'BILL', 'PERCENTAGE_BILL_STRATEGY', 'UBER', 'Get 15% off on a spend of 250 & Above.', '2016-02-11', '2016-03-14', 'ACTIVE', '250', 'Y', 'Y', '1', 'INTERNAL', '1', '1', '0', '15');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('7', 'UBER', '2016-02-11', '2016-03-14', 'Y', 'Y', '100000', 'ACTIVE', '0', 'N');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) SELECT COUPON_DETAIL_ID,'UNIT_REGION','NCR','java.lang.String','1','1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE LIKE 'UBER';


#COUPON FOR MUMBAI CHAIV50
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('8', 'BILL', 'FLAT_BILL_STRATEGY', 'Get Rs.50 off on a spend of 300 and above', 'Get Rs.50 off on a spend of 300 and above', '2016-02-13', '2016-05-12', 'ACTIVE', '300', 'Y', 'Y', '1', 'INTERNAL', '1', '1', '0', '50');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('8', 'CHAIV50', '2016-02-13', '2016-05-12', 'Y', 'Y', '100000', 'ACTIVE', '0', 'N');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) SELECT COUPON_DETAIL_ID,'UNIT_REGION','MUMBAI','java.lang.String','1','1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'CHAIV50';


UPDATE OFFER_DETAIL_DATA SET OFFER_TYPE = 'PERCENTAGE_BILL_STRATEGY',
OFFER_VALUE = 15,
MIN_VALUE = 250,
OFFER_TEXT = 'Get 15% off on a bill of 250 & Above',
OFFER_DESCRIPTION = 'Get 15% off on a bill of 250 & Above'
WHERE OFFER_DETAIL_ID = 8;


#COUPON FOR CHAAMY16 
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE)
VALUES ('17', 'ITEM', 'PERCENTAGE_ITEM_STRATEGY', 'Get Free Desi Chai (Regular)', 'Get Free Desi Chai (Regular)', '2016-02-15', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '1', '1', '0', '100');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
VALUES ('17', 'CHAAMY16', '2016-02-15', '2016-03-31', 'Y', 'N', '40000', 'ACTIVE', '0', 'N');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP)
SELECT COUPON_DETAIL_ID,'PRODUCT','10-Regular','java.lang.Integer','1','1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'CHAAMY16';

INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE) VALUES ('17','PRODUCT','10');