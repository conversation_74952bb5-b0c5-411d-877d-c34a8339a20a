ALTER TABLE SETTLEMENT_DETAIL ADD COLUMN SETTLEMENT_STATUS VARCHAR(20) NOT NULL DEFAULT 'UNSETTLED';
ALTER TABLE PAYMENT_MODE ADD COLUMN GENERATE_PULL BOOLEAN NOT NULL;
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='1';
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='2';
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='3';
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='4';
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='5';
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='6';
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='7';
UPDATE PAYMENT_MODE SET `GENERATE_PULL`='1' WHERE `PAYMENT_MODE_ID`='8';

INSERT INTO PAYMENT_MODE (`MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`, `GENERATE_PULL`) 
VALUES ('OtherPayments', 'COUPON', 'Other Payments', 'CREDIT', '0');


#FOR PROMINADE
INSERT INTO PARTNER_ATTRIBUTES (PARTNER_ID,MAPPING_TYPE,MAPPING_VALUE,PARTNER_TYPE)
VALUES
( '12019','TENANT_ID','PRDLEAS00081215','SALES_REPORT'),
( '12019','FTP_SERVER','************','SALES_REPORT'),
( '12019','FTP_PORT','21','SALES_REPORT'),
( '12019','USERNAME','dlfposuser','SALES_REPORT'),
( '12019','PASSWORD','dlfposuser','SALES_REPORT'),
( '12019','STATUS','ACTIVE','SALES_REPORT');


INSERT INTO REPORT_STATUS_EVENT_DATA 
VALUES (1,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',0,1,'2016-02-24 18:30:00','CLOSED','SALES Report'),
(2,12019,100000,'2016-03-07 09:13:50',1,1,'PRDLEAS00081215',1,1,'2016-02-24 18:30:00','CLOSED','SALES Report'),
(3,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',2,1,'2016-02-24 18:30:00','CLOSED','SALES Report'),
(4,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',101,1,'2016-02-24 18:30:00','CLOSED','SALES Report'),
(5,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',0,2,'2016-02-25 18:30:00','CLOSED','SALES Report'),
(6,12019,100000,'2016-03-07 09:13:50',1,2,'PRDLEAS00081215',1,2,'2016-02-25 18:30:00','CLOSED','SALES Report'),
(7,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',2,2,'2016-02-25 18:30:00','CLOSED','SALES Report'),
(8,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',101,2,'2016-02-25 18:30:00','CLOSED','SALES Report'),
(9,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',0,3,'2016-02-26 18:30:00','CLOSED','SALES Report'),
(10,12019,100000,'2016-03-07 09:13:50',2,133,'PRDLEAS00081215',1,3,'2016-02-26 18:30:00','CLOSED','SALES Report'),
(11,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',2,3,'2016-02-26 18:30:00','CLOSED','SALES Report'),
(12,12019,100000,'2016-03-07 09:13:50',0,8,'PRDLEAS00081215',101,3,'2016-02-26 18:30:00','CLOSED','SALES Report'),
(13,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',0,4,'2016-02-27 18:30:00','CLOSED','SALES Report'),
(14,12019,100000,'2016-03-07 09:13:50',133,284,'PRDLEAS00081215',1,4,'2016-02-27 18:30:00','CLOSED','SALES Report'),
(15,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',2,4,'2016-02-27 18:30:00','CLOSED','SALES Report'),
(16,12019,100000,'2016-03-07 09:13:50',8,18,'PRDLEAS00081215',101,4,'2016-02-27 18:30:00','CLOSED','SALES Report'),
(17,12019,100000,'2016-03-07 09:13:50',0,335,'PRDLEAS00081215',0,5,'2016-02-28 18:30:00','CLOSED','SALES Report'),
(18,12019,100000,'2016-03-07 09:13:50',284,354,'PRDLEAS00081215',1,5,'2016-02-28 18:30:00','CLOSED','SALES Report'),
(19,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',2,5,'2016-02-28 18:30:00','CLOSED','SALES Report'),
(20,12019,100000,'2016-03-07 09:13:50',18,18,'PRDLEAS00081215',101,5,'2016-02-28 18:30:00','CLOSED','SALES Report'),
(21,12019,100000,'2016-03-07 09:13:50',335,335,'PRDLEAS00081215',0,6,'2016-02-29 18:30:00','CLOSED','SALES Report'),
(22,12019,100000,'2016-03-07 09:13:50',354,418,'PRDLEAS00081215',1,6,'2016-02-29 18:30:00','CLOSED','SALES Report'),
(23,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',2,6,'2016-02-29 18:30:00','CLOSED','SALES Report'),
(24,12019,100000,'2016-03-07 09:13:50',18,18,'PRDLEAS00081215',101,6,'2016-02-29 18:30:00','CLOSED','SALES Report'),
(25,12019,100000,'2016-03-07 09:13:50',335,335,'PRDLEAS00081215',0,7,'2016-03-01 18:30:00','CLOSED','SALES Report'),
(26,12019,100000,'2016-03-07 09:13:50',418,484,'PRDLEAS00081215',1,7,'2016-03-01 18:30:00','CLOSED','SALES Report'),
(27,12019,100000,'2016-03-07 09:13:50',0,0,'PRDLEAS00081215',2,7,'2016-03-01 18:30:00','CLOSED','SALES Report'),
(28,12019,100000,'2016-03-07 09:13:50',18,18,'PRDLEAS00081215',101,7,'2016-03-01 18:30:00','CLOSED','SALES Report'),
(29,12019,100000,'2016-03-07 09:13:54',335,335,'PRDLEAS00081215',0,8,'2016-03-02 18:30:00','CLOSED','SALES Report'),
(30,12019,100000,'2016-03-07 09:13:59',484,586,'PRDLEAS00081215',1,8,'2016-03-02 18:30:00','CLOSED','SALES Report'),
(31,12019,100000,'2016-03-07 09:14:00',0,0,'PRDLEAS00081215',2,8,'2016-03-02 18:30:00','CLOSED','SALES Report'),
(32,12019,100000,'2016-03-07 09:14:00',18,18,'PRDLEAS00081215',101,8,'2016-03-02 18:30:00','CLOSED','SALES Report'),
(33,12019,100000,'2016-03-07 09:15:09',335,335,'PRDLEAS00081215',0,9,'2016-03-03 18:30:00','CLOSED','SALES Report'),
(34,12019,100000,'2016-03-07 09:15:14',586,704,'PRDLEAS00081215',1,9,'2016-03-03 18:30:00','CLOSED','SALES Report'),
(35,12019,100000,'2016-03-07 09:15:15',0,0,'PRDLEAS00081215',2,9,'2016-03-03 18:30:00','CLOSED','SALES Report'),
(36,12019,100000,'2016-03-07 09:15:15',18,18,'PRDLEAS00081215',101,9,'2016-03-03 18:30:00','CLOSED','SALES Report'),
(37,12019,100000,'2016-03-07 09:15:44',335,335,'PRDLEAS00081215',0,10,'2016-03-04 18:30:00','CLOSED','SALES Report'),
(38,12019,100000,'2016-03-07 09:15:51',704,905,'PRDLEAS00081215',1,10,'2016-03-04 18:30:00','CLOSED','SALES Report'),
(39,12019,100000,'2016-03-07 09:15:51',0,0,'PRDLEAS00081215',2,10,'2016-03-04 18:30:00','CLOSED','SALES Report'),
(40,12019,100000,'2016-03-07 09:15:52',18,18,'PRDLEAS00081215',101,10,'2016-03-04 18:30:00','CLOSED','SALES Report'),
(41,12019,100000,'2016-03-07 09:16:15',335,335,'PRDLEAS00081215',0,11,'2016-03-05 18:30:00','CLOSED','SALES Report'),
(42,12019,100000,'2016-03-07 09:16:22',905,1095,'PRDLEAS00081215',1,11,'2016-03-05 18:30:00','CLOSED','SALES Report'),
(43,12019,100000,'2016-03-07 09:16:22',0,0,'PRDLEAS00081215',2,11,'2016-03-05 18:30:00','CLOSED','SALES Report'),
(44,12019,100000,'2016-03-07 09:16:22',18,18,'PRDLEAS00081215',101,11,'2016-03-05 18:30:00','CLOSED','SALES Report');
