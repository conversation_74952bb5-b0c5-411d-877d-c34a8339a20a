ALTER TABLE ORDER_SETTLEMENT ADD COLUMN EXTRA_VOUCHERS DECIMAL(10,2) DEFAULT 0.00 NOT NULL;

ALTER TABLE SETTLEMENT_DETAIL MODIFY COLUMN SETTLEMENT_AMOUNT DECIMAL(10,2) DEFAULT NULL;
ALTER TABLE SETTLEMENT_DETAIL MODIFY COLUMN TOTAL_AMOUNT DECIMAL(10,2) NOT NULL;
ALTER TABLE SETTLEMENT_DETAIL MODIFY COLUMN UNSETTLED_AMOUNT DECIMAL(10,2) DEFAULT NULL;
ALTER TABLE SETTLEMENT_DETAIL MODIFY COLUMN CLOSING_AMOUNT DECIMAL(10,2) DEFAULT NULL;

ALTER TABLE PULL_DETAIL MODIFY COLUMN PULL_AMOUNT DECIMAL(10,2) NOT NULL;
ALTER TABLE PULL_DENOMINATION MODIFY COLUMN TOTAL_AMOUNT DECIMAL(10,2) NOT NULL;
ALTER TABLE SETTLEMENT_DENOMINATION MODIFY COLUMN TOTAL_AMOUNT DECIMAL(10,2) NOT NULL;

ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN GMV DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN DISCOUNT DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN NET_SALES_AMOUNT DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN VAT DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN MRP_VAT DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN NET_PRICE_VAT DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN S_CHARGE DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN SERVICE_TAX DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN SB_CESS DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN ROUND_OFF_AMOUNT DECIMAL(10,2) NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN TOTAL_AMOUNT DECIMAL(10,2) NOT NULL;

ALTER TABLE PULL_DETAIL DROP FOREIGN KEY PULL_DETAIL_ibfk_5;
ALTER TABLE PULL_DETAIL DROP COLUMN UNIT_CLOSURE_ENTRY;

ALTER TABLE UNIT_CLOSURE_ENTRY drop FOREIGN KEY UNIT_CLOSURE_ENTRY_ibfk_1;
ALTER TABLE UNIT_CLOSURE_ENTRY drop column CLOSURE_UNIT;
ALTER TABLE UNIT_CLOSURE_ENTRY drop FOREIGN KEY UNIT_CLOSURE_ENTRY_ibfk_2;
ALTER TABLE UNIT_CLOSURE_ENTRY drop column PAYMENT_MODE;
DROP TABLE IF EXISTS UNIT_CLOSURE_ENTRY;
