CREATE TABLE CREDIT_ACCOUNT_DETAIL
(
CREDIT_ACCOUNT_DE<PERSON><PERSON>_ID INTEGER NOT NULL AUTO_INCREMENT,
LEGAL_NAME VARCHAR(100) NOT NULL,
DISPLAY_NAME VARCHAR(100) NOT NULL,
ADDRESS VARCHAR(1000) NULL,
TAN_NUMBER VARCHAR(20) NULL,
PAN_NUMBER VARCHAR(20) NULL,
CERTIFICATE_OF_INCORPORATION VARCHAR(20) NULL,
BANK_DETAIL  VARCHAR(200) NULL,
CONTACT_PERSON VARCHAR(100) NOT NULL,
CONTACT_PERSON_NUMBER VARCHAR(10) NOT NULL,
CONTACT_PERSON_EMAIL VARCHAR(60) NULL,
ACCOUNT_CONTACT_PERSON VARCHAR(100) NOT NULL,
ACCOUNT_CONTACT_PERSON_NUMBER VARCHAR(10) NOT NULL,
ACCOUNT_CONTACT_PERSON_EMAIL VARCHAR(60) NULL,
CREDIT_DAYS INTEGER NOT NULL,
COMPANY_CONTACT VARCHAR(100) NOT NULL,
ACCOUNT_STATUS VARCHAR(15) NOT NULL DEFAULT 'ACTIVE',
PRIMARY KEY (CREDIT_ACCOUNT_DETAIL_ID)
)
