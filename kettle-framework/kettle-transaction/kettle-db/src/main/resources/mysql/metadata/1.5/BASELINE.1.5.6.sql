#COUPONS ALREADY DEPLOYED ON PRODUCTION


INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, CO<PERSON>ON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE)
	VALUES ( '1', 'CHAI54', '2016-02-16', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ( '1', 'CHAI55', '2016-02-16', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ( '1', 'CHAI56', '2016-02-16', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
	

	
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
 VALUES ('BILL', 'FLAT_BILL_STRATEGY', 'Rs.20 OFF on spent amount', 'Get flat Rs.20 OFF on your spent amount ', '2016-02-16', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '1', '1', '0', '20');
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
 VALUES ('BILL', 'FLAT_BILL_STRATEGY', 'Rs.30 OFF on spent amount', 'Get flat Rs.30 OFF on your spent amount ', '2016-02-16', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '1', '1', '0', '30');
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
 VALUES ('BILL', 'FLAT_BILL_STRATEGY', 'Rs.50 OFF on Rs.250 or more', 'Get flat Rs.50 OFF on bill amount of Rs.250 or more', '2016-02-16', '2016-03-31', 'ACTIVE', '250', 'Y', 'Y', '1', 'INTERNAL', '1', '1', '0', '50');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ('9', 'CHAI51', '2016-02-16', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ('10', 'CHAI52', '2016-02-16', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ('11', 'CHAI53', '2016-02-16', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');

	
	
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
	VALUES ('12', 'ITEM', 'PERCENTAGE_ITEM_STRATEGY', '1 Chai Free', 'Get a Desi Chai free on your order', '2016-02-17', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '2', '1', '0', '100');
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
	VALUES ('13', 'ITEM', 'PERCENTAGE_ITEM_STRATEGY', '50% OFF on Desi Chai Kettle', 'Get 50% OFF on Desi Chai Kettle on Delivery', '2016-02-17', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '2', '1', '0', '50');
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
	VALUES ('14', 'ITEM', 'FLAT_ITEM_STRATEGY', 'Sambossa @ Rs.20', 'Add a Sambossa for just Rs.20 to the order', '2016-02-17', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '2', '1', '0', '0');
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
	VALUES ('15', 'ITEM', 'PERCENTAGE_ITEM_STRATEGY', 'Free Sambossa', 'Get a Sambossa free on your order', '2016-02-17', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '2', '1', '0', '100');
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) 
	VALUES ('16', 'ITEM', 'PERCENTAGE_ITEM_STRATEGY', '50% OFF on Sambossa', 'Get 50% OFF on Sambossa in your order', '2016-02-17', '2016-03-31', 'ACTIVE', '0', 'Y', 'Y', '1', 'INTERNAL', '2', '1', '0', '50');

	
	
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE)
	VALUES ('12', 'FREECHAI01', '2016-02-17', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ('13', 'DESIKETTLE', '2016-02-17', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ('14', 'ADDSAM01', '2016-02-17', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'Y');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE)
	VALUES ('14', 'ADDSAM02', '2016-02-17', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'Y');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ('14', 'ADDSAM03', '2016-02-17', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'Y');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
	VALUES ('15', 'FREESAM01', '2016-02-17', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE)
	VALUES ('16', 'CODSAM01', '2016-02-17', '2016-03-31', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');


	
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'PRODUCT','10-Regular','java.lang.Integer','1','1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'FREECHAI01';

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'PRODUCT','10-Regular','java.lang.Integer','1','1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'DESIKETTLE';

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'PRODUCT', '651-Single', 'java.lang.Integer', '1', '1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'FREESAM01';
	
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'PRODUCT', '652-Single', 'java.lang.Integer', '1', '1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'FREESAM01';

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'PRODUCT', '651-Double', 'java.lang.Integer', '1', '1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'CODSAM01';
	
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'PRODUCT', '652-Double', 'java.lang.Integer', '1', '1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'CODSAM01';

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'ORDER_SOURCE', 'COD', 'java.lang.String', '1', '1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'CODSAM01';

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'ORDER_SOURCE', 'COD', 'java.lang.String', '1', '1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'DESIKETTLE';
	
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP)
SELECT COUPON_DETAIL_ID,'ORDER_SOURCE','CAFE','java.lang.String','1','1' 
	FROM COUPON_DETAIL_DATA WHERE COUPON_CODE in('FREECHAI01','FREESAM01','CHAI51','CHAI52','CHAI53','ADDSAM01','ADDSAM02','ADDSAM03');

	

INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE) 
	VALUES ('12', 'PRODUCT', '10');
INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE)
	VALUES ('13', 'PRODUCT', '10');
INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE) 
	VALUES ('15', 'PRODUCT', '651');
INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE)
	VALUES ('16', 'PRODUCT', '651');
INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE) 
	VALUES ('15', 'PRODUCT', '652');
INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE) 
	VALUES ('16', 'PRODUCT', '652');

	
	
	
#YES bank coupon code run on production on 17-02-2016
INSERT INTO OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE)
VALUES ('18', 'BILL', 'PERCENTAGE_BILL_STRATEGY', 'Yes Bank', 'Get 20% off on a spend of 500 & Above.', '2016-02-19', '2016-05-18', 'ACTIVE', '500', 'Y', 'Y', '1', 'INTERNAL', '1', '1', '0', '20');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) 
VALUES ('18', 'CHAIY16', '2016-02-19', '2016-05-18', 'Y', 'Y', '10000', 'ACTIVE', '0', 'N');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP) 
SELECT COUPON_DETAIL_ID,'UNIT_REGION','NCR','java.lang.String','1','1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE LIKE 'CHAIY16';

	
	

