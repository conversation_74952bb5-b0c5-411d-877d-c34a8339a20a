
#COUPONS CREATED FOR MARKETING
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('BILL', 'PERCENTAGE_BILL_STRATEGY', 'Marketing: New Born - Week 1', 'Get 10% OFF on any bill value', '2016-06-04', '2016-06-12', 'ACTIVE', '0', 'Y', 'Y', '0', 'CUSTOMER', '1', '1', '0', '10');
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('BILL', 'PERCENTAGE_BILL_STRATEGY', 'Marketing: Spike - Week 1', 'Get 15% OFF on bill value of 200 and above', '2016-06-04', '2016-06-12', 'ACTIVE', '200', 'Y', 'Y', '0', 'CUSTOMER', '1', '1', '0', '15');
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('BILL', 'PERCENTAGE_BILL_STRATEGY', 'Marketing: Regulars/Toddlers - Week 1', 'Get 15% OFF on bill value of 250 and above', '2016-06-04', '2016-06-12', 'ACTIVE', '250', 'Y', 'Y', '0', 'CUSTOMER', '1', '1', '0', '15');
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('BILL', 'FLAT_BILL_STRATEGY', 'Marketing: Loyals /Fans - Week 1', 'Get Rs 50 OFF on bill value of 300 and above', '2016-06-04', '2016-06-12', 'ACTIVE', '300', 'Y', 'Y', '0', 'CUSTOMER', '1', '1', '0', '50');
INSERT INTO OFFER_DETAIL_DATA (OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('BILL', 'PERCENTAGE_ITEM_STRATEGY', 'Marketing: Casual - Week 1', 'Buy One Get One Desi Chai (Regular) Free', '2016-06-04', '2016-06-12', 'ACTIVE', '0', 'Y', 'Y', '0', 'CUSTOMER', '1', '1', '0', '100');



INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('71', 'CHLNB16', '2016-06-04', '2016-06-12', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('72', 'CHLSP16', '2016-06-04', '2016-06-12', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('73', 'CHLRT16', '2016-06-04', '2016-06-12', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('74', 'CHLFL16', '2016-06-04', '2016-06-12', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('75', 'CHLLC16', '2016-06-04', '2016-06-12', 'Y', 'Y', '2000', 'ACTIVE', '0', 'Y');


INSERT INTO OFFER_METADATA (OFFER_ID, MAPPING_TYPE, MAPPING_VALUE, STATUS) VALUES ('75', 'PRODUCT', '10', 'ACTIVE');


INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP, DIMENSION, STATUS) VALUES ('28386', 'PRODUCT', '10', 'java.lang.Integer', '1', '1', 'Regular', 'ACTIVE');

INSERT INTO KETTLE_MASTER.OFFER_DETAIL_DATA (OFFER_DETAIL_ID, OFFER_CATEGORY, OFFER_TYPE, OFFER_TEXT, OFFER_DESCRIPTION, START_DATE, END_DATE, OFFER_STATUS, MIN_VALUE, VALIDATE_CUSTOMER, INCLUDE_TAXES, PRIORITY, OFFER_SCOPE, MIN_ITEM_COUNT, QUANTITY_LIMIT, LOYALTY_LIMIT, OFFER_VALUE) VALUES ('76', 'BILL', 'PERCENTAGE_BILL_STRATEGY', '10% OFF on a Bill Value', '10% OFF on a Bill Value', '2016-06-11', '2016-06-19', 'ACTIVE', '0', 'Y', 'Y', '1', 'CUSTOMER', '1', '1', '0', '10');

INSERT INTO KETTLE_MASTER.COUPON_DETAIL_DATA (COUPON_DETAIL_ID, OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('28387', '76', 'CHLFA16', '2016-06-11', '2016-06-19', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO KETTLE_MASTER.COUPON_DETAIL_DATA (COUPON_DETAIL_ID, OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('28388', '76', 'CHLSS16', '2016-06-11', '2016-06-19', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO KETTLE_MASTER.COUPON_DETAIL_DATA (COUPON_DETAIL_ID, OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('28389', '76', 'CHLCA16', '2016-06-11', '2016-06-19', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO KETTLE_MASTER.COUPON_DETAIL_DATA (COUPON_DETAIL_ID, OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('28390', '76', 'CHLRE16', '2016-06-11', '2016-06-19', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO KETTLE_MASTER.COUPON_DETAIL_DATA (COUPON_DETAIL_ID, OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('28391', '76', 'CHLTO16', '2016-06-11', '2016-06-19', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');
INSERT INTO KETTLE_MASTER.COUPON_DETAIL_DATA (COUPON_DETAIL_ID, OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE) VALUES ('28392', '76', 'CHLLO16', '2016-06-11', '2016-06-19', 'Y', 'Y', '2000', 'ACTIVE', '0', 'N');


#Inventory tracked false for Garden Fresh
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET IS_INVENTORY_TRACKED='N' WHERE PRODUCT_ID='868';


#Update Call Center Number for Units
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='100';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='101';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='102';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='103';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='104';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='105';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='106';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='107';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='108';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='109';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='110';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='111';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='112';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='113';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='114';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1101';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1201';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1202';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1203';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1204';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1205';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1206';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1207';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1208';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='1209';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_2=NULL WHERE ADDRESS_ID='10000';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12077';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12031';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12041';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12037';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12050';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12061';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12032';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12056';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12104';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12055';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12131';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424', CONTACT_NUM_2=NULL WHERE ADDRESS_ID='12030';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12150';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12054';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12053';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12040';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12177';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12062';
UPDATE KETTLE_MASTER.ADDRESS_INFO SET CONTACT_NUM_1='18001202424' WHERE ADDRESS_ID='12073';
