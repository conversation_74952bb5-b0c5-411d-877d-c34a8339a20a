CREATE TABLE `KETTLE_DEV`.`UNIT_EMPLOYEE_HEALTH_DATA` (
  `UNIT_EMPLOYEE_ID` INT NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NULL,
  `TEMP_THRESHOLD` VARCHAR(45) NULL,
  `UPDATED_BY` INT(11) NULL,
  `UPDATED_AT` TIMESTAMP NULL,
  `STATUS` VARCHAR(45) NULL,
  PRIMARY KEY (`UNIT_EMPLOYEE_ID`));


CREATE TABLE `KETTLE_DEV`.`EMPLOYEE_HEALTH_DATA` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `NAME` VARCHAR(150) NULL,
  `TEMP` VARCHAR(45) NULL,
  `UNIT_EMPLOYEE_ID` INT(11) NULL,
  PRIMARY KEY (`ID`),
  INDEX `UNIT_EMP_ID_idx` (`UNIT_EMPLOYEE_ID` ASC),
  CONSTRAINT `UNIT_EMP_ID`
    FOREI<PERSON>N KEY (`UNIT_EMPLOYEE_ID`)
    REFERENCES `KETTLE_DEV`.`UNIT_EMPLOYEE_HEALTH_DATA` (`UNIT_EMPLOYEE_ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
