CREATE TABLE KETTLE_DEV.PNL_ADJUSTMENT_DETAIL (
    ADJUSTMENT_ID INT NOT NULL PRIMARY KEY,
    PNL_HEADER_NAME VARCHAR(50) NOT NULL,
    PNL_HEADER_DETAIL VARCHAR(50) NOT NULL,
    PNL_HEADER_COLUMN_NAME VARCHAR(50) NOT NULL,
    PNL_HEADER_TYPE VARCHAR(50) NOT NULL,
    ADJUSTMENT_TYPE VARCHAR(50) NOT NULL,
    ADJUSTMENT_VALUE DECIMAL(12 , 6 ) NOT NULL,
    UNIT_ID INT NOT NULL,
    STATUS VARCHAR(20) NOT NULL,
    MONTH INT NOT NULL,
    YEAR INT NOT NULL,
    CREATED_BY INT NOT NULL,
    CREATION_TIME TIMESTAMP NOT NULL,
    CREATE_COMMENT VARCHAR(50) NOT NULL,
    CREATE_COMMENT_TEXT VARCHAR(500) NOT NULL,
    REJECTED_BY INT NULL,
    REJECTION_TIME TIMESTAMP NULL,
    REJECT_COMMENT VARCHAR(50) NULL,
    REJECT_COMMENT_TEXT VARCHAR(500) NULL,
    APPROVED_BY INT NULL,
    APPROVAL_TIME TIMESTAMP NULL,
    APPROVED_COMMENT VARCHAR(50) NULL,
    APPROVED_COMMENT_TEXT VARCHAR(500) NULL,
    CANCELLED_BY INT NULL,
    CANCELLATION_TIME TIMESTAMP NULL,
    CANCELLATION_COMMENT VARCHAR(50) NULL,
    CANCELLATION_COMMENT_TEXT VARCHAR(500) NULL,
    IS_APPLIED VARCHAR(10) NULL
);

ALTER TABLE `KETTLE_DEV`.`PNL_ADJUSTMENT_DETAIL`
CHANGE COLUMN `ADJUSTMENT_ID` `ADJUSTMENT_ID` INT(11) NOT NULL AUTO_INCREMENT ;

ALTER TABLE `KETTLE_DEV`.`PNL_ADJUSTMENT_DETAIL`
CHANGE COLUMN `ADJUSTMENT_VALUE` `ADJUSTMENT_VALUE` DECIMAL(20,6) NOT NULL ;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN REVENUE_ADJUSTMENT DECIMAL(10,2) NULL,
ADD COLUMN COST_ADJUSTMENT DECIMAL(10,2) NULL;



ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_LHI DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_LHI_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_IT DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_IT_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_MAINTENANCE DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_MAINTENANCE_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_OFFICE_EQUIPMENT DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_OFFICE_EQUIPMENT_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_CHAI_MONK DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_CHAI_MONK_TAX DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN CONSUMABLE_LHI DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_LHI_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_IT DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_IT_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_MAINTENANCE DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_MAINTENANCE_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_OFFICE_EQUIPMENT DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_OFFICE_EQUIPMENT_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_CHAI_MONK DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_CHAI_MONK_TAX DECIMAL(10,2) NULL;

CREATE TABLE KETTLE_DEV.UNIT_EXPENDITURE_AGGREGATE_DETAIL
(
    UNIT_AGGREGATE_DETAIL_ID INT PRIMARY KEY AUTO_INCREMENT,
    AGGREGATE_STATUS VARCHAR(20),
    UNIT_EXPENDITURE_ID INT,
    UNIT_ID INT NOT NULL,
    UNIT_NAME VARCHAR(100),
    YEAR INT,
    MONTH INT,
    DAY INT,
    BUSINESS_DATE DATE,
    SUMO_DAY_CLOSE_EVENT_ID INT,
    CALCULATION_TYPE VARCHAR(60),
    TOTAL_TICKETS INTEGER NULL,
    TOTAL_SALES DECIMAL(10,2) NULL,
    TOTAL_APC DECIMAL(10,2) NULL,
    TOTAL_GMV DECIMAL(10,2) NULL,
    TOTAL_DISCOUNT DECIMAL(10,2) NULL,
    TOTAL_DISCOUNT_LOYAL_TEA DECIMAL(10,2) NULL,
    TOTAL_DISCOUNT_MARKETING DECIMAL(10,2) NULL,
    TOTAL_DISCOUNT_OPS DECIMAL(10,2) NULL,
    TOTAL_DISCOUNT_BD DECIMAL(10,2) NULL,
    TOTAL_DISCOUNT_EMPLOYEE_FICO  DECIMAL(10,2) NULL,
    DINE_IN_SALES  DECIMAL(10,2) NULL,
    DINE_IN_APC  DECIMAL(10,2) NULL,
    DINE_IN_DISCOUNT  DECIMAL(10,2) NULL,
    DELIVERY_SALES  DECIMAL(10,2) NULL,
    DELIVERY_APC  DECIMAL(10,2) NULL,
    DELIVERY_DISCOUNT DECIMAL(10,2) NULL,
    DIRECT_VARIABLE_COGS DECIMAL(10,2) NULL,
    COGS DECIMAL(10,2) NULL,
    WASTAGE_EXPIRY DECIMAL(10,2) NULL,
    STOCK_VARIANCE DECIMAL(10,2) NULL,
    DIRECT_VARIABLE_OTHERS DECIMAL(10,2) NULL,
    INDIRECT_VARIABLE_OTHERS DECIMAL(10,2) NULL,
    DIRECT_FIXED_COST DECIMAL(10,2) NULL,
    EMPLOYEE_MEAL DECIMAL(10,2) NULL,
    MANPOWER_FIXED DECIMAL(10,2) NULL,
    MANPOWER_INCENTIVE DECIMAL(10,2) NULL,
    FACILITIES_PROPERTY DECIMAL(10,2) NULL,
    EMPLOYEE_MEAL_SALES DECIMAL(10,2) NULL,
    INDIRECT_FIXED_COST DECIMAL(10,2) NULL,
    CONSUMABLE DECIMAL(10,2) NULL,
    FACILITIES_FIXED DECIMAL(10,2) NULL,
    SUPPORT_COMM_WH DECIMAL(10,2) NULL,
    SUPPORT_OPS_MANAGEMENT DECIMAL(10,2) NULL,
    LOGISTICS DECIMAL(10,2) NULL,
    FACILITIES_VARIABLE DECIMAL(10,2) NULL,
    COMMISSION_CARD_WALLETS DECIMAL(10,2) NULL,
    COMMISSION_CP DECIMAL(10,2) NULL,
    DELIVERY_CHARGES DECIMAL(10,2) NULL,
    ANY_OTHER_VARIABLE DECIMAL(10,2) NULL,
    MAINTENANCE DECIMAL(10,2) NULL,
    MARKETING_LS DECIMAL(10,2) NULL,
    GROWTH_PNL DECIMAL(10,2) NULL,
    ALLOCATION DECIMAL(10,2) NULL,
    MARKETING_CORP DECIMAL(10,2) NULL,
    SUPPORT_HQ DECIMAL(10,2) NULL,
    FICO_PAYOUTS DECIMAL(10,2) NULL,
    TECHNOLOGY DECIMAL(10,2) NULL,
    GROWTH_CAPEX DECIMAL(10,2) NULL,
    CAPEX_FIXED_ASSETS DECIMAL(10,2) NULL,
    INDIRECT_INCOME DECIMAL(10,2) NULL,
    AMORTISATION_DEPRECIATION DECIMAL(10,2) NULL,
    GROWTH_SD DECIMAL(10,2) NULL,
    EMPLOYEE_DISCOUNT DECIMAL(10,2) NULL,
    EMP_MEAL_APC DECIMAL(10,2) NULL
);


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
    ADD COLUMN MAINTENANCE_PEST_CONTROL_CAFE DECIMAL(20,2),
    ADD COLUMN CONVEYANCE_ODC DECIMAL(20,2);



ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_KITCHEN_EQUIPMENT DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_KITCHEN_EQUIPMENT_TAX DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN CONSUMABLE_KITCHEN_EQUIPMENT DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_KITCHEN_EQUIPMENT_TAX DECIMAL(10,2) NULL;


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN FIXED_ASSETS_VEHICLE DECIMAL(10,2) NULL,
ADD COLUMN FIXED_ASSETS_VEHICLE_TAX DECIMAL(10,2) NULL,
ADD COLUMN FIXED_ASSETS_VEHICLE_HQ DECIMAL(10,2) NULL,
ADD COLUMN FIXED_ASSETS_VEHICLE_HQ_TAX DECIMAL(10,2) NULL,
ADD COLUMN LIABILITY_NO_LONGER_REQUIRED_WRITTEN_BACK DECIMAL(10,2) NULL,
ADD COLUMN INTEREST_ON_TERM_LOAN DECIMAL(10,2) NULL,
ADD COLUMN AMORTIZATION_OF_INTANGIBLE_ASSETS DECIMAL(10,2) NULL;


ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN FIXED_ASSETS_VEHICLE DECIMAL(10,2) NULL,
ADD COLUMN LIABILITY_NO_LONGER_REQUIRED_WRITTEN_BACK DECIMAL(10,2) NULL,
ADD COLUMN INTEREST_ON_TERM_LOAN DECIMAL(10,2) NULL,
ADD COLUMN AMORTIZATION_OF_INTANGIBLE_ASSETS DECIMAL(10,2) NULL;


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
    ADD COLUMN DEPRECIATION_OF_FURNITURE_FIXTURE DECIMAL(20,2),
ADD COLUMN DEPRECIATION_OF_KITCHEN_EQUIPMENTS DECIMAL(20,2),
ADD COLUMN DEPRECIATION_OF_EQUIPMENTS DECIMAL(20,2),
ADD COLUMN DEPRECIATION_OF_IT DECIMAL(20,2),
ADD COLUMN DEPRECIATION_OF_VEHICLE DECIMAL(20,2),
ADD COLUMN DEPRECIATION_OF_OTHERS DECIMAL(20,2),
ADD COLUMN DEPRECIATION_OF_OFFICE_EQUIPMENTS DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_AGGREGATE_DETAIL
ADD COLUMN GROSS_PROFIT DECIMAL(20,2) NULL,
ADD COLUMN GROSS_PROFIT_PERCENTAGE DECIMAL(20,2) NULL,
ADD COLUMN CONTRIBUTION_PERCENTAGE DECIMAL(20,2) NULL,
ADD COLUMN OPERATING_PROFIT DECIMAL(20,2) NULL,
ADD COLUMN OPERATING_PROFIT_PERCENTAGE DECIMAL(20,2) NULL,
ADD COLUMN NET_PROFIT DECIMAL(20,2) NULL,
ADD COLUMN NET_PROFIT_PERCENTAGE DECIMAL(20,2) NULL,
ADD COLUMN CASH_BURN DECIMAL(20,2) NULL;