
ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
    CHANGE EMPLOYEE_MEAL_GMV EMPLOYEE_MEAL_SALES_GMV DECIMAL(20,2),
    CHAN<PERSON> FIXED_ASSETS CONSUMABLE_FIXED_ASSETS DECIMAL(20,2),
    CHANGE SALES_INCENTIVE PERFORMANCE_INCENTIVE DECIMAL(20,2),
    CHANGE VEHICLE_REGULAR_MAINTENANCE VEHICLE_REGULAR_MAINTENANCE_CAFE DECIMAL(20,2),
    CHANGE PARKING_CHARGES PARKING_CHARGES_CAFE DECIMAL(20,2),
    <PERSON>AN<PERSON> CANCELLATION_CHARGES CANCELLATION_CHARGES_CHANNEL_PARTNERS DECIMAL(20,2),
    CHAN<PERSON> COMMISSION_CHANGE COMMISSION_CHANGE_CAFE DECIMAL(20,2),
    <PERSON><PERSON><PERSON> OPENING_LICENSES_FEES PRE_OPENING_LICENSES_FEES DECIMAL(20,2),
    <PERSON>AN<PERSON> BUILDING_MAINTENANCE BUILDING_MAINTENANCE_CAFE DECIMAL(20,2),
    CHANGE EQUIPMENT_MAINTENANCE EQUIPMENT_MAINTENANCE_CAFE DECIMAL(20,2),
    CHANGE TECHNOLOGY_VARIABLE TECHNOLOGY_POS DECIMAL(20,2),
    CHANGE FIXED_ASSETS_CAPEX FIXED_ASSETS_HQ DECIMAL(20,2),
    CHANGE FIXED_ASSETS_CAPEX_TAX FIXED_ASSETS_HQ_TAX DECIMAL(20,2),
    CHANGE CAR_LEASE CAR_LEASE_SR DECIMAL(20,2),
    CHANGE DRIVER_SALARY DRIVER_SALARY_SR DECIMAL(20,2),
    CHANGE GRATUITY GRATUITY_EXPENSE DECIMAL(20,2),
    CHANGE FIXED_ASSETS_EQUIPMENT FIXED_ASSETS_EQUIPMENT_CAFE DECIMAL(20,2),
    CHANGE FIXED_ASSET_FURNITURE FIXED_ASSET_FURNITURE_CAFE DECIMAL(20,2),
    CHANGE FIXED_ASSETS_IT FIXED_ASSET_IT_CAFE DECIMAL(20,2),
    CHANGE FIXED_ASSETS_KITCHEN_EQUIPMENT FIXED_ASSETS_KITCHEN_EQUIPMENT_CAFE DECIMAL(20,2),
    CHANGE FIXED_ASSETS_OFFICE_EQUIPMENT FIXED_ASSETS_OFFICE_EQUIPMENT_CAFE DECIMAL(20,2),
    CHANGE INTREST_ON_FDR INTEREST_ON_FDR DECIMAL(20,2),
    CHANGE PROFIT_SALE_MUTUTAL_FUND PROFIT_OR_LOSS_SALE_MUTUTAL_FUND DECIMAL(20,2),
    CHANGE INTREST_INCOME_TAX_REFUND INTEREST_INCOME_TAX_REFUND DECIMAL(20,2),
    CHANGE SCRAPE SCRAPE_CHARGES DECIMAL(20,2),
    CHANGE MARKETING_NPI MARKETING_NPI_CAFE DECIMAL(20,2);

ALTER TABLE KETTLE_MASTER_DEV.REF_LOOKUP_TYPE
    MODIFY RTL_CODE VARCHAR(100),
    MODIFY RTL_NAME VARCHAR(100),
    MODIFY RTL_GROUP VARCHAR(100);

INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP_TYPE(RTL_GROUP,RTL_CODE,RTL_NAME,STATUS) VALUES("ADJUSTMENT_COMMENT","CREATE_ADJUSTMENT_REASONS","CREATE ADJUSTMENT","ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP_TYPE(RTL_GROUP,RTL_CODE,RTL_NAME,STATUS) VALUES("ADJUSTMENT_COMMENT","APPROVE_ADJUSTMENT_REASONS","APPROVE ADJUSTMENT","ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP_TYPE(RTL_GROUP,RTL_CODE,RTL_NAME,STATUS) VALUES("ADJUSTMENT_COMMENT","REJECT_ADJUSTMENT_REASONS","REJECT ADJUSTMENT","ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP_TYPE(RTL_GROUP,RTL_CODE,RTL_NAME,STATUS) VALUES("ADJUSTMENT_COMMENT","CANCEL_ADJUSTMENT_REASONS","CANCEL ADJUSTMENT","ACTIVE");

INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='CREATE_ADJUSTMENT_REASONS' ),"Wrong Stock entry","Wrong Stock entry",'CR1',"ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='CREATE_ADJUSTMENT_REASONS' ),"Wrong Wastage entry","Wrong Wastage entry",'CR2',"ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='CREATE_ADJUSTMENT_REASONS' ),"Order Punching error","Order Punching error",'CR3',"ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='CREATE_ADJUSTMENT_REASONS' ),"Recipe issue","Recipe issue",'CR4',"ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='CREATE_ADJUSTMENT_REASONS' ),"Wrong Petty Cash entry","Wrong Petty Cash entry",'CR5',"ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='CREATE_ADJUSTMENT_REASONS' ),"Day close issue","Day close issue",'CR6',"ACTIVE");



INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='APPROVE_ADJUSTMENT_REASONS' ),"Others","Others",'AR1',"ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='REJECT_ADJUSTMENT_REASONS' ),"Others","Others",'RR1',"ACTIVE");
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP(RTL_ID,RL_CODE,RL_NAME,RL_SHORT_CODE,RL_STATUS)
VALUES((SELECT R.RTL_ID FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE R WHERE R.RTL_CODE='CANCEL_ADJUSTMENT_REASONS' ),"Others","Others",'CAR1',"ACTIVE");


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_AGGREGATE_DETAIL ADD COLUMN REVENUE DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_AGGREGATE_DETAIL ADD COLUMN CONTRIBUTION DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN SECURITY_DEPOSIT_PROPERTY DECIMAL(20,2),
ADD COLUMN SECURITY_DEPOSIT_MVAT DECIMAL(20,2),
ADD COLUMN SECURITY_DEPOSIT_ELECTRICITY DECIMAL(20,2),
ADD COLUMN MARKETING_DISCOUNT_ECOM DECIMAL(20,2),
ADD COLUMN SHIPPING_CHARGES DECIMAL(20,2),
ADD COLUMN OTHER_TRANSACTION_CHARGES DECIMAL(20,2),
ADD COLUMN DISCOUNT_DEALER_MARGIN DECIMAL(20,2),
ADD COLUMN PERFORMANCE_MARKETING_SERVICE DECIMAL(20,2),
ADD COLUMN SHARE_STAMPING_CHARGES DECIMAL(20,2),
ADD COLUMN INSURANCE_MARINE DECIMAL(20,2),
ADD COLUMN COMISSION_CHANNEL_PARTNER_FIXED DECIMAL(20,2),
ADD COLUMN OTHER_CHARGES_ECOM DECIMAL(20,2),
ADD COLUMN COGS_TRADING_GOODS DECIMAL(20,2),
ADD COLUMN ROYALTY_FEES DECIMAL(20,2),
ADD COLUMN FREIGHT_CHARGES DECIMAL(20,2);