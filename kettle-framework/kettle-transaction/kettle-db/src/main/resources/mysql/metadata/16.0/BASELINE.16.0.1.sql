DROP TABLE IF EXISTS KETTLE_JOBS_DUMP.AGGREGATED_RESULT_DEFINITION;

USE KETTLE_JOBS_DUMP;

CREATE TABLE `AGGREGATED_RESULT_DEFINITION` (
    `AGGREGATED_RESULT_DEFINITION_ID` int(11) NOT NULL AUTO_INCREMENT,
    `BUSINESS_DATE` varchar(40) DEFAULT NULL,
    `UNIT_ID` int(11) DEFAULT NULL,
    `UNIT_NAME` varchar(255) DEFAULT NULL,
    `REFERENCE_NUMBER` varchar(255) DEFAULT NULL,
    `STATE` varchar(100) DEFAULT NULL,
    `STATE_CODE` varchar(10) DEFAULT NULL,
    `PARTICULARS` varchar(255) DEFAULT NULL,
    `TOTAL_SALES` decimal(16,6) DEFAULT NULL,
    `DEBIT_CREDIT` varchar(10) DEFAULT NULL,
    `ACCOUNT_CODE` varchar(255) DEFAULT NULL,
    `KEY_TYPE` varchar(40) DEFAULT NULL,
    `<PERSON>EY_ID` varchar(40) DEFAULT NULL,
    `JOB_ID` int(11) DEFAULT NULL,
    `EXECUTION_ID` int(11) DEFAULT NULL,
    `EXECUTION_TIME` timestamp NULL DEFAULT NULL,
    `TYPE_OF_DATA` varchar(40) DEFAULT NULL,
    `STATUS` varchar(10) DEFAULT NULL,
    `STEP_ID` int(11) DEFAULT NULL,
    PRIMARY KEY (`AGGREGATED_RESULT_DEFINITION_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1298 DEFAULT CHARSET=utf8;


INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("channel_partner_collection_job","channel_partner_collection_job","ADHOC","channel_partner_collection_job.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("credit_payment","credit_payment","ADHOC","credit_payment.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("gift_card_discount_job","gift_card_discount_job","ADHOC","gift_card_discount_job.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("gift_card_payments_job","gift_card_payments_job","ADHOC","gift_card_payments_job.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("gift_card_sales_job","gift_card_sales_job","ADHOC","gift_card_sales_job.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("tax_data_job","tax_data_job","ADHOC","tax_data_job.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("sales_data_job","sales_data","ADHOC","sales_data.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("round_off_job","round_off_job","ADHOC","round_off_job.kjb","ACTIVE");

INSERT INTO KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_NAME,JOB_DESCRIPTION,JOB_TYPE,JOB_FILE_NAME,JOB_STATUS)
VALUES("report_final_job","report_final_job","ADHOC","report_final_job.kjb","ACTIVE");