ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN GENDER VARCHAR(10) NULL;
ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN DATE_OF_BIRTH TIMESTAMP NULL;
ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN ANNIVERSARY  TIMESTAMP NULL;


CREATE TABLE `KETTLE_DEV`.`CUSTOMER_ADDITIONAL_DETAIL` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `CUSTOMER_ID` INT(11) NULL,
  `CAMPAIGN_NAME` VARCHAR(250) NOT NULL,
  `SMS_SENT` varchar(11) NOT NULL,
  `BUSINESS_DATE` DATE NOT NULL,
  PRIMARY KEY (`ID`));


CREATE TABLE KETTLE_DEV.CRM_APP_SCREEN_DETAIL
(
    KEY_ID       INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    CITY_TYPE    VARCHAR(40),
    <PERSON>ITY         VARCHAR(40),
    SCREEN_TYPE  VARCHAR(40),
    PATH         VARCHAR(255),
    CONTENT_TYPE VARCHAR(40),
    STATUS       VARCHAR(40),
    UPDATED_BY   INT,
    UPDATED_ON   DATE
);