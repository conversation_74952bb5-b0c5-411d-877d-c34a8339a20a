ALTER TABLE KETTLE_DEV.UNIT_PRODUCT_STOCK_DATA
    MODIFY CAFE_OPENING DATETIME,
    MODIFY CAFE_CLOSING DATETIME;

ALTER TABLE KETTLE_DEV.UNIT_PRODUCT_STOCK_EVENT_AGGREGATE
    MODIFY CAFE_OPENING DATETIME,
    MODIFY CAFE_CLOSING DATETIME;


ALTER TABLE KETTLE_DEV.MENU_PRODUCT_COGS_DRILLDOWN
ADD COLUMN TAXABLE_QUANTITY DECIMAL(16,6) NOT NULL,
ADD COLUMN TAXABLE_COST DECIMAL(16,6) NOT NULL,
ADD COLUMN TAXABLE_PERCENTAGE DECIMAL(16,6) NOT NULL,
ADD COLUMN TAXABLE_AMOUNT DECIMAL(16,6) NOT NULL;


ALTER TABLE KETTLE_DEV.MENU_PRODUCT_COST_DETAIL
ADD COLUMN UNIT_ID INT NOT NULL,
ADD COLUMN BRAND_ID INT NOT NULL,
ADD COLUMN ORDER_TYPE VARCHAR(20) NOT NULL,
ADD COLUMN TAXABLE_QUANTITY DECIMAL(16,6) NOT NULL,
ADD COLUMN TAXABLE_COST DECIMAL(16,6) NOT NULL,
ADD COLUMN TOTAL_AMOUNT DECIMAL(16,6) NOT NULL,
ADD COLUMN TAXABLE_AMOUNT DECIMAL(16,6) NOT NULL;