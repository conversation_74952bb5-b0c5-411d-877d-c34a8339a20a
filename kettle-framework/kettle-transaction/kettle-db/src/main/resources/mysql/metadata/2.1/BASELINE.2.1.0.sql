DROP PROCEDURE IF EXISTS UNIT_WISE_CUSTOMER_SCORE;
delimiter $$
CREATE PROCEDURE UNIT_WISE_CUSTOMER_SCORE
()
proc_label : BEGIN
DROP TABLE IF EXISTS KETTLE.TEMP_ORDER_DETAIL;
 
DROP TABLE IF EXISTS KETTLE.TEMP_EXCLUDE_ORDER_DETAIL;
 
CREATE TABLE KETTLE.TEMP_ORDER_DETAIL (
    ORDER_ID INTEGER NOT NULL,
    CUSTOMER_ID INTEGER NOT NULL,
    UNIT_ID INTEGER NOT NULL,
    ORDER_STATUS VARCHAR(30) NOT NULL,
    PRIMARY KEY (ORDER_ID)
);

CREATE TABLE KETTLE.TEMP_EXCLUDE_ORDER_DETAIL (
    ORDER_ID INTEGER NOT NULL,
    PRIMARY KEY (ORDER_ID)
);

INSERT INTO KETTLE.TEMP_ORDER_DETAIL
select ORDER_ID, CUSTOMER_ID, UNIT_ID, ORDER_STATUS from KETTLE.OR<PERSON>R_DETAIL where
ORDER_STATUS <> 'CANCELLED' AND <PERSON><PERSON><PERSON>L_PARTNER_ID <> 6 AND CUSTOMER_ID <> 9463
AND BILLING_SERVER_TIME BETWEEN DATE_SUB(CURDATE(),INTERVAL 31 DAY) AND CURDATE()
            AND CUSTOMER_ID > 5;
           
INSERT INTO  KETTLE.TEMP_EXCLUDE_ORDER_DETAIL
            SELECT
        MAX(od.ORDER_ID) ORDER_ID
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.CUSTOMER_ID > 5 AND CUSTOMER_ID <> 9463
            AND od.CHANNEL_PARTNER_ID <> 6
            AND BILLING_SERVER_TIME BETWEEN DATE_SUB(CURDATE(),INTERVAL 31 DAY) AND CURDATE()
    GROUP BY od.CUSTOMER_ID;
   
SELECT
    od.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    COUNT(*) TOTAL_ORDERS_WITH_CUSTOMERS,
    SUM(CASE
        WHEN a.ORDER_ID IS NULL THEN 1
        ELSE 0
    END) SCORE
FROM
    KETTLE.TEMP_ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    KETTLE.TEMP_EXCLUDE_ORDER_DETAIL a ON a.ORDER_ID = od.ORDER_ID
GROUP BY od.UNIT_ID , ud.UNIT_NAME , ud.UNIT_CATEGORY;
END$$

delimiter ;


ALTER TABLE UNIT_DETAIL
ADD COLUMN UNIT_MANAGER INTEGER NULL;

ALTER TABLE EMPLOYEE_DETAIL
ADD COLUMN EMP_EMAIL VARCHAR(100) NULL