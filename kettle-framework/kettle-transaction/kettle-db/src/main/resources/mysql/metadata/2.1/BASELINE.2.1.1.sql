ALTER TABLE LOYALTY_SCORE
ADD COLUMN LAST_ORDER_TIME TIMESTAMP NULL;

ALTER TABLE LOYALTY_SCORE
ADD COLUMN LAST_ORDER_ID INTEGER NULL;


ALTER TABLE LOYALTY_SCORE
ADD COLUMN ORDER_COUNT INTEGER NULL;

ALTER TABLE LOYALTY_SCORE
ADD COLUMN AVAILED_SIGNUP_OFFER VARCHAR(1) NOT NULL DEFAULT 'N';

UPDATE LOYALTY_SCORE ls,
    (SELECT 
        CUSTOMER_ID,
            MAX(ORDER_ID) ORDER_ID,
            MAX(BILLING_SERVER_TIME) BILLING_SERVER_TIME,
            COUNT(*) ORDER_COUNT
    FROM
        ORDER_DETAIL
    WHERE
        ORDER_STATUS <> 'CANCELLED'
    GROUP BY CUSTOMER_ID) a 
SET 
    ls.LAST_ORDER_TIME = a.BILLING_SERVER_TIME,
    ls.LAST_ORDER_ID = a.ORDER_ID,
    ls.ORDER_COUNT = a.ORDER_COUNT;
    
INSERT INTO CUSTOMER_OFFER_DETAIL(CUSTOMER_ID, OFFER_CODE, AVAIL_TIME, ORDER_ID, IS_AVAILED)
select CUSTOMER_ID, 'LOYALTEA',  min(BILLING_SERVER_TIME),min(ORDER_ID),'Y' from KETTLE_DEV.ORDER_DETAIL
group by CUSTOMER_ID having count(*) >= 1;

update LOYALTY_SCORE ls,(select CUSTOMER_ID, count(*) from KETTLE_DEV.ORDER_DETAIL
group by CUSTOMER_ID having count(*) >= 1)a  set ls.AVAILED_SIGNUP_OFFER = 'Y'
where ls.CUSTOMER_ID = a.CUSTOMER_ID;