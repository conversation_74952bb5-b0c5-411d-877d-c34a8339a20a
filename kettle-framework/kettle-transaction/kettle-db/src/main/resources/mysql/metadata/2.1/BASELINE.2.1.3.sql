
ALTER TABLE UNIT_EXPENSE_DETAIL
ADD COLUMN CONSUMABLES_STATIONARY DECIMAL(10,2),
ADD COLUMN CONSUMABLES_UNIFORM DECIMAL(10,2),
ADD COLUMN CONSUMABLES_EQUIPMENT DECIMAL(10,2),
ADD COLUMN CONSUMABLES_CUTLERY DECIMAL(10,2),
ADD COLUMN MSP DECIMAL(10,2),
ADD COLUMN SYSTEM_RENT DECIMAL(10,2),
ADD COLUMN INSURANCE DECIMAL(10,2);

UPDATE `KETTLE_DEV`.`COMPLIMENTARY_CODE` SET `STATUS`='IN_ACTIVE' WHERE `COMP_ID`='2104';
UPDATE `KETTLE_DEV`.`COMPLIMENTARY_CODE` SET `STATUS`='IN_ACTIVE' WHERE `COMP_ID`='2101';

CREATE TABLE KETTLE_DEV.EMPLOYEE_MEAL_DATA (
    EMPLOYEE_MEAL_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    ORDER_ID INTEGER NOT NULL,
    PRODUCT_ID INTEGER NOT NULL,
    DIMENSION_CODE VARCHAR(15) NOT NULL,
    EMPLOYEE_ID INTEGER NOT NULL,
    UNIT_ID INTEGER NOT NULL,
    PRODUCT_TYPE_ID INTEGER NOT NULL,
    BUSINESS_DATE DATE NOT NULL,
    QUANTITY INTEGER NOT NULL
);

ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION
ADD COLUMN TRANSACTION_SYSTEM_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION
ADD COLUMN SCM_SYSTEM_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION
ADD COLUMN ANALYTICS_SYSTEM_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION
ADD COLUMN ADMIN_SYSTEM_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION
ADD COLUMN CLM_SYSTEM_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION
ADD COLUMN CRM_SYSTEM_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

UPDATE `KETTLE_MASTER_DEV`.`DESIGNATION` SET `TRANSACTION_SYSTEM_ACCESS`='Y', `SCM_SYSTEM_ACCESS`='Y' WHERE `DESIGNATION_ID`='1003';
UPDATE `KETTLE_MASTER_DEV`.`DESIGNATION` SET `DESIGNATION_NAME`='Shift Manager', `DESIGNATION_DESC`='Shift Manager', `TRANSACTION_SYSTEM_ACCESS`='Y', `SCM_SYSTEM_ACCESS`='Y' WHERE `DESIGNATION_ID`='1002';
UPDATE `KETTLE_MASTER_DEV`.`DESIGNATION` SET `DESIGNATION_NAME`='Chai Trainer', `DESIGNATION_DESC`='Chai Trainer' WHERE `DESIGNATION_ID`='1001';

INSERT INTO `KETTLE_MASTER_DEV`.`DESIGNATION` (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`, `TRANSACTION_SYSTEM_ACCESS`, `SCM_SYSTEM_ACCESS`, `ANALYTICS_SYSTEM_ACCESS`, `ADMIN_SYSTEM_ACCESS`, `CLM_SYSTEM_ACCESS`, `CRM_SYSTEM_ACCESS`) VALUES ('1004', 'Team Member', 'Team Member', 'N', 'N', 'N', 'N', 'N', 'N');
INSERT INTO `KETTLE_MASTER_DEV`.`DESIGNATION` (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`, `TRANSACTION_SYSTEM_ACCESS`, `SCM_SYSTEM_ACCESS`, `ANALYTICS_SYSTEM_ACCESS`, `ADMIN_SYSTEM_ACCESS`, `CLM_SYSTEM_ACCESS`, `CRM_SYSTEM_ACCESS`) VALUES ('1005', 'CRE', 'CRE', 'Y', 'Y', 'N', 'N', 'N', 'Y');
INSERT INTO `KETTLE_MASTER_DEV`.`DESIGNATION` (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`, `TRANSACTION_SYSTEM_ACCESS`, `SCM_SYSTEM_ACCESS`, `ANALYTICS_SYSTEM_ACCESS`, `ADMIN_SYSTEM_ACCESS`, `CLM_SYSTEM_ACCESS`, `CRM_SYSTEM_ACCESS`) VALUES ('1006', 'Assistant Manager', 'Assistant Manager', 'Y', 'Y', 'N', 'N', 'N', 'Y');
INSERT INTO `KETTLE_MASTER_DEV`.`DESIGNATION` (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`, `TRANSACTION_SYSTEM_ACCESS`, `SCM_SYSTEM_ACCESS`, `ANALYTICS_SYSTEM_ACCESS`, `ADMIN_SYSTEM_ACCESS`, `CLM_SYSTEM_ACCESS`, `CRM_SYSTEM_ACCESS`) VALUES ('1007', 'Area Manager', 'Area Manager', 'Y', 'Y', 'Y', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_MASTER_DEV`.`DESIGNATION` (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`, `TRANSACTION_SYSTEM_ACCESS`, `SCM_SYSTEM_ACCESS`, `ANALYTICS_SYSTEM_ACCESS`, `ADMIN_SYSTEM_ACCESS`, `CLM_SYSTEM_ACCESS`, `CRM_SYSTEM_ACCESS`) VALUES ('1008', 'Utility', 'Utility', 'N', 'N', 'N', 'N', 'N', 'N');
INSERT INTO `KETTLE_MASTER_DEV`.`DESIGNATION` (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`, `TRANSACTION_SYSTEM_ACCESS`, `SCM_SYSTEM_ACCESS`, `ANALYTICS_SYSTEM_ACCESS`, `ADMIN_SYSTEM_ACCESS`, `CLM_SYSTEM_ACCESS`, `CRM_SYSTEM_ACCESS`) VALUES ('1502', 'Manager', 'Manager', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');


INSERT INTO `KETTLE_MASTER_DEV`.`DEPARTMENT_DESIGNATION_MAPPING` (`DEPT_ID`, `DESIGNATION_ID`) VALUES ('101', '1004');
INSERT INTO `KETTLE_MASTER_DEV`.`DEPARTMENT_DESIGNATION_MAPPING` (`DEPT_ID`, `DESIGNATION_ID`) VALUES ('101', '1005');
INSERT INTO `KETTLE_MASTER_DEV`.`DEPARTMENT_DESIGNATION_MAPPING` (`DEPT_ID`, `DESIGNATION_ID`) VALUES ('101', '1006');
INSERT INTO `KETTLE_MASTER_DEV`.`DEPARTMENT_DESIGNATION_MAPPING` (`DEPT_ID`, `DESIGNATION_ID`) VALUES ('101', '1007');
INSERT INTO `KETTLE_MASTER_DEV`.`DEPARTMENT_DESIGNATION_MAPPING` (`DEPT_ID`, `DESIGNATION_ID`) VALUES ('101', '1008');


UPDATE `KETTLE_MASTER_DEV`.`DESIGNATION` SET `TRANSACTION_SYSTEM_ACCESS`='Y', `ANALYTICS_SYSTEM_ACCESS`='Y' WHERE `DESIGNATION_ID`='1101';
UPDATE `KETTLE_MASTER_DEV`.`DESIGNATION` SET `TRANSACTION_SYSTEM_ACCESS`='Y' WHERE `DESIGNATION_ID`='1102';
UPDATE `KETTLE_MASTER_DEV`.`DESIGNATION` SET `TRANSACTION_SYSTEM_ACCESS`='Y' WHERE `DESIGNATION_ID`='1103';

UPDATE `KETTLE_MASTER_DEV`.`DEPARTMENT_DESIGNATION_MAPPING` SET `DESIGNATION_ID`='1502' WHERE `DEPT_ID`='102' and`DESIGNATION_ID`='1003';

UPDATE `KETTLE_MASTER_DEV`.`EMPLOYEE_DETAIL` SET `DESIGNATION_ID`='1502' WHERE `DEPTARTMENT_ID`='102' and`DESIGNATION_ID`='1003';

