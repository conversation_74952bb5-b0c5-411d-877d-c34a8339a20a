CREATE TABLE KETTLE_DEV.TEMP_ACCESS_CODE(
TEMP_ACCESS_CODE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ID INTEGER NOT NULL,
TEMP_CODE VARCHAR(10) NOT NULL,
CREATION_TIME TIMESTAMP NOT NULL,
EXPIRATION_TIME TIMESTAMP NOT NULL,
CODE_STATUS VARCHAR(15) NOT NULL
);

CREATE TABLE KETTLE_DEV.TEMP_ACCESS_CODE_USAGE_DATA(
TEMP_ACCESS_CODE_USAGE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
TEMP_ACCESS_CODE_ID INTEGER NULL,
CONTACT_NUMBER VARCHAR(15) NOT NULL,
ACCESS_GRANTED VARCHAR(1) NOT NULL,
REASON_FOR_DENIAL VARCHAR(50) NULL,
USAGE_TIME INTEGER NULL
);

ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN TEMP_CODE VARCHAR(10) NULL;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN FREE_INTERNET_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N'

CREATE TABLE CUSTOMER_NOTIFICATION_DETAIL (
    NOTIFICATION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    CONTACT VARCHAR(12),
    MESSAGE VARCHAR(500),
    SERVICE_CLIENT VARCHAR(100),
    TYPE VARCHAR(50),
    NOTIFICATION_SENT VARCHAR(1),
    NOTIFICATION_TIME TIMESTAMP
);
