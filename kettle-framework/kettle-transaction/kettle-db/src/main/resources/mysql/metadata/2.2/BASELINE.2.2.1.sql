ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN CUSTOMER_NAME VARCHAR(50) DEFAULT NULL;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN UNIT_REFERENCE_NAME VARCHAR(50) DEFAULT NULL;

UPDATE KETTLE_DEV.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DEV.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID 
SET 
    od.CUSTOMER_NAME = ci.FIRST_NAME
WHERE
    od.CUSTOMER_ID = ci.CUSTOMER_ID
AND od.CUSTOMER_ID > 5;


