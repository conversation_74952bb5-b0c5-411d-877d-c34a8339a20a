CREATE INDEX INDEX_ORDER_ID_ORDER_STATUS_EVENT ON ORDER_STATUS_EVENT (ORDER_ID) USING BTREE;
CREATE INDEX INDEX_FROM_STATUS_ORDER_STATUS_EVENT ON ORDER_STATUS_EVENT (FROM_STATUS) USING BTREE;
CREATE INDEX INDEX_TO_STATUS_ORDER_STATUS_EVENT ON ORDER_STATUS_EVENT (TO_STATUS) USING BTREE;
CREATE INDEX INDEX_TRANSITION_STATUS_ORDER_STATUS_EVENT ON ORDER_STATUS_EVENT (TRANSITION_STATUS) USING BTREE;




ALTER TABLE KETTLE_DEV.CUSTOMER_INFO
ADD COLUMN IS_SMS_SUBSCRIBER VARCHAR(1) DEFAULT 'Y';

ALTER TABLE KETTLE_DEV.CUSTOMER_INFO
ADD COLUMN IS_EMAIL_SUBSCRIBER VARCHAR(1) DEFAULT 'Y';

ALTER TABLE KETTLE_DEV.CUSTOMER_INFO
ADD COLUMN IS_LOYALTY_SUBSCRIBER VARCHAR(1) DEFAULT 'Y';



ALTER TABLE KETTLE_DEV.ASSEMBLY_LOG_DATA
ADD COLUMN ORDER_ID INTEGER;

ALTER TABLE KETTLE_DEV.ASSEMBLY_LOG_DATA
ADD COLUMN TIME_TO_CANCEL INTEGER;

ALTER TABLE KETTLE_DEV.WORKSTATION_LOG
CHANGE COLUMN TIME_TO_DISPATCH TIME_TO_CANCEL INTEGER;


INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.*','Master Service all privilege','ACTIVE','MASTER_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.user-management.*','User Management privilege','ACTIVE','MASTER_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.offer-management.*','Offer management privilege','ACTIVE','MASTER_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('kettle-service.cash-management.*','Cash management all privilege','ACTIVE','MASTER_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('kettle-checklist.checklist-management.*','Checklist management all privilege','ACTIVE','MASTER_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.unit-metadata.*','Unit management privilege','ACTIVE','MASTER_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.product-metadata.*','Product management privilege','ACTIVE','MASTER_SERVICE');


INSERT INTO KETTLE_MASTER_DEV.EMPLOYEE_PERMISSION_MAPPING
(EMPLOYEE_ID, ACL_ID, PERMISSION, EPM_STATUS)
SELECT '100000',ACL_ID,'1111','ACTIVE' FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
WHERE ACL_MODULE = 'master-service.*';

CREATE TABLE KETTLE_DEV.AUDIT_FORM_INFO (
    AUDIT_FORM_INFO_ID INTEGER PRIMARY KEY,
    RESPONSE_ID VARCHAR(100),
    RESPONSE_TOKEN VARCHAR(100),
    FORM_ID INTEGER,
    UNIT_ID INTEGER,
    MANAGER_ID INTEGER,
    DUTY_MANAGER_ID INTEGER,
    AUDIT_DATE DATE,
    AUDIT_TIME TIMESTAMP NULL,
    AUDITOR_ID INTEGER
);



ALTER TABLE KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
CHANGE COLUMN ACL_MODULE ACL_MODULE VARCHAR(250) NOT NULL UNIQUE;


ALTER TABLE KETTLE_MASTER_DEV.EMPLOYEE_PERMISSION_MAPPING ADD UNIQUE `unique_permission_set`(EMPLOYEE_ID, ACL_ID);


INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.recipe.*','Recipe View privilge','ACTIVE','MASTER_SERVICE');


INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.recipe.add.*','Recipe Edit/Add privilge','ACTIVE','MASTER_SERVICE');


INSERT INTO KETTLE_MASTER.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('master-service.kiosk-management.*','Monk Unit Management Access','ACTIVE','MASTER_SERVICE');


INSERT INTO KETTLE_MASTER.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('kettle-service.report-metadata.*','Reporting Access','ACTIVE','KETTLE_SERVICE');


INSERT INTO KETTLE_MASTER.ACCESS_CONTROL_LIST_DATA
(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES('kettle-service.report-metadata.report.*','BRM Report privilge','ACTIVE','KETTLE_SERVICE');

