ALTER TABLE KETTLE_DEV.CHANNEL_PARTNER ADD COLUMN CREDIT_ACCOUNT_ID INT NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN CREDIT_ACCOUNT_ID INT NULL;

update kettle_dev.channel_partner set CREDIT_ACCOUNT_ID = 1 where PARTNER_CODE = "ZOMATO";
update kettle_dev.channel_partner set CREDIT_ACCOUNT_ID = 2 where PARTNER_CODE = "FOODPANDA";
update kettle_dev.channel_partner set CREDIT_ACCOUNT_ID = 3 where PARTNER_CODE = "SWIGGY";
update kettle_dev.channel_partner set CREDIT_ACCOUNT_ID = 4 where PARTNER_CODE = "TINYOWL";

update kettle_master_dev.unit_detail set CREDIT_ACCOUNT_ID = 7 where UNIT_ID = 10014;
update kettle_master_dev.unit_detail set CREDIT_ACCOUNT_ID = 10 where UNIT_ID = 10004;

CREATE INDEX ORDER_ID_WORKSTATION_LOG ON KETTLE.WORKSTATION_LOG(ORDER_ID) USING BTREE;
CREATE INDEX ORDER_ITEM_ID_WORKSTATION_LOG ON KETTLE.WORKSTATION_LOG(ORDER_ITEM_ID) USING BTREE;
CREATE INDEX UNIT_ID_WORKSTATION_LOG ON KETTLE.WORKSTATION_LOG(UNIT_ID) USING BTREE;
CREATE INDEX UNIT_ID_ASSEMBLY_LOG_DATA ON KETTLE.ASSEMBLY_LOG_DATA(UNIT_ID) USING BTREE;
CREATE INDEX ORDER_ID_ASSEMBLY_LOG_DATA ON KETTLE.ASSEMBLY_LOG_DATA(ORDER_ID) USING BTREE;