
ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN CANCELATION_REASON_ID INTEGER NULL;
ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN WASTAGE_TYPE VARCHAR(20) NULL;
ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN WASTAGE_KETTLE_ID INTEGER NULL;

CREATE TABLE KETTLE_DEV.HOUSE_COST_EVENT(
HOUSE_COST_EVENT_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
CUSTOMER_ID INTEGER NULL,
EMP_ID INTEGER NULL,
<PERSON><PERSON><PERSON><PERSON>ION_REASON VARCHAR(100) NULL,
CANCELATION_REASON_ID INTEGER NULL,
WASTAGE_TYPE VARCHAR(20) NULL,
ORDER_SOURCE VARCHAR(10) NULL,
UNIT_ID INTEGER NULL,
GENERATION_TIME TIMESTAMP NULL,
TOTAL_AMOUNT DECIMAL(16,6) NULL,
CANCELLED_BY INTEGER NULL,
<PERSON><PERSON><PERSON>_APPROVED_BY INTEGER NULL,
CUSTOMER_NAME VARCHAR(30) NULL,
KETTLE_ORDER_ID INTEGER NULL,
WASTAGE_SUMO_ID INTEGER NULL
);

CREATE TABLE KETTLE_DEV.HOUSE_COST_ITEM_DATA(
HOUSE_COST_ITEM_DATA_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
HOUSE_COST_EVENT_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
PRODUCT_NAME VARCHAR(100) NULL,
QUANTITY DECIMAL(16,6) NULL,
PRICE DECIMAL(16,6) NULL,
COST_PRICE DECIMAL(16,6) NULL,
TOTAL_COST DECIMAL(16,6) NULL,
TOTAL_AMOUNT DECIMAL(16,6) NULL,
DIMENSION VARCHAR(10) NULL,
RECIPE_ID INTEGER NULL,
LINKED_ORDER_ITEM_ID INTEGER NULL);

CREATE TABLE KETTLE_DEV.HOUSE_COST_CONSUMABLE_DATA(
HOUSE_COST_CONSUMABLE_DATA_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
HOUSE_COST_EVENT_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
PRODUCT_NAME VARCHAR(100) NULL,
QUANTITY DECIMAL(16,6) NULL,
DIMENSION VARCHAR(10) NULL);


ALTER TABLE KETTLE_DEV.ORDER_ITEM
ADD COLUMN CANCELATION_REASON_ID INTEGER NULL;
ALTER TABLE KETTLE_DEV.ORDER_ITEM
ADD COLUMN WASTAGE_BOOKED VARCHAR(1) NULL;


ALTER TABLE KETTLE_DEV.COMPLIMENTARY_CODE
ADD COLUMN CATEGORY VARCHAR(15) NULL;


INSERT INTO KETTLE_DEV.COMPLIMENTARY_CODE (COMP_ID, COMP_CODE, NAME, DESCRIPTION, STATUS, IS_ACCOUNTABLE, CATEGORY) VALUES ('2107', 'Charity', 'Charity', 'Complimentary Codes', 'ACTIVE', 'N', 'INTERNAL');

INSERT INTO KETTLE_DEV.COMPLIMENTARY_CODE (COMP_ID, COMP_CODE, NAME, DESCRIPTION, STATUS, IS_ACCOUNTABLE, CATEGORY) VALUES ('2108', 'Cafe Wastage', 'Cafe Wastage', 'Complimentary Codes', 'ACTIVE', 'N', 'EXTERNAL');


UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='SYSTEM' WHERE COMP_ID='1';
UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='SYSTEM' WHERE COMP_ID='2100';
UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='SYSTEM' WHERE COMP_ID='2101';
UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='INTERNAL' WHERE COMP_ID='2102';
UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='SYSTEM' WHERE COMP_ID='2103';
UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='SYSTEM' WHERE COMP_ID='2104';
UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='INTERNAL' WHERE COMP_ID='2105';
UPDATE KETTLE_DEV.COMPLIMENTARY_CODE SET CATEGORY='INTERNAL' WHERE COMP_ID='2106';


ALTER TABLE KETTLE_DEV.HOUSE_COST_EVENT
ADD COLUMN COMPLIMENTARY_REASON_ID INTEGER NULL;


ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN TOKEN_NUMBER INTEGER;

CREATE TABLE KETTLE_DEV.UNIT_TOKEN_SEQUENCE (
SEQUENCE_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
NEXT_VALUE INTEGER NOT NULL
);

ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN ORDER_TYPE VARCHAR(30) NULL;
ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN LINKED_ORDER_ID INTEGER NULL;

ALTER TABLE KETTLE_DEV.ORDER_DETAIL ADD COLUMN MANUAL_BILL_BOOK_NO INT(11);
ALTER TABLE KETTLE_DEV.ORDER_DETAIL ADD INDEX `ORDER_DETAIL_MANUAL_BILL_BOOK_NO` USING BTREE (`MANUAL_BILL_BOOK_NO` ASC);