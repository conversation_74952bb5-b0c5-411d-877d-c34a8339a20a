
CREATE TABLE `KETTLE_DEV`.`UNIT_EXPENSE_RECORD` (
  `RECORD_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NOT NULL,
  `EXPENSE_CATEGORY` VARCHAR(100) NOT NULL,
  `EXPENSE_TYPE` VARCHAR(100) NOT NULL,
  `PAYMENT_TYPE` VARCHAR(45) NOT NULL,
  `AMOUNT` DECIMAL(10,2) NOT NULL,
  `COMMENT` VARCHAR(300) NULL,
  `CREATED_BY` INT NOT NULL,
  `CREATED_ON` TIMESTAMP NOT NULL,
  `STATUS` VARCHAR(15) NOT NULL,
  `CANCELLED_BY` INT NULL,
  `CANCELLED_ON` TIMESTAMP NULL,
  PRIMARY KEY (`RECORD_ID`));
  
  ALTER TABLE KETTLE_DEV.UNIT_EXPENSE_RECORD
ADD COLUMN BUDGET_CATEGORY_ID INTEGER NULL,
ADD COLUMN BUDGET_CATEGORY VARCHAR(200) NULL,
ADD COLUMN EXPENSE_TYPE_ID INTEGER NOT NULL,
ADD COLUMN IS_ACCOUNTABLE_IN_PNL VARCHAR(1) NOT NULL,
ADD COLUMN CANCELLATION_REASON VARCHAR(200) NULL;


ALTER TABLE KETTLE_DEV.UNIT_EXPENSE_RECORD 
DROP COLUMN PAYMENT_TYPE; 


CREATE TABLE `KETTLE_DEV`.`METER_READING_DETAILS_DATA` (
  `DATA_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NOT NULL,
  `BILL_TYPE` VARCHAR(25) NOT NULL,
  `METER_NO` INT(11) NOT NULL,
  `CURRENT_UNIT` INT(11) NOT NULL,
  `ENTRY_TYPE` VARCHAR(25) NOT NULL,
  `CALCULATION_INDEX` VARCHAR(10) NOT NULL,
  `STATUS` VARCHAR(15) NOT NULL,
  `CREATED_BY` INT(11) NOT NULL,
  `CREATED_ON` TIMESTAMP NULL,
  `CANCELLED_BY` INT(11) NULL,
  `CANCELLED_ON` TIMESTAMP NULL,
  PRIMARY KEY (`DATA_ID`),
  INDEX `METER_READING_DETAILS_DATA_UNIT_ID` (`UNIT_ID` ASC),
  INDEX `METER_READING_DETAILS_DATA_CALCULATION_INDEX` (`CALCULATION_INDEX` ASC));
  
  
CREATE TABLE `KETTLE_DEV`.`UNIT_BUDGET_EXCEEDED_DATA` (
  `EXCEEDED_DATA_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NOT NULL,
  `EXPENSE_TYPE` VARCHAR(200) NOT NULL,
  `EXPENSE_LABEL` VARCHAR(200) NOT NULL,
  `BUDGET_CATEGORY` VARCHAR(200) NOT NULL,
  `DAY` INT(11) NOT NULL,
  `MONTH` INT(11) NOT NULL,
  `YEAR` INT(11) NOT NULL,
  `NOTIFICATION_TYPE` VARCHAR(25) NOT NULL,
  `BUDGET_AMOUNT` DECIMAL(10,2) NOT NULL,
  `CURRENT_AMOUNT` DECIMAL(10,2) NOT NULL,
  `REQUESTED_AMOUNT` DECIMAL(10,2) NOT NULL,
  `EXPENSE_SOURCE` VARCHAR(25) NOT NULL,
  `CREATED_BY` INT(11) NOT NULL,
  `CREATED_ON` TIMESTAMP NULL,
  PRIMARY KEY (`EXCEEDED_DATA_ID`),
  INDEX `UNIT_BUDGET_EXCEEDED_DATA_UNIT_ID` USING BTREE (`UNIT_ID` ASC),
  INDEX `UNIT_BUDGET_EXCEEDED_DATA_NOTIFICATION_TYPE` USING BTREE (`NOTIFICATION_TYPE` ASC),
  INDEX `UNIT_BUDGET_EXCEEDED_DATA_BUDGET_CATEGORY` USING BTREE (`BUDGET_CATEGORY` ASC),
  INDEX `UNIT_BUDGET_EXCEEDED_DATA_CREATED_ON` USING BTREE (`CREATED_ON` ASC),
  UNIQUE INDEX `UNIT_BUDGET_EXCEEDED_DATA_UNIQUE_DAY_ENTRY` (`UNIT_ID` ASC, `DAY` ASC, `MONTH` ASC, `YEAR` ASC, `BUDGET_CATEGORY` ASC, `NOTIFICATION_TYPE` ASC));