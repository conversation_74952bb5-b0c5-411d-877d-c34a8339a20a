ALTER TABLE KETTLE_DEV.CASH_CARD_EVENT_LOG ADD COLUMN EMP_ID INT(11) NOT NULL;
ALTER TABLE KETTLE_DEV.CASH_CARD_EVENT_LOG MODIFY COLUMN EVENT_DETAIL VARCHAR(250) NOT NULL;

DROP TABLE IF EXISTS `KETTLE_DEV`.`UNIT_MANUAL_BILL_BOOK_DETAIL` ;
CREATE TABLE `KETTLE_DEV`.`UNIT_MANUAL_BILL_BOOK_DETAIL` (
  `UNIT_MANUAL_BILL_BOOK_DETAIL_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `TRANSFER_ORDER_ID` INT(11) NULL,
  `UNIT_ID` INT(11) NOT NULL,
  `STATE_ID` INT(11) NOT NULL,
  `START_NO` INT(11) NOT NULL,
  `END_NO` INT(11) NOT NULL,
  `STATUS` VARCHAR(30) NOT NULL,
  `CREATION_TIME` TIMESTAMP NOT NULL,
  `ACTIVATION_TIME` TIMESTAMP NULL,
  `DEACTIVATION_TIME` TIMESTAMP NULL,
  PRIMARY KEY (`UNIT_MANUAL_BILL_BOOK_DETAIL_ID`),
  UNIQUE INDEX `START_NO_END_NO_UNIQUE` USING BTREE (STATE_ID ASC,`START_NO` ASC, `END_NO` ASC),
  INDEX `UNIT_MANUAL_BILL_BOOK_DETAIL_UNIT_ID` USING BTREE (`UNIT_ID` ASC),
  INDEX `UNIT_MANUAL_BILL_BOOK_DETAIL_STATE_ID` USING BTREE (`STATE_ID` ASC),
  INDEX `UNIT_MANUAL_BILL_BOOK_DETAIL_STATUS` USING BTREE (`STATUS` ASC),
  INDEX `UNIT_MANUAL_BILL_BOOK_DETAIL_START_NO` USING BTREE (`START_NO` ASC),
  INDEX `UNIT_MANUAL_BILL_BOOK_DETAIL_END_NO` USING BTREE (`END_NO` ASC));