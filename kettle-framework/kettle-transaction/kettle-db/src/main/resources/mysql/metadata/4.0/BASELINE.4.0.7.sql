CREATE TABLE KETTLE_DEV.DAY_WISE_TARGETS_DATA(
DAY_WISE_TARGETS_DATA_ID INTEGER NULL PRIMARY KEY AUTO_INCREMENT,
BUSINESS_DATE DATE NULL,
UNIT_ID INTEGER NULL,
TARGET_NET_TICKETS INTEGER NULL,
TARGET_NET_APC DECIMAL(10,2) NULL,
TARGET_NET_SALES DECIMAL(10,2) NULL,
TARGET_NET_DELIVERY_TICKETS INTEGER NULL,
TARGET_NET_DELIVERY_SALES DECIMAL(10,2) NULL,
TARGET_NET_DELIVERY_APC DECIMAL(10,2) NULL,
TARGET_DINE_IN_TICKETS INTEGER NULL,
TARGET_NET_DINE_IN_APC DECIMAL(10,2) NULL,
TARGET_NET_DINE_IN_SALES DECIMAL(10,2) NULL,
TARGET_COLD_PENETRATION DECIMAL(10,2) NULL,
TARGET_CUSTOMER_CAPTURE_PERCENT_OF_TKTS DECIMAL(10,2) NULL,
TARGET_GIFT_CARD_AMOUNT DECIMAL(10,2) NULL,
TARGET_MERCHANDISE_SALES DECIMAL(10,2) NULL,
TARGET_STATUS VARCHAR(15) NULL
);

CREATE TABLE KETTLE_DUMP.DAY_WISE_SALES_DATA(
DAY_WISE_SALES_DATA_ID INTEGER NULL PRIMARY KEY AUTO_INCREMENT,
BUSINESS_DATE DATE NULL,
UNIT_ID INTEGER NULL,
UNIT_NAME VARCHAR(100) NULL,
UNIT_CATEGORY VARCHAR(100) NULL,
BILL_CREATION_SECONDS DECIMAL(10,2) NULL,
NET_TICKETS INTEGER NULL,
NET_APC DECIMAL(10,2) NULL,
NET_SALES DECIMAL(10,2) NULL,
NET_DELIVERY_TICKETS INTEGER NULL,
NET_DELIVERY_SALES DECIMAL(10,2) NULL,
NET_DELIVERY_APC DECIMAL(10,2) NULL,
DINE_IN_TICKETS INTEGER NULL,
NET_DINE_IN_APC DECIMAL(10,2) NULL,
NET_DINE_IN_SALES DECIMAL(10,2) NULL,
COLD_PENETRATION DECIMAL(10,2) NULL,
CUSTOMER_CAPTURE_PERCENT_OF_TKTS DECIMAL(10,2) NULL,
GIFT_CARD_AMOUNT DECIMAL(10,2) NULL,
GIFT_CARD_REDEMPTION DECIMAL(10,2) NULL,
MERCHANDISE_SALES DECIMAL(10,2) NULL,
AREA_MANAGER VARCHAR(100) NULL
);



CREATE INDEX UNIT_ID_DAY_WISE_SALES_DATA ON  KETTLE_DUMP.DAY_WISE_SALES_DATA(UNIT_ID) USING BTREE;
CREATE INDEX BUSINESS_DATE_DAY_WISE_SALES_DATA ON  KETTLE_DUMP.DAY_WISE_SALES_DATA(BUSINESS_DATE) USING BTREE;
CREATE INDEX UNIT_CATEGORY_DAY_WISE_SALES_DATA ON  KETTLE_DUMP.DAY_WISE_SALES_DATA(UNIT_CATEGORY) USING BTREE;
CREATE INDEX UNIT_NAME_DAY_WISE_SALES_DATA ON  KETTLE_DUMP.DAY_WISE_SALES_DATA(UNIT_NAME) USING BTREE;
CREATE INDEX UNIT_ID_DAY_WISE_TARGETS_DATA ON  KETTLE_DUMP.DAY_WISE_TARGETS_DATA(UNIT_ID) USING BTREE;
CREATE INDEX BUSINESS_DATE_DAY_WISE_TARGETS_DATA ON  KETTLE_DUMP.DAY_WISE_TARGETS_DATA(BUSINESS_DATE) USING BTREE;
CREATE INDEX TARGET_STATUS_UNIT_ID ON  KETTLE_DUMP.DAY_WISE_TARGETS_DATA(TARGET_STATUS) USING BTREE;


DROP PROCEDURE IF EXISTS KETTLE_DUMP.SP_INSERT_DAY_WISE_SALES_DATA;
DELIMITER $$
CREATE PROCEDURE KETTLE_DUMP.SP_INSERT_DAY_WISE_SALES_DATA(IN TILL_DATE DATE)
proc_label : BEGIN

DECLARE LAST_DATE DATE;
select case when max(BUSINESS_DATE) is null then '2015-08-26' else max(BUSINESS_DATE) end into LAST_DATE from KETTLE_DUMP.DAY_WISE_SALES_DATA;

IF LAST_DATE IS NOT NULL AND LAST_DATE > TILL_DATE
THEN 
	LEAVE proc_label;
END IF;

label_loop : LOOP
if  TILL_DATE >= LAST_DATE then
delete from KETTLE_DUMP.DAY_WISE_SALES_DATA where BUSINESS_DATE = LAST_DATE;

INSERT INTO KETTLE_DUMP.DAY_WISE_SALES_DATA(BUSINESS_DATE,UNIT_ID,
UNIT_NAME ,
UNIT_CATEGORY,
BILL_CREATION_SECONDS ,
NET_TICKETS ,
NET_APC ,
NET_SALES ,
NET_DELIVERY_TICKETS ,
NET_DELIVERY_SALES ,
NET_DELIVERY_APC ,
DINE_IN_TICKETS ,
NET_DINE_IN_SALES ,
NET_DINE_IN_APC ,
COLD_PENETRATION ,
CUSTOMER_CAPTURE_PERCENT_OF_TKTS ,
GIFT_CARD_AMOUNT ,
GIFT_CARD_REDEMPTION ,
MERCHANDISE_SALES ,
AREA_MANAGER

)
SELECT
LAST_DATE,
ud.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.BILL_CREATION_SECONDS,
    A.NET_TICKETS,
    A.NET_APC,
    A.NET_SALES,
	A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
        TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES  - A.GIFT_CARD_AMOUNT)/(A.NET_TICKETS - A.NET_DELIVERY_TICKETS),0) NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    A.COLD_PENETRATION,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    A.MERCHANDISE_SALES,
   UCASE(ed.EMP_NAME) AREA_MANAGER
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    INNER JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
        LEFT OUTER JOIN
    (SELECT
        m.UNIT_ID,
			m.BILL_CREATION_SECONDS, 
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            AVG(BILL_CREATION_SECONDS) AS BILL_CREATION_SECONDS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
                       AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(LAST_DATE, INTERVAL 295 MINUTE) AND DATE_ADD(LAST_DATE, INTERVAL 1735 MINUTE)

    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(LAST_DATE, INTERVAL 295 MINUTE) AND DATE_ADD(LAST_DATE, INTERVAL 1735 MINUTE)
) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            CASE
                WHEN oi.TAX_CODE = 'GIFT_CARD' THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN  oi.TAX_CODE <> 'GIFT_CARD'  THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(LAST_DATE, INTERVAL 295 MINUTE) AND DATE_ADD(LAST_DATE, INTERVAL 1735 MINUTE)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(LAST_DATE, INTERVAL 295 MINUTE) AND DATE_ADD(LAST_DATE, INTERVAL 1735 MINUTE)
            AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(LAST_DATE, INTERVAL 295 MINUTE) AND DATE_ADD(LAST_DATE, INTERVAL 1735 MINUTE)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(LAST_DATE, INTERVAL 295 MINUTE) AND DATE_ADD(LAST_DATE, INTERVAL 1735 MINUTE)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
ORDER BY AREA_MANAGER , ud.UNIT_NAME;

  
        ELSE
			LEAVE label_loop;
        END IF;
		
        SET LAST_DATE = DATE_ADD(LAST_DATE, INTERVAL 1 DAY);
 
        
        END LOOP label_loop;

END$$
DELIMITER ;
