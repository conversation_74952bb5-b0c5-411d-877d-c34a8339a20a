ALTER TABLE KETTLE_DEV.UNIT_CLOSURE_DETAILS
ADD COLUMN IS_PNL_GENERATED VARCHAR(1) NULL,
ADD COLUMN PNL_INSTANCE_ID INTEGER NULL;

CREATE INDEX UNIT_CLOSURE_DETAILS_IS_PNL_GENERATED ON KETTLE_DEV.UNIT_CLOSURE_DETAILS(IS_PNL_GENERATED) USING BTREE;

CREATE TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL (
    UNIT_EXPENDITURE_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    EXPEDITURE_STATUS VARCHAR(100) NULL,
    KETTLE_DAY_CLOSE_EVENT_ID INTEGER NULL,
    SUMO_DAY_CLOSE_EVENT_ID INTEGER NULL,
    UNIT_ID INTEGER NULL,
    CALC<PERSON>LATION_DAY INTEGER NULL,
    CA<PERSON><PERSON><PERSON>TION_MONTH INTEGER NULL,
    CALCULATION_YEAR INTEGER NULL,
    UNIT_NAME VARCHAR(100) NULL,
    CALCULATION_TYPE VARCHAR(15) NULL,
    BUSINESS_DATE DATE NULL,
    ON_REVENUE_SHARE VARCHAR(1) NULL,
    TOTAL_TICKET INTEGER NULL,
    TOTAL_SALES DECIMAL(10 , 2 ) NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    TOTAL_GMV DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_TICKET INTEGER NULL,
    DINE_IN_SALES DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_GMV DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_TICKET INTEGER NULL,
    DELIVERY_SALES DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_GMV DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SALES DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_NET_SALES DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_REDEMPTION DECIMAL(10 , 2 ) NULL,
    DINE_IN_COGS DECIMAL(10 , 2 ) NULL,
    DELIVERY_COGS DECIMAL(10 , 2 ) NULL,
    EMPLOYEE_MEAL_COGS DECIMAL(10 , 2 ) NULL,
    EMPLOYEE_MEAL_SALES DECIMAL(10 , 2 ) NULL,
    EMPLOYEE_MEAL_TICKET INTEGER NULL,
    UNSATISFIED_CUSTOMER_COST DECIMAL(10 , 2 ) NULL,
    EXPIRY_WASTAGE DECIMAL(10 , 2 ) NULL,
    WASTAGE_OTHER DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_UTILITY DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_STATIONARY DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_UNIFORM DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_EQUIPMENT DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_CUTLERY DECIMAL(10 , 2 ) NULL,
    FIXED_ASSETS DECIMAL(10 , 2 ) NULL,
    SALARY DECIMAL(10 , 2 ) NULL,
    MEDICAL_INSURANCE DECIMAL(10 , 2 ) NULL,
    SALARY_INCENTIVE DECIMAL(10 , 2 ) NULL,
    SECURITY_GUARD_CHARGES DECIMAL(10 , 2 ) NULL,
    SALES_INCENTIVE DECIMAL(10 , 2 ) NULL,
    DEPRECIATION_ON_BIKE DECIMAL(10 , 2 ) NULL,
    FUEL_CHARGES DECIMAL(10 , 2 ) NULL,
    VEHICLE_REGULAR_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    VEHICLE_MONTHLY_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    PARKING_CHARGES DECIMAL(10 , 2 ) NULL,
    MARKETING_AND_SAMPLING DECIMAL(10 , 2 ) NULL,
    ADVERTISEMENT_BTL DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_MARKETING DECIMAL(10 , 2 ) NULL,
    LOGISTIC_CHARGES DECIMAL(10 , 2 ) NULL,
    ENERGY_ELECTRICITY DECIMAL(10 , 2 ) NULL,
    ENERGY_DG DECIMAL(10 , 2 ) NULL,
    WATER_CHARGES DECIMAL(10 , 2 ) NULL,
    COMMUNICATION_INTERNET DECIMAL(10 , 2 ) NULL,
    COMMUNICATION_TELEPHONE DECIMAL(10 , 2 ) NULL,
    COMMUNICATION_ILL DECIMAL(10 , 2 ) NULL,
    CREDIT_CARD_TRANSACTION_CHARGES DECIMAL(10 , 2 ) NULL,
    VOUCHER_TRANSACTION_CHARGES DECIMAL(10 , 2 ) NULL,
    WALLETS_TRANSACTION_CHARGES DECIMAL(10 , 2 ) NULL,
    COMMISSION_CHANNEL_PARTNERS DECIMAL(10 , 2 ) NULL,
    COMMISSION_CHANGE DECIMAL(10 , 2 ) NULL,
    PAYROLL_PROCESSING_FEES DECIMAL(10 , 2 ) NULL,
    NEWSPAPER_CHARGES DECIMAL(10 , 2 ) NULL,
    LOCAL_CONVEYENCE DECIMAL(10 , 2 ) NULL,
    STAFF_WELFARE_EXPENSES DECIMAL(10 , 2 ) NULL,
    COURIER_CHARGES DECIMAL(10 , 2 ) NULL,
    PRINTING_AND_STATIONARY DECIMAL(10 , 2 ) NULL,
    BUSINESS_PROMOTION DECIMAL(10 , 2 ) NULL,
    LEGAL_CHARGES DECIMAL(10 , 2 ) NULL,
    LEGAL_CHARGES_CAFE DECIMAL(10 , 2 ) NULL,
    PROFESSIONAL_CHARGES DECIMAL(10 , 2 ) NULL,
    CENVAT_REVERSAL DECIMAL(10 , 2 ) NULL,
    PROPERTY_TAX DECIMAL(10 , 2 ) NULL,
    OPENING_LICENSES_FEES DECIMAL(10 , 2 ) NULL,
    REGISTRATION_CHARGES DECIMAL(10 , 2 ) NULL,
    STAMP_DUTY_CHARGES DECIMAL(10 , 2 ) NULL,
    DESIGNING_FEES DECIMAL(10 , 2 ) NULL,
    BUILDING_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    CLEANING_CHARGES DECIMAL(10 , 2 ) NULL,
    COMPUTER_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    PEST_CONTROL_CHARGES DECIMAL(10 , 2 ) NULL,
    EQUIPMENT_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    PRONTO_AMC DECIMAL(10 , 2 ) NULL,
    MAINTENANCE_SALARY DECIMAL(10 , 2 ) NULL,
    DG_RENTAL DECIMAL(10 , 2 ) NULL,
    EDC_RENTAL DECIMAL(10 , 2 ) NULL,
    SYSTEM_RENTAL DECIMAL(10 , 2 ) NULL,
    RO_RENTAL DECIMAL(10 , 2 ) NULL,
    INSURANCE_CHARGES DECIMAL(10 , 2 ) NULL,
    PROPERTY_FIX_RENT DECIMAL(10 , 2 ) NULL,
    REVENUE_SHARE DECIMAL(10 , 2 ) NULL,
    FIX_CAM DECIMAL(10 , 2 ) NULL,
    CHILLING_CHARGES DECIMAL(10 , 2 ) NULL,
    MARKETING_CHARGES DECIMAL(10 , 2 ) NULL,
    PETTY_CASH_RENTALS DECIMAL(10 , 2 ) NULL,
    MUSIC_RENTALS DECIMAL(10 , 2 ) NULL,
    INTERNET_PARTNER_RENTAL DECIMAL(10 , 2 ) NULL,
    SUPPORT_OPERATIONS DECIMAL(10 , 2 ) NULL,
    SUPPORT_HEAD_OFFICE DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_PLATFORM_CHARGES DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_TRAINING DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_MAIL DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_OTHERS DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_DIGITAL DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_AD_OFFLINE DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_AD_ONLINE DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_OUTDOOR DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_PNS DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_AGENCY_FEES DECIMAL(10 , 2 ) NULL,
    STOCK_VARIANCE DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_VARIABLE DECIMAL(10 , 2 ) NULL,
    MARKETING_LOCAL_STORE_VARIABLE DECIMAL(10 , 2 ) NULL,
    SUPPORT_VARIABLE DECIMAL(10 , 2 ) NULL,
    MAINTENANCE_VARIABLE DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_VARIABLE DECIMAL(10 , 2 ) NULL,
    FIXED_COST_VARIABLE DECIMAL(10 , 2 ) NULL,
    MANPOWER_VARIABLE DECIMAL(10 , 2 ) NULL,
    SUPPLY_CHAIN_VARIABLE DECIMAL(10 , 2 ) NULL,
    DELIVERY_CHARGES_VARIABLE DECIMAL(10 , 2 ) NULL,
    ANY_OTHER_VARIABLES_1 DECIMAL(10 , 2 ) NULL,
    ANY_OTHER_VARIABLES_2 DECIMAL(10 , 2 ) NULL,
    ANY_OTHER_VARIABLES_3 DECIMAL(10 , 2 ) NULL,
     CONSUMABLE_OTHER DECIMAL(10,2) NULL,
    EXPENSE_OTHER DECIMAL(10,2) NULL,
    COMMISSION_OTHER DECIMAL(10,2) NULL
);

CREATE INDEX UNIT_EXPENDITURE_DETAIL_KETTLE_DAY_CLOSE_EVENT_ID ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(KETTLE_DAY_CLOSE_EVENT_ID) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_SUMO_DAY_CLOSE_EVENT_ID ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(SUMO_DAY_CLOSE_EVENT_ID) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_UNIT_ID ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(UNIT_ID) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_CALCULATION_MONTH ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(CALCULATION_MONTH) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_CALCULATION_YEAR ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(CALCULATION_YEAR) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_CALCULATION_DAY ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(CALCULATION_DAY) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_UNIT_NAME ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(UNIT_NAME) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_CALCULATION_TYPE ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(CALCULATION_TYPE) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_BUSINESS_DATE ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(BUSINESS_DATE) USING BTREE;
CREATE INDEX UNIT_EXPENDITURE_DETAIL_EXPEDITURE_STATUS ON KETTLE_DEV.UNIT_EXPENDITURE_DETAIL(EXPEDITURE_STATUS) USING BTREE;

ALTER TABLE KETTLE_DEV.UNIT_EXPENSE_RECORD ADD COLUMN PNL_DETAIL_ID INTEGER NULL;
CREATE INDEX UNIT_EXPENSE_RECORD_PNL_DETAIL_ID ON KETTLE_DEV.UNIT_EXPENSE_RECORD(PNL_DETAIL_ID) USING BTREE;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL ADD COLUMN ENERGY_DG_RUNNING DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.METER_READING_DETAILS_DATA
ADD COLUMN BUSINESS_DATE DATE NULL;

CREATE TABLE KETTLE_DEV.UNIT_METADATA_EVENT (
EVENT_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
ENTRY_MONTH INTEGER NOT NULL,
ENTRY_YEAR INTEGER NOT NULL,
CREATED_AT TIMESTAMP NULL,
CREATED_BY INTEGER NOT NULL,
EVENT_TYPE VARCHAR(100),

FILE_PATH VARCHAR(200)
);

CREATE TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL (
    UNIT_BUDGET_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER NULL,
    CALCULATION_MONTH INTEGER NULL,
    CALCULATION_YEAR INTEGER NULL,
    UNIT_NAME VARCHAR(100) NULL,
    TOTAL_TICKET INTEGER NULL,
    TOTAL_SALES DECIMAL(10 , 2 ) NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    TOTAL_GMV DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_TICKET INTEGER NULL,
    DINE_IN_SALES DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_GMV DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_TICKET INTEGER NULL,
    DELIVERY_SALES DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_GMV DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SALES DECIMAL(10 , 2 ) NULL,
    DINE_IN_COGS DECIMAL(10 , 2 ) NULL,
    DELIVERY_COGS DECIMAL(10 , 2 ) NULL,
    EMPLOYEE_MEAL_COGS DECIMAL(10 , 2 ) NULL,
    UNSATISFIED_CUSTOMER_COST DECIMAL(10 , 2 ) NULL,
    EXPIRY_WASTAGE DECIMAL(10 , 2 ) NULL,
    WASTAGE_OTHER DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_UTILITY DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_STATIONARY DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_UNIFORM DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_EQUIPMENT DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_CUTLERY DECIMAL(10 , 2 ) NULL,
    FIXED_ASSETS DECIMAL(10 , 2 ) NULL,
    SALARY DECIMAL(10 , 2 ) NULL,
    MEDICAL_INSURANCE DECIMAL(10 , 2 ) NULL,
    SALARY_INCENTIVE DECIMAL(10 , 2 ) NULL,
    SECURITY_GUARD_CHARGES DECIMAL(10 , 2 ) NULL,
    SALES_INCENTIVE DECIMAL(10 , 2 ) NULL,
    DEPRECIATION_ON_BIKE DECIMAL(10 , 2 ) NULL,
    FUEL_CHARGES DECIMAL(10 , 2 ) NULL,
    VEHICLE_REGULAR_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    VEHICLE_MONTHLY_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    PARKING_CHARGES DECIMAL(10 , 2 ) NULL,
    MARKETING_AND_SAMPLING DECIMAL(10 , 2 ) NULL,
    ADVERTISEMENT_BTL DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_MARKETING DECIMAL(10 , 2 ) NULL,
    LOGISTIC_CHARGES DECIMAL(10 , 2 ) NULL,
    ENERGY_ELECTRICITY DECIMAL(10 , 2 ) NULL,
    ENERGY_DG DECIMAL(10 , 2 ) NULL,
    WATER_CHARGES DECIMAL(10 , 2 ) NULL,
    COMMUNICATION_INTERNET DECIMAL(10 , 2 ) NULL,
    COMMUNICATION_TELEPHONE DECIMAL(10 , 2 ) NULL,
    COMMUNICATION_ILL DECIMAL(10 , 2 ) NULL,
    CREDIT_CARD_TRANSACTION_PERCENTAGE DECIMAL(10 , 2 ) NULL,
    CREDIT_CARD_TRANSACTION_CHARGES DECIMAL(10 , 2 ) NULL,
    VOUCHER_TRANSACTION_CHARGES DECIMAL(10 , 2 ) NULL,
    WALLETS_TRANSACTION_CHARGES DECIMAL(10 , 2 ) NULL,
    COMMISSION_CHANNEL_PARTNERS DECIMAL(10 , 2 ) NULL,
    COMMISSION_CHANGE DECIMAL(10 , 2 ) NULL,
    PAYROLL_PROCESSING_FEES DECIMAL(10 , 2 ) NULL,
    NEWSPAPER_CHARGES DECIMAL(10 , 2 ) NULL,
    LOCAL_CONVEYENCE DECIMAL(10 , 2 ) NULL,
    STAFF_WELFARE_EXPENSES DECIMAL(10 , 2 ) NULL,
    COURIER_CHARGES DECIMAL(10 , 2 ) NULL,
    PRINTING_AND_STATIONARY DECIMAL(10 , 2 ) NULL,
    BUSINESS_PROMOTION DECIMAL(10 , 2 ) NULL,
    LEGAL_CHARGES DECIMAL(10 , 2 ) NULL,
    LEGAL_CHARGES_CAFE DECIMAL(10 , 2 ) NULL,
    PROFESSIONAL_CHARGES DECIMAL(10 , 2 ) NULL,
    CENVAT_REVERSAL DECIMAL(10 , 2 ) NULL,
    PROPERTY_TAX DECIMAL(10 , 2 ) NULL,
    OPENING_LICENSES_FEES DECIMAL(10 , 2 ) NULL,
    REGISTRATION_CHARGES DECIMAL(10 , 2 ) NULL,
    STAMP_DUTY_CHARGES DECIMAL(10 , 2 ) NULL,
    DESIGNING_FEES DECIMAL(10 , 2 ) NULL,
    BUILDING_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    CLEANING_CHARGES DECIMAL(10 , 2 ) NULL,
    COMPUTER_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    PEST_CONTROL_CHARGES DECIMAL(10 , 2 ) NULL,
    EQUIPMENT_MAINTENANCE DECIMAL(10 , 2 ) NULL,
    PRONTO_AMC DECIMAL(10 , 2 ) NULL,
    MAINTENANCE_SALARY DECIMAL(10 , 2 ) NULL,
    DG_RENTAL DECIMAL(10 , 2 ) NULL,
    EDC_RENTAL DECIMAL(10 , 2 ) NULL,
    SYSTEM_RENTAL DECIMAL(10 , 2 ) NULL,
    RO_RENTAL DECIMAL(10 , 2 ) NULL,
    INSURANCE_CHARGES DECIMAL(10 , 2 ) NULL,
    PROPERTY_FIX_RENT DECIMAL(10 , 2 ) NULL,
    REVENUE_SHARE DECIMAL(10 , 2 ) NULL,
    FIX_CAM DECIMAL(10 , 2 ) NULL,
    CHILLING_CHARGES DECIMAL(10 , 2 ) NULL,
    MARKETING_CHARGES DECIMAL(10 , 2 ) NULL,
    PETTY_CASH_RENTALS DECIMAL(10 , 2 ) NULL,
    MUSIC_RENTALS DECIMAL(10 , 2 ) NULL,
    INTERNET_PARTNER_RENTAL DECIMAL(10 , 2 ) NULL,
    SUPPORT_OPERATIONS DECIMAL(10 , 2 ) NULL,
    SUPPORT_HEAD_OFFICE DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_PLATFORM_CHARGES DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_TRAINING DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_MAIL DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_OTHERS DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_DIGITAL DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_AD_OFFLINE DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_AD_ONLINE DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_OUTDOOR DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_PNS DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_AGENCY_FEES DECIMAL(10 , 2 ) NULL,
    STOCK_VARIANCE DECIMAL(10 , 2 ) NULL,
    TECHNOLOGY_VARIABLE DECIMAL(10 , 2 ) NULL,
    MARKETING_LOCAL_STORE_VARIABLE DECIMAL(10 , 2 ) NULL,
    SUPPORT_VARIABLE DECIMAL(10 , 2 ) NULL,
    MAINTENANCE_VARIABLE DECIMAL(10 , 2 ) NULL,
    CORPORATE_MARKETING_VARIABLE DECIMAL(10 , 2 ) NULL,
    FIXED_COST_VARIABLE DECIMAL(10 , 2 ) NULL,
    MANPOWER_VARIABLE DECIMAL(10 , 2 ) NULL,
    SUPPLY_CHAIN_VARIABLE DECIMAL(10 , 2 ) NULL,
    DELIVERY_CHARGES_VARIABLE DECIMAL(10 , 2 ) NULL,
    ANY_OTHER_VARIABLES_1 DECIMAL(10 , 2 ) NULL,
    ANY_OTHER_VARIABLES_2 DECIMAL(10 , 2 ) NULL,
    ANY_OTHER_VARIABLES_3 DECIMAL(10 , 2 ) NULL,
    ON_REVENUE_SHARE VARCHAR(1) NULL,
    ELECTRICITY_METER_1_FIXED_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_1_PER_UNIT_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_1_TAX_PERCENTAGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_1_OTHER_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_2_FIXED_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_2_PER_UNIT_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_2_TAX_PERCENTAGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_2_OTHER_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_3_FIXED_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_3_PER_UNIT_CHARGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_3_TAX_PERCENTAGE DECIMAL(10 , 2 ) NULL,
    ELECTRICITY_METER_3_OTHER_CHARGE DECIMAL(10 , 2 ) NULL,
    DG_METER_FIXED_CHARGE DECIMAL(10 , 2 ) NULL,
    DG_METER_PER_UNIT_CHARGE DECIMAL(10 , 2 ) NULL,
    DG_METER_TAX_PERCENTAGE DECIMAL(10 , 2 ) NULL,
    DG_METER_OTHER_CHARGE DECIMAL(10 , 2 ) NULL,
    CONSUMABLE_OTHER DECIMAL(10 , 2 ) NULL,
    EXPENSE_OTHER DECIMAL(10 , 2 ) NULL,
    COMMISSION_OTHER DECIMAL(10 , 2 ) NULL
);
ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL ADD COLUMN ENERGY_DG_RUNNING DECIMAL(10,2) NULL;

CREATE INDEX UNIT_BUDGETORY_DETAIL_UNIT_ID ON KETTLE_DEV.UNIT_BUDGETORY_DETAIL(UNIT_ID) USING BTREE;
CREATE INDEX UNIT_BUDGETORY_DETAIL_CALCULATION_MONTH ON KETTLE_DEV.UNIT_BUDGETORY_DETAIL(CALCULATION_MONTH) USING BTREE;
CREATE INDEX UNIT_BUDGETORY_DETAIL_CALCULATION_YEAR ON KETTLE_DEV.UNIT_BUDGETORY_DETAIL(CALCULATION_YEAR) USING BTREE;
CREATE INDEX UNIT_BUDGETORY_DETAIL_UNIT_NAME ON KETTLE_DEV.UNIT_BUDGETORY_DETAIL(UNIT_NAME) USING BTREE;


ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN REVENUE_SHARE_PERCENTAGE  DECIMAL(10,2) NULL,
ADD COLUMN SALES_INCENTIVE_PERCENTAGE  DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN CENVAT_REVERSAL_PERCENTAGE  DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN TRAINING_COGS  DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN TRAINING_COGS  DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN BUDGET_STATUS  VARCHAR(15) NULL;

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN BUDGET_STATUS  VARCHAR(15) NULL;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN GROSS_COST DECIMAL(10, 2) NULL,
ADD COLUMN GROSS_PROFIT DECIMAL(10, 2) NULL,
ADD COLUMN GROSS_PROFIT_PERCENTAGE DECIMAL(10, 2) NULL,
ADD COLUMN TOTAL_COST DECIMAL(10, 2) NULL,
ADD COLUMN NET_PROFIT DECIMAL(10, 2) NULL,
ADD COLUMN NET_PROFIT_PERCENTAGE DECIMAL(10, 2) NULL;

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN GROSS_COST DECIMAL(10, 2) NULL,
ADD COLUMN GROSS_PROFIT DECIMAL(10, 2) NULL,
ADD COLUMN GROSS_PROFIT_PERCENTAGE DECIMAL(10, 2) NULL,
ADD COLUMN TOTAL_COST DECIMAL(10, 2) NULL,
ADD COLUMN NET_PROFIT DECIMAL(10, 2) NULL,
ADD COLUMN NET_PROFIT_PERCENTAGE DECIMAL(10, 2) NULL;


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN NET_SALES DECIMAL(10,2) NULL;


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN DELIVERY_GIFT_CARD_SALES DECIMAL(10,2) NULL,
ADD COLUMN DELIVERY_GIFT_CARD_REDEMPTION DECIMAL(10,2) NULL,
ADD COLUMN DELIVERY_GIFT_CARD_NET_SALES DECIMAL(10,2) NULL,
ADD COLUMN DELIVERY_NET_SALES DECIMAL(10,2) NULL,
ADD COLUMN DINE_IN_GIFT_CARD_SALES DECIMAL(10,2) NULL,
ADD COLUMN DINE_IN_GIFT_CARD_REDEMPTION DECIMAL(10,2) NULL,
ADD COLUMN DINE_IN_GIFT_CARD_NET_SALES DECIMAL(10,2) NULL,
ADD COLUMN DINE_IN_NET_SALES DECIMAL(10,2) NULL;

