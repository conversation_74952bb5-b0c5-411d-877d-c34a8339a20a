ALTER TABLE KETTLE_DEV.DELIVERY_DETAIL ADD COLUMN  DELIVERY_SOURCE  VARCHAR(50) NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('StringDelimited', 'CNML', 'charity.notification.emailIds');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ((SELECT ATTRIBUTE_DEF_ID from  `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` WHERE ATTRIBUTE_NAME = 'charity.notification.emailIds'), '<EMAIL>', 'KETTLE_SERVICE');

ALTER TABLE `KETTLE_DEV`.`METER_READING_DETAILS_DATA` 
ADD COLUMN `UPDATED_BY` INT(11) NULL,
ADD COLUMN `UPDATED_ON` TIMESTAMP NULL;