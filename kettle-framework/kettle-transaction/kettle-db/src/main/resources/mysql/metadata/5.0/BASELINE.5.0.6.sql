CREATE TABLE `KETTLE_DEV`.`WALLET_DATA` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `ACCOUNT_TYPE` VARCHAR(25) NOT NULL,
  `ACCOUNT_NO` VARCHAR(16) NOT NULL,
  `ACCOUNT_HOLDER_NAME` VARCHAR(100) NOT NULL,
  `ACCOUNT_HOLDER_CONTACT` VARCHAR(10) NOT NULL,
  `ACCOUNT_HOLDER_EMAIL` VARCHAR(100) NOT NULL,
  `WALLET_TYPE` VARCHAR(45) NOT NULL,
  `OPENINIG_AMOUNT` DECIMAL(16,6) NOT NULL,
  `TOTAL_AMOUNT` DECIMAL(16,6) NOT NULL,
  `CURRENT_BALANCE` DECIMAL(16,6) NULL,
  `ISSUED_AMOUNT` DECIMAL(16,6) NULL,
  `APPROVED_AMOUNT` DECIMAL(16,6) NULL,
  `RE<PERSON>ECTED_AMOUNT` DECIMAL(16,6) NULL,
  `PENDING_APPROVAL` DECIMAL(16,6) NULL,
  `SPENT_AMOUNT` DECIMAL(16,6) NULL,
  `LAST_UPDATE_TIME` TIMESTAMP NULL,
  `STATUS` VARCHAR(10) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ACCOUNT_NO_UNIQUE` (`ACCOUNT_NO` ASC));
  
  
  CREATE TABLE `KETTLE_DEV`.`WALLET_TRANSACTION_DATA` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `WALLET_ID` INT(11) NOT NULL,
  `VOUCHER_ID` INT(11) NULL,
  `TRANSACTION_TYPE` VARCHAR(10) NOT NULL,
  `TRANSACTION_AMOUNT` DECIMAL(16,6) NOT NULL,
  `TRANSACTION_CODE` VARCHAR(15) NOT NULL,
  `TRANSACTION_CODE_TYPE` VARCHAR(45) NOT NULL,
  `TRANSACTION_STATUS` VARCHAR(20) NOT NULL,
  `TRANSACTION_TIME` TIMESTAMP NULL,
  `CREATED_BY` INT(11) NULL,
  `ENTITY` VARCHAR(15) NOT NULL,
  IS_FORCED VARCHAR(1) NULL,
  PRIMARY KEY (`ID`),
  INDEX `WALLET_TRANSACTION_DATA_FK_WALLET_ID` (`WALLET_ID` ASC),
  CONSTRAINT `WALLET_TRANSACTION_DATA_FK`
    FOREIGN KEY (`WALLET_ID`)
    REFERENCES `kettle_dev`.`WALLET_DATA` (`ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
    
    
 CREATE TABLE `KETTLE_DEV`.`VOUCHER_DATA` (
  `VOUCHER_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `GENERATED_VOUCHER_ID` VARCHAR(30) NOT NULL,
  `ACCOUNT_TYPE` VARCHAR(25) NOT NULL,
  `ACCOUNT_NO` VARCHAR(16) NOT NULL,
  `WALLET_ID` INT(11) NOT NULL,
  `BUSINESS_DATE` DATE NOT NULL,
  `EXPENSE_TYPE` VARCHAR(50) NULL,
  `EXPENSE_DETAIL` VARCHAR(100) NULL,
  `CURRENT_STATUS` VARCHAR(25) NOT NULL,
  `ISSUED_AMOUNT` DECIMAL(16,6) NOT NULL,
  `EXPENSE_AMOUNT` DECIMAL(16,6) NULL,
  `ISSUED_TO` INT(11) NULL,
  `ISSUED_TIME` TIMESTAMP NULL,
  `ISSUED_BY` INT(11) NULL,
  `LAST_UPDATE_TIME` TIMESTAMP NULL,
  `IS_REIMBURSED`  VARCHAR(1) NULL, 
  PRIMARY KEY (`VOUCHER_ID`),
  UNIQUE INDEX `GENERATED_VOUCHER_ID_UNIQUE` (`GENERATED_VOUCHER_ID` ASC));

  
CREATE TABLE `KETTLE_DEV`.`VOUCHER_FILE_DATA` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `VOUCHER_ID` INT(11) NOT NULL,
  `FILE_PATH` VARCHAR(500) NULL,
  `FILE_NAME` VARCHAR(100) NOT NULL,
  `STATUS` VARCHAR(10) NOT NULL,
  `CREATED_ON` TIMESTAMP NULL,
  `MIME_TYPE` VARCHAR(10) NOT NULL,
  `STORAGE_TYPE` VARCHAR(15) NOT NULL,
  `S3_BUCKET` VARCHAR(180) NULL,
  `S3_KEY` VARCHAR(180) NULL,
  `S3_URL` VARCHAR(500) NULL,
  PRIMARY KEY (`ID`),
  INDEX `VOUCHER_FILE_DATA_FK_VOUCHER_IDX` (`VOUCHER_ID` ASC),
  CONSTRAINT `VOUCHER_FILE_DATA_FK_VOUCHER_ID`
    FOREIGN KEY (`VOUCHER_ID`)
    REFERENCES `KETTLE_DEV`.`VOUCHER_DATA` (`VOUCHER_ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
    
    CREATE TABLE `KETTLE_DEV`.`VOUCHER_STATUS_DATA` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `VOUCHER_ID` INT(11) NOT NULL,
  `FROM_STATUS` VARCHAR(25) NOT NULL,
  `TO_STATUS` VARCHAR(25) NOT NULL,
  `GENERATED_BY` INT(11) NOT NULL,
  `ACTION_COMMENT` VARCHAR(1000) NULL,
  `ACTION_TIME` TIMESTAMP NULL,
  `TRANSITION_STATUS` VARCHAR(10) NOT NULL,
  PRIMARY KEY (`ID`),
  INDEX `VOUCHER_STATUS_DATA_FK_VOUCHER_IDX` (`VOUCHER_ID` ASC),
  CONSTRAINT `VOUCHER_STATUS_DATA_FK_VOUCHER_ID`
    FOREIGN KEY (`VOUCHER_ID`)
    REFERENCES `KETTLE_DEV`.`VOUCHER_DATA` (`VOUCHER_ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
    
    
    CREATE TABLE WALLET_AUTHORIZATION_REQUEST
(AUTHORIZATION_REQUEST_ID INT NOT NULL AUTO_INCREMENT,
AUTHORIZATON_ID VARCHAR(50) NOT NULL,
AUTHORIZATION_MODE VARCHAR(10) NOT NULL,
AUTHORZATION_CODE VARCHAR(1000) NOT NULL,
AUTHORZATION_TEXT VARCHAR(1000) NOT NULL,
ADD_TIME TIMESTAMP NULL,
VALIDATION_TIME TIMESTAMP NULL,
PRIMARY KEY (AUTHORIZATION_REQUEST_ID)
);

CREATE TABLE KETTLE_DEV.TOPUP_DENOMINATION(
ID INT PRIMARY KEY AUTO_INCREMENT,
WALLET_TRANSACTION_ID INT NOT NULL,
DENOMINATION INT NOT NULL,
PACKET_COUNT INT NULL,
LOOSE_CURRENCY_COUNT INT NULL,
TOTAL_AMOUNT DECIMAL(16,6) NOT NULL,
FOREIGN KEY(WALLET_TRANSACTION_ID) REFERENCES WALLET_TRANSACTION_DATA(ID),
FOREIGN KEY(DENOMINATION) REFERENCES DENOMINATION(DENOMINATION_ID)
);



    
  INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES 
((SELECT ATTRIBUTE_DEF_ID FROM KETTLE_MASTER_DEV.CONFIG_ATTRIBUTE_DEFINITION WHERE ATTRIBUTE_NAME = 'sms.otp.client')
, 'SOLUTION_INFINI', 'FORMS_SERVICE');
    
    
    
    
CREATE TABLE KETTLE_DEV.CHANNEL_PARTNER_COMMISSION(
PARTNER_COMMISSION_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
PARTNER_ID INTEGER NOT NULL,
COMMISSION_RATE DECIMAL(10,2) NOT NULL,
TAX_RATE DECIMAL(10,2) NOT NULL,
START_DATE DATE NOT NULL,
END_DATE DATE NOT NULL
);

CREATE INDEX CHANNEL_PARTNER_COMMISSION_PARTNER_ID ON KETTLE_DEV.CHANNEL_PARTNER_COMMISSION(PARTNER_ID) USING BTREE;
CREATE INDEX CHANNEL_PARTNER_COMMISSION_START_DATE ON KETTLE_DEV.CHANNEL_PARTNER_COMMISSION(START_DATE) USING BTREE;
CREATE INDEX CHANNEL_PARTNER_COMMISSION_END_DATE ON KETTLE_DEV.CHANNEL_PARTNER_COMMISSION(END_DATE) USING BTREE;

INSERT INTO KETTLE_DEV.CHANNEL_PARTNER_COMMISSION(PARTNER_ID, COMMISSION_RATE,TAX_RATE, START_DATE, END_DATE)
select PARTNER_ID, COMMISSION_RATE,'18.00', '2015-01-01', '9999-12-01' from KETTLE_DEV.CHANNEL_PARTNER;

ALTER TABLE KETTLE_DEV.CHANNEL_PARTNER DROP COLUMN COMMISSION_RATE;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN DINE_IN_COGS_TAX DECIMAL(10,2) NULL,
ADD COLUMN DELIVERY_COGS_TAX DECIMAL(10,2) NULL,
ADD COLUMN EMPLOYEE_MEAL_COGS_TAX DECIMAL(10,2) NULL,
ADD COLUMN UNSATISFIED_CUSTOMER_COST_TAX DECIMAL(10,2) NULL,
ADD COLUMN EXPIRY_WASTAGE_TAX DECIMAL(10,2) NULL,
ADD COLUMN WASTAGE_OTHER_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_OTHER_TAX DECIMAL(10,2) NULL,
ADD COLUMN FIXED_ASSETS_TAX DECIMAL(10,2) NULL,
ADD COLUMN FIXED_ASSETS_CAPEX DECIMAL(10,2) NULL,
ADD COLUMN FIXED_ASSETS_CAPEX_TAX DECIMAL(10,2) NULL,
ADD COLUMN MARKETING_AND_SAMPLING_TAX DECIMAL(10,2) NULL,
ADD COLUMN TRAINING_COGS_TAX DECIMAL(10,2) NULL,
ADD COLUMN STOCK_VARIANCE_TAX DECIMAL(10,2) NULL,
ADD COLUMN CONSUMABLE_MARKETING_TAX DECIMAL(10,2) NULL;


ALTER TABLE KETTLE_DEV.INVENTORY_UPDATE_DATA ADD COLUMN EXPIRE_QUANTITY INTEGER DEFAULT 0;
ALTER TABLE KETTLE_DEV.UNIT_PRODUCT_INVENTORY ADD COLUMN EXPIRE_QUANTITY INTEGER DEFAULT 0;
