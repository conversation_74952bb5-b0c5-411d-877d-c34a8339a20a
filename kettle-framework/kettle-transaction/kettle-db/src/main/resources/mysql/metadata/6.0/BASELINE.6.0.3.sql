
SET SQL_SAFE_UPDATES = 0;
UPDATE KETTLE_DEV.UNIT_EXPENSE_RECORD u SET BUDGET_CATEGORY = (SELECT BUDGET_CATEGORY FROM KETTLE_MASTER_DEV.EXPENSE_METADATA WHERE EXPENSE_METADATA_ID = u.EXPENSE_TYPE_ID);
UPDATE KETTLE_DEV.UNIT_EXPENSE_RECORD u SET BUDGET_CATEGORY = (SELECT BUDGET_CATEGORY FROM KETTLE_MASTER_DEV.EXPENSE_METADATA WHERE EXPENSE_METADATA_ID = u.EXPENSE_TYPE_ID);



CREATE TABLE KETTLE_DEV.ORDER_NPS_RESPONSE_DATA(
RESPONSE_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
SURVEY_RESPONSE_ID INTEGER NOT NULL,
NPS_QUESTION VARCHAR(100),
NPS_RESPONSE VARCHAR(100)
);


CREATE INDEX EVENT_TRIGGER_TIME_ORDER_FEEDBACK_EVENT ON ORDER_FEEDBACK_EVENT(EVENT_TRIGGER_TIME) USING BTREE;
CREATE INDEX EVENT_TYPE_ORDER_FEEDBACK_EVENT ON ORDER_FEEDBACK_EVENT(EVENT_TYPE) USING BTREE;


CREATE INDEX EVENT_TYPE_ORDER_FEEDBACK_DETAIL ON ORDER_FEEDBACK_DETAIL(EVENT_TYPE) USING BTREE;

