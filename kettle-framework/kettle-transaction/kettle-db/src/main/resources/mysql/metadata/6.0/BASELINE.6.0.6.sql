# Takes a Longer Time
ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN IS_GIFT_CARD_ORDER VARCHAR(1) NULL,
ADD COLUMN IS_NEW_CUSTOMER VARCHAR(1) NULL;

CREATE INDEX ORDER_DETAIL_IS_GIFT_CARD_ORDER ON KETTLE_DEV.ORDER_DETAIL(IS_GIFT_CARD_ORDER) USING BTREE;

#############################Update Old Data#######################


CREATE TABLE KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING(CUSTOMER_ID INTEGER NULL, ORDER_ID INTEGER NULL, IS_GIFT_CARD_ORDER VARCHAR(1));

CREATE INDEX TEMP_ORDER_NEW_CUSTOMER_MAPPING_CUSTOMER_ID ON KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING(CUSTOMER_ID) USING BTREE;
CREATE INDEX TEMP_ORDER_NEW_CUSTOMER_MAPPING_ORDER_ID ON KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING(ORDER_ID) USING BTREE;
CREATE INDEX TEMP_ORDER_NEW_CUSTOMER_MAPPING_IS_GIFT_CARD_ORDER  ON KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING(IS_GIFT_CARD_ORDER) USING BTREE;

TRUNCATE KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING;

INSERT INTO KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING(CUSTOMER_ID, ORDER_ID)
select CUSTOMER_ID,MIN(ORDER_ID) from ORDER_DETAIL
where CUSTOMER_ID > 5
group by CUSTOMER_ID;

update KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING
SET IS_GIFT_CARD_ORDER = 'N';

UPDATE KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING m,
    (SELECT 
        a.CUSTOMER_ID, MIN(a.ORDER_ID) ORDER_ID
    FROM
        (SELECT 
        od.ORDER_ID,
            MIN(od.CUSTOMER_ID) CUSTOMER_ID,
            SUM(oi.QUANTITY * oi.PRICE) AMOUNT,
            MIN(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MIN(od.TOTAL_AMOUNT) TOTAL_AMOUNT
    FROM
        KETTLE_DEV.ORDER_DETAIL od, KETTLE_DEV.ORDER_ITEM oi, KETTLE_MASTER_DEV.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND pd.TAX_CODE = 'GIFT_CARD'
            AND od.CUSTOMER_ID > 5
    GROUP BY od.ORDER_ID) a
    WHERE
        a.AMOUNT = a.TAXABLE_AMOUNT
            AND a.AMOUNT = a.TOTAL_AMOUNT
    GROUP BY a.CUSTOMER_ID) x 
SET 
    IS_GIFT_CARD_ORDER = 'Y'
WHERE
    x.ORDER_ID = m.ORDER_ID
        AND x.CUSTOMER_ID = m.CUSTOMER_ID
;

UPDATE KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING t, (
select CUSTOMER_ID,MIN(ORDER_ID)ORDER_ID from ORDER_DETAIL
where CUSTOMER_ID > 5
AND ORDER_ID NOT IN  (select ORDER_ID from KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING where  IS_GIFT_CARD_ORDER = 'Y')
group by CUSTOMER_ID
)o
SET t.ORDER_ID = o.ORDER_ID
where t.CUSTOMER_ID = o.CUSTOMER_ID
;

update KETTLE_DEV.ORDER_DETAIL
SET IS_NEW_CUSTOMER = 'N',
IS_GIFT_CARD_ORDER = 'N';

update KETTLE_DEV.ORDER_DETAIL o, KETTLE_DEV.TEMP_ORDER_NEW_CUSTOMER_MAPPING t
SET IS_NEW_CUSTOMER = 'Y'
where t.ORDER_ID = o.ORDER_ID
;

#############################

DROP TABLE IF EXISTS KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING;
CREATE TABLE KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING(UNIT_ID INTEGER NULL, CUSTOMER_ID INTEGER NULL, ORDER_ID INTEGER NULL, IS_GIFT_CARD_ORDER VARCHAR(1));

CREATE INDEX ORDER_NEW_CUSTOMER_MAPPING_CUSTOMER_ID ON KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING(CUSTOMER_ID) USING BTREE;
CREATE INDEX ORDER_NEW_CUSTOMER_MAPPING_UNIT_ID ON KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX ORDER_NEW_CUSTOMER_MAPPING_ORDER_ID ON KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING(ORDER_ID) USING BTREE;
CREATE INDEX ORDER_NEW_CUSTOMER_MAPPING_IS_GIFT_CARD_ORDER  ON KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING(IS_GIFT_CARD_ORDER) USING BTREE;

#############################Mark as new customer after day close#######################


DROP PROCEDURE IF EXISTS KETTLE_DEV.MARK_CUSTOMER_AS_NEW_FOR_ORDER;
DELIMITER $$
CREATE PROCEDURE KETTLE_DEV.MARK_CUSTOMER_AS_NEW_FOR_ORDER(IN INPUT_UNIT_ID INTEGER, IN IN_BUSINESS_DATE DATE)
proc_label : BEGIN

DELETE FROM KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING WHERE UNIT_ID = INPUT_UNIT_ID;

INSERT INTO KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING(CUSTOMER_ID, UNIT_ID)
select DISTINCT CUSTOMER_ID, UNIT_ID from ORDER_DETAIL
where CUSTOMER_ID > 5
AND BUSINESS_DATE = IN_BUSINESS_DATE
AND UNIT_ID = INPUT_UNIT_ID;

update KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING t, (
select t.CUSTOMER_ID, MIN(od.ORDER_ID) ORDER_ID from KETTLE_DEV.ORDER_DETAIL od, KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING t
where od.CUSTOMER_ID = t.CUSTOMER_ID
AND t.UNIT_ID = INPUT_UNIT_ID
GROUP BY t.CUSTOMER_ID
)s
set t.ORDER_ID = s.ORDER_ID
where s.CUSTOMER_ID = t.CUSTOMER_ID
;

update KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING
SET IS_GIFT_CARD_ORDER = 'N'
WHERE UNIT_ID = INPUT_UNIT_ID;

UPDATE KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING m,
    (SELECT 
        a.CUSTOMER_ID, MIN(a.ORDER_ID) ORDER_ID
    FROM
        (SELECT 
        od.ORDER_ID,
            MIN(od.CUSTOMER_ID) CUSTOMER_ID,
            SUM(oi.QUANTITY * oi.PRICE) AMOUNT,
            MIN(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MIN(od.TOTAL_AMOUNT) TOTAL_AMOUNT
    FROM
        KETTLE_DEV.ORDER_DETAIL od, KETTLE_DEV.ORDER_ITEM oi, KETTLE_MASTER_DEV.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND pd.TAX_CODE = 'GIFT_CARD'
            AND od.CUSTOMER_ID IN (select CUSTOMER_ID from KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING where UNIT_ID = INPUT_UNIT_ID)
    GROUP BY od.ORDER_ID) a
    WHERE
        a.AMOUNT = a.TAXABLE_AMOUNT
            AND a.AMOUNT = a.TOTAL_AMOUNT
    GROUP BY a.CUSTOMER_ID) x 
SET 
    IS_GIFT_CARD_ORDER = 'Y'
WHERE
    x.ORDER_ID = m.ORDER_ID
        AND x.CUSTOMER_ID = m.CUSTOMER_ID
        AND m.UNIT_ID = INPUT_UNIT_ID
;

UPDATE KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING t, (
select o.CUSTOMER_ID,MIN(o.ORDER_ID)ORDER_ID from ORDER_DETAIL o,KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING  t
where o.CUSTOMER_ID = t.CUSTOMER_ID
AND t.UNIT_ID = INPUT_UNIT_ID
AND o.ORDER_ID NOT IN  (select ORDER_ID from KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING where  UNIT_ID = INPUT_UNIT_ID AND IS_GIFT_CARD_ORDER = 'Y')
group by o.CUSTOMER_ID
)o
SET t.ORDER_ID = o.ORDER_ID
where t.CUSTOMER_ID = o.CUSTOMER_ID
AND t.UNIT_ID = INPUT_UNIT_ID
;

update KETTLE_DEV.ORDER_DETAIL
SET IS_NEW_CUSTOMER = 'N'
WHERE UNIT_ID = INPUT_UNIT_ID
AND BUSINESS_DATE = IN_BUSINESS_DATE
;

update KETTLE_DEV.ORDER_DETAIL o, KETTLE_DEV.ORDER_NEW_CUSTOMER_MAPPING t
SET IS_NEW_CUSTOMER = 'Y'
where t.ORDER_ID = o.ORDER_ID
AND t.UNIT_ID = INPUT_UNIT_ID
AND o.BUSINESS_DATE = IN_BUSINESS_DATE
;

#################################

ALTER TABLE KETTLE_DEV.DAY_WISE_NPS_SCORE
ADD COLUMN NPS_CATEGORY VARCHAR(30) NULL;

CREATE INDEX DAY_WISE_NPS_SCORE_NPS_CATEGORY ON KETTLE_DEV.DAY_WISE_NPS_SCORE(NPS_CATEGORY) USING BTREE;

UPDATE KETTLE_DEV.DAY_WISE_NPS_SCORE
SET NPS_CATEGORY = 'QTD_OVERALL';


#############################NPS Monthly Score Calculation#######################


DROP PROCEDURE IF EXISTS KETTLE_DEV.SP_CALCULATE_MONTHLY_NPS_SCORE;

DELIMITER $$
CREATE PROCEDURE KETTLE_DEV.SP_CALCULATE_MONTHLY_NPS_SCORE(IN TILL_DATE DATE)
proc_label : BEGIN
 
DECLARE LAST_DATE DATE;
DECLARE QUARTER_START_DATE  DATE;
select case when max(BUSINESS_DATE) is null then '2017-09-28' else max(BUSINESS_DATE) end into LAST_DATE from KETTLE_DEV.DAY_WISE_NPS_SCORE;
 
IF LAST_DATE IS NOT NULL AND LAST_DATE > TILL_DATE
THEN
                LEAVE proc_label;
END IF;
 
label_loop : LOOP
if  TILL_DATE >= LAST_DATE then

select case when MONTH(LAST_DATE) = 1 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL -2 MONTH)  
    when MONTH(LAST_DATE) >= 2 AND MONTH(LAST_DATE) <= 4 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 1 MONTH)  
    when MONTH(LAST_DATE) >= 5 AND MONTH(LAST_DATE) <= 7  then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 4 MONTH) 
    when MONTH(LAST_DATE) >= 8 AND MONTH(LAST_DATE) <= 10 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 7 MONTH) 
    when MONTH(LAST_DATE) >= 11 AND MONTH(LAST_DATE) <= 12 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 10 MONTH) 
    end into QUARTER_START_DATE ;

delete from KETTLE_DEV.DAY_WISE_NPS_SCORE where BUSINESS_DATE = LAST_DATE;
 
 
INSERT INTO KETTLE_DEV.DAY_WISE_NPS_SCORE(NPS_CATEGORY, BUSINESS_DATE,
UNIT_ID	,
UNIT_NAME	,
UNIT_CATEGORY	,
TOTAL_TICKET	,
TOTAL_NEGATIVE	,
TOTAL_NEUTRAL	,
TOTAL_POSITIVE	,
POSITIVE_PERCENTAGE	,
NEGATIVE_PERCENTAGE	,
NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	, RANK_OF_THE_DAY)
SELECT	
	'QTD_OVERALL',
    m.BUSINESS_DATE	,
m.UNIT_ID	,
m.UNIT_NAME	,
m.UNIT_CATEGORY	,
m.TOTAL_TICKET	,
m.TOTAL_NEGATIVE	,
m.TOTAL_NEUTRAL	,
m.TOTAL_POSITIVE	,
m.POSITIVE_PERCENTAGE	,
m.NEGATIVE_PERCENTAGE	,
m.NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
m.NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	,

    COUNT(DISTINCT s2.NPS_SCORE) AS rank
FROM
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            SUM(a.TOTAL_TICKET)TOTAL_TICKET,
            SUM(a.TOTAL_NEGATIVE)TOTAL_NEGATIVE,
            SUM(a.TOTAL_NEUTRAL)TOTAL_NEUTRAL,
            SUM(a.TOTAL_POSITIVE)TOTAL_POSITIVE,
            TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100, 2) NEUTRAL_PERCENTAGE,
            
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END ) AS TOTAL_CAFE_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_CAFE_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_CAFE_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_CAFE_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEUTRAL_PERCENTAGE,
            
             SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END ) AS TOTAL_COD_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_COD_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_COD_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_COD_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEUTRAL_PERCENTAGE,
            
			ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) NPS_SCORE,
           
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2)) CAFE_NPS_SCORE,
        
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2)) COD_NPS_SCORE 
    FROM
        (SELECT
        nd.UNIT_ID,ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM 
        KETTLE_DEV.ORDER_NPS_DETAIL nd
        INNER JOIN KETTLE_DEV.ORDER_DETAIL ud ON ud.ORDER_ID=nd.ORDER_ID
    WHERE         DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID,ud.ORDER_SOURCE) a, KETTLE_MASTER_DEV.UNIT_DETAIL ud
    WHERE        a.UNIT_ID = ud.UNIT_ID  and ud.UNIT_NAME NOT LIKE '%ODC%' GROUP BY 1,2,3,4) m
        JOIN
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            a.TOTAL_TICKET,
            a.TOTAL_NEGATIVE,
            a.TOTAL_NEUTRAL,
            a.TOTAL_POSITIVE,
            TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEUTRAL / a.TOTAL_TICKET * 100, 2) NEUTRAL_PERCENTAGE,
            ROUND(TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2)) - ROUND(TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2)) NPS_SCORE
    FROM
        (SELECT
        UNIT_ID,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE_DEV.ORDER_NPS_DETAIL nd
    WHERE
        DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID) a, KETTLE_MASTER_DEV.UNIT_DETAIL ud
    WHERE
        a.UNIT_ID = ud.UNIT_ID and ud.UNIT_NAME NOT LIKE '%ODC%') s2 ON (m.NPS_SCORE <= s2.NPS_SCORE)
GROUP BY m.UNIT_ID;
 

 
INSERT INTO KETTLE_DEV.DAY_WISE_NPS_SCORE(NPS_CATEGORY, BUSINESS_DATE,
UNIT_ID	,
UNIT_NAME	,
UNIT_CATEGORY	,
TOTAL_TICKET	,
TOTAL_NEGATIVE	,
TOTAL_NEUTRAL	,
TOTAL_POSITIVE	,
POSITIVE_PERCENTAGE	,
NEGATIVE_PERCENTAGE	,
NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	, RANK_OF_THE_DAY)
SELECT	
	'QTD_NEW_CUSTOMER',
    m.BUSINESS_DATE	,
m.UNIT_ID	,
m.UNIT_NAME	,
m.UNIT_CATEGORY	,
m.TOTAL_TICKET	,
m.TOTAL_NEGATIVE	,
m.TOTAL_NEUTRAL	,
m.TOTAL_POSITIVE	,
m.POSITIVE_PERCENTAGE	,
m.NEGATIVE_PERCENTAGE	,
m.NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
m.NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	,

    COUNT(DISTINCT s2.NPS_SCORE) AS rank
FROM
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            SUM(a.TOTAL_TICKET)TOTAL_TICKET,
            SUM(a.TOTAL_NEGATIVE)TOTAL_NEGATIVE,
            SUM(a.TOTAL_NEUTRAL)TOTAL_NEUTRAL,
            SUM(a.TOTAL_POSITIVE)TOTAL_POSITIVE,
            TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100, 2) NEUTRAL_PERCENTAGE,
            
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END ) AS TOTAL_CAFE_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_CAFE_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_CAFE_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_CAFE_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEUTRAL_PERCENTAGE,
            
             SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END ) AS TOTAL_COD_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_COD_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_COD_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_COD_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEUTRAL_PERCENTAGE,
            
			ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) NPS_SCORE,
           
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2)) CAFE_NPS_SCORE,
        
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2)) COD_NPS_SCORE 
    FROM
        (SELECT
        nd.UNIT_ID,ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM 
        KETTLE_DEV.ORDER_NPS_DETAIL nd
        INNER JOIN KETTLE_DEV.ORDER_DETAIL ud ON ud.ORDER_ID=nd.ORDER_ID
    WHERE       ud.IS_NEW_CUSTOMER = 'Y' AND  DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID,ud.ORDER_SOURCE) a, KETTLE_MASTER_DEV.UNIT_DETAIL ud
    WHERE        a.UNIT_ID = ud.UNIT_ID  and ud.UNIT_NAME NOT LIKE '%ODC%' GROUP BY 1,2,3,4) m
        JOIN
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            a.TOTAL_TICKET,
            a.TOTAL_NEGATIVE,
            a.TOTAL_NEUTRAL,
            a.TOTAL_POSITIVE,
            TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEUTRAL / a.TOTAL_TICKET * 100, 2) NEUTRAL_PERCENTAGE,
            ROUND(TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2)) - ROUND(TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2)) NPS_SCORE
    FROM
        (SELECT
        nd.UNIT_ID,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE_DEV.ORDER_NPS_DETAIL nd,KETTLE_DEV.ORDER_DETAIL ud
    WHERE 
		nd.ORDER_ID = ud.ORDER_ID
        AND ud.IS_NEW_CUSTOMER = 'Y'
        AND DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID) a, KETTLE_MASTER_DEV.UNIT_DETAIL ud
    WHERE
        a.UNIT_ID = ud.UNIT_ID and ud.UNIT_NAME NOT LIKE '%ODC%') s2 ON (m.NPS_SCORE <= s2.NPS_SCORE)
GROUP BY m.UNIT_ID;




 
        ELSE
                                                LEAVE label_loop;
        END IF;
                               
        SET LAST_DATE = DATE_ADD(LAST_DATE, INTERVAL 1 DAY);
        
        END LOOP label_loop;
 
END$$
DELIMITER ;

#######################################



UPDATE KETTLE_DUMP.ORDER_DETAIL o,
    (SELECT 
        a.ORDER_ID
    FROM
        (SELECT 
        od.ORDER_ID,
            SUM(oi.QUANTITY * oi.PRICE) AMOUNT,
            MIN(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MIN(od.TOTAL_AMOUNT) TOTAL_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND pd.TAX_CODE = 'GIFT_CARD'
    GROUP BY od.ORDER_ID) a
    WHERE
        a.AMOUNT > 0
            AND a.AMOUNT = a.TAXABLE_AMOUNT
            AND a.AMOUNT = a.TOTAL_AMOUNT) t 
SET 
    IS_GIFT_CARD_ORDER = 'Y'
WHERE
    t.ORDER_ID = o.ORDER_ID;



