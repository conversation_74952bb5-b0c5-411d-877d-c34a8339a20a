#----------------START FOR TABLE SERVICE ------------------
DROP TABLE IF EXISTS KETTLE_DEV.TABLE_ORDER_MAPPING;
CREATE TABLE KETTLE_DEV.TABLE_ORDER_MAPPING (
    MAPPING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    TABLE_REQUEST_ID INTEGER NOT NULL,
    ORDER_ID INTEGER NOT NULL
);

CREATE INDEX TABLE_REQUEST_ID_TABLE_ORDER_MAPPING ON KETTLE_DEV.TABLE_ORDER_MAPPING(TABLE_REQUEST_ID) USING BTREE;
CREATE INDEX ORDER_ID_TABLE_ORDER_MAPPING ON KETTLE_DEV.TABLE_ORDER_MAPPING(ORDER_ID) USING BTREE;

DROP TABLE IF EXISTS KETTLE_DEV.UNIT_TABLE_MAPPING;
CREATE TABLE KETTLE_DEV.UNIT_TABLE_MAPPING (
    TABLE_REQUEST_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER NOT NULL,
    TABLE_NUMBER INTEGER NOT NULL,
    CUSTOMER_ID INTEGER,
    CONTACT VARCHAR(12),
    CUSTOMER_NAME VARCHAR(50),
    TOTAL_ORDERS INTEGER NOT NULL,
    TOTAL_AMOUNT INTEGER NOT NULL,
    TABLE_STATUS VARCHAR(20) NOT NULL
);

CREATE INDEX UNIT_ID_UNIT_TABLE_MAPPING ON KETTLE_DEV.UNIT_TABLE_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX TABLE_NUMBER_UNIT_TABLE_MAPPING ON KETTLE_DEV.UNIT_TABLE_MAPPING(TABLE_NUMBER) USING BTREE;
CREATE INDEX CUSTOMER_ID_UNIT_TABLE_MAPPING ON KETTLE_DEV.UNIT_TABLE_MAPPING(CUSTOMER_ID) USING BTREE;
CREATE INDEX TABLE_STATUS_UNIT_TABLE_MAPPING ON KETTLE_DEV.UNIT_TABLE_MAPPING(TABLE_STATUS) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.PAYMENT_MODE (MODE_NAME, MODE_TYPE, MODE_DESCRIPTION, SETTLEMENT_TYPE, GENERATE_PULL, COMMISSION_RATE, MODE_STATUS, MODE_CATEGORY, AUTOMATIC_PULL_VALIDATE, AUTOMATIC_TRANSFER, AUTOMATIC_CLOSE_TRANSFER, IS_EDITABLE, NEEDS_SETTLEMENT_SLIP_NUMBER, VALIDATION_SOURCE) 
VALUES ('DineInCredit', 'CREDIT', 'DineInCredit', 'CREDIT', '0', '0', 'ACTIVE', 'OFFLINE', 'Y', 'Y', 'Y', 'N', 'N', 'Settlement Report');


 INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING (PAYMENT_MODE_ID,UNIT_ID, MAPPING_STATUS)
 SELECT PAYMENT_MODE_ID, UNIT_ID , 'ACTIVE'
 FROM KETTLE_MASTER_DEV.PAYMENT_MODE PM 
 INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL UD
 WHERE PM.PAYMENT_MODE_ID = 21 
 AND UNIT_STATUS = 'ACTIVE';
 
 ALTER TABLE KETTLE_DEV.ORDER_DETAIL ADD COLUMN TABLE_REQUEST_ID INTEGER;
 
#----------------END FOR TABLE SERVICE ------------------


DROP PROCEDURE IF EXISTS KETTLE.SP_CALCULATE_MONTHLY_NPS_SCORE;

DELIMITER $$
CREATE PROCEDURE KETTLE.SP_CALCULATE_MONTHLY_NPS_SCORE(IN TILL_DATE DATE)
proc_label : BEGIN
 
DECLARE LAST_DATE DATE;
DECLARE QUARTER_START_DATE  DATE;
select case when max(BUSINESS_DATE) is null then '2017-09-28' else max(BUSINESS_DATE) end into LAST_DATE from KETTLE.DAY_WISE_NPS_SCORE;
 
IF LAST_DATE IS NOT NULL AND LAST_DATE > TILL_DATE
THEN
                LEAVE proc_label;
END IF;
 
label_loop : LOOP
if  TILL_DATE >= LAST_DATE then

select case when MONTH(LAST_DATE) = 1 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL -2 MONTH)  
    when MONTH(LAST_DATE) >= 2 AND MONTH(LAST_DATE) <= 4 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 1 MONTH)  
    when MONTH(LAST_DATE) >= 5 AND MONTH(LAST_DATE) <= 7  then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 4 MONTH) 
    when MONTH(LAST_DATE) >= 8 AND MONTH(LAST_DATE) <= 10 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 7 MONTH) 
    when MONTH(LAST_DATE) >= 11 AND MONTH(LAST_DATE) <= 12 then DATE_ADD(MAKEDATE(YEAR(LAST_DATE) , 1), INTERVAL 10 MONTH) 
    end into QUARTER_START_DATE ;

delete from KETTLE.DAY_WISE_NPS_SCORE where BUSINESS_DATE = LAST_DATE;

INSERT INTO KETTLE.DAY_WISE_NPS_SCORE(NPS_CATEGORY, BUSINESS_DATE,
UNIT_ID	,
UNIT_NAME	,
UNIT_CATEGORY	,
TOTAL_TICKET	,
TOTAL_NEGATIVE	,
TOTAL_NEUTRAL	,
TOTAL_POSITIVE	,
POSITIVE_PERCENTAGE	,
NEGATIVE_PERCENTAGE	,
NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	, RANK_OF_THE_DAY)
SELECT	
	'DAILY_OVERALL',
    m.BUSINESS_DATE	,
m.UNIT_ID	,
m.UNIT_NAME	,
m.UNIT_CATEGORY	,
m.TOTAL_TICKET	,
m.TOTAL_NEGATIVE	,
m.TOTAL_NEUTRAL	,
m.TOTAL_POSITIVE	,
m.POSITIVE_PERCENTAGE	,
m.NEGATIVE_PERCENTAGE	,
m.NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
m.NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	,

    COUNT(DISTINCT s2.NPS_SCORE) AS rank
FROM
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            SUM(a.TOTAL_TICKET)TOTAL_TICKET,
            SUM(a.TOTAL_NEGATIVE)TOTAL_NEGATIVE,
            SUM(a.TOTAL_NEUTRAL)TOTAL_NEUTRAL,
            SUM(a.TOTAL_POSITIVE)TOTAL_POSITIVE,
            TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100, 2) NEUTRAL_PERCENTAGE,
            
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END ) AS TOTAL_CAFE_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_CAFE_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_CAFE_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_CAFE_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEUTRAL_PERCENTAGE,
            
             SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END ) AS TOTAL_COD_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_COD_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_COD_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_COD_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEUTRAL_PERCENTAGE,
            
			ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) NPS_SCORE,
           
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2)) CAFE_NPS_SCORE,
        
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2)) COD_NPS_SCORE 
    FROM
        (SELECT
        nd.UNIT_ID,ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM 
        KETTLE.ORDER_NPS_DETAIL nd
        INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID=nd.ORDER_ID
    WHERE         DATE(nd.SURVEY_CREATION_TIME) > LAST_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= DATE_ADD(LAST_DATE , INTERVAL 1 DAY)
    GROUP BY nd.UNIT_ID,ud.ORDER_SOURCE) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE        a.UNIT_ID = ud.UNIT_ID  and ud.UNIT_NAME NOT LIKE '%ODC%' GROUP BY 1,2,3,4) m
        JOIN
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            a.TOTAL_TICKET,
            a.TOTAL_NEGATIVE,
            a.TOTAL_NEUTRAL,
            a.TOTAL_POSITIVE,
            TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEUTRAL / a.TOTAL_TICKET * 100, 2) NEUTRAL_PERCENTAGE,
            ROUND(TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2)) - ROUND(TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2)) NPS_SCORE
    FROM
        (SELECT
        UNIT_ID,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    WHERE
        DATE(nd.SURVEY_CREATION_TIME) > LAST_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= DATE_ADD(LAST_DATE , INTERVAL 1 DAY)
    GROUP BY nd.UNIT_ID) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        a.UNIT_ID = ud.UNIT_ID and ud.UNIT_NAME NOT LIKE '%ODC%') s2 ON (m.NPS_SCORE <= s2.NPS_SCORE)
GROUP BY m.UNIT_ID;
 

 
INSERT INTO KETTLE.DAY_WISE_NPS_SCORE(NPS_CATEGORY, BUSINESS_DATE,
UNIT_ID	,
UNIT_NAME	,
UNIT_CATEGORY	,
TOTAL_TICKET	,
TOTAL_NEGATIVE	,
TOTAL_NEUTRAL	,
TOTAL_POSITIVE	,
POSITIVE_PERCENTAGE	,
NEGATIVE_PERCENTAGE	,
NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	, RANK_OF_THE_DAY)
SELECT	
	'DAILY_NEW_CUSTOMER',
    m.BUSINESS_DATE	,
m.UNIT_ID	,
m.UNIT_NAME	,
m.UNIT_CATEGORY	,
m.TOTAL_TICKET	,
m.TOTAL_NEGATIVE	,
m.TOTAL_NEUTRAL	,
m.TOTAL_POSITIVE	,
m.POSITIVE_PERCENTAGE	,
m.NEGATIVE_PERCENTAGE	,
m.NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
m.NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	,

    COUNT(DISTINCT s2.NPS_SCORE) AS rank
FROM
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            SUM(a.TOTAL_TICKET)TOTAL_TICKET,
            SUM(a.TOTAL_NEGATIVE)TOTAL_NEGATIVE,
            SUM(a.TOTAL_NEUTRAL)TOTAL_NEUTRAL,
            SUM(a.TOTAL_POSITIVE)TOTAL_POSITIVE,
            TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100, 2) NEUTRAL_PERCENTAGE,
            
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END ) AS TOTAL_CAFE_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_CAFE_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_CAFE_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_CAFE_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEUTRAL_PERCENTAGE,
            
             SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END ) AS TOTAL_COD_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_COD_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_COD_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_COD_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEUTRAL_PERCENTAGE,
            
			ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) NPS_SCORE,
           
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2)) CAFE_NPS_SCORE,
        
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2)) COD_NPS_SCORE 
    FROM
        (SELECT
        nd.UNIT_ID,ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM 
        KETTLE.ORDER_NPS_DETAIL nd
        INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID=nd.ORDER_ID
    WHERE       ud.IS_NEW_CUSTOMER = 'Y' AND  DATE(nd.SURVEY_CREATION_TIME) > LAST_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= DATE_ADD(LAST_DATE , INTERVAL 1 DAY)
    GROUP BY nd.UNIT_ID,ud.ORDER_SOURCE) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE        a.UNIT_ID = ud.UNIT_ID  and ud.UNIT_NAME NOT LIKE '%ODC%' GROUP BY 1,2,3,4) m
        JOIN
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            a.TOTAL_TICKET,
            a.TOTAL_NEGATIVE,
            a.TOTAL_NEUTRAL,
            a.TOTAL_POSITIVE,
            TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEUTRAL / a.TOTAL_TICKET * 100, 2) NEUTRAL_PERCENTAGE,
            ROUND(TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2)) - ROUND(TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2)) NPS_SCORE
    FROM
        (SELECT
        nd.UNIT_ID,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd,KETTLE.ORDER_DETAIL ud
    WHERE 
		nd.ORDER_ID = ud.ORDER_ID
        AND ud.IS_NEW_CUSTOMER = 'Y'
        AND DATE(nd.SURVEY_CREATION_TIME) > LAST_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= DATE_ADD(LAST_DATE , INTERVAL 1 DAY)
    GROUP BY nd.UNIT_ID) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        a.UNIT_ID = ud.UNIT_ID and ud.UNIT_NAME NOT LIKE '%ODC%') s2 ON (m.NPS_SCORE <= s2.NPS_SCORE)
GROUP BY m.UNIT_ID;
 
 
INSERT INTO KETTLE.DAY_WISE_NPS_SCORE(NPS_CATEGORY, BUSINESS_DATE,
UNIT_ID	,
UNIT_NAME	,
UNIT_CATEGORY	,
TOTAL_TICKET	,
TOTAL_NEGATIVE	,
TOTAL_NEUTRAL	,
TOTAL_POSITIVE	,
POSITIVE_PERCENTAGE	,
NEGATIVE_PERCENTAGE	,
NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	, RANK_OF_THE_DAY)
SELECT	
	'QTD_OVERALL',
    m.BUSINESS_DATE	,
m.UNIT_ID	,
m.UNIT_NAME	,
m.UNIT_CATEGORY	,
m.TOTAL_TICKET	,
m.TOTAL_NEGATIVE	,
m.TOTAL_NEUTRAL	,
m.TOTAL_POSITIVE	,
m.POSITIVE_PERCENTAGE	,
m.NEGATIVE_PERCENTAGE	,
m.NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
m.NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	,

    COUNT(DISTINCT s2.NPS_SCORE) AS rank
FROM
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            SUM(a.TOTAL_TICKET)TOTAL_TICKET,
            SUM(a.TOTAL_NEGATIVE)TOTAL_NEGATIVE,
            SUM(a.TOTAL_NEUTRAL)TOTAL_NEUTRAL,
            SUM(a.TOTAL_POSITIVE)TOTAL_POSITIVE,
            TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100, 2) NEUTRAL_PERCENTAGE,
            
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END ) AS TOTAL_CAFE_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_CAFE_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_CAFE_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_CAFE_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEUTRAL_PERCENTAGE,
            
             SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END ) AS TOTAL_COD_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_COD_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_COD_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_COD_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEUTRAL_PERCENTAGE,
            
			ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) NPS_SCORE,
           
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2)) CAFE_NPS_SCORE,
        
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2)) COD_NPS_SCORE 
    FROM
        (SELECT
        nd.UNIT_ID,ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM 
        KETTLE.ORDER_NPS_DETAIL nd
        INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID=nd.ORDER_ID
    WHERE         DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID,ud.ORDER_SOURCE) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE        a.UNIT_ID = ud.UNIT_ID  and ud.UNIT_NAME NOT LIKE '%ODC%' GROUP BY 1,2,3,4) m
        JOIN
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            a.TOTAL_TICKET,
            a.TOTAL_NEGATIVE,
            a.TOTAL_NEUTRAL,
            a.TOTAL_POSITIVE,
            TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEUTRAL / a.TOTAL_TICKET * 100, 2) NEUTRAL_PERCENTAGE,
            ROUND(TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2)) - ROUND(TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2)) NPS_SCORE
    FROM
        (SELECT
        UNIT_ID,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    WHERE
        DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        a.UNIT_ID = ud.UNIT_ID and ud.UNIT_NAME NOT LIKE '%ODC%') s2 ON (m.NPS_SCORE <= s2.NPS_SCORE)
GROUP BY m.UNIT_ID;
 

 
INSERT INTO KETTLE.DAY_WISE_NPS_SCORE(NPS_CATEGORY, BUSINESS_DATE,
UNIT_ID	,
UNIT_NAME	,
UNIT_CATEGORY	,
TOTAL_TICKET	,
TOTAL_NEGATIVE	,
TOTAL_NEUTRAL	,
TOTAL_POSITIVE	,
POSITIVE_PERCENTAGE	,
NEGATIVE_PERCENTAGE	,
NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	, RANK_OF_THE_DAY)
SELECT	
	'QTD_NEW_CUSTOMER',
    m.BUSINESS_DATE	,
m.UNIT_ID	,
m.UNIT_NAME	,
m.UNIT_CATEGORY	,
m.TOTAL_TICKET	,
m.TOTAL_NEGATIVE	,
m.TOTAL_NEUTRAL	,
m.TOTAL_POSITIVE	,
m.POSITIVE_PERCENTAGE	,
m.NEGATIVE_PERCENTAGE	,
m.NEUTRAL_PERCENTAGE	,
TOTAL_CAFE_TICKETS	,
TOTAL_CAFE_NEGATIVE	,
TOTAL_CAFE_NEUTRAL	,
TOTAL_CAFE_POSITIVE	,
CAFE_POSITIVE_PERCENTAGE	,
CAFE_NEGATIVE_PERCENTAGE	,
CAFE_NEUTRAL_PERCENTAGE	,
TOTAL_COD_TICKETS	,
TOTAL_COD_NEGATIVE	,
TOTAL_COD_NEUTRAL	,
TOTAL_COD_POSITIVE	,
COD_POSITIVE_PERCENTAGE	,
COD_NEGATIVE_PERCENTAGE	,
COD_NEUTRAL_PERCENTAGE	,
m.NPS_SCORE	,
CAFE_NPS_SCORE	,
COD_NPS_SCORE	,

    COUNT(DISTINCT s2.NPS_SCORE) AS rank
FROM
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            SUM(a.TOTAL_TICKET)TOTAL_TICKET,
            SUM(a.TOTAL_NEGATIVE)TOTAL_NEGATIVE,
            SUM(a.TOTAL_NEUTRAL)TOTAL_NEUTRAL,
            SUM(a.TOTAL_POSITIVE)TOTAL_POSITIVE,
            TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100, 2) NEUTRAL_PERCENTAGE,
            
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END ) AS TOTAL_CAFE_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_CAFE_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_CAFE_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_CAFE_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END) *100,2) CAFE_NEUTRAL_PERCENTAGE,
            
             SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END ) AS TOTAL_COD_TICKETS,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) AS TOTAL_COD_NEGATIVE,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END ) AS TOTAL_COD_NEUTRAL,
                        SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) AS TOTAL_COD_POSITIVE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_POSITIVE_PERCENTAGE,
            TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEGATIVE_PERCENTAGE,
             TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEUTRAL END)/
            SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END) *100,2) COD_NEUTRAL_PERCENTAGE,
            
			ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100, 2)) NPS_SCORE,
           
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE<>'COD' THEN a.TOTAL_TICKET END )* 100, 2)) CAFE_NPS_SCORE,
        
            ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_POSITIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2))
		-   ROUND(TRUNCATE(SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_NEGATIVE END ) /SUM(CASE WHEN ORDER_SOURCE='COD' THEN a.TOTAL_TICKET END )* 100, 2)) COD_NPS_SCORE 
    FROM
        (SELECT
        nd.UNIT_ID,ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM 
        KETTLE.ORDER_NPS_DETAIL nd
        INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID=nd.ORDER_ID
    WHERE       ud.IS_NEW_CUSTOMER = 'Y' AND  DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID,ud.ORDER_SOURCE) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE        a.UNIT_ID = ud.UNIT_ID  and ud.UNIT_NAME NOT LIKE '%ODC%' GROUP BY 1,2,3,4) m
        JOIN
    (SELECT
        LAST_DATE BUSINESS_DATE,
            a.UNIT_ID,
            ud.UNIT_NAME,
            ud.UNIT_CATEGORY,
            a.TOTAL_TICKET,
            a.TOTAL_NEGATIVE,
            a.TOTAL_NEUTRAL,
            a.TOTAL_POSITIVE,
            TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2) POSITIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2) NEGATIVE_PERCENTAGE,
            TRUNCATE(a.TOTAL_NEUTRAL / a.TOTAL_TICKET * 100, 2) NEUTRAL_PERCENTAGE,
            ROUND(TRUNCATE(a.TOTAL_POSITIVE / a.TOTAL_TICKET * 100, 2)) - ROUND(TRUNCATE(a.TOTAL_NEGATIVE / a.TOTAL_TICKET * 100, 2)) NPS_SCORE
    FROM
        (SELECT
        nd.UNIT_ID,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd,KETTLE.ORDER_DETAIL ud
    WHERE 
		nd.ORDER_ID = ud.ORDER_ID
        AND ud.IS_NEW_CUSTOMER = 'Y'
        AND DATE(nd.SURVEY_CREATION_TIME) >= QUARTER_START_DATE
            AND DATE(nd.SURVEY_CREATION_TIME) <= LAST_DATE
    GROUP BY nd.UNIT_ID) a, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        a.UNIT_ID = ud.UNIT_ID and ud.UNIT_NAME NOT LIKE '%ODC%') s2 ON (m.NPS_SCORE <= s2.NPS_SCORE)
GROUP BY m.UNIT_ID;




 
        ELSE
                                                LEAVE label_loop;
        END IF;
                               
        SET LAST_DATE = DATE_ADD(LAST_DATE, INTERVAL 1 DAY);
        
        END LOOP label_loop;
 
END$$
DELIMITER ;
