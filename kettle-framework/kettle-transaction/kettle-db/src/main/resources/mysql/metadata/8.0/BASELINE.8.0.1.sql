ALTER TABLE KETTLE_DEV.CUSTOMER_INFO 
ADD COLUMN REF_CODE VARCHAR(15) UNIQUE,
ADD COLUMN IS_REF_SUBSCRIBER VARCHAR(1),
ADD COLUMN REF_ACQUISITION_SOURCE VARCHAR(20),
ADD COLUMN REFERRED_ON TIMESTAMP NULL,
ADD COLUMN REFERRAL_DATA_ID INTEGER,
ADD COLUMN REFERRER_ID INTEGER,
ADD COLUMN IS_REFERRER_AWARDED VARCHAR(1) DEFAULT 'N';

SET FOREIGN_KEY_CHECKS=0;
DROP TABLE IF EXISTS KETTLE_DEV.REFERRAL_MAPPING_DATA;
CREATE TABLE KETTLE_DEV.REFERRAL_MAPPING_DATA (
	REFERRAL_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
	REFERRER_ID INTEGER,
	REFERREL_CODE VARCHAR(15),
	CONTACT_NUMBER VARCHAR(10),
	<PERSON><PERSON><PERSON>ENT_ID INTEGER,
	REFERENT_NAME VARCHAR(50),
	<PERSON><PERSON><PERSON><PERSON>L_SOURCE VARCHAR(20),
	REFERRAL_SOURCE_CATEGORY VARCHAR(10),
	REFERRAL_STATUS VARCHAR(30),
	REFERRAL_SOURCE_URL VARCHAR(50),
	CAMPAIGN_ID VARCHAR(20),
	CREATION_TIME TIMESTAMP NULL,
	LAST_UPDATE_TIME TIMESTAMP NULL
);



CREATE INDEX REFERREL_CODE_REFERRAL_MAPPING_DATA ON 
KETTLE_DEV.REFERRAL_MAPPING_DATA(REFERREL_CODE) USING BTREE;

CREATE INDEX CONTACT_NUMBER_REFERRAL_MAPPING_DATA ON 
KETTLE_DEV.REFERRAL_MAPPING_DATA(CONTACT_NUMBER) USING BTREE;


DROP TABLE IF EXISTS KETTLE_MASTER_DEV.CASH_METADATA;
CREATE TABLE KETTLE_MASTER_DEV.CASH_METADATA(
	METADATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
	METADATA_TYPE VARCHAR(20),
	START_DATE DATE NULL,
	END_DATE DATE NULL,
	METADATA_VALUE VARCHAR(20),
	METADATA_STATUS VARCHAR(10),
	METADATA_VALUE_TYPE VARCHAR(40)
);


INSERT INTO KETTLE_MASTER_DEV.CASH_METADATA (METADATA_ID, METADATA_TYPE, START_DATE, END_DATE, METADATA_VALUE, METADATA_STATUS,METADATA_VALUE_TYPE) 
VALUES ('1', 'TOPUP_REFERRER', '2019-06-01', '3000-06-01', '300', 'ACTIVE','Integer.class');
INSERT INTO KETTLE_MASTER_DEV.CASH_METADATA (METADATA_ID, METADATA_TYPE, START_DATE, END_DATE, METADATA_VALUE, METADATA_STATUS,METADATA_VALUE_TYPE)
VALUES ('2', 'TOPUP_REFERENT', '2019-06-01', '3000-06-01', '300', 'ACTIVE','Integer.class');
INSERT INTO KETTLE_MASTER_DEV.CASH_METADATA (METADATA_ID, METADATA_TYPE, START_DATE, END_DATE, METADATA_VALUE, METADATA_STATUS,METADATA_VALUE_TYPE)
VALUES ('3', 'REDEMPTION', '2019-06-01', '3000-06-01', '100', 'ACTIVE','Integer.class');
INSERT INTO KETTLE_MASTER_DEV.CASH_METADATA (METADATA_ID, METADATA_TYPE, START_DATE, END_DATE, METADATA_VALUE, METADATA_STATUS,METADATA_VALUE_TYPE)
VALUES ('4', 'VALIDITY', '2019-06-01', '3000-06-01', '30', 'ACTIVE','Integer.class');



DROP TABLE IF EXISTS KETTLE_DEV.CASH_DATA;
CREATE TABLE KETTLE_DEV.CASH_DATA(
	CASH_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
	CUSTOMER_ID INTEGER NOT NULL,
    ACCUMULATED_AMOUNT DECIMAL(10,2),
	CURRENT_AMOUNT DECIMAL(10,2),
	REDEEMED_AMOUNT DECIMAL(10,2),
	EXPIRED_AMOUNT DECIMAL(10,2),
	RETAINED_AMOUNT DECIMAL(10,2),
	REFERRAL_CODE VARCHAR(15),
	CREATION_TIME TIMESTAMP  NULL,
	LAST_UPDATE_TIME TIMESTAMP NULL
);

CREATE INDEX CUSTOMER_ID_CASH_DATA ON KETTLE_DEV.CASH_DATA(CUSTOMER_ID) USING BTREE;

DROP TABLE IF EXISTS KETTLE_DEV.CASH_LOG_DATA;
CREATE TABLE KETTLE_DEV.CASH_LOG_DATA(
	CASH_LOG_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
	CASH_DATA_ID INTEGER NOT NULL,
	TRANSACTION_TYPE VARCHAR(20),
	TRANSACTION_CODE VARCHAR(20),
	TRANSACTION_CODE_TYPE VARCHAR(20),
    TRANSACTION_AMOUNT DECIMAL(10,2),
	ORDER_ID INTEGER NULL,
	TRANSACTION_TIME TIMESTAMP NULL,
	FOREIGN KEY (CASH_DATA_ID) REFERENCES KETTLE_DEV.CASH_DATA(CASH_DATA_ID)
);

CREATE INDEX CASH_DATA_ID_CASH_LOG_DATA ON KETTLE_DEV.CASH_LOG_DATA(CASH_DATA_ID) USING BTREE;


DROP TABLE IF EXISTS KETTLE_DEV.CASH_PACKET_DATA;
CREATE TABLE KETTLE_DEV.CASH_PACKET_DATA(
	CASH_PACKET_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
	CUSTOMER_ID INTEGER NOT NULL,
	CASH_DATA_ID INTEGER NOT NULL,
	INITIAL_AMOUNT DECIMAL(10,2),
	REDEEMED_AMOUNT DECIMAL(10,2),
	EXPIRED_AMOUNT DECIMAL(10,2),
	RETAINED_AMOUNT DECIMAL(10,2),
	CURRENT_AMOUNT DECIMAL(10,2),
	EXPIRATION_DATE TIMESTAMP NULL,
	INITIAL_EXPIRATION_DATE TIMESTAMP NULL,
	TRANSACTION_CODE VARCHAR(20),
	TRANSACTION_CODE_TYPE VARCHAR(20),
	LAST_UPDATE_TIME TIMESTAMP NULL,
	REFERENT_ID INTEGER,
	REFERRAL_DATA_ID INTEGER,
	CREATION_DATE DATE NULL,
	CREATION_TIME TIMESTAMP NULL,
	ACTIVATION_TIME TIMESTAMP NULL,
	EVENT_STATUS VARCHAR(20)
);

CREATE INDEX CUSTOMER_ID_CASH_PACKET_DATA ON KETTLE_DEV.CASH_PACKET_DATA(CUSTOMER_ID) USING BTREE;
CREATE INDEX CASH_DATA_ID_CASH_PACKET_DATA ON KETTLE_DEV.CASH_PACKET_DATA(CASH_DATA_ID) USING BTREE;
CREATE INDEX EXPIRATION_DATE_CASH_PACKET_DATA ON  KETTLE_DEV.CASH_PACKET_DATA(EXPIRATION_DATE) USING BTREE;
CREATE INDEX INITIAL_EXPIRATION_DATE_CASH_PACKET_DATA ON  KETTLE_DEV.CASH_PACKET_DATA(INITIAL_EXPIRATION_DATE) USING BTREE;
CREATE INDEX EVENT_STATUS_CASH_PACKET_DATA ON  KETTLE_DEV.CASH_PACKET_DATA(EVENT_STATUS) USING BTREE;
CREATE INDEX ACTIVATION_TIME_CASH_PACKET_DATA ON  KETTLE_DEV.CASH_PACKET_DATA(ACTIVATION_TIME) USING BTREE;
CREATE INDEX REFERENT_ID_CASH_PACKET_DATA ON KETTLE_DEV.CASH_PACKET_DATA(REFERENT_ID) USING BTREE;


DROP TABLE IF EXISTS KETTLE_DEV.CASH_PACKET_LOG_DATA;
CREATE TABLE KETTLE_DEV.CASH_PACKET_LOG_DATA(
	CASH_PACKET_LOG_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
	CASH_PACKET_ID INTEGER NOT NULL,
	TRANSACTION_TYPE VARCHAR(20),
	TRANSACTION_CODE VARCHAR(20),
	TRANSACTION_CODE_TYPE VARCHAR(20),
	TRANSACTION_AMOUNT DECIMAL(10,2),
	TRANSACTION_REASON VARCHAR(30),
	CASH_LOG_DATA_ID INTEGER NULL,
	TRANSACTION_TIME TIMESTAMP NULL,
	ORDER_ID INTEGER NULL,
	FOREIGN KEY (CASH_PACKET_ID) REFERENCES KETTLE_DEV.CASH_PACKET_DATA(CASH_PACKET_ID),
	FOREIGN KEY (CASH_LOG_DATA_ID) REFERENCES KETTLE_DEV.CASH_LOG_DATA(CASH_LOG_DATA_ID)
);



