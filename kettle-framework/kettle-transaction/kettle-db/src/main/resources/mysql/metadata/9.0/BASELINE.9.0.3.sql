ALTER TABLE KETTLE_DEV.CUSTOMER_INFO 
ADD COLUMN OPT_OUT_FACE_IT VARCHAR(1) NULL;

ALTER TABLE KETTLE_DEV.CUSTOMER_INFO 
ADD COLUMN OPT_OUT_TIME TIMESTAMP NULL;

CREATE INDEX CUSTOMER_INFO_OPT_OUT_FACE_IT ON KETTLE_DEV.CUSTOMER_INFO(OPT_OUT_FACE_IT) USING BTREE;

UPDATE KETTLE_DEV.CUSTOMER_INFO SET OPT_OUT_FACE_IT = 'N';


INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (<PERSON><PERSON>_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME) VALUES ('rekognition-service.rekog.*', 'Rekognition Service', 'ACTIVE', 'REKOGNITION_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS) VALUES ('2', '1111', (select ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'rekognition-service.rekog.*'), 'ACTIVE');

