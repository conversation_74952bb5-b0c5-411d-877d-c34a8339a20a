DROP TABLE IF EXISTS ORDER_EMAIL_NOTIFICATION
;
DROP TABLE IF EXISTS UNIT_TAX_MAPPING
;
DROP TABLE IF EXISTS TAX_PROFILE
;
DROP TABLE IF EXISTS REPORT_OUTPUT_DETAILS
;
DROP TABLE IF EXISTS REPORT_EXECUTION_PARAMS
;
DROP TABLE IF EXISTS REPORT_EXECUTION_DETAIL
;
DROP TABLE IF EXISTS REPORT_PARAMS
;
DROP TABLE IF EXISTS REPORT_ATTRIBUTES
;
DROP TABLE IF EXISTS ATTRIBUTE_DEFINITION
;
DROP TABLE IF EXISTS REPORT_DEFINITION
;
DROP TABLE IF EXISTS CLOSURE_PAYMENT_DETAILS
;
DROP TABLE IF EXISTS CLOSURE_STATUS
;
DROP TABLE IF EXISTS UNIT_CLOSURE_DETAILS
;
DROP TABLE IF EXISTS UNIT_SEQUENCE_ID
;
DROP TABLE IF EXISTS ORDER_ITEM_ADDON
;
DROP TABLE IF EXISTS ORDER_SETTLEMENT
;
DROP TABLE IF EXISTS ORDER_ITEM
;
DROP TABLE IF EXISTS ORDER_DETAIL
;
DROP TABLE IF EXISTS PAYMENT_MODE
;
DROP TABLE IF EXISTS CHANNEL_PARTNER
;
DROP TABLE IF EXISTS EMPLOYEE_SESSION_DETAILS
;
DROP TABLE IF EXISTS UNIT_PRODUCT_PRICING
;
DROP TABLE IF EXISTS UNIT_PRODUCT_MAPPING
;
DROP TABLE IF EXISTS CUSTOMER_ADDRESS_INFO
;
DROP TABLE IF EXISTS CUSTOMER_INFO
;
DROP TABLE IF EXISTS PRODUCT_RELATIONSHIP
;
DROP TABLE IF EXISTS PRODUCT_DETAIL
;
DROP TABLE IF EXISTS VENDOR_DETAIL
;
DROP TABLE IF EXISTS EMPLOYEE_TRAINING_DETAIL
;
DROP TABLE IF EXISTS TRAINING_DESIGNATION_MAPPING
;
DROP TABLE IF EXISTS TRAINING_DETAIL
;
DROP TABLE IF EXISTS EMPLOYEE_UNIT_MAPPING
;
DROP TABLE IF EXISTS EMPLOYEE_PASS_CODE
;
DROP TABLE IF EXISTS EMPLOYEE_DETAIL
;
DROP TABLE IF EXISTS DEPARTMENT_DESIGNATION_MAPPING
;
DROP TABLE IF EXISTS DESIGNATION
;
DROP TABLE IF EXISTS DEPARTMENT
;
DROP TABLE IF EXISTS UNIT_DETAIL
;
DROP TABLE IF EXISTS BUSINESS_DIVISION
;
DROP TABLE IF EXISTS COMPANY_DETAIL
;
DROP TABLE IF EXISTS ADDRESS_INFO
;
DROP TABLE IF EXISTS REF_LOOKUP
;
DROP TABLE IF EXISTS REF_LOOKUP_TYPE
;

CREATE TABLE REF_LOOKUP_TYPE(
RTL_ID INT NOT NULL AUTO_INCREMENT ,
RTL_GROUP VARCHAR(15) NOT NULL,
RTL_CODE VARCHAR(20) NOT NULL,
RTL_NAME VARCHAR(30) NOT NULL,
PRIMARY KEY (RTL_ID),
UNIQUE KEY(RTL_GROUP,RTL_CODE)
);

CREATE TABLE REF_LOOKUP(
RL_ID INT NOT NULL AUTO_INCREMENT,
RTL_ID INT NOT NULL,
RL_CODE VARCHAR(20) NOT NULL,
RL_NAME VARCHAR(30) NOT NULL,
PRIMARY KEY (RL_ID),
UNIQUE KEY(RTL_ID,RL_CODE),
FOREIGN KEY FK_REF_LOOKUP_TPYE_TO_VALUE(RTL_ID)
	REFERENCES REF_LOOKUP_TYPE (RTL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE ADDRESS_INFO (
ADDRESS_ID INT NOT NULL AUTO_INCREMENT,
ADDRESS_LINE_1 VARCHAR (255) NOT NULL,
ADDRESS_LINE_2 VARCHAR (255) NULL,
ADDRESS_LINE_3 VARCHAR (255) NULL,
CITY VARCHAR (128) NOT NULL,
STATE VARCHAR (128) NOT NULL,
COUNTRY	VARCHAR (128) NOT NULL,
ZIPCODE	VARCHAR (40) NOT NULL,
CONTACT_NUM_1 VARCHAR(32) NOT NULL,
CONTACT_NUM_2 VARCHAR(32) NULL,
ADDRESS_TYPE VARCHAR(50) NOT NULL DEFAULT 'RESIDENTIAL',
PRIMARY KEY (ADDRESS_ID)
);

CREATE TABLE COMPANY_DETAIL (
COMPANY_ID INT NOT NULL AUTO_INCREMENT,
COMPANY_NAME VARCHAR (255) NOT NULL,
COMPANY_DESCRIPTION VARCHAR (5000) NOT NULL,
REGD_ADDR_ID INT NOT NULL,
CIN VARCHAR(21) NOT NULL,
SERVICE_TAX_NO VARCHAR(15) NOT NULL,
WEBSITE_ADDR VARCHAR(255) NOT NULL,
PRIMARY KEY (COMPANY_ID),
FOREIGN KEY FK_COMP_REG_ADDR(REGD_ADDR_ID)
	REFERENCES ADDRESS_INFO (ADDRESS_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE BUSINESS_DIVISION (
BUSINESS_DIV_ID INT NOT NULL AUTO_INCREMENT,
BUSINESS_DIV_NAME VARCHAR(255) NOT NULL,
BUSIENSS_DIV_DESC VARCHAR(5000) NOT NULL,
BUSIENSS_DIV_CATEGORY VARCHAR(30) NOT NULL,
COMPANY_ID INT NOT NULL,
PRIMARY KEY (BUSINESS_DIV_ID),
FOREIGN KEY FK_BUSINESS_DIV_COMPANY_ID (COMPANY_ID)
	REFERENCES COMPANY_DETAIL(COMPANY_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE UNIT_DETAIL (
UNIT_ID INT NOT NULL AUTO_INCREMENT,
UNIT_NAME VARCHAR(255) NOT NULL,
UNIT_REGION VARCHAR(25) NOT NULL,
UNIT_CATEGORY VARCHAR(30) NOT NULL,
UNIT_EMAIL VARCHAR(50) NOT NULL,
START_DATE TIMESTAMP NOT NULL,
UNIT_STATUS VARCHAR(15) NOT NULL,
TIN VARCHAR(15) NOT NULL,
UNIT_ADDR_ID INT NOT NULL,
BUSINESS_DIV_ID INT NOT NULL,
PRIMARY KEY (UNIT_ID),
FOREIGN KEY FK_UNIT_ADDR_INFO (UNIT_ADDR_ID)
	REFERENCES ADDRESS_INFO (ADDRESS_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_UNIT_BUSINESS_DIV (BUSINESS_DIV_ID)
	REFERENCES BUSINESS_DIVISION (BUSINESS_DIV_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE DEPARTMENT(
DEPT_ID INT NOT NULL AUTO_INCREMENT,
DEPT_NAME VARCHAR(255) NOT NULL,
DEPT_DESC VARCHAR(500) NOT NULL,
BUSINESS_DIV_ID INT NOT NULL,
PRIMARY KEY (DEPT_ID),
FOREIGN KEY FK_DEPT_BUSINESS_DIV (BUSINESS_DIV_ID)
	REFERENCES BUSINESS_DIVISION (BUSINESS_DIV_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE DESIGNATION (
DESIGNATION_ID INT NOT NULL,
DESIGNATION_NAME VARCHAR(255) NOT NULL,
DESIGNATION_DESC VARCHAR(500) NOT NULL,
PRIMARY KEY (DESIGNATION_ID)
);

CREATE TABLE DEPARTMENT_DESIGNATION_MAPPING (
DEPT_ID INT NOT NULL,
DESIGNATION_ID INT NOT NULL,
PRIMARY KEY(DEPT_ID, DESIGNATION_ID),
FOREIGN KEY FK_DEPARTMENT_MAPPING (DEPT_ID)
	REFERENCES DEPARTMENT(DEPT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_DESIGNATION_MAPPING(DESIGNATION_ID)
	REFERENCES DESIGNATION(DESIGNATION_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE EMPLOYEE_DETAIL (
EMP_ID INT NOT NULL AUTO_INCREMENT,
EMP_NAME VARCHAR(255) NOT NULL,
EMP_GENDER VARCHAR(1) NOT NULL,
EMP_CURRENT_ADDR INT NOT NULL,
EMP_PERMANENT_ADDR INT NOT NULL,
EMP_CONTACT_NUM_1 VARCHAR(32) NOT NULL, 
EMP_CONTACT_NUM_2 VARCHAR(32) NULL, 
DEPTARTMENT_ID INT NOT NULL,
DESIGNATION_ID INT NOT NULL,
EMPLOYMENT_TYPE VARCHAR(10) NOT NULL,
EMPLOYMENT_STATUS VARCHAR(10) NOT NULL,
BIOMETRIC_IDENTIFIER VARCHAR(255) NULL,
JOINING_DATE DATE NOT NULL,
TERMINATION_DATE DATE NULL,
REPORTING_MANAGER_ID INT NULL,
PRIMARY KEY(EMP_ID),
FOREIGN KEY FK_EMP_CURRENT_ADDR (EMP_CURRENT_ADDR)
	REFERENCES ADDRESS_INFO(ADDRESS_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_EMP_PERMANENT_ADDR (EMP_PERMANENT_ADDR)
	REFERENCES ADDRESS_INFO(ADDRESS_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_EMP_DEPARTMENT (DEPTARTMENT_ID)
	REFERENCES DEPARTMENT(DEPT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_EMP_DESIGNATION (DESIGNATION_ID)
	REFERENCES DESIGNATION(DESIGNATION_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_EMP_REPORTING_MANAGER (REPORTING_MANAGER_ID)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE EMPLOYEE_PASS_CODE
(
EMP_SURROGATE_ID INT NOT NULL AUTO_INCREMENT,
EMP_ID INT NOT NULL,
EMP_PASS_CODE VARCHAR(255) NOT NULL,
PRIMARY KEY(EMP_SURROGATE_ID),
LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
FOREIGN KEY FK_EMPLOYEE_PASS_CODE_EMP_DETAIL (EMP_ID)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE EMPLOYEE_UNIT_MAPPING
(
EMP_UNIT_KEY_ID INT NOT NULL AUTO_INCREMENT,
EMP_ID INT NOT NULL,
UNIT_ID INT NOT NULL,
PRIMARY KEY(EMP_UNIT_KEY_ID),
UNIQUE KEY(EMP_ID, UNIT_ID),
FOREIGN KEY FK_EMPLOYEE_PASS_CODE_EMP_DETAIL(EMP_ID)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_EMPLOYEE_PASS_CODE_UNIT_DETAIL(UNIT_ID)
	REFERENCES UNIT_DETAIL(UNIT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
    
);

CREATE TABLE TRAINING_DETAIL(
TRAINING_ID INT NOT NULL AUTO_INCREMENT,
TRAINING_NAME VARCHAR(255) NOT NULL,
TRAINING_DESC VARCHAR(5000) NOT NULL,
IS_MATERIAL_AVAILABLE VARCHAR(1) NOT NULL,
ON_JOB_TRAINING VARCHAR(1) NOT NULL,
PRIMARY KEY(TRAINING_ID)
);

CREATE TABLE TRAINING_DESIGNATION_MAPPING (
TRAINING_ID INT NOT NULL,
DEPARTMENT_ID INT NOT NULL,
DESIGNATION_ID INT NOT NULL,
IS_MANDATORY VARCHAR(1) NOT NULL,
PRIMARY KEY(TRAINING_ID, DEPARTMENT_ID, DESIGNATION_ID),
FOREIGN KEY FK_TRAINING_DESIGNATION_MAPPING_TRAINING(TRAINING_ID)
	REFERENCES TRAINING_DETAIL(TRAINING_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_TRAINING_DESIGNATION_MAPPING_DEPARTMENT(DEPARTMENT_ID)
	REFERENCES DEPARTMENT(DEPT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_TRAINING_DESIGNATION_MAPPING_DESIGNATION(DESIGNATION_ID)
	REFERENCES DESIGNATION(DESIGNATION_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE

);


CREATE TABLE EMPLOYEE_TRAINING_DETAIL(
EMP_ID INT NOT NULL,
TRAINING_ID INT NOT NULL,
TRAINING_STATUS VARCHAR(15) NOT NULL,
TRAINING_START_DATE DATE NULL,
TRAINING_END_DATE DATE NULL,
TRAINIED_BY INT NULL,
TRAINING_SCORE INT NULL,
NEEDS_RE_TRAINING VARCHAR(1) NOT NULL,
TRAINING_FEEDBACK VARCHAR(5000) NULL,
IN_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
OUT_TMSTMP TIMESTAMP NOT NULL DEFAULT '2030-12-01 00:00:00',
PRIMARY KEY (EMP_ID, TRAINING_ID, OUT_TMSTMP),
FOREIGN KEY FK_EMPLOYEE_TRAINING_DETAIL_EMPLOYEE(EMP_ID)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_EMPLOYEE_TRAINING_DETAIL_TRAINER(TRAINIED_BY)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_EMPLOYEE_TRAINING_DETAIL_TRAINING(TRAINING_ID)
	REFERENCES TRAINING_DETAIL(TRAINING_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE VENDOR_DETAIL (
VENDOR_ID INT NOT NULL AUTO_INCREMENT,
VENDOR_NAME VARCHAR (255) NOT NULL,
VENDOR_DESCRIPTION VARCHAR (5000) NOT NULL,
VENDOR_ADDR_ID INT NOT NULL,
TIN VARCHAR(15) NOT NULL,
PRIMARY KEY (VENDOR_ID),
FOREIGN KEY FK_VENDOR_ADDR_ID_ADDR(VENDOR_ADDR_ID)
	REFERENCES ADDRESS_INFO (ADDRESS_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE PRODUCT_DETAIL (
PRODUCT_ID INT NOT NULL AUTO_INCREMENT,
PRODUCT_NAME VARCHAR (255) NOT NULL,
PRODUCT_DESCRIPTION VARCHAR (5000) NOT NULL,
VENDOR_ID INT NOT NULL,
PRODUCT_TYPE INT NOT NULL,
PRODUCT_SUB_TYPE INT NOT NULL,
PRODUCT_STATUS VARCHAR(20) NOT NULL,
ATTRIBUTE VARCHAR(20) NULL,
PRODUCT_START_DATE DATE NOT NULL,
PRODUCT_END_DATE DATE NOT NULL DEFAULT '9999-12-01',
PRODUCT_SKU_CODE VARCHAR (30) NOT NULL,
DIMENSION_CODE INT NOT NULL DEFAULT 1,
PRICE_TYPE VARCHAR(10) NOT NULL DEFAULT 'NET_PRICE',
ADDITIONAL_ITEM_TYPES INT NULL,
IN_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
OUT_TMSTMP TIMESTAMP NOT NULL DEFAULT '2030-12-01 00:00:00',
PRIMARY KEY (PRODUCT_ID),
FOREIGN KEY FK_VENDOR_PRODUCT_DETAIL(VENDOR_ID)
	REFERENCES VENDOR_DETAIL(VENDOR_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_DIMENSION_CODE_RTL_TYPE(DIMENSION_CODE)
	REFERENCES REF_LOOKUP_TYPE(RTL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ADDITIONAL_ITEM_TYPES_RTL_TYPE(ADDITIONAL_ITEM_TYPES)
	REFERENCES REF_LOOKUP_TYPE(RTL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_PRODUCT_TYPE_REF_LOOKUP(PRODUCT_TYPE)
	REFERENCES REF_LOOKUP_TYPE(RTL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_PRODUCT_SUB_TYPE_REF_LOOKUP(PRODUCT_SUB_TYPE)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE PRODUCT_RELATIONSHIP (
PRODUCT_ID INT NOT NULL,
CONSTITUENT_PRODUCT_ID INT NOT NULL,
RELATIONSHIP_TYPE VARCHAR(10) NOT NULL,
QUANTITY INT NOT NULL,
PRICE_MULTIPLIER DECIMAL(4,4) NOT NULL,
FOREIGN KEY FK_PRODUCT_RELATIONSHIP_PRODUCT(PRODUCT_ID)
	REFERENCES PRODUCT_DETAIL(PRODUCT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_PRODUCT_RELATIONSHIP_CONSTITUENT_PRODUCT(CONSTITUENT_PRODUCT_ID)
	REFERENCES PRODUCT_DETAIL(PRODUCT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE CUSTOMER_INFO(
CUSTOMER_ID INT NOT NULL AUTO_INCREMENT,
FIRST_NAME VARCHAR(50) NULL,
MIDDLE_NAME VARCHAR(50) NULL,
LAST_NAME VARCHAR (50) NULL,
COUNTRY_CODE VARCHAR(5) NOT NULL DEFAULT '+91',
CONTACT_NUMBER VARCHAR(12) NOT NULL,
EMAIL_ID VARCHAR(50) NULL,
IS_NUMBER_VERIFIED VARCHAR(1) NULL,
IS_EMAIL_VERIFIED VARCHAR(1) NULL,
PRIMARY KEY(CUSTOMER_ID),
UNIQUE KEY UK_CONTACT_NUM_COUNTRY(CONTACT_NUMBER, COUNTRY_CODE)
);

CREATE TABLE CUSTOMER_ADDRESS_INFO (
ADDRESS_ID INT NOT NULL AUTO_INCREMENT,
ADDRESS_LINE_1 VARCHAR (255) NOT NULL,
ADDRESS_LINE_2 VARCHAR (255) NULL,
ADDRESS_LINE_3 VARCHAR (255) NULL,
CITY VARCHAR (128) NOT NULL,
STATE VARCHAR (128) NOT NULL,
COUNTRY	VARCHAR (128) NOT NULL,
ZIPCODE	VARCHAR (40) NOT NULL,
CUSTOMER_ID INT NOT NULL,
ADDRESS_TYPE VARCHAR(50) NOT NULL DEFAULT 'RESIDENTIAL',
PRIMARY KEY (ADDRESS_ID),
FOREIGN KEY FK_CUSTOMER_ADDRESS_INFO(CUSTOMER_ID)
	REFERENCES CUSTOMER_INFO(CUSTOMER_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE UNIT_PRODUCT_MAPPING (
UNIT_PROD_REF_ID INT NOT NULL AUTO_INCREMENT,
UNIT_ID INT NOT NULL,
PRODUCT_ID INT NOT NULL,
PRODUCT_STATUS VARCHAR(15) NOT NULL DEFAULT 'ACTIVE',
LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRODUCT_START_DATE DATE NOT NULL,
PRODUCT_END_DATE DATE NOT NULL DEFAULT '9999-12-01',
PRIMARY KEY (UNIT_PROD_REF_ID),
UNIQUE KEY UK_UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID)	,
FOREIGN KEY FK_PRODUCT_UNIT_PRODUCT_MAPPING(PRODUCT_ID)
	REFERENCES PRODUCT_DETAIL(PRODUCT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_UNIT_UNIT_PRODUCT_MAPPING(UNIT_ID)
	REFERENCES UNIT_DETAIL(UNIT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE UNIT_PRODUCT_PRICING(
UNIT_PROD_PRICE_ID INT NOT NULL AUTO_INCREMENT,
UNIT_PROD_REF_ID INT NOT NULL,
DIMENSION_CODE INT NOT NULL DEFAULT 1,
LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRICE DECIMAL(10,2),
PRIMARY KEY(UNIT_PROD_PRICE_ID),
UNIQUE KEY (UNIT_PROD_REF_ID, DIMENSION_CODE),
FOREIGN KEY FK_UNIT_PRODUCT_REF(UNIT_PROD_REF_ID)
	REFERENCES UNIT_PRODUCT_MAPPING(UNIT_PROD_REF_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_UNIT_PRODUCT_DIMENSION_CODE(DIMENSION_CODE)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE

);

CREATE TABLE EMPLOYEE_SESSION_DETAILS(
SESSION_DETAIL_ID INT NOT NULL AUTO_INCREMENT,
EMPLOYEE_ID INT NOT NULL,
UNIT_ID INT NOT NULL,
LOGIN_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
LOGIN_ATTEMPT VARCHAR(30) NULL,
LOGOUT_TIME TIMESTAMP NULL,
SESSION_ID VARCHAR(100) NULL,
PRIMARY KEY(SESSION_DETAIL_ID)
);

CREATE TABLE CHANNEL_PARTNER(
PARTNER_ID INT NOT NULL AUTO_INCREMENT,
PARTNER_CODE VARCHAR(50) NOT NULL,
PARTNER_DISPLAY_NAME VARCHAR(100) NOT NULL,
PRIMARY KEY(PARTNER_ID)
);

CREATE TABLE PAYMENT_MODE(
PAYMENT_MODE_ID INT NOT NULL AUTO_INCREMENT,
MODE_NAME VARCHAR(100) NOT NULL,
MODE_TYPE VARCHAR(15) NOT NULL,
MODE_DESCRIPTION VARCHAR(255) NOT NULL,
SETTLEMENT_TYPE VARCHAR(20) NOT NULL,
PRIMARY KEY(PAYMENT_MODE_ID)
);

CREATE TABLE ORDER_DETAIL (
ORDER_ID INT NOT NULL AUTO_INCREMENT,
GENERATED_ORDER_ID VARCHAR(30) NOT NULL,
UNIT_ORDER_ID INTEGER NOT NULL,
CUSTOMER_ID INT NULL,
EMP_ID INT NOT NULL,
ORDER_STATUS VARCHAR(15) NOT NULL DEFAULT 'UN_SETTLED',
CANCELATION_REASON VARCHAR(100) NULL,
SETTLEMENT_TYPE VARCHAR(10) NULL,
UNIT_ID INT NOT NULL,
HAS_PARCEL VARCHAR(1) NOT NULL DEFAULT 'N',
BILL_START_TIME TIMESTAMP NOT NULL,
BILL_GENERATION_TIME TIMESTAMP NULL,
CHANNEL_PARTNER_ID INT NOT NULL DEFAULT 1,
TOTAL_AMOUNT DECIMAL(10,2) NULL,
TAXABLE_AMOUNT DECIMAL(10,2) NULL,
DISCOUNT_PERCENT DECIMAL(10,2) NULL,
DISCOUNT_AMOUNT DECIMAL(10,2) NULL,
DISCOUNT_REASON_ID INT NULL,
DISCOUNT_REASON VARCHAR(255) NULL,
NET_PRICE_VAT_PERCENT DECIMAL(10,2) NULL,
NET_PRICE_VAT_AMOUNT DECIMAL(10,2) NULL,
MRP_VAT_PERCENT DECIMAL(10,2) NULL,
MRP_VAT_AMOUNT DECIMAL(10,2) NULL,
SERVICE_TAX_PERCENT DECIMAL(10,2) NULL,
SERVICE_TAX_AMOUNT DECIMAL(10,2) NULL,
SURCHARGE_TAX_PERCENT DECIMAL(10,2) NULL,
SURCHARGE_TAX_AMOUNT DECIMAL(10,2) NULL,
GST_PERCENT DECIMAL(10,2) NULL,
GST_AMOUNT DECIMAL(10,2) NULL,
SERVICE_CHARGE_PERCENT DECIMAL(10,2) NULL,
SERVICE_CHARGE_AMOUNT DECIMAL(10,2) NULL,
ROUND_OFF_AMOUNT DECIMAL(10,2) NULL,
SETTLED_AMOUNT DECIMAL(10,2) NULL,
PRIMARY KEY(ORDER_ID),
UNIQUE KEY (GENERATED_ORDER_ID),
FOREIGN KEY FK_ORDER_UNIT(UNIT_ID)
	REFERENCES UNIT_DETAIL(UNIT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ORDER_EMPLOYEE(EMP_ID)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ORDER_CHANNEL_PARTNER(CHANNEL_PARTNER_ID)
	REFERENCES CHANNEL_PARTNER(PARTNER_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ORDER_DISCOUNT_CODE(DISCOUNT_REASON_ID)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE ORDER_ITEM(
ORDER_ITEM_ID INT NOT NULL AUTO_INCREMENT,
ORDER_ID INT NOT NULL,
PRODUCT_ID INT NOT NULL,
PRODUCT_NAME VARCHAR(255) NOT NULL,
QUANTITY INT NOT NULL,
PRICE DECIMAL(10,2) NOT NULL,
HAS_ADDON VARCHAR(1) NOT NULL DEFAULT 'N',
TOTAL_AMOUNT DECIMAL(10,2) NOT NULL,
DISCOUNT_PERCENT DECIMAL(10,2) NULL,
DISCOUNT_AMOUNT DECIMAL(10,2) NULL,
DISCOUNT_REASON_ID INT NULL,
DISCOUNT_REASON VARCHAR(255) NULL,
IS_COMPLIMENTARY VARCHAR(1) NULL,
COMPLIMENTARY_TYPE_ID INT NULL,
COMPLIMENTARY_REASON VARCHAR(150) NULL,
DIMENSION VARCHAR(10) NULL,
BILL_TYPE VARCHAR(10) NOT NULL DEFAULT 'NET_PRICE',
PRIMARY KEY (ORDER_ITEM_ID),
FOREIGN KEY FK_ORDER_ITEM_ORDER(ORDER_ID)
	REFERENCES ORDER_DETAIL(ORDER_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ORDER_ITEM_PRODUCT(PRODUCT_ID)
	REFERENCES PRODUCT_DETAIL(PRODUCT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ORDER_ITEM_COMPLEMENTARY_TYPE(COMPLIMENTARY_TYPE_ID)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ORDER_ITEM_DISCOUNT_CODE(DISCOUNT_REASON_ID)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);


CREATE TABLE ORDER_SETTLEMENT(
SETTLEMENT_ID INT NOT NULL AUTO_INCREMENT,
ORDER_ID INT NOT NULL,
PAYMENT_MODE_ID INT NOT NULL,
AMOUNT_PAID DECIMAL(10,2) NULL,
ROUND_OFF_AMOUNT DECIMAL(10,2) NULL,
PRIMARY KEY(SETTLEMENT_ID),
FOREIGN KEY FK_SETTLEMENT_ORDER(ORDER_ID)
	REFERENCES ORDER_DETAIL(ORDER_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_SETTLEMENT_PAYMENT_MODE(PAYMENT_MODE_ID)
	REFERENCES PAYMENT_MODE(PAYMENT_MODE_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE

);

CREATE TABLE ORDER_ITEM_ADDON(
ORDER_ITEM_ADDON_ID INT NOT NULL AUTO_INCREMENT,
ORDER_ITEM_ID INT NOT NULL,
ADDON_ID INT NOT NULL,
PRIMARY KEY (ORDER_ITEM_ADDON_ID),
FOREIGN KEY FK_ORDER_ITEM_ADDON(ORDER_ITEM_ID)
	REFERENCES ORDER_ITEM(ORDER_ITEM_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ADDON_ID(ADDON_ID)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE

);

CREATE TABLE UNIT_CLOSURE_DETAILS(
CLOSURE_ID INT NOT NULL AUTO_INCREMENT,
UNIT_ID INT NOT NULL,
BUSINESS_DATE DATE NOT NULL,
CLOSURE_START_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
CLOSURE_END_TIME TIMESTAMP NULL,
LAST_ORDER_ID INT NULL,
EMPLOYEE_ID INT NOT NULL,
RECONCILIATION_STATUS VARCHAR(30) NULL,
CLOSURE_COMMENT VARCHAR(500) NULL,
PRIMARY KEY (CLOSURE_ID)
);

CREATE TABLE CLOSURE_STATUS(
CLOSURE_STATUS_ID  INT NOT NULL AUTO_INCREMENT,
CLOSURE_ID INT NOT NULL,
CLOSURE_STATUS VARCHAR(20) NOT NULL,
UPDATE_TMSTMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY (CLOSURE_STATUS_ID),
FOREIGN KEY FK_CLOSURE_STATUS_CLOSURE(CLOSURE_ID)
	REFERENCES UNIT_CLOSURE_DETAILS(CLOSURE_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE CLOSURE_PAYMENT_DETAILS(
PAYMENT_CLOSURE_ID INT NOT NULL AUTO_INCREMENT,
CLOSURE_ID INT NOT NULL,
PAYMENT_MODE_ID INT NOT NULL,
EXPECTED_AMOUNT DECIMAL(10,2) NULL,
ACTUAL_AMOUNT DECIMAL(10,2) NULL,
RECONCILIATION_STATUS VARCHAR(20) NOT NULL,
PRIMARY KEY (PAYMENT_CLOSURE_ID),
FOREIGN KEY FK_CLOSURE_DETAIL_CLOSURE(CLOSURE_ID)
	REFERENCES UNIT_CLOSURE_DETAILS(CLOSURE_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE

);

CREATE TABLE REPORT_DEFINITION(
REPORT_DEF_ID INT NOT NULL AUTO_INCREMENT,
REPORT_NAME VARCHAR(200) NOT NULL,
REPORT_DESCRIPTION VARCHAR(2000) NOT NULL,
REPORT_TYPE VARCHAR(30) NOT NULL,
AUTO_TRIGGERED VARCHAR(1) NOT NULL,
REPORT_FREQUENCY VARCHAR(20) NOT NULL,
HAS_MULTIPLE_SECTIONS VARCHAR(1) NOT NULL,
PRIMARY KEY(REPORT_DEF_ID)
);


CREATE TABLE ATTRIBUTE_DEFINITION(
ATTRIBUTE_DEF_ID INT NOT NULL AUTO_INCREMENT,
ATTRIBUTE_NAME VARCHAR(30) NOT NULL,
ATTRIBUTE_CODE VARCHAR(30) NOT NULL,
ATTRIBUTE_DESC VARCHAR(100) NOT NULL,
ATTRIBUTE_TYPE VARCHAR(20) NOT NULL,
PRIMARY KEY(ATTRIBUTE_DEF_ID)
);

CREATE TABLE REPORT_ATTRIBUTES(
REPORT_ATTRIBUTES_ID INT NOT NULL AUTO_INCREMENT,
ATTRIBUTE_DEF_ID INT NOT NULL,
REPORT_DEF_ID INT NOT NULL,
STRING_VALUE VARCHAR(255) NULL,
LONG_VALUE LONG NULL,
INTEGER_VALUE INTEGER NULL,
DOUBLE_VALUE DECIMAL(10,2) NULL,
BOOLEAN_VALUE VARCHAR(1) NULL,
DATE_VALUE DATE NULL,
TIMESTAMP_VALUE TIMESTAMP NULL,
PRIMARY KEY(REPORT_ATTRIBUTES_ID),
FOREIGN KEY FK_REPORT_DEFINITION_ATTRIBUTES(REPORT_DEF_ID)
	REFERENCES REPORT_DEFINITION(REPORT_DEF_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ATTRIBUTE_DEFINITION(ATTRIBUTE_DEF_ID)
	REFERENCES ATTRIBUTE_DEFINITION(ATTRIBUTE_DEF_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE REPORT_PARAMS(
REPORT_PARAMS_ID INT NOT NULL AUTO_INCREMENT,
ATTRIBUTE_DEF_ID INT NOT NULL,
REPORT_DEF_ID INT NOT NULL,
PRIMARY KEY(REPORT_PARAMS_ID),
FOREIGN KEY FK_REPORT_DEFINITION_ATTRIBUTES(REPORT_DEF_ID)
	REFERENCES REPORT_DEFINITION(REPORT_DEF_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ATTRIBUTE_DEFINITION(ATTRIBUTE_DEF_ID)
	REFERENCES ATTRIBUTE_DEFINITION(ATTRIBUTE_DEF_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE REPORT_EXECUTION_DETAIL(
EXECUTION_DETAIL_ID INT NOT NULL AUTO_INCREMENT,
REPORT_DEF_ID INT NOT NULL,
CURRENT_STATUS VARCHAR(30) NOT NULL,
PRIMARY KEY(EXECUTION_DETAIL_ID),
FOREIGN KEY FK_REPORT_EXECUTION_DETAIL_REPORT(REPORT_DEF_ID)
	REFERENCES REPORT_DEFINITION(REPORT_DEF_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE REPORT_EXECUTION_PARAMS(
EXECUTION_PARAMS_ID  INT NOT NULL AUTO_INCREMENT,
EXECUTION_DETAIL_ID INT NOT NULL,
REPORT_PARAMS_ID INT NOT NULL,
STRING_VALUE VARCHAR(255) NULL,
LONG_VALUE LONG NULL,
INTEGER_VALUE INTEGER NULL,
DOUBLE_VALUE DECIMAL(10,2) NULL,
BOOLEAN_VALUE VARCHAR(1) NULL,
DATE_VALUE DATE NULL,
TIMESTAMP_VALUE TIMESTAMP NULL,
PRIMARY KEY (EXECUTION_PARAMS_ID),
FOREIGN KEY FK_REPORT_EXECUTION_PARAMS_DETAIL(EXECUTION_DETAIL_ID)
	REFERENCES REPORT_EXECUTION_DETAIL(EXECUTION_DETAIL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
    ,
FOREIGN KEY FK_REPORT_EXECUTION_PARAMS_ATTRIBUTE(REPORT_PARAMS_ID)
	REFERENCES REPORT_PARAMS(REPORT_PARAMS_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE REPORT_OUTPUT_DETAILS(
REPORT_OUTPUT_ID   INT NOT NULL AUTO_INCREMENT,
EXECUTION_DETAIL_ID INT NOT NULL,
OUTPUT_LOCATION VARCHAR(255) NULL,
REPORT_GENERATED VARCHAR(1) NOT NULL,
EMAILED_REPORT VARCHAR(1) NOT NULL,
EMAILED_TO VARCHAR(255) NULL, 
PRIMARY KEY(REPORT_OUTPUT_ID),
FOREIGN KEY FK_REPORT_OUTPUT_DETAILS(EXECUTION_DETAIL_ID)
	REFERENCES REPORT_EXECUTION_DETAIL(EXECUTION_DETAIL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);


CREATE TABLE TAX_PROFILE(
TAX_PROFILE_ID INT NOT NULL AUTO_INCREMENT,
TAX_TYPE VARCHAR(20) NOT NULL,
TAX_NAME VARCHAR (40) NOT NULL,
PRIMARY KEY(TAX_PROFILE_ID)
);

CREATE TABLE UNIT_TAX_MAPPING(
UNIT_TAX_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
TAX_PROFILE_ID INT NOT NULL ,
UNIT_ID INT NOT NULL,
TAX_PERCENTAGE DECIMAL(10,2) NOT NULL,
PROFILE_STATUS VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
STATE VARCHAR(20) NOT NULL,
PRIMARY KEY(UNIT_TAX_MAPPING_ID),
FOREIGN KEY FK_TAX_PROFILE_MAPPING(TAX_PROFILE_ID)
	REFERENCES TAX_PROFILE(TAX_PROFILE_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_TAX_UNIT_ID_MAPPING(UNIT_ID)
	REFERENCES UNIT_DETAIL(UNIT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE UNIT_SEQUENCE_ID (
SEQUENCE_ID INTEGER NOT NULL AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
NEXT_VALUE INTEGER NOT NULL,
PRIMARY KEY (SEQUENCE_ID)
);

CREATE TABLE ORDER_EMAIL_NOTIFICATION (
ORDER_EMAIL_ID INT NOT NULL AUTO_INCREMENT,
ORDER_ID INT NOT NULL,
RETRY_COUNT INT NOT NULL DEFAULT 1,
EMAIL_ADDRESS VARCHAR(50) NULL,
USER_REQUESTED VARCHAR(1) NOT NULL DEFAULT 'N',
IS_EMAIL_DELIVERED VARCHAR(1) NULL DEFAULT 'N',
REQUEST_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
EXECUTION_TIME TIMESTAMP NULL,
ERROR_MESSAGE VARCHAR(5000) NULL,
PRIMARY KEY (ORDER_EMAIL_ID)
);

DELETE FROM ORDER_EMAIL_NOTIFICATION
;
DELETE FROM REPORT_ATTRIBUTES
;
DELETE FROM ATTRIBUTE_DEFINITION
;
DELETE FROM REPORT_DEFINITION
;
DELETE FROM UNIT_TAX_MAPPING
;
DELETE FROM TAX_PROFILE
;
DELETE FROM UNIT_SEQUENCE_ID
;
DELETE FROM ORDER_SETTLEMENT
;
DELETE FROM ORDER_ITEM_ADDON;

DELETE FROM ORDER_ITEM;

DELETE FROM ORDER_DETAIL;

DELETE FROM PAYMENT_MODE
;
DELETE FROM CHANNEL_PARTNER
;
DELETE FROM UNIT_PRODUCT_PRICING
;
DELETE FROM UNIT_PRODUCT_MAPPING
;
DELETE FROM CUSTOMER_ADDRESS_INFO
;
DELETE FROM CUSTOMER_INFO
;
DELETE FROM PRODUCT_RELATIONSHIP
;
DELETE FROM PRODUCT_DETAIL
;
DELETE FROM VENDOR_DETAIL
;
DELETE FROM EMPLOYEE_TRAINING_DETAIL
;
DELETE FROM TRAINING_DESIGNATION_MAPPING
;
DELETE FROM TRAINING_DETAIL
;
DELETE FROM EMPLOYEE_UNIT_MAPPING
;
DELETE FROM EMPLOYEE_PASS_CODE
;
SET FOREIGN_KEY_CHECKS = 0;

DELETE FROM  EMPLOYEE_DETAIL;

SET FOREIGN_KEY_CHECKS = 1;

DELETE FROM DEPARTMENT_DESIGNATION_MAPPING
;
DELETE FROM DESIGNATION
;
DELETE FROM DEPARTMENT
;
DELETE FROM UNIT_DETAIL
;
DELETE FROM BUSINESS_DIVISION
;
DELETE FROM COMPANY_DETAIL
;
DELETE FROM ADDRESS_INFO
;
DELETE FROM REF_LOOKUP
;
DELETE FROM REF_LOOKUP_TYPE
;

##### Data Input

INSERT INTO CUSTOMER_INFO
VALUES 
(1,'Chaayos',NULL,'Receipt','+91','9971098656','<EMAIL>','Y','Y'),
(2,'MOHIT',NULL,'MALIK','+91','9599597740','<EMAIL>','Y','Y'),
(3,'MAYANK',NULL,'MALIK','+91','7837560420','<EMAIL>','N','Y'),
(4,'ATUL',NULL,'VERMA','+91','9999999999','<EMAIL>','N','Y');

INSERT INTO CUSTOMER_ADDRESS_INFO
VALUES
(1000000,'17-A Singar Nagar','Alambagh',NULL,'Lucknow','Uttar Pradesh','India','226005',1,'RESIDENTIAL'),
(1000001,'A-01,Lilac-2','Sector-49',NULL,'Gurgaon','Haryana','India','122101',2,'RESIDENTIAL'),
(1000002,'A-02,ORCHID PETALS','Sector-49',NULL,'Gurgaon','Haryana','India','122101',3,'RESIDENTIAL');

INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,
`ADDRESS_LINE_1`,
`ADDRESS_LINE_2`,
`ADDRESS_LINE_3`,
`CITY`,
`STATE`,
`COUNTRY`,
`ZIPCODE`,
`CONTACT_NUM_1`,
`CONTACT_NUM_2`)
VALUES
(1,
'17-A Singar Nagar',
null,
null,
'Lucknow',
'Uttar Pradesh',
'India',
'226005',
'+91-5222452790',
null);

INSERT INTO COMPANY_DETAIL
(`COMPANY_ID`,
`COMPANY_NAME`,
`COMPANY_DESCRIPTION`,
`REGD_ADDR_ID`,
`CIN`,
`SERVICE_TAX_NO`,
`WEBSITE_ADDR`)
VALUES
(1000,
'Sunshine Teahouse Private Limited',
'Sunshine Teahouse Private Limited',
1,
'U55204UP2012PTC049883',
'AARCS3853MSD001',
'www.chaayos.com');

INSERT INTO BUSINESS_DIVISION
(`BUSINESS_DIV_ID`,
`BUSINESS_DIV_NAME`,
`BUSIENSS_DIV_DESC`,
`BUSIENSS_DIV_CATEGORY`,
`COMPANY_ID`)
VALUES
(1000,
'Chaayos',
'Chaayos Cafe Chain',
'Cafe',
1000);

INSERT INTO DEPARTMENT
(`DEPT_ID`,
`DEPT_NAME`,
`DEPT_DESC`,
`BUSINESS_DIV_ID`)
VALUES
(101,
'Cafe Operations',
'Cafe Operations',
1000);

INSERT INTO DEPARTMENT
(`DEPT_ID`,
`DEPT_NAME`,
`DEPT_DESC`,
`BUSINESS_DIV_ID`)
VALUES
(102,
'Corporate Operations',
'Corporate Operations',
1000);

INSERT INTO DESIGNATION
(`DESIGNATION_ID`,
`DESIGNATION_NAME`,
`DESIGNATION_DESC`)
VALUES
(1001,
'Trainer',
'Trainer');

INSERT INTO DESIGNATION
(`DESIGNATION_ID`,
`DESIGNATION_NAME`,
`DESIGNATION_DESC`)
VALUES
(1002,
'Associate Manager',
'Associate Manager');

INSERT INTO DESIGNATION
(`DESIGNATION_ID`,
`DESIGNATION_NAME`,
`DESIGNATION_DESC`)
VALUES
(1003,
'Manager',
'Manager');

INSERT INTO DEPARTMENT_DESIGNATION_MAPPING
(`DEPT_ID`,
`DESIGNATION_ID`)
VALUES
(101,
1001);

INSERT INTO DEPARTMENT_DESIGNATION_MAPPING
(`DEPT_ID`,
`DESIGNATION_ID`)
VALUES
(101,
1002);

INSERT INTO DEPARTMENT_DESIGNATION_MAPPING
(`DEPT_ID`,
`DESIGNATION_ID`)
VALUES
(101,
1003);


INSERT INTO DEPARTMENT_DESIGNATION_MAPPING
(`DEPT_ID`,
`DESIGNATION_ID`)
VALUES
(102,
1003);



INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(1,
'DIMENSION',
'None',
'None');


INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(3,
'DIMENSION',
'RL',
'Regular, Large');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(4,
'ADDONS',
'HOT_BEVERAGES',
'Hot Beverages');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(1,
1,
'None',
'None');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(30,
3,
'REGULAR',
'Regular');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(31,
3,
'LARGE',
'Large');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(100,
4,
'Parcel',
'Parcel');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(101,
4,
'Tulsi',
'Tulsi');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(102,
4,
'Adrak',
'Adrak');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(103,
4,
'Eliachi',
'Eliachi');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(104,
4,
'Saunf',
'Saunf');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(105,
4,
'Laung',
'Laung');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(106,
4,
'Cinnamon',
'Cinnamon');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(107,
4,
'Masala',
'Masala');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(108,
4,
'Kali Mirch',
'Kali Mirch');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(109,
4,
'Mint',
'Mint');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(110,
4,
'Ajwain',
'Ajwain');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(111,
4,
'Moti Eliachi',
'Moti Eliachi');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(112,
4,
'Hari Mirch',
'Hari Mirch');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(113,
4,
'Extra Tea',
'Extra Tea');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(114,
4,
'Extra Milk',
'Extra Milk');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(115,
4,
'Honey',
'Honey');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(116,
4,
'Mulethi',
'Mulethi');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(117,
4,
'Kesar',
'Kesar');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(25,
'ADDONS',
'COLD_BEVERAGES',
'Cold Beverages');


INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2501,
25,
'Parcel',
'Parcel');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(26,
'ADDONS',
'FOOD',
'Food');


INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2601,
26,
'Parcel',
'Parcel');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2602,
26,
'WrapToSandwich',
'Wrap To Sandwich');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2603,
26,
'Mayonnaise',
'Mayonnaise');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(27,
'ADDONS',
'Others',
'Others');


INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2701,
27,
'Parcel',
'Parcel');


INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(5,
'CATEGORY',
'HotBeverages',
'Hot Beverages');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(6,
'CATEGORY',
'ColdBeverages',
'Cold Beverages');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(7,
'CATEGORY',
'Food',
'Food');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(8,
'CATEGORY',
'Combos',
'Combos');


INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(9,
'CATEGORY',
'Merchandise',
'Merchandise');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(10,
'CATEGORY',
'Others',
'Others');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(501,
5,
'IndianChai',
'Indian Chai');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(502,
5,
'SpecialityTeas',
'Speciality Teas');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(503,
5,
'ClassicTeas',
'Classic Teas');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(504,
5,
'UnChaiHot',
'Un-Chai Hot');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(601,
6,
'HomemadeIceTeas',
'Homemade Ice Teas');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(602,
6,
'RealFruitShakes',
'Real Fruit Shakes');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(603,
6,
'Shakes',
'Shakes');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(604,
6,
'UnChaiCold',
'Un-Chai Cold');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(701,
7,
'Sandwich',
'Sandwich');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(702,
7,
'Wrap',
'Wrap');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(703,
7,
'Nashta',
'Nashta');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(801,
8,
'ComboNashta',
'Combo Nashta');



INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(901,
9,
'ChaiPatti',
'Chai Patti');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(902,
9,
'BigKettle',
'Big Kettle');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(903,
9,
'SmallKettle',
'Small Kettle');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(1001,
10,
'Cakes',
'Cakes');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(1002,
10,
'Cookies',
'Cookies');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(1003,
10,
'Extras',
'Extras');


INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(20,
'DISCOUNT',
'DiscountCode',
'Discount Codes');


INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2000,
20,
'CorporateCustomer',
'Corporate Customer');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2001,
20,
'Promotional',
'Promotional');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2002,
20,
'UnsatisfiedCustomer',
'Unsatisfied Customer');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2003,
20,
'Loyalty',
'Loyalty');
INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2004,
20,
'MarketingVoucher',
'Marketing Voucher');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2005,
20,
'ODC',
'ODC'); 

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2006,
20,
'Other',
'Other');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(21,
'COMPLIMENTARY',
'ComplimentaryCode',
'Complimentary Code');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2100,
21,
'EmployeeMeal',
'Employee Meal');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2101,
21,
'Loyalty',
'Loyalty');

INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2102,
21,
'UnsatisfiedCustomer',
'Unsatisfied Customer');


INSERT INTO REF_LOOKUP
(`RL_ID`,
`RTL_ID`,
`RL_CODE`,
`RL_NAME`)
VALUES
(2103,
21,
'AnyOther',
'Any Other');


INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(1,
'CHAAYOS',
'Chaayos (Dine In)');

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(2,
'CHAAYOS_DELIVERY',
'Chaayos (Delivery)');

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(3,
'ZOMATO',
'Zomato');

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(4,
'TINYOWL',
'Tiny Owl');

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(5,
'FOODPANDA',
'Food Panda');

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(6,
'SWIGGY',
'Swiggy');

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(7,
'OLACAFE',
'OLA Cafe');            
            
INSERT INTO PAYMENT_MODE
(`PAYMENT_MODE_ID`,
`MODE_NAME`,
`MODE_TYPE`,
`MODE_DESCRIPTION`,
`SETTLEMENT_TYPE`)
VALUES
(1,
'Cash',
'CASH',
'Cash Payment',
'DEBIT'
);

            
INSERT INTO PAYMENT_MODE
(`PAYMENT_MODE_ID`,
`MODE_NAME`,
`MODE_TYPE`,
`MODE_DESCRIPTION`,
`SETTLEMENT_TYPE`)
VALUES
(2,
'CreditDebitCard',
'CARD',
'Visa/Master Card',
'DEBIT'
);

            
INSERT INTO PAYMENT_MODE
(`PAYMENT_MODE_ID`,
`MODE_NAME`,
`MODE_TYPE`,
`MODE_DESCRIPTION`,
`SETTLEMENT_TYPE`)
VALUES
(3,
'AMEX',
'AMEX Card',
'AMEX Card',
'DEBIT'
);

            
INSERT INTO PAYMENT_MODE
(`PAYMENT_MODE_ID`,
`MODE_NAME`,
`MODE_TYPE`,
`MODE_DESCRIPTION`,
`SETTLEMENT_TYPE`)
VALUES
(4,
'Sodexo',
'COUPON',
'Sodexo Coupon',
'CREDIT'
);

            
INSERT INTO PAYMENT_MODE
(`PAYMENT_MODE_ID`,
`MODE_NAME`,
`MODE_TYPE`,
`MODE_DESCRIPTION`,
`SETTLEMENT_TYPE`)
VALUES
(5,
'TicketRestaurant',
'COUPON',
'Ticket Restaurant',
'CREDIT'
);
            
INSERT INTO PAYMENT_MODE
(`PAYMENT_MODE_ID`,
`MODE_NAME`,
`MODE_TYPE`,
`MODE_DESCRIPTION`,
`SETTLEMENT_TYPE`)
VALUES
(6,
'Credit',
'CREDIT',
'Credit',
'CREDIT'
);

INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`)
VALUES
(100,'No-71, Ground Floor,Good Earth City Centre','Sector-50',null,'Gurgaon','Haryana','India','122018','+91-9717779761',null),
(101,'Building No:5, DLF cyber city','Near Indian Oil Petrol Pump',null,'Gurgaon','Haryana','India','122002','+91-***********',null),
(102,'Tower B , Ground Floor, DLF IT Park(Noida)','Sector-62',null,'Noida','Uttar Pradesh','India','201309','+91-9971869060',null),
(103,'Unitech Infospace ,Old Delhi Gurgaon Rd','Sector-21',null,'Gurgaon','Haryana','India','122016','+91-***********',null),
(104,'Atrium, Plot no:7, Ground Floor, IHDP Business Park','Sector-127',null,'Noida','Uttar Pradesh','India','201313','+91-9971531030',null),
(105,'SG-006, Ground Floor, GALLERIA Market, DLF Phase IV','Sector-28',null,'Gurgaon','Haryana','India','122009','+91-9650064464',null),
(106,'C-14, SDA Market','Opposite to IIT Gate, Hauz Khas',null,'New Delhi','New Delhi','India','110016','+91-9717779837',null),
(107,'No-18,NWA Club Road','Punjabi Bagh',null,'New Delhi','New Delhi','India','110026','+91-9717477289','011-39595097'),
(108,'No-26, 1st Floor','Hauz Khas Village',null,'New Delhi','New Delhi','India','110017',' +91-9599597741',null),
(10000,'17-A','SINGAR NAGAR',null,'Lucknow','Uttar Pradesh','India','210006','+91-',null),
(10001,'78 A','Street No 8','Khajoori Khas','Delhi','New Delhi','India','110094','+91-9015406073',null),
(10002,'U-Block 44/21' ,null,null, 'Gurgaon','Haryana','India','123456','+91-8800753456',null),
(10003,'H-no:140 A','Top Floor,katwariya sarai',null,'New delhi','New Delhi','India','110011','+************0','+91-9716062856'),
(10004,'vpo-karmanpur','distt-ballia',null,'ballia','karmanpur','India','277208','+************0','+91-9716062856'),
(10005,'RZM 15','VIJAY ENCLAVE',null,'NEW DELHI','NEW DELHI','India','110045','+91-9968114731',null),
(10006,'h.no-269','c-2,new palam vihar,near water tank',null,'gurgaon','Haryana','India','122001','+91-9811668751',null),
(10007,'C4B/295B','Janak puri',null,'New Delhi','New Delhi','India','110058','+91-8377095504','+91-7827016910'),
(10008,'189 A,Street # 12, Gopal Nagar','Najafgarh',null,'New delhi','New Delhi','India','110043','+91-7835800585','+91-9540445948'),
(10009,'R-21','ANAND VIHAR UTTAM NAGAR',null,'New delhi','New Delhi','India','110059','+91-9711184700','+91-7530876041'),
(10010,'Salarpur','Bhangel',null,'Noida','Uttar Pradesh','India','123456','+91-9911901274',null),
(10011,'H.No.- 37,Street#15','Vill-Ismailpur, Near Shiv Enclave',null,'Faridabad','Haryana','India','123456','+91-9716046663','+91-9210465723'),
(10012,'H. No.-113/1A','Munirka Village',null,'New Delhi','New Delhi','India','110067','+91-9871050225','+91-9540660638'),
(10013,'VPO-Ustehar,Near Sheetla Mandir','Teh - Baijnath',null,'Kangra','Himachal Pradesh','India','176125','+91-9871050225','+91-9540660638');

INSERT INTO UNIT_DETAIL
(`UNIT_ID`,`UNIT_NAME`,`UNIT_REGION`,`UNIT_EMAIL`,`UNIT_CATEGORY`,`START_DATE`,`UNIT_STATUS`,`TIN`,`UNIT_ADDR_ID`,`BUSINESS_DIV_ID`)
VALUES
(10000,'Good Earth City Centre','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',100,1000),
(10001,'DLF Cyber City','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',101,1000),
(10002,'DLF IT Park(Noida)','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',102,1000),
(10003,'Unitech Infospace','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',103,1000),
(10004,'IHDP Business Park','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',104,1000),
(10005,'GALLERIA Market','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',105,1000),
(10006,'SDA Market','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',106,1000),
(10007,'Punjabi Bagh','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',107,1000),
(10008,'Hauz Khas Village','NCR','<EMAIL>','CAFE','2014-01-01','Active','***********',108,1000)
;

INSERT INTO `EMPLOYEE_DETAIL`
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(100000,'Nitin Saluja','M',10000,10000,'+91-9971098656',null,102,1003,'FULL_TIME','ACTIVE',null,'2014-03-04','9999-12-01',null),
(100011,'Vinod Kumar Rathore','M',10012,10013,'+91-9871050225','+91-9540660638',102,1003,'FULL_TIME','ACTIVE',null,'2014-03-13','9999-12-01',100000),
(100001,'Jayendra Kanaujiya','M',10001,10001,'+91-9015406073',null,101,1003,'FULL_TIME','ACTIVE',null,'2014-03-04','9999-12-01',100011),
(100002,'Sidharth Chopra','M',10002,10002,'+91-8800753456',null,101,1003,'FULL_TIME','ACTIVE',null,'2014-03-04','9999-12-01',100011),
(100003,'Abhishek Kumar Singh','M',10003,10004,'+************0','+91-9716062856',101,1003,'FULL_TIME','ACTIVE',null,'2014-03-04','9999-12-01',100011),
(100004,'Deepak Dogra','M',10005,10005,'+91-9968114731',null,101,1003,'FULL_TIME','ACTIVE',null,'2014-01-05','9999-12-01',100011),
(100005,'Amjad Hussain','M',10006,10006,'+91-9811668751',null,101,1003,'FULL_TIME','ACTIVE',null,'2013-12-11','9999-12-01',100011),
(100006,'Amardeep Dhoundiyal','M',10007,10007,'+91-8377095504','+91-7827016910',101,1003,'FULL_TIME','ACTIVE',null,'2014-11-22','9999-12-01',100011),
(100007,'Virender Sharma','M',10008,10008,'+91-7835800585','+91-9540445948',101,1003,'FULL_TIME','ACTIVE',null,'2013-11-21','9999-12-01',100011),
(100008,'Gagan Batra','M',10009,10009,'+91-9711184700','+91-7530876041',101,1003,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',100011),
(100009,'Kapil','M',10010,10010,'+91-9911901274',null,101,1003,'FULL_TIME','ACTIVE',null,'2014-04-19','9999-12-01',100011),
(100010,'Prasanjeet','M',10011,10011,'+91-9716046663','+91-9210465723',101,1003,'FULL_TIME','ACTIVE',null,'2014-03-13','9999-12-01',100011);

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100000,10000),
(100000,10001),
(100000,10002),
(100000,10003),
(100000,10004),
(100000,10005),
(100000,10006),
(100000,10007),
(100000,10008),
(100011,10000),
(100011,10001),
(100011,10002),
(100011,10003),
(100011,10004),
(100011,10005),
(100011,10006),
(100011,10007),
(100011,10008),
(100005,10000),
(100001,10001),
(100010,10002),
(100003,10003),
(100009,10004),
(100004,10005),
(100006,10006),
(100008,10007),
(100007,10008);

INSERT INTO VENDOR_DETAIL
(`VENDOR_ID`,
`VENDOR_NAME`,
`VENDOR_DESCRIPTION`,
`VENDOR_ADDR_ID`,
`TIN`)
VALUES
(100,
'Chaayos',
'Chaayos',
100,
'***********');

INSERT INTO PRODUCT_DETAIL (PRODUCT_ID, PRODUCT_NAME, PRODUCT_DESCRIPTION, VENDOR_ID, PRODUCT_TYPE, PRODUCT_SUB_TYPE, PRODUCT_STATUS, ATTRIBUTE, PRODUCT_START_DATE, PRODUCT_END_DATE, PRODUCT_SKU_CODE, DIMENSION_CODE, PRICE_TYPE, ADDITIONAL_ITEM_TYPES, IN_TMSTMP, OUT_TMSTMP)
VALUES
(10,'Desi - Kadak','Extra Strong Tea',100,5,501,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011011',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(20,'Sulemani','-------',100,5,501,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011012',1,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(30,'Cutting','-----',100,5,501,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011013',1,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(50,'Desi - Paani Kam','More Milk Less Water',100,5,501,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011015',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(60,'Gods','Kangra Masala From The Hills',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011016',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
#(70,'Rose Cardamom','Forget Your Spa',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011017',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(80,'Kulhad','Saffron Masala Chai Served In A Traditional Indian Kulhad ',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011018',1,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(90,'Cinnamon Green','Green Tea With A Hint Of Cinnamon',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011019',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(100,'Lemon Green','---------',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011020',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(110,'Mint & Lemon Green','--------',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011021',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(120,'Moroccan Mint','Cheapest Ticket To Morocco',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011022',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(130,'Honey Ginger Lemon','Warm Your Soul',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011023',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(140,'Aam Papad','Heavenly Mango And Chai',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011024',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(150,'Pahadi','Our Version Of Kashmiri Kahwa',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011025',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(160,'Rose Cardamom','Our Best, You Gotta try this',100,5,502,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011026',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(170,'Green','Go Green, Go Lean',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011027',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(180,'English Breakfast','Made in India , named after them',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011028',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(190,'Earl Greay','The Earl left , Tea reamined ',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011029',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(200,'Orange Pekoe','Nothing Orange about it',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011030',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(210,'Darjeeling First Flush','Indias finest',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011031',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(220,'Lemon Grass','Tired of Your Boss, Try This',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011032',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(230,'Camomile','Tired of Your Boss, Try This',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011033',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(240,'Jasmine Tea','Gift from Our Chinese Brothers',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011034',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(250,'Darjeeling Muscatel','Darjeeling 2nd Flush',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011035',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(260,'Lopchu','Golden Flowery Orange Pekoe from Lopchu Estates',100,5,503,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011036',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(270,'Hot Chocolate','--------',100,5,504,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011037',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(280,'Hot Coffee','--------',100,5,504,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011038',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(290,'Bournvita','------',100,5,504,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011039',3,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(300,'Lemon','Lemon Ice Tea',100,6,601,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011040',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(310,'Peach','Peach Ice Tea',100,6,601,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011041',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(320,'Passion Fruit','Passion Fruit Ice Tea',100,6,601,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011042',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(330,'Strawberry','Strawberry Ice Tea',100,6,601,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011043',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(340,'Kiwi','Kiwi Ice Tea',100,6,601,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011044',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(350,'Red Berry','Redberry Ice Tea',100,6,601,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011045',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(360,'Kiwi Shake',' --',100,6,602,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011046',1,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(370,'Strawberry Shake','----',100,6,602,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011047',1,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(380,'Chikoo Shake','---',100,6,602,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011048',1,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(390,'Banana Shake','-----',100,6,602,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011049',1,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(400,'Mango Shake','-----',100,6,602,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011050',1,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(410,'Black Grape Shake','-----',100,6,602,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011051',1,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(420,'Chocolate Shake','----',100,6,603,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011052',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(430,'Rooh afza','-----',100,6,603,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011053',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(440,'Thandai','-----',100,6,603,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011054',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(450,'Mint Lemonade','Freshness Redefined',100,6,604,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011055',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(460,'Modinagar Shikanji','The Original, All The Way From Modinagar',100,6,604,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011056',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(470,'Fresh Lime','----------',100,6,604,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011057',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(480,'Cold Coffee','-------',100,6,604,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011058',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(490,'Homemade Chaach','----------',100,6,604,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011059',3,'NET_PRICE',25,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(500,'Sicilian Chicken','Spicy Italian Style Grilled Chicken In Focaccia Bread',100,7,701,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011060',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(510,'Green Chicken','Chicken,Cheese And Coriander In A Multigrain Bread Sandwich Extremely Flavourful',100,7,701,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011061',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(520,'Pepper Chicken','Pepper Roasted Chicken With A Dash Of Honey In A Croissant',100,7,701,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011062',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(530,'Napoli','Garlic Roasted Mushrooms With Italian Herbs,Olive Oil And Cheese Served With A Focaccia Bread',100,7,701,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011063',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(540,'Spinach Corn Cheese','Fresh Spinach,Crispy Corns,Melted Cheese,Served In A Croissant',100,7,701,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011064',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(550,'Balsamico','Babycorn,Roma Tomatoes And Peppers Dressed With Olive Oil In A Multigrain Bread',100,7,701,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011065',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(560,'Chocolate Bun','Hazelnut Flavoured Nutella Dip With Toasted Bun,Couldnt Get Sweeter',100,7,701,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011066',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(570,'Butter-Chicken Wrap','Delhis Favorite Dish!',100,7,702,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011067',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(580,'Mutton Lazeez Wrap','Melt-in Mouth Spicy Hyderabadi Mutton Kebab',100,7,702,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011068',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(590,'Kadhaiya Paneer','Kadhai Paneer',100,7,702,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011069',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(600,'Chatpata Kebab Wrap','Homemade Soya Kebab With Chatpata Flavours',100,7,702,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011070',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(610,'2 Minutes','Masaledar Maggi With Crunchy Veggies In A Sandwich',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011071',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(620,'Homestyle Aloo','Ghar Jaisa Aloo Sandwich,Guranteed To Remind You Of Mom',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011072',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(630,'Bombay Speacial','Staple Street Food Of Bombay!',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011073',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(640,'Keema Pav','Melt-in Mouth Mutton Keema In A Homemade Pav',100,7,703,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011074',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(650,'Egg Bun','Breakfast Staple , Chopped Boiled Eggs With Basil, Crunchy Corn And Mayo In A Sesame Bun',100,7,703,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011075',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(660,'Poha','Maa Ke Haath Ka Poha- With The Tanginess Of Lemon And Crispiness Of Peanuts',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011076',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(670,'Vada Pav','A Bombay Favorite-Spicy Potato Vada With Hari Chutney And Lasun Chutney In A Homemade Pav',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011077',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(680,'Bun Bhujia','Everyday Comfort Food-Bun Maska With Crunchy Bhujia',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011078',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(690,'Bun Maska','Freshly Toasted Bun With More Than A Jallop Of Butter',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011079',1,'NET_PRICE',26,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(700,'Desi Chai Pack','-----',100,9,901,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011080',1,'MRP',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(710,'Tulsi Adrak Chai Pack','-----',100,9,901,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011081',1,'MRP',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(720,'Green Tea Pack','-----',100,9,901,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011082',1,'MRP',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(730,'Chai Masala Pack ','-----',100,9,901,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011083',1,'MRP',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(740,'Blueberry','',100,10,1001,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011084',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(750,'Banana','',100,10,1001,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011085',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(760,'Carrot','',100,10,1001,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011086',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(770,'Lemon','',100,10,1001,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011087',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(780,'Rusk','',100,10,1002,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011088',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(790,'Jeera','',100,10,1002,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011089',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(800,'Badam Pista','',100,10,1002,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011090',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(810,'Oatmeal','',100,10,1002,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011091',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(820,'Chai Nashta','',100,8,801,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011092',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(830,'Chai Sandwich','',100,8,801,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011093',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(40,'Thandi Chai','-----',100,6,604,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011014',1,'NET_PRICE',4,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(840,'Big Kettle','',100,9,902,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011094',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(850,'Small Kettle','',100,9,903,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011095',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00'),
(860,'Honey','',100,10,1003,'ACTIVE',NULL,'2013-01-01','9999-12-01','CH1011096',1,'NET_PRICE',27,'2015-08-12 11:54:05','2030-12-01 00:00:00');

INSERT INTO TAX_PROFILE
(`TAX_PROFILE_ID`,`TAX_TYPE`,`TAX_NAME`)
VALUES
(1,'NET_PRICE_VAT','VAT'),
(2,'MRP_VAT','VAT'),
(3,'SURCHARGE','Surcharge On Vat'),
(4,'SERVICE_TAX','Service Tax'),
(5,'GST','GST')
;

INSERT INTO UNIT_TAX_MAPPING
(`TAX_PROFILE_ID`,`UNIT_ID`,`TAX_PERCENTAGE`,`PROFILE_STATUS`,`STATE`)
VALUES
(1,10000,12.50,'ACTIVE','HARYANA'),
(2,10000,5.00,'ACTIVE','HARYANA'),
(3,10000,5.00,'ACTIVE','HARYANA'),
(4,10000,5.60,'ACTIVE','HARYANA'),
(5,10000,0.00,'ACTIVE','HARYANA'),
(1,10001,12.50,'ACTIVE','HARYANA'),
(2,10001,5.00,'ACTIVE','HARYANA'),
(3,10001,5.00,'ACTIVE','HARYANA'),
(4,10001,5.60,'ACTIVE','HARYANA'),
(5,10001,0.00,'ACTIVE','HARYANA'),
(1,10003,12.50,'ACTIVE','HARYANA'),
(2,10003,5.00,'ACTIVE','HARYANA'),
(3,10003,5.00,'ACTIVE','HARYANA'),
(4,10003,5.60,'ACTIVE','HARYANA'),
(5,10003,0.00,'ACTIVE','HARYANA'),
(1,10005,12.50,'ACTIVE','HARYANA'),
(2,10005,5.00,'ACTIVE','HARYANA'),
(3,10005,5.00,'ACTIVE','HARYANA'),
(4,10005,5.60,'ACTIVE','HARYANA'),
(5,10005,0.00,'ACTIVE','HARYANA'),
(1,10002,14.00,'ACTIVE','UTTAR PRADESH'),
(2,10002,5.00,'ACTIVE','UTTAR PRADESH'),
(3,10002,0.00,'ACTIVE','UTTAR PRADESH'),
(4,10002,5.60,'ACTIVE','UTTAR PRADESH'),
(5,10002,0.00,'ACTIVE','UTTAR PRADESH'),
(1,10004,14.00,'ACTIVE','UTTAR PRADESH'),
(2,10004,5.00,'ACTIVE','UTTAR PRADESH'),
(3,10004,0.00,'ACTIVE','UTTAR PRADESH'),
(4,10004,5.60,'ACTIVE','UTTAR PRADESH'),
(5,10004,0.00,'ACTIVE','UTTAR PRADESH'),
(1,10006,12.50,'ACTIVE','NEW DELHI'),
(2,10006,5.00,'ACTIVE','NEW DELHI'),
(3,10006,0.00,'ACTIVE','NEW DELHI'),
(4,10006,5.60,'ACTIVE','NEW DELHI'),
(5,10006,0.00,'ACTIVE','NEW DELHI'),
(1,10007,12.50,'ACTIVE','NEW DELHI'),
(2,10007,5.00,'ACTIVE','NEW DELHI'),
(3,10007,0.00,'ACTIVE','NEW DELHI'),
(4,10007,5.60,'ACTIVE','NEW DELHI'),
(5,10007,0.00,'ACTIVE','NEW DELHI'),
(1,10008,12.50,'ACTIVE','NEW DELHI'),
(2,10008,5.00,'ACTIVE','NEW DELHI'),
(3,10008,0.00,'ACTIVE','NEW DELHI'),
(4,10008,5.60,'ACTIVE','NEW DELHI'),
(5,10008,0.00,'ACTIVE','NEW DELHI');

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_START_DATE)
SELECT UD.UNIT_ID, PD.PRODUCT_ID, '2014-01-01' FROM PRODUCT_DETAIL PD, UNIT_DETAIL UD
;

INSERT INTO `UNIT_PRODUCT_PRICING`
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 20),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 30),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 400),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 410),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',42),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',62),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 20),30,'2014-01-01 00:00:00',42),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 20),31,'2014-01-01 00:00:00',62),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',42),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',62),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 400),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 410),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',40),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',60),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 20),1,'2014-01-01 00:00:00',17),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 30),1,'2014-01-01 00:00:00',17),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',40),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',60),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 400),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 410),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',42),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',62),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 20),30,'2014-01-01 00:00:00',42),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 20),31,'2014-01-01 00:00:00',62),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',42),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',62),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 400),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 410),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',40),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',60),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 20),1,'2014-01-01 00:00:00',17),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 30),1,'2014-01-01 00:00:00',17),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',40),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',60),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',115),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 400),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 410),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 20),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 30),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 400),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 410),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 20),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 30),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 400),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 410),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 20),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 30),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 400),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 410),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 10),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 10),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 20),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 30),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 50),30,'2014-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 50),31,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 60),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 60),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 80),1,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 90),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 90),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 100),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 100),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 110),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 110),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 120),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 120),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 130),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 130),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 140),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 140),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 150),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 150),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 160),30,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 160),31,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 170),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 170),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 180),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 180),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 190),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 190),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 200),30,'2014-01-01 00:00:00',79),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 200),31,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 210),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 210),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 220),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 220),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 240),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 240),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 250),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 250),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 260),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 260),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 270),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 270),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 280),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 280),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 290),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 290),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 300),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 300),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 310),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 310),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 320),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 320),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 330),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 330),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 340),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 340),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 350),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 350),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 360),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 370),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 380),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 390),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 400),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 410),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 420),30,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 420),31,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 430),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 430),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 440),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 440),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 450),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 450),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 460),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 460),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 470),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 470),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 480),30,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 480),31,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 490),30,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 490),31,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 500),1,'2014-01-01 00:00:00',159),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 510),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 520),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 530),1,'2014-01-01 00:00:00',149),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 540),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 550),1,'2014-01-01 00:00:00',129),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 560),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 570),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 580),1,'2014-01-01 00:00:00',169),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 590),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 600),1,'2014-01-01 00:00:00',139),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 610),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 620),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 630),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 640),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 650),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 660),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 670),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 680),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 690),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',119),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',99),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 820),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 830),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 820),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 820),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 830),1,'2014-01-01 00:00:00',49),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 700),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 710),1,'2014-01-01 00:00:00',189.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 720),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 730),1,'2014-01-01 00:00:00',284.1),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 40),1,'2014-01-01 00:00:00',69),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 740),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 750),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 760),1,'2014-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 770),1,'2014-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 780),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 790),1,'2014-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 800),1,'2014-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 810),1,'2014-01-01 00:00:00',55),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10000 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10005 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10001 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10002 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10003 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10004 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10006 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10008 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 840),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 850),1,'2014-01-01 00:00:00',0),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10007 AND PRODUCT_ID = 860),1,'2014-01-01 00:00:00',20);



INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
SELECT EMP_ID, '0UxQYmqlaaaY1jDVAZWNQQ==' from EMPLOYEE_DETAIL;
INSERT INTO REPORT_DEFINITION
(`REPORT_DEF_ID`,`REPORT_NAME`,`REPORT_DESCRIPTION`,`REPORT_TYPE`,`AUTO_TRIGGERED`,`REPORT_FREQUENCY`,`HAS_MULTIPLE_SECTIONS`)
VALUES
(100,'Managers Report Per Unit','Managers Report for each unit','SALES','Y','DAILY','Y'),
(102,'Daily Sales Report','Managers Report across all units','SALES','Y','DAILY','Y'),
(103,'Montly Managers Report Per Unit','Managers Report for each unit','SALES','N','DAILY','Y'),
(104,'Monthly Sales Report','Managers Report across all units','SALES','N','DAILY','Y'),
(110,'Managers Adhoc Report Per Unit','Managers Report for each unit','SALES','Y','DAILY','Y');

INSERT INTO ATTRIBUTE_DEFINITION
(`ATTRIBUTE_DEF_ID`,`ATTRIBUTE_NAME`,`ATTRIBUTE_CODE`,`ATTRIBUTE_DESC`,`ATTRIBUTE_TYPE`)
VALUES
(1000,'Report Execution Class','REPORT_EXECUTION_CLASS','Report Execution Class','STRING'),
(1001,'Output Directory','OUTPUT_DIRECTORY','Output Directory','STRING'),
(1003,'Email Id','EMAIL_ID','Email Id','STRING'),
(1004,'Unit Id','UNIT_ID','Unit Id','INTEGER'),
(1005,'Start Time','START_TIME','Start Time','DATE'),
(1006,'End Time','END_TIME','End Time','DATE'),
(1007,'Business Date','BUSINESS_DATE','Business Date','DATE'),
(1008,'Triggered By','TRIGGERED_BY','Triggered By','STRING');

INSERT INTO REPORT_ATTRIBUTES
(`REPORT_ATTRIBUTES_ID`,`REPORT_DEF_ID`,`ATTRIBUTE_DEF_ID`,`STRING_VALUE`,`LONG_VALUE`,`INTEGER_VALUE`,`DOUBLE_VALUE`,`BOOLEAN_VALUE`,`DATE_VALUE`,`TIMESTAMP_VALUE`)
VALUES
(1001,100,1000,'com.stpl.tech.kettle.core.ExecutionImpl',null,null,null,null,null,null),
(1002,100,1001,'${ROOT_DIR}/${ENV_NAME}/UNIT_DAILY_MANAGER_REPORT/${BUSINESS_DATE}/',null,null,null,null,null,null),
(1031,103,1000,'com.stpl.tech.kettle.core.ExecutionImpl',null,null,null,null,null,null),
(1032,103,1001,'${ROOT_DIR}/${ENV_NAME}/UNIT_MONTHLY_MANAGER_REPORT/${BUSINESS_DATE}/',null,null,null,null,null,null),
(1101,110,1000,'com.stpl.tech.kettle.core.ExecutionImpl',null,null,null,null,null,null),
(1102,110,1001,'${ROOT_DIR}/${ENV_NAME}/UNIT_ADHOC_REPORT/${BUSINESS_DATE}/',null,null,null,null,null,null);

INSERT INTO REPORT_PARAMS
(`ATTRIBUTE_DEF_ID`,`REPORT_DEF_ID`)
VALUES
(1004, 100),
(1005, 100),
(1006, 100),
(1007, 100),
(1008, 100),
(1004, 110),
(1005, 110),
(1006, 110),
(1007, 110),
(1008, 110);

INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`)
VALUES
(10020,'Village Bursoli','Post Office Dharasu',null,'Pouri Garwal','Uttarakhand','India','246162','+91-8527475663',null),
(10021,'Dhawalgiri B-5 Flat No 77C','Sector 34',null,'Noida','Uttar Pradesh','India','201301','+91-9999626297',null);

INSERT INTO `EMPLOYEE_DETAIL`
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(100012,'Rohit Wali','M',10021,10021,'+91-9999626297',null,101,1003,'FULL_TIME','ACTIVE',null,'2014-03-04','9999-12-01',100011),
(100013,'Mohan Singh','M',10020,10020,'+91-8527475663',null,101,1003,'FULL_TIME','ACTIVE',null,'2014-03-04','9999-12-01',100011);

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100013,10002),
(100012,10002),
(100012,10004);

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100013,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100012,'0UxQYmqlaaaY1jDVAZWNQQ==' );
