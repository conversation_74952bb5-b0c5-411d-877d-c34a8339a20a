#Script for fixing Loyalty events for a customers with Customer_id = 58332 and 56528

#CONFIRM DETAILS
select * from CUSTOMER_INFO WHERE CONTACT_NUMBER = '9892903332';
#58332	viki +91 9892903332	<EMAIL>

select * from ORDER_DETAIL where GENERATED_ORDER_ID = '7590000876700041';
#513711	7590000876700041	19621	56528

#FIX ORDER
UPDATE ORDER_DETAIL
SET CUSTOMER_ID = 58332
WHERE GENERATED_ORDER_ID = 7590000876700041
AND ORDER_ID = 513711;

--------------------------------------------------------------------------------------------------
#FIX LOYALTY EVENT
SELECT * from LOYALTY_EVENTS WHERE ORDER_ID = 513711;

UPDATE LOYALTY_EVENTS SET CUSTOMER_ID = 58332
WHERE LOYALTY_EVENTS_ID = 416602 AND ORDER_ID = 513711;

select * from LOYALTY_SCORE where CUSTOMER_ID = 56528;
UPDATE LOYALTY_SCORE
SET ACQUIRED_POINTS = ACQUIRED_POINTS - 10, CUMULATIVE_POINTS = CUMULATIVE_POINTS - 10
WHERE LOYALTY_POINTS_ID = 55537 AND CUSTOMER_ID = 56528;

select * from LOYALTY_SCORE where CUSTOMER_ID = 58332;
UPDATE LOYALTY_SCORE
SET ACQUIRED_POINTS = ACQUIRED_POINTS + 10, CUMULATIVE_POINTS = CUMULATIVE_POINTS + 10
WHERE LOYALTY_POINTS_ID = 57341 AND CUSTOMER_ID = 58332;