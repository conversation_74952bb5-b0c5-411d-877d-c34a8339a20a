DROP PROCEDURE IF EXISTS KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA;
DEL<PERSON>ITER $$
CREATE PROCEDURE KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA()
proc_label : BEGIN

DECLARE TILL_DATE DATE;
DECLARE CURR_DATE DATE;

select case when max(BUSINESS_DATE) is null then '2018-01-18' else max(BUSINESS_DATE) end into CURR_DATE from KETTLE_DUMP.ORDER_DETAIL;
select case when max(LAST_ORDER_DATE) is null then '2018-01-18' else max(LAST_ORDER_DATE) end into TILL_DATE from CLM_ANALYTICS.CUSTOMER_DATA_NEW;


IF CURR_DATE IS NOT NULL AND CURR_DATE <= TILL_DATE
THEN 
	LEAVE proc_label;
END IF;

DROP TABLE IF EXISTS CLM_ANALYTICS.ACTIVE_CUSTOMERS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS AS SELECT DISTINCT od.CUSTOMER_ID FROM
    KETTLE_DUMP.ORDER_DETAIL od
WHERE
    od.BUSINESS_DATE = CURR_DATE
        AND od.CUSTOMER_ID > 5
        AND CUSTOMER_ID NOT IN (67456,142315);

CREATE INDEX CUSTOMER_ID_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(CUSTOMER_ID) USING BTREE;

ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS ADD COLUMN IS_NEW VARCHAR(1) NOT NULL DEFAULT 'N';

CREATE INDEX IS_NEW_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(IS_NEW) USING BTREE;

update CLM_ANALYTICS.ACTIVE_CUSTOMERS a 
left outer join CLM_ANALYTICS.CUSTOMER_DATA_NEW c
ON a.CUSTOMER_ID = c.CUSTOMER_ID 
SET IS_NEW = case when c.CUSTOMER_ID IS NOT NULL THEN 'N' ELSE 'Y' END;

DROP TABLE IF EXISTS CLM_ANALYTICS.TEMP_CUSTOMER_DATA;
CREATE TABLE CLM_ANALYTICS.TEMP_CUSTOMER_DATA (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
   	UNIQUE_VISIT_DAYS INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_TEMP_CUSTOMER_ID ON CLM_ANALYTICS.TEMP_CUSTOMER_DATA(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.TEMP_CUSTOMER_DATA
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_DISCOUNT
)

  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT
  FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  AND od.BUSINESS_DATE = CURR_DATE
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
(select
	od.CUSTOMER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC
 FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
  AND od.BUSINESS_DATE <= CURR_DATE
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  GROUP BY od.CUSTOMER_ID
  )a
  SET 
  c.TOTAL_UNITS_VISITED = a.TOTAL_UNITS_VISITED,
  c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS,
  c.MINIMUM_APC = a.MINIMUM_APC,
  c.MAXIMUM_APC = a.MAXIMUM_APC,
  c.DELIVERY_MINIMUM_APC = a.DELIVERY_MINIMUM_APC,
  c.DELIVERY_MAXIMUM_APC = a.DELIVERY_MAXIMUM_APC,
  c.DINE_IN_MINIMUM_APC = a.DINE_IN_MINIMUM_APC,
  c.DINE_IN_MAXIMUM_APC = a.DINE_IN_MAXIMUM_APC
  WHERE a.CUSTOMER_ID = c.CUSTOMER_ID;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
        WHERE o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os, KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
    od.ORDER_ID = os.ORDER_ID AND
    od.CUSTOMER_ID = a.CUSTOMER_ID
     AND    od.ORDER_STATUS <> 'CANCELLED'
     AND od.BUSINESS_DATE = CURR_DATE
     GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
        AND o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY o.CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        a.CUSTOMER_ID,
            SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN
                    a.HOT_QUANTITY = 0
                        AND a.HOT_QUANTITY = 0
                        AND a.FOOD_QUANTITY = 0
                        AND a.COLD_QUANTITY = 0
                        AND a.BAKERY_QUANTITY = 0
                        AND a.COMBO_QUANTITY = 0
                        AND a.MERCHANDISE_QUANTITY = 0
                        AND a.OTHERS_QUANTITY = 0
                        AND a.GIFT_CARD_QUANTITY > 0
                THEN
                    1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND od.BUSINESS_DATE = CURR_DATE 
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET = a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
             AND od.BUSINESS_DATE <= CURR_DATE
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
        AND od.CUSTOMER_ID = a.CUSTOMER_ID
        
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = CURR_DATE
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;



update CLM_ANALYTICS.TEMP_CUSTOMER_DATA c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c1,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

INSERT INTO CLM_ANALYTICS.CUSTOMER_DATA_NEW
(CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
TICKET_COUNT,
CANCELLED_TICKET_COUNT,
TICKET_WITH_OFFER,
TICKET_WITH_REDEMPTION,
DINE_IN_TICKET,
DELIVERY_TICKET,
TAKE_AWAY_TICKET,
ZOMATO_TICKET,
SWIGGY_TICKET,
FOOD_PANDA_TICKET,
UBER_EATS_TICKET,
OLD_APP_TICKET,
WEB_APP_TICKET,
CALL_CENTER_TICKET,
OTHER_PARTNER_TICKET,
FIRST_UNIT_ID,
LAST_UNIT_ID,
PEOPLE_PER_TICKET,
MINIMUM_PEOPLE_PER_ORDER,
MAXIMUM_PEOPLE_PER_ORDER,
DAY_GAP_SINCE_LAST_ORDER,
TICKET_WITH_FOOD,
TICKET_WITH_VEG,
TICKET_WITH_NON_VEG,
TICKET_WITH_HOT,
TICKET_WITH_COLD,
TICKET_WITH_BAKERY,
TICKET_WITH_COMBO,
TICKET_WITH_MERCHANDISE,
TICKET_WITH_OTHER,
TICKET_ON_MONDAY,
TICKET_ON_TUESDAY,
TICKET_ON_WEDNESDAY,
TICKET_ON_THURSDAY,
TICKET_ON_FRIDAY,
TICKET_ON_SATURDAY,
TICKET_ON_SUNDAY,
TICKET_ON_WEEKDAY,
TICKET_ON_WEEKEND,
TICKET_IN_BREAKFAST,
TICKET_IN_LUNCH,
TICKET_IN_EVENING,
TICKET_IN_DINNER,
TICKET_IN_POST_DINNER,
TICKET_IN_NIGHT,
CASH_TICKET,
CARD_TICKET,
AMEX_TICKET,
PAYTM_TICKET,
GIFT_CARD_TICKET,
MOBIKWIK_TICKET,
ONLINE_PAYMENT_TICKET,
OTHER_PAYMENT_TICKET,
SPLIT_PAYMENT_TICKET,
LAST_PAYMENT_MODE,
ONE_NPS_TICKET,
TWO_NPS_TICKET,
THREE_NPS_TICKET,
FOUR_NPS_TICKET,
FIVE_NPS_TICKET,
SIX_NPS_TICKET,
SEVEN_NPS_TICKET,
EIGHT_NPS_TICKET,
NINE_NPS_TICKET,
TEN_NPS_TICKET,
LAST_NPS_SCORE,
NEGATIVE_NPS_TICKET,
POSITIVE_NPS_TICKET,
NEUTRAL_NPS_TICKET,
ONE_FEEDBACK_TICKET,
TWO_FEEDBACK_TICKET,
THREE_FEEDBACK_TICKET,
FOUR_FEEDBACK_TICKET,
FIVE_FEEDBACK_TICKET,
LAST_FEEDBACK_SCORE,
TOTAL_SPEND,
TOTAL_DISCOUNT,
TOTAL_APC,
MINIMUM_APC,
MAXIMUM_APC,
DELIVERY_SPEND,
DELIVERY_DISCOUNT,
DELIVERY_APC,
DELIVERY_MINIMUM_APC,
DELIVERY_MAXIMUM_APC,
DINE_IN_SPEND,
DINE_IN_DISCOUNT,
DINE_IN_APC,
DINE_IN_MINIMUM_APC,
DINE_IN_MAXIMUM_APC,
CASH_SPEND,
CARD_SPEND,
AMEX_SPEND,
PAYTM_SPEND,
GIFT_CARD_SPEND,
MOBIKWIK_SPEND,
ONLINE_SPEND,
OTHER_PAYMENT_SPEND,
TICKET_WITH_GIFT_CARD,
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
ZERO_AMOUNT_TICKET_COUNT,
ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
ONLY_GIFT_CARD_TICKET,
DINE_IN_APC_IN_LAST_NIETY_DAYS,
DINE_IN_SPEND_IN_LAST_NINETY_DAYS,
DINE_IN_TICKET_IN_LAST_NINETY_DAYS,
DELIVERY_APC_IN_LAST_NIETY_DAYS,
DELIVERY_SPEND_IN_LAST_NINETY_DAYS,
DELIVERY_TICKET_IN_LAST_NINETY_DAYS,
APC_IN_LAST_NIETY_DAYS,
SPEND_IN_LAST_NINETY_DAYS,
TICKET_IN_LAST_NINETY_DAYS)
select 
t.CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
COALESCE(TICKET_COUNT,0),
COALESCE(CANCELLED_TICKET_COUNT,0),
COALESCE(TICKET_WITH_OFFER,0),
COALESCE(TICKET_WITH_REDEMPTION,0),
COALESCE(DINE_IN_TICKET,0),
COALESCE(DELIVERY_TICKET,0),
COALESCE(TAKE_AWAY_TICKET,0),
COALESCE(ZOMATO_TICKET,0),
COALESCE(SWIGGY_TICKET,0),
COALESCE(FOOD_PANDA_TICKET,0),
COALESCE(UBER_EATS_TICKET,0),
COALESCE(OLD_APP_TICKET,0),
COALESCE(WEB_APP_TICKET,0),
COALESCE(CALL_CENTER_TICKET,0),
COALESCE(OTHER_PARTNER_TICKET,0),
FIRST_UNIT_ID,
LAST_UNIT_ID,
COALESCE(PEOPLE_PER_TICKET,0),
COALESCE(MINIMUM_PEOPLE_PER_ORDER,0),
COALESCE(MAXIMUM_PEOPLE_PER_ORDER,0),
COALESCE(DAY_GAP_SINCE_LAST_ORDER,0),
COALESCE(TICKET_WITH_FOOD,0),
COALESCE(TICKET_WITH_VEG,0),
COALESCE(TICKET_WITH_NON_VEG,0),
COALESCE(TICKET_WITH_HOT,0),
COALESCE(TICKET_WITH_COLD,0),
COALESCE(TICKET_WITH_BAKERY,0),
COALESCE(TICKET_WITH_COMBO,0),
COALESCE(TICKET_WITH_MERCHANDISE,0),
COALESCE(TICKET_WITH_OTHER,0),
COALESCE(TICKET_ON_MONDAY,0),
COALESCE(TICKET_ON_TUESDAY,0),
COALESCE(TICKET_ON_WEDNESDAY,0),
COALESCE(TICKET_ON_THURSDAY,0),
COALESCE(TICKET_ON_FRIDAY,0),
COALESCE(TICKET_ON_SATURDAY,0),
COALESCE(TICKET_ON_SUNDAY,0),
COALESCE(TICKET_ON_WEEKDAY,0),
COALESCE(TICKET_ON_WEEKEND,0),
COALESCE(TICKET_IN_BREAKFAST,0),
COALESCE(TICKET_IN_LUNCH,0),
COALESCE(TICKET_IN_EVENING,0),
COALESCE(TICKET_IN_DINNER,0),
COALESCE(TICKET_IN_POST_DINNER,0),
COALESCE(TICKET_IN_NIGHT,0),
COALESCE(CASH_TICKET,0),
COALESCE(CARD_TICKET,0),
COALESCE(AMEX_TICKET,0),
COALESCE(PAYTM_TICKET,0),
COALESCE(GIFT_CARD_TICKET,0),
COALESCE(MOBIKWIK_TICKET,0),
COALESCE(ONLINE_PAYMENT_TICKET,0),
COALESCE(OTHER_PAYMENT_TICKET,0),
COALESCE(SPLIT_PAYMENT_TICKET,0),
COALESCE(LAST_PAYMENT_MODE,0),
COALESCE(ONE_NPS_TICKET,0),
COALESCE(TWO_NPS_TICKET,0),
COALESCE(THREE_NPS_TICKET,0),
COALESCE(FOUR_NPS_TICKET,0),
COALESCE(FIVE_NPS_TICKET,0),
COALESCE(SIX_NPS_TICKET,0),
COALESCE(SEVEN_NPS_TICKET,0),
COALESCE(EIGHT_NPS_TICKET,0),
COALESCE(NINE_NPS_TICKET,0),
COALESCE(TEN_NPS_TICKET,0),
LAST_NPS_SCORE,
COALESCE(NEGATIVE_NPS_TICKET,0),
COALESCE(POSITIVE_NPS_TICKET,0),
COALESCE(NEUTRAL_NPS_TICKET,0),
COALESCE(ONE_FEEDBACK_TICKET,0),
COALESCE(TWO_FEEDBACK_TICKET,0),
COALESCE(THREE_FEEDBACK_TICKET,0),
COALESCE(FOUR_FEEDBACK_TICKET,0),
COALESCE(FIVE_FEEDBACK_TICKET,0),
LAST_FEEDBACK_SCORE,
COALESCE(TOTAL_SPEND,0),
COALESCE(TOTAL_DISCOUNT,0),
COALESCE(TOTAL_APC,0),
COALESCE(MINIMUM_APC,0),
COALESCE(MAXIMUM_APC,0),
COALESCE(DELIVERY_SPEND,0),
COALESCE(DELIVERY_DISCOUNT,0),
COALESCE(DELIVERY_APC,0),
COALESCE(DELIVERY_MINIMUM_APC,0),
COALESCE(DELIVERY_MAXIMUM_APC,0),
COALESCE(DINE_IN_SPEND,0),
COALESCE(DINE_IN_DISCOUNT,0),
COALESCE(DINE_IN_APC,0),
COALESCE(DINE_IN_MINIMUM_APC,0),
COALESCE(DINE_IN_MAXIMUM_APC,0),
COALESCE(CASH_SPEND,0),
COALESCE(CARD_SPEND,0),
COALESCE(AMEX_SPEND,0),
COALESCE(PAYTM_SPEND,0),
COALESCE(GIFT_CARD_SPEND,0),
COALESCE(MOBIKWIK_SPEND,0),
COALESCE(ONLINE_SPEND,0),
COALESCE(OTHER_PAYMENT_SPEND,0),
COALESCE(TICKET_WITH_GIFT_CARD,0),
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
COALESCE(ZERO_AMOUNT_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT,0),
COALESCE(ONLY_GIFT_CARD_TICKET,0)
from CLM_ANALYTICS.TEMP_CUSTOMER_DATA t, CLM_ANALYTICS.ACTIVE_CUSTOMERS a
where a.CUSTOMER_ID = t.CUSTOMER_ID
and a.IS_NEW = 'Y'
;

UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW a,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA t,
    CLM_ANALYTICS.ACTIVE_CUSTOMERS m 
SET 
    a.TOTAL_UNITS_VISITED = t.TOTAL_UNITS_VISITED,
    a.UNIQUE_VISIT_DAYS = t.UNIQUE_VISIT_DAYS,
    a.LAST_ORDER_ID = t.LAST_ORDER_ID,
    a.LAST_ORDER_DATE = t.LAST_ORDER_DATE,
    a.TICKET_COUNT = a.TICKET_COUNT + COALESCE(t.TICKET_COUNT, 0),
    a.CANCELLED_TICKET_COUNT = a.CANCELLED_TICKET_COUNT + COALESCE(t.CANCELLED_TICKET_COUNT, 0),
    a.TICKET_WITH_OFFER = a.TICKET_WITH_OFFER + COALESCE(t.TICKET_WITH_OFFER, 0),
    a.TICKET_WITH_REDEMPTION = a.TICKET_WITH_REDEMPTION + COALESCE(t.TICKET_WITH_REDEMPTION, 0),
    a.DINE_IN_TICKET = a.DINE_IN_TICKET + COALESCE(t.DINE_IN_TICKET, 0),
    a.DELIVERY_TICKET = a.DELIVERY_TICKET + COALESCE(t.DELIVERY_TICKET, 0),
    a.TAKE_AWAY_TICKET = a.TAKE_AWAY_TICKET + COALESCE(t.TAKE_AWAY_TICKET, 0),
    a.ZOMATO_TICKET = a.ZOMATO_TICKET + COALESCE(t.ZOMATO_TICKET, 0),
    a.SWIGGY_TICKET = a.SWIGGY_TICKET + COALESCE(t.SWIGGY_TICKET, 0),
    a.FOOD_PANDA_TICKET = a.FOOD_PANDA_TICKET + COALESCE(t.FOOD_PANDA_TICKET, 0),
    a.UBER_EATS_TICKET = a.UBER_EATS_TICKET + COALESCE(t.UBER_EATS_TICKET, 0),
    a.OLD_APP_TICKET = a.OLD_APP_TICKET + COALESCE(t.OLD_APP_TICKET, 0),
    a.WEB_APP_TICKET = a.WEB_APP_TICKET + COALESCE(t.WEB_APP_TICKET, 0),
    a.CALL_CENTER_TICKET = a.CALL_CENTER_TICKET + COALESCE(t.CALL_CENTER_TICKET, 0),
    a.OTHER_PARTNER_TICKET = a.OTHER_PARTNER_TICKET + COALESCE(t.OTHER_PARTNER_TICKET, 0),
    a.LAST_UNIT_ID = t.LAST_UNIT_ID,
    a.PEOPLE_PER_TICKET = COALESCE(t.PEOPLE_PER_TICKET, 0),
    a.MINIMUM_PEOPLE_PER_ORDER = COALESCE(t.MINIMUM_PEOPLE_PER_ORDER, 0),
    a.MAXIMUM_PEOPLE_PER_ORDER = COALESCE(t.MAXIMUM_PEOPLE_PER_ORDER, 0),
    a.DAY_GAP_SINCE_LAST_ORDER = COALESCE(t.DAY_GAP_SINCE_LAST_ORDER, 0),
    a.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD + COALESCE(t.TICKET_WITH_FOOD, 0),
    a.TICKET_WITH_VEG = a.TICKET_WITH_VEG + COALESCE(t.TICKET_WITH_VEG, 0),
    a.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG + COALESCE(t.TICKET_WITH_NON_VEG, 0),
    a.TICKET_WITH_HOT = a.TICKET_WITH_HOT + COALESCE(t.TICKET_WITH_HOT, 0),
    a.TICKET_WITH_COLD = a.TICKET_WITH_COLD + COALESCE(t.TICKET_WITH_COLD, 0),
    a.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY + COALESCE(t.TICKET_WITH_BAKERY, 0),
    a.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO + COALESCE(t.TICKET_WITH_COMBO, 0),
    a.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE + COALESCE(t.TICKET_WITH_MERCHANDISE, 0),
    a.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER + COALESCE(t.TICKET_WITH_OTHER, 0),
    a.TICKET_ON_MONDAY = a.TICKET_ON_MONDAY + COALESCE(t.TICKET_ON_MONDAY, 0),
    a.TICKET_ON_TUESDAY = a.TICKET_ON_TUESDAY + COALESCE(t.TICKET_ON_TUESDAY, 0),
    a.TICKET_ON_WEDNESDAY = a.TICKET_ON_WEDNESDAY + COALESCE(t.TICKET_ON_WEDNESDAY, 0),
    a.TICKET_ON_THURSDAY = a.TICKET_ON_THURSDAY + COALESCE(t.TICKET_ON_THURSDAY, 0),
    a.TICKET_ON_FRIDAY = a.TICKET_ON_FRIDAY + COALESCE(t.TICKET_ON_FRIDAY, 0),
    a.TICKET_ON_SATURDAY = a.TICKET_ON_SATURDAY + COALESCE(t.TICKET_ON_SATURDAY, 0),
    a.TICKET_ON_SUNDAY = a.TICKET_ON_SUNDAY + COALESCE(t.TICKET_ON_SUNDAY, 0),
    a.TICKET_ON_WEEKDAY = a.TICKET_ON_WEEKDAY + COALESCE(t.TICKET_ON_WEEKDAY, 0),
    a.TICKET_ON_WEEKEND = a.TICKET_ON_WEEKEND + COALESCE(t.TICKET_ON_WEEKEND, 0),
    a.TICKET_IN_BREAKFAST = a.TICKET_IN_BREAKFAST + COALESCE(t.TICKET_IN_BREAKFAST, 0),
    a.TICKET_IN_LUNCH = a.TICKET_IN_LUNCH + COALESCE(t.TICKET_IN_LUNCH, 0),
    a.TICKET_IN_EVENING = a.TICKET_IN_EVENING + COALESCE(t.TICKET_IN_EVENING, 0),
    a.TICKET_IN_DINNER = a.TICKET_IN_DINNER + COALESCE(t.TICKET_IN_DINNER, 0),
    a.TICKET_IN_POST_DINNER = a.TICKET_IN_POST_DINNER + COALESCE(t.TICKET_IN_POST_DINNER, 0),
    a.TICKET_IN_NIGHT = a.TICKET_IN_NIGHT + COALESCE(t.TICKET_IN_NIGHT, 0),
    a.CASH_TICKET = a.CASH_TICKET + COALESCE(t.CASH_TICKET, 0),
    a.CARD_TICKET = a.CARD_TICKET + COALESCE(t.CARD_TICKET, 0),
    a.AMEX_TICKET = a.AMEX_TICKET + COALESCE(t.AMEX_TICKET, 0),
    a.PAYTM_TICKET = a.PAYTM_TICKET + COALESCE(t.PAYTM_TICKET, 0),
    a.GIFT_CARD_TICKET = a.GIFT_CARD_TICKET + COALESCE(t.GIFT_CARD_TICKET, 0),
    a.MOBIKWIK_TICKET = a.MOBIKWIK_TICKET + COALESCE(t.MOBIKWIK_TICKET, 0),
    a.ONLINE_PAYMENT_TICKET = a.ONLINE_PAYMENT_TICKET + COALESCE(t.ONLINE_PAYMENT_TICKET, 0),
    a.OTHER_PAYMENT_TICKET = a.OTHER_PAYMENT_TICKET + COALESCE(t.OTHER_PAYMENT_TICKET, 0),
    a.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET + COALESCE(t.SPLIT_PAYMENT_TICKET, 0),
    a.LAST_PAYMENT_MODE = t.LAST_PAYMENT_MODE,
    a.ONE_NPS_TICKET = COALESCE(t.ONE_NPS_TICKET, 0),
    a.TWO_NPS_TICKET = COALESCE(t.TWO_NPS_TICKET, 0),
    a.THREE_NPS_TICKET = COALESCE(t.THREE_NPS_TICKET, 0),
    a.FOUR_NPS_TICKET = COALESCE(t.FOUR_NPS_TICKET, 0),
    a.FIVE_NPS_TICKET = COALESCE(t.FIVE_NPS_TICKET, 0),
    a.SIX_NPS_TICKET = COALESCE(t.SIX_NPS_TICKET, 0),
    a.SEVEN_NPS_TICKET = COALESCE(t.SEVEN_NPS_TICKET, 0),
    a.EIGHT_NPS_TICKET = COALESCE(t.EIGHT_NPS_TICKET, 0),
    a.NINE_NPS_TICKET = COALESCE(t.NINE_NPS_TICKET, 0),
    a.TEN_NPS_TICKET = COALESCE(t.TEN_NPS_TICKET, 0),
    a.LAST_NPS_SCORE = t.LAST_NPS_SCORE,
    a.NEGATIVE_NPS_TICKET = COALESCE(t.NEGATIVE_NPS_TICKET, 0),
    a.POSITIVE_NPS_TICKET = COALESCE(t.POSITIVE_NPS_TICKET, 0),
    a.NEUTRAL_NPS_TICKET = COALESCE(t.NEUTRAL_NPS_TICKET, 0),
    a.ONE_FEEDBACK_TICKET = COALESCE(t.ONE_FEEDBACK_TICKET, 0),
    a.TWO_FEEDBACK_TICKET = COALESCE(t.TWO_FEEDBACK_TICKET, 0),
    a.THREE_FEEDBACK_TICKET = COALESCE(t.THREE_FEEDBACK_TICKET, 0),
    a.FOUR_FEEDBACK_TICKET = COALESCE(t.FOUR_FEEDBACK_TICKET, 0),
    a.FIVE_FEEDBACK_TICKET = COALESCE(t.FIVE_FEEDBACK_TICKET, 0),
    a.LAST_FEEDBACK_SCORE = t.LAST_FEEDBACK_SCORE,
    a.TOTAL_SPEND = a.TOTAL_SPEND + COALESCE(t.TOTAL_SPEND, 0),
    a.TOTAL_DISCOUNT = a.TOTAL_DISCOUNT + COALESCE(t.TOTAL_DISCOUNT, 0),
    a.TOTAL_APC = COALESCE(t.TOTAL_APC, 0),
    a.MINIMUM_APC = COALESCE(t.MINIMUM_APC, 0),
    a.MAXIMUM_APC = COALESCE(t.MAXIMUM_APC, 0),
    a.DELIVERY_SPEND = a.DELIVERY_SPEND + COALESCE(t.DELIVERY_SPEND, 0),
    a.DELIVERY_DISCOUNT = a.DELIVERY_DISCOUNT + COALESCE(t.DELIVERY_DISCOUNT, 0),
    a.DELIVERY_APC = COALESCE(t.DELIVERY_APC, 0),
    a.DELIVERY_MINIMUM_APC = COALESCE(t.DELIVERY_MINIMUM_APC, 0),
    a.DELIVERY_MAXIMUM_APC = COALESCE(t.DELIVERY_MAXIMUM_APC, 0),
    a.DINE_IN_SPEND = a.DINE_IN_SPEND + COALESCE(t.DINE_IN_SPEND, 0),
    a.DINE_IN_DISCOUNT = a.DINE_IN_DISCOUNT + COALESCE(t.DINE_IN_DISCOUNT, 0),
    a.DINE_IN_APC = COALESCE(t.DINE_IN_APC, 0),
    a.DINE_IN_MINIMUM_APC = COALESCE(t.DINE_IN_MINIMUM_APC, 0),
    a.DINE_IN_MAXIMUM_APC = COALESCE(t.DINE_IN_MAXIMUM_APC, 0),
    a.CASH_SPEND = a.CASH_SPEND + COALESCE(t.CASH_SPEND, 0),
    a.CARD_SPEND = a.CARD_SPEND + COALESCE(t.CARD_SPEND, 0),
    a.AMEX_SPEND = a.AMEX_SPEND + COALESCE(t.AMEX_SPEND, 0),
    a.PAYTM_SPEND = a.PAYTM_SPEND + COALESCE(t.PAYTM_SPEND, 0),
    a.GIFT_CARD_SPEND = a.GIFT_CARD_SPEND + COALESCE(t.GIFT_CARD_SPEND, 0),
    a.MOBIKWIK_SPEND = a.MOBIKWIK_SPEND + COALESCE(t.MOBIKWIK_SPEND, 0),
    a.ONLINE_SPEND = a.ONLINE_SPEND + COALESCE(t.ONLINE_SPEND, 0),
    a.OTHER_PAYMENT_SPEND = a.OTHER_PAYMENT_SPEND + COALESCE(t.OTHER_PAYMENT_SPEND, 0),
    a.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD + COALESCE(t.TICKET_WITH_GIFT_CARD, 0),
    a.LAST_UNIT_NAME = t.LAST_UNIT_NAME,
    a.ZERO_AMOUNT_TICKET_COUNT = a.ZERO_AMOUNT_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT = a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT = a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0),
    a.ONLY_GIFT_CARD_TICKET = a.ONLY_GIFT_CARD_TICKET + COALESCE(t.ONLY_GIFT_CARD_TICKET, 0)
WHERE
    a.CUSTOMER_ID = t.CUSTOMER_ID
        AND t.CUSTOMER_ID = m.CUSTOMER_ID
        AND m.IS_NEW = 'N';


END$$
DELIMITER ;


DROP PROCEDURE IF EXISTS KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_90_DAYS;
DELIMITER $$
CREATE PROCEDURE KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_90_DAYS()
proc_label : BEGIN

DECLARE TILL_DATE DATE;
DECLARE CURR_DATE DATE;

select case when max(BUSINESS_DATE) is null then '2018-01-18' else max(BUSINESS_DATE) end into CURR_DATE from KETTLE_DUMP.ORDER_DETAIL;
select case when max(LAST_ORDER_DATE) is null then '2018-01-18' else max(LAST_ORDER_DATE) end into TILL_DATE from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS;


IF CURR_DATE IS NOT NULL AND CURR_DATE <= TILL_DATE
THEN 
	LEAVE proc_label;
END IF;

DROP TABLE IF EXISTS CLM_ANALYTICS.ACTIVE_CUSTOMERS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS AS SELECT DISTINCT od.CUSTOMER_ID FROM
    KETTLE_DUMP.ORDER_DETAIL od
WHERE
    od.BUSINESS_DATE = CURR_DATE
        AND od.CUSTOMER_ID > 5
        AND CUSTOMER_ID NOT IN (67456,142315);

CREATE INDEX CUSTOMER_ID_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(CUSTOMER_ID) USING BTREE;

ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS ADD COLUMN IS_NEW VARCHAR(1) NOT NULL DEFAULT 'N';

CREATE INDEX IS_NEW_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(IS_NEW) USING BTREE;

update CLM_ANALYTICS.ACTIVE_CUSTOMERS a 
left outer join CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c
ON a.CUSTOMER_ID = c.CUSTOMER_ID 
SET IS_NEW = case when c.CUSTOMER_ID IS NOT NULL THEN 'N' ELSE 'Y' END;

DROP TABLE IF EXISTS CLM_ANALYTICS.TEMP_CUSTOMER_DATA;
CREATE TABLE CLM_ANALYTICS.TEMP_CUSTOMER_DATA (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    UNIQUE_VISIT_DAYS INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_TEMP_CUSTOMER_ID ON CLM_ANALYTICS.TEMP_CUSTOMER_DATA(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.TEMP_CUSTOMER_DATA
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_DISCOUNT
)

  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT
  FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  AND od.BUSINESS_DATE = CURR_DATE
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
(select
	od.CUSTOMER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC
 FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
   AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -90 DAY)
  AND od.BUSINESS_DATE <= CURR_DATE
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  GROUP BY od.CUSTOMER_ID
  )a
  SET 
  c.TOTAL_UNITS_VISITED = a.TOTAL_UNITS_VISITED,
  c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS,
  c.MINIMUM_APC = a.MINIMUM_APC,
  c.MAXIMUM_APC = a.MAXIMUM_APC,
  c.DELIVERY_MINIMUM_APC = a.DELIVERY_MINIMUM_APC,
  c.DELIVERY_MAXIMUM_APC = a.DELIVERY_MAXIMUM_APC,
  c.DINE_IN_MINIMUM_APC = a.DINE_IN_MINIMUM_APC,
  c.DINE_IN_MAXIMUM_APC = a.DINE_IN_MAXIMUM_APC
  WHERE a.CUSTOMER_ID = c.CUSTOMER_ID;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
        WHERE o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os, KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
    od.ORDER_ID = os.ORDER_ID AND
    od.CUSTOMER_ID = a.CUSTOMER_ID
     AND    od.ORDER_STATUS <> 'CANCELLED'
     AND od.BUSINESS_DATE = CURR_DATE
     GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
        AND o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY o.CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        a.CUSTOMER_ID,
            SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN
                    a.HOT_QUANTITY = 0
                        AND a.HOT_QUANTITY = 0
                        AND a.FOOD_QUANTITY = 0
                        AND a.COLD_QUANTITY = 0
                        AND a.BAKERY_QUANTITY = 0
                        AND a.COMBO_QUANTITY = 0
                        AND a.MERCHANDISE_QUANTITY = 0
                        AND a.OTHERS_QUANTITY = 0
                        AND a.GIFT_CARD_QUANTITY > 0
                THEN
                    1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND od.BUSINESS_DATE = CURR_DATE 
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET = a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -90 DAY)
             AND od.BUSINESS_DATE <= CURR_DATE
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
        AND od.CUSTOMER_ID = a.CUSTOMER_ID
        
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = CURR_DATE
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;



update CLM_ANALYTICS.TEMP_CUSTOMER_DATA c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c1,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

INSERT INTO CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS
(CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
TICKET_COUNT,
CANCELLED_TICKET_COUNT,
TICKET_WITH_OFFER,
TICKET_WITH_REDEMPTION,
DINE_IN_TICKET,
DELIVERY_TICKET,
TAKE_AWAY_TICKET,
ZOMATO_TICKET,
SWIGGY_TICKET,
FOOD_PANDA_TICKET,
UBER_EATS_TICKET,
OLD_APP_TICKET,
WEB_APP_TICKET,
CALL_CENTER_TICKET,
OTHER_PARTNER_TICKET,
FIRST_UNIT_ID,
LAST_UNIT_ID,
PEOPLE_PER_TICKET,
MINIMUM_PEOPLE_PER_ORDER,
MAXIMUM_PEOPLE_PER_ORDER,
DAY_GAP_SINCE_LAST_ORDER,
TICKET_WITH_FOOD,
TICKET_WITH_VEG,
TICKET_WITH_NON_VEG,
TICKET_WITH_HOT,
TICKET_WITH_COLD,
TICKET_WITH_BAKERY,
TICKET_WITH_COMBO,
TICKET_WITH_MERCHANDISE,
TICKET_WITH_OTHER,
TICKET_ON_MONDAY,
TICKET_ON_TUESDAY,
TICKET_ON_WEDNESDAY,
TICKET_ON_THURSDAY,
TICKET_ON_FRIDAY,
TICKET_ON_SATURDAY,
TICKET_ON_SUNDAY,
TICKET_ON_WEEKDAY,
TICKET_ON_WEEKEND,
TICKET_IN_BREAKFAST,
TICKET_IN_LUNCH,
TICKET_IN_EVENING,
TICKET_IN_DINNER,
TICKET_IN_POST_DINNER,
TICKET_IN_NIGHT,
CASH_TICKET,
CARD_TICKET,
AMEX_TICKET,
PAYTM_TICKET,
GIFT_CARD_TICKET,
MOBIKWIK_TICKET,
ONLINE_PAYMENT_TICKET,
OTHER_PAYMENT_TICKET,
SPLIT_PAYMENT_TICKET,
LAST_PAYMENT_MODE,
ONE_NPS_TICKET,
TWO_NPS_TICKET,
THREE_NPS_TICKET,
FOUR_NPS_TICKET,
FIVE_NPS_TICKET,
SIX_NPS_TICKET,
SEVEN_NPS_TICKET,
EIGHT_NPS_TICKET,
NINE_NPS_TICKET,
TEN_NPS_TICKET,
LAST_NPS_SCORE,
NEGATIVE_NPS_TICKET,
POSITIVE_NPS_TICKET,
NEUTRAL_NPS_TICKET,
ONE_FEEDBACK_TICKET,
TWO_FEEDBACK_TICKET,
THREE_FEEDBACK_TICKET,
FOUR_FEEDBACK_TICKET,
FIVE_FEEDBACK_TICKET,
LAST_FEEDBACK_SCORE,
TOTAL_SPEND,
TOTAL_DISCOUNT,
TOTAL_APC,
MINIMUM_APC,
MAXIMUM_APC,
DELIVERY_SPEND,
DELIVERY_DISCOUNT,
DELIVERY_APC,
DELIVERY_MINIMUM_APC,
DELIVERY_MAXIMUM_APC,
DINE_IN_SPEND,
DINE_IN_DISCOUNT,
DINE_IN_APC,
DINE_IN_MINIMUM_APC,
DINE_IN_MAXIMUM_APC,
CASH_SPEND,
CARD_SPEND,
AMEX_SPEND,
PAYTM_SPEND,
GIFT_CARD_SPEND,
MOBIKWIK_SPEND,
ONLINE_SPEND,
OTHER_PAYMENT_SPEND,
TICKET_WITH_GIFT_CARD,
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
ZERO_AMOUNT_TICKET_COUNT,
ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
ONLY_GIFT_CARD_TICKET)
select 
t.CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
COALESCE(TICKET_COUNT,0),
COALESCE(CANCELLED_TICKET_COUNT,0),
COALESCE(TICKET_WITH_OFFER,0),
COALESCE(TICKET_WITH_REDEMPTION,0),
COALESCE(DINE_IN_TICKET,0),
COALESCE(DELIVERY_TICKET,0),
COALESCE(TAKE_AWAY_TICKET,0),
COALESCE(ZOMATO_TICKET,0),
COALESCE(SWIGGY_TICKET,0),
COALESCE(FOOD_PANDA_TICKET,0),
COALESCE(UBER_EATS_TICKET,0),
COALESCE(OLD_APP_TICKET,0),
COALESCE(WEB_APP_TICKET,0),
COALESCE(CALL_CENTER_TICKET,0),
COALESCE(OTHER_PARTNER_TICKET,0),
FIRST_UNIT_ID,
LAST_UNIT_ID,
COALESCE(PEOPLE_PER_TICKET,0),
COALESCE(MINIMUM_PEOPLE_PER_ORDER,0),
COALESCE(MAXIMUM_PEOPLE_PER_ORDER,0),
COALESCE(DAY_GAP_SINCE_LAST_ORDER,0),
COALESCE(TICKET_WITH_FOOD,0),
COALESCE(TICKET_WITH_VEG,0),
COALESCE(TICKET_WITH_NON_VEG,0),
COALESCE(TICKET_WITH_HOT,0),
COALESCE(TICKET_WITH_COLD,0),
COALESCE(TICKET_WITH_BAKERY,0),
COALESCE(TICKET_WITH_COMBO,0),
COALESCE(TICKET_WITH_MERCHANDISE,0),
COALESCE(TICKET_WITH_OTHER,0),
COALESCE(TICKET_ON_MONDAY,0),
COALESCE(TICKET_ON_TUESDAY,0),
COALESCE(TICKET_ON_WEDNESDAY,0),
COALESCE(TICKET_ON_THURSDAY,0),
COALESCE(TICKET_ON_FRIDAY,0),
COALESCE(TICKET_ON_SATURDAY,0),
COALESCE(TICKET_ON_SUNDAY,0),
COALESCE(TICKET_ON_WEEKDAY,0),
COALESCE(TICKET_ON_WEEKEND,0),
COALESCE(TICKET_IN_BREAKFAST,0),
COALESCE(TICKET_IN_LUNCH,0),
COALESCE(TICKET_IN_EVENING,0),
COALESCE(TICKET_IN_DINNER,0),
COALESCE(TICKET_IN_POST_DINNER,0),
COALESCE(TICKET_IN_NIGHT,0),
COALESCE(CASH_TICKET,0),
COALESCE(CARD_TICKET,0),
COALESCE(AMEX_TICKET,0),
COALESCE(PAYTM_TICKET,0),
COALESCE(GIFT_CARD_TICKET,0),
COALESCE(MOBIKWIK_TICKET,0),
COALESCE(ONLINE_PAYMENT_TICKET,0),
COALESCE(OTHER_PAYMENT_TICKET,0),
COALESCE(SPLIT_PAYMENT_TICKET,0),
COALESCE(LAST_PAYMENT_MODE,0),
COALESCE(ONE_NPS_TICKET,0),
COALESCE(TWO_NPS_TICKET,0),
COALESCE(THREE_NPS_TICKET,0),
COALESCE(FOUR_NPS_TICKET,0),
COALESCE(FIVE_NPS_TICKET,0),
COALESCE(SIX_NPS_TICKET,0),
COALESCE(SEVEN_NPS_TICKET,0),
COALESCE(EIGHT_NPS_TICKET,0),
COALESCE(NINE_NPS_TICKET,0),
COALESCE(TEN_NPS_TICKET,0),
LAST_NPS_SCORE,
COALESCE(NEGATIVE_NPS_TICKET,0),
COALESCE(POSITIVE_NPS_TICKET,0),
COALESCE(NEUTRAL_NPS_TICKET,0),
COALESCE(ONE_FEEDBACK_TICKET,0),
COALESCE(TWO_FEEDBACK_TICKET,0),
COALESCE(THREE_FEEDBACK_TICKET,0),
COALESCE(FOUR_FEEDBACK_TICKET,0),
COALESCE(FIVE_FEEDBACK_TICKET,0),
LAST_FEEDBACK_SCORE,
COALESCE(TOTAL_SPEND,0),
COALESCE(TOTAL_DISCOUNT,0),
COALESCE(TOTAL_APC,0),
COALESCE(MINIMUM_APC,0),
COALESCE(MAXIMUM_APC,0),
COALESCE(DELIVERY_SPEND,0),
COALESCE(DELIVERY_DISCOUNT,0),
COALESCE(DELIVERY_APC,0),
COALESCE(DELIVERY_MINIMUM_APC,0),
COALESCE(DELIVERY_MAXIMUM_APC,0),
COALESCE(DINE_IN_SPEND,0),
COALESCE(DINE_IN_DISCOUNT,0),
COALESCE(DINE_IN_APC,0),
COALESCE(DINE_IN_MINIMUM_APC,0),
COALESCE(DINE_IN_MAXIMUM_APC,0),
COALESCE(CASH_SPEND,0),
COALESCE(CARD_SPEND,0),
COALESCE(AMEX_SPEND,0),
COALESCE(PAYTM_SPEND,0),
COALESCE(GIFT_CARD_SPEND,0),
COALESCE(MOBIKWIK_SPEND,0),
COALESCE(ONLINE_SPEND,0),
COALESCE(OTHER_PAYMENT_SPEND,0),
COALESCE(TICKET_WITH_GIFT_CARD,0),
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
COALESCE(ZERO_AMOUNT_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT,0),
COALESCE(ONLY_GIFT_CARD_TICKET,0)
from CLM_ANALYTICS.TEMP_CUSTOMER_DATA t, CLM_ANALYTICS.ACTIVE_CUSTOMERS a
where a.CUSTOMER_ID = t.CUSTOMER_ID
and a.IS_NEW = 'Y'
;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS a,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA t,
    CLM_ANALYTICS.ACTIVE_CUSTOMERS m 
SET 
    a.TOTAL_UNITS_VISITED = t.TOTAL_UNITS_VISITED,
    a.UNIQUE_VISIT_DAYS = t.UNIQUE_VISIT_DAYS,
    a.LAST_ORDER_ID = t.LAST_ORDER_ID,
    a.LAST_ORDER_DATE = t.LAST_ORDER_DATE,
    a.TICKET_COUNT = a.TICKET_COUNT + COALESCE(t.TICKET_COUNT, 0),
    a.CANCELLED_TICKET_COUNT = a.CANCELLED_TICKET_COUNT + COALESCE(t.CANCELLED_TICKET_COUNT, 0),
    a.TICKET_WITH_OFFER = a.TICKET_WITH_OFFER + COALESCE(t.TICKET_WITH_OFFER, 0),
    a.TICKET_WITH_REDEMPTION = a.TICKET_WITH_REDEMPTION + COALESCE(t.TICKET_WITH_REDEMPTION, 0),
    a.DINE_IN_TICKET = a.DINE_IN_TICKET + COALESCE(t.DINE_IN_TICKET, 0),
    a.DELIVERY_TICKET = a.DELIVERY_TICKET + COALESCE(t.DELIVERY_TICKET, 0),
    a.TAKE_AWAY_TICKET = a.TAKE_AWAY_TICKET + COALESCE(t.TAKE_AWAY_TICKET, 0),
    a.ZOMATO_TICKET = a.ZOMATO_TICKET + COALESCE(t.ZOMATO_TICKET, 0),
    a.SWIGGY_TICKET = a.SWIGGY_TICKET + COALESCE(t.SWIGGY_TICKET, 0),
    a.FOOD_PANDA_TICKET = a.FOOD_PANDA_TICKET + COALESCE(t.FOOD_PANDA_TICKET, 0),
    a.UBER_EATS_TICKET = a.UBER_EATS_TICKET + COALESCE(t.UBER_EATS_TICKET, 0),
    a.OLD_APP_TICKET = a.OLD_APP_TICKET + COALESCE(t.OLD_APP_TICKET, 0),
    a.WEB_APP_TICKET = a.WEB_APP_TICKET + COALESCE(t.WEB_APP_TICKET, 0),
    a.CALL_CENTER_TICKET = a.CALL_CENTER_TICKET + COALESCE(t.CALL_CENTER_TICKET, 0),
    a.OTHER_PARTNER_TICKET = a.OTHER_PARTNER_TICKET + COALESCE(t.OTHER_PARTNER_TICKET, 0),
    a.LAST_UNIT_ID = t.LAST_UNIT_ID,
    a.PEOPLE_PER_TICKET = COALESCE(t.PEOPLE_PER_TICKET, 0),
    a.MINIMUM_PEOPLE_PER_ORDER = COALESCE(t.MINIMUM_PEOPLE_PER_ORDER, 0),
    a.MAXIMUM_PEOPLE_PER_ORDER = COALESCE(t.MAXIMUM_PEOPLE_PER_ORDER, 0),
    a.DAY_GAP_SINCE_LAST_ORDER = COALESCE(t.DAY_GAP_SINCE_LAST_ORDER, 0),
    a.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD + COALESCE(t.TICKET_WITH_FOOD, 0),
    a.TICKET_WITH_VEG = a.TICKET_WITH_VEG + COALESCE(t.TICKET_WITH_VEG, 0),
    a.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG + COALESCE(t.TICKET_WITH_NON_VEG, 0),
    a.TICKET_WITH_HOT = a.TICKET_WITH_HOT + COALESCE(t.TICKET_WITH_HOT, 0),
    a.TICKET_WITH_COLD = a.TICKET_WITH_COLD + COALESCE(t.TICKET_WITH_COLD, 0),
    a.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY + COALESCE(t.TICKET_WITH_BAKERY, 0),
    a.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO + COALESCE(t.TICKET_WITH_COMBO, 0),
    a.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE + COALESCE(t.TICKET_WITH_MERCHANDISE, 0),
    a.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER + COALESCE(t.TICKET_WITH_OTHER, 0),
    a.TICKET_ON_MONDAY = a.TICKET_ON_MONDAY + COALESCE(t.TICKET_ON_MONDAY, 0),
    a.TICKET_ON_TUESDAY = a.TICKET_ON_TUESDAY + COALESCE(t.TICKET_ON_TUESDAY, 0),
    a.TICKET_ON_WEDNESDAY = a.TICKET_ON_WEDNESDAY + COALESCE(t.TICKET_ON_WEDNESDAY, 0),
    a.TICKET_ON_THURSDAY = a.TICKET_ON_THURSDAY + COALESCE(t.TICKET_ON_THURSDAY, 0),
    a.TICKET_ON_FRIDAY = a.TICKET_ON_FRIDAY + COALESCE(t.TICKET_ON_FRIDAY, 0),
    a.TICKET_ON_SATURDAY = a.TICKET_ON_SATURDAY + COALESCE(t.TICKET_ON_SATURDAY, 0),
    a.TICKET_ON_SUNDAY = a.TICKET_ON_SUNDAY + COALESCE(t.TICKET_ON_SUNDAY, 0),
    a.TICKET_ON_WEEKDAY = a.TICKET_ON_WEEKDAY + COALESCE(t.TICKET_ON_WEEKDAY, 0),
    a.TICKET_ON_WEEKEND = a.TICKET_ON_WEEKEND + COALESCE(t.TICKET_ON_WEEKEND, 0),
    a.TICKET_IN_BREAKFAST = a.TICKET_IN_BREAKFAST + COALESCE(t.TICKET_IN_BREAKFAST, 0),
    a.TICKET_IN_LUNCH = a.TICKET_IN_LUNCH + COALESCE(t.TICKET_IN_LUNCH, 0),
    a.TICKET_IN_EVENING = a.TICKET_IN_EVENING + COALESCE(t.TICKET_IN_EVENING, 0),
    a.TICKET_IN_DINNER = a.TICKET_IN_DINNER + COALESCE(t.TICKET_IN_DINNER, 0),
    a.TICKET_IN_POST_DINNER = a.TICKET_IN_POST_DINNER + COALESCE(t.TICKET_IN_POST_DINNER, 0),
    a.TICKET_IN_NIGHT = a.TICKET_IN_NIGHT + COALESCE(t.TICKET_IN_NIGHT, 0),
    a.CASH_TICKET = a.CASH_TICKET + COALESCE(t.CASH_TICKET, 0),
    a.CARD_TICKET = a.CARD_TICKET + COALESCE(t.CARD_TICKET, 0),
    a.AMEX_TICKET = a.AMEX_TICKET + COALESCE(t.AMEX_TICKET, 0),
    a.PAYTM_TICKET = a.PAYTM_TICKET + COALESCE(t.PAYTM_TICKET, 0),
    a.GIFT_CARD_TICKET = a.GIFT_CARD_TICKET + COALESCE(t.GIFT_CARD_TICKET, 0),
    a.MOBIKWIK_TICKET = a.MOBIKWIK_TICKET + COALESCE(t.MOBIKWIK_TICKET, 0),
    a.ONLINE_PAYMENT_TICKET = a.ONLINE_PAYMENT_TICKET + COALESCE(t.ONLINE_PAYMENT_TICKET, 0),
    a.OTHER_PAYMENT_TICKET = a.OTHER_PAYMENT_TICKET + COALESCE(t.OTHER_PAYMENT_TICKET, 0),
    a.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET + COALESCE(t.SPLIT_PAYMENT_TICKET, 0),
    a.LAST_PAYMENT_MODE = t.LAST_PAYMENT_MODE,
    a.ONE_NPS_TICKET = COALESCE(t.ONE_NPS_TICKET, 0),
    a.TWO_NPS_TICKET = COALESCE(t.TWO_NPS_TICKET, 0),
    a.THREE_NPS_TICKET = COALESCE(t.THREE_NPS_TICKET, 0),
    a.FOUR_NPS_TICKET = COALESCE(t.FOUR_NPS_TICKET, 0),
    a.FIVE_NPS_TICKET = COALESCE(t.FIVE_NPS_TICKET, 0),
    a.SIX_NPS_TICKET = COALESCE(t.SIX_NPS_TICKET, 0),
    a.SEVEN_NPS_TICKET = COALESCE(t.SEVEN_NPS_TICKET, 0),
    a.EIGHT_NPS_TICKET = COALESCE(t.EIGHT_NPS_TICKET, 0),
    a.NINE_NPS_TICKET = COALESCE(t.NINE_NPS_TICKET, 0),
    a.TEN_NPS_TICKET = COALESCE(t.TEN_NPS_TICKET, 0),
    a.LAST_NPS_SCORE = t.LAST_NPS_SCORE,
    a.NEGATIVE_NPS_TICKET = COALESCE(t.NEGATIVE_NPS_TICKET, 0),
    a.POSITIVE_NPS_TICKET = COALESCE(t.POSITIVE_NPS_TICKET, 0),
    a.NEUTRAL_NPS_TICKET = COALESCE(t.NEUTRAL_NPS_TICKET, 0),
    a.ONE_FEEDBACK_TICKET = COALESCE(t.ONE_FEEDBACK_TICKET, 0),
    a.TWO_FEEDBACK_TICKET = COALESCE(t.TWO_FEEDBACK_TICKET, 0),
    a.THREE_FEEDBACK_TICKET = COALESCE(t.THREE_FEEDBACK_TICKET, 0),
    a.FOUR_FEEDBACK_TICKET = COALESCE(t.FOUR_FEEDBACK_TICKET, 0),
    a.FIVE_FEEDBACK_TICKET = COALESCE(t.FIVE_FEEDBACK_TICKET, 0),
    a.LAST_FEEDBACK_SCORE = t.LAST_FEEDBACK_SCORE,
    a.TOTAL_SPEND = a.TOTAL_SPEND + COALESCE(t.TOTAL_SPEND, 0),
    a.TOTAL_DISCOUNT = a.TOTAL_DISCOUNT + COALESCE(t.TOTAL_DISCOUNT, 0),
    a.TOTAL_APC = COALESCE(t.TOTAL_APC, 0),
    a.MINIMUM_APC = COALESCE(t.MINIMUM_APC, 0),
    a.MAXIMUM_APC = COALESCE(t.MAXIMUM_APC, 0),
    a.DELIVERY_SPEND = a.DELIVERY_SPEND + COALESCE(t.DELIVERY_SPEND, 0),
    a.DELIVERY_DISCOUNT = a.DELIVERY_DISCOUNT + COALESCE(t.DELIVERY_DISCOUNT, 0),
    a.DELIVERY_APC = COALESCE(t.DELIVERY_APC, 0),
    a.DELIVERY_MINIMUM_APC = COALESCE(t.DELIVERY_MINIMUM_APC, 0),
    a.DELIVERY_MAXIMUM_APC = COALESCE(t.DELIVERY_MAXIMUM_APC, 0),
    a.DINE_IN_SPEND = a.DINE_IN_SPEND + COALESCE(t.DINE_IN_SPEND, 0),
    a.DINE_IN_DISCOUNT = a.DINE_IN_DISCOUNT + COALESCE(t.DINE_IN_DISCOUNT, 0),
    a.DINE_IN_APC = COALESCE(t.DINE_IN_APC, 0),
    a.DINE_IN_MINIMUM_APC = COALESCE(t.DINE_IN_MINIMUM_APC, 0),
    a.DINE_IN_MAXIMUM_APC = COALESCE(t.DINE_IN_MAXIMUM_APC, 0),
    a.CASH_SPEND = a.CASH_SPEND + COALESCE(t.CASH_SPEND, 0),
    a.CARD_SPEND = a.CARD_SPEND + COALESCE(t.CARD_SPEND, 0),
    a.AMEX_SPEND = a.AMEX_SPEND + COALESCE(t.AMEX_SPEND, 0),
    a.PAYTM_SPEND = a.PAYTM_SPEND + COALESCE(t.PAYTM_SPEND, 0),
    a.GIFT_CARD_SPEND = a.GIFT_CARD_SPEND + COALESCE(t.GIFT_CARD_SPEND, 0),
    a.MOBIKWIK_SPEND = a.MOBIKWIK_SPEND + COALESCE(t.MOBIKWIK_SPEND, 0),
    a.ONLINE_SPEND = a.ONLINE_SPEND + COALESCE(t.ONLINE_SPEND, 0),
    a.OTHER_PAYMENT_SPEND = a.OTHER_PAYMENT_SPEND + COALESCE(t.OTHER_PAYMENT_SPEND, 0),
    a.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD + COALESCE(t.TICKET_WITH_GIFT_CARD, 0),
    a.LAST_UNIT_NAME = t.LAST_UNIT_NAME,
    a.ZERO_AMOUNT_TICKET_COUNT = a.ZERO_AMOUNT_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT = a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT = a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0),
    a.ONLY_GIFT_CARD_TICKET = a.ONLY_GIFT_CARD_TICKET + COALESCE(t.ONLY_GIFT_CARD_TICKET, 0)
WHERE
    a.CUSTOMER_ID = t.CUSTOMER_ID
        AND t.CUSTOMER_ID = m.CUSTOMER_ID
        AND m.IS_NEW = 'N';

delete from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS WHERE LAST_ORDER_DATE < DATE_ADD(CURR_DATE, INTERVAL -90 DAY);

END$$
DELIMITER ;


DROP PROCEDURE IF EXISTS KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_30_DAYS;
DELIMITER $$
CREATE PROCEDURE KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_30_DAYS()
proc_label : BEGIN

DECLARE TILL_DATE DATE;
DECLARE CURR_DATE DATE;

select case when max(BUSINESS_DATE) is null then '2018-01-18' else max(BUSINESS_DATE) end into CURR_DATE from KETTLE_DUMP.ORDER_DETAIL;
select case when max(LAST_ORDER_DATE) is null then '2018-01-18' else max(LAST_ORDER_DATE) end into TILL_DATE from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS;


IF CURR_DATE IS NOT NULL AND CURR_DATE <= TILL_DATE
THEN 
	LEAVE proc_label;
END IF;

DROP TABLE IF EXISTS CLM_ANALYTICS.ACTIVE_CUSTOMERS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS AS SELECT DISTINCT od.CUSTOMER_ID FROM
    KETTLE_DUMP.ORDER_DETAIL od
WHERE
    od.BUSINESS_DATE = CURR_DATE
        AND od.CUSTOMER_ID > 5
        AND CUSTOMER_ID NOT IN (67456,142315);

CREATE INDEX CUSTOMER_ID_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(CUSTOMER_ID) USING BTREE;

ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS ADD COLUMN IS_NEW VARCHAR(1) NOT NULL DEFAULT 'N';

CREATE INDEX IS_NEW_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(IS_NEW) USING BTREE;

update CLM_ANALYTICS.ACTIVE_CUSTOMERS a 
left outer join CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c
ON a.CUSTOMER_ID = c.CUSTOMER_ID 
SET IS_NEW = case when c.CUSTOMER_ID IS NOT NULL THEN 'N' ELSE 'Y' END;

DROP TABLE IF EXISTS CLM_ANALYTICS.TEMP_CUSTOMER_DATA;
CREATE TABLE CLM_ANALYTICS.TEMP_CUSTOMER_DATA (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    UNIQUE_VISIT_DAYS INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_TEMP_CUSTOMER_ID ON CLM_ANALYTICS.TEMP_CUSTOMER_DATA(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.TEMP_CUSTOMER_DATA
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_DISCOUNT
)

  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT
  FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  AND od.BUSINESS_DATE = CURR_DATE
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
(select
	od.CUSTOMER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC
 FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
   AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -30 DAY)
  AND od.BUSINESS_DATE <= CURR_DATE
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  GROUP BY od.CUSTOMER_ID
  )a
  SET 
  c.TOTAL_UNITS_VISITED = a.TOTAL_UNITS_VISITED,
  c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS,
  c.MINIMUM_APC = a.MINIMUM_APC,
  c.MAXIMUM_APC = a.MAXIMUM_APC,
  c.DELIVERY_MINIMUM_APC = a.DELIVERY_MINIMUM_APC,
  c.DELIVERY_MAXIMUM_APC = a.DELIVERY_MAXIMUM_APC,
  c.DINE_IN_MINIMUM_APC = a.DINE_IN_MINIMUM_APC,
  c.DINE_IN_MAXIMUM_APC = a.DINE_IN_MAXIMUM_APC
  WHERE a.CUSTOMER_ID = c.CUSTOMER_ID;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
        WHERE o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os, KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
    od.ORDER_ID = os.ORDER_ID AND
    od.CUSTOMER_ID = a.CUSTOMER_ID
     AND    od.ORDER_STATUS <> 'CANCELLED'
     AND od.BUSINESS_DATE = CURR_DATE
     GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
        AND o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY o.CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        a.CUSTOMER_ID,
            SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN
                    a.HOT_QUANTITY = 0
                        AND a.HOT_QUANTITY = 0
                        AND a.FOOD_QUANTITY = 0
                        AND a.COLD_QUANTITY = 0
                        AND a.BAKERY_QUANTITY = 0
                        AND a.COMBO_QUANTITY = 0
                        AND a.MERCHANDISE_QUANTITY = 0
                        AND a.OTHERS_QUANTITY = 0
                        AND a.GIFT_CARD_QUANTITY > 0
                THEN
                    1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND od.BUSINESS_DATE = CURR_DATE 
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET = a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -30 DAY)
             AND od.BUSINESS_DATE <= CURR_DATE
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
        AND od.CUSTOMER_ID = a.CUSTOMER_ID
        
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = CURR_DATE
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;



update CLM_ANALYTICS.TEMP_CUSTOMER_DATA c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c1,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

INSERT INTO CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS
(CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
TICKET_COUNT,
CANCELLED_TICKET_COUNT,
TICKET_WITH_OFFER,
TICKET_WITH_REDEMPTION,
DINE_IN_TICKET,
DELIVERY_TICKET,
TAKE_AWAY_TICKET,
ZOMATO_TICKET,
SWIGGY_TICKET,
FOOD_PANDA_TICKET,
UBER_EATS_TICKET,
OLD_APP_TICKET,
WEB_APP_TICKET,
CALL_CENTER_TICKET,
OTHER_PARTNER_TICKET,
FIRST_UNIT_ID,
LAST_UNIT_ID,
PEOPLE_PER_TICKET,
MINIMUM_PEOPLE_PER_ORDER,
MAXIMUM_PEOPLE_PER_ORDER,
DAY_GAP_SINCE_LAST_ORDER,
TICKET_WITH_FOOD,
TICKET_WITH_VEG,
TICKET_WITH_NON_VEG,
TICKET_WITH_HOT,
TICKET_WITH_COLD,
TICKET_WITH_BAKERY,
TICKET_WITH_COMBO,
TICKET_WITH_MERCHANDISE,
TICKET_WITH_OTHER,
TICKET_ON_MONDAY,
TICKET_ON_TUESDAY,
TICKET_ON_WEDNESDAY,
TICKET_ON_THURSDAY,
TICKET_ON_FRIDAY,
TICKET_ON_SATURDAY,
TICKET_ON_SUNDAY,
TICKET_ON_WEEKDAY,
TICKET_ON_WEEKEND,
TICKET_IN_BREAKFAST,
TICKET_IN_LUNCH,
TICKET_IN_EVENING,
TICKET_IN_DINNER,
TICKET_IN_POST_DINNER,
TICKET_IN_NIGHT,
CASH_TICKET,
CARD_TICKET,
AMEX_TICKET,
PAYTM_TICKET,
GIFT_CARD_TICKET,
MOBIKWIK_TICKET,
ONLINE_PAYMENT_TICKET,
OTHER_PAYMENT_TICKET,
SPLIT_PAYMENT_TICKET,
LAST_PAYMENT_MODE,
ONE_NPS_TICKET,
TWO_NPS_TICKET,
THREE_NPS_TICKET,
FOUR_NPS_TICKET,
FIVE_NPS_TICKET,
SIX_NPS_TICKET,
SEVEN_NPS_TICKET,
EIGHT_NPS_TICKET,
NINE_NPS_TICKET,
TEN_NPS_TICKET,
LAST_NPS_SCORE,
NEGATIVE_NPS_TICKET,
POSITIVE_NPS_TICKET,
NEUTRAL_NPS_TICKET,
ONE_FEEDBACK_TICKET,
TWO_FEEDBACK_TICKET,
THREE_FEEDBACK_TICKET,
FOUR_FEEDBACK_TICKET,
FIVE_FEEDBACK_TICKET,
LAST_FEEDBACK_SCORE,
TOTAL_SPEND,
TOTAL_DISCOUNT,
TOTAL_APC,
MINIMUM_APC,
MAXIMUM_APC,
DELIVERY_SPEND,
DELIVERY_DISCOUNT,
DELIVERY_APC,
DELIVERY_MINIMUM_APC,
DELIVERY_MAXIMUM_APC,
DINE_IN_SPEND,
DINE_IN_DISCOUNT,
DINE_IN_APC,
DINE_IN_MINIMUM_APC,
DINE_IN_MAXIMUM_APC,
CASH_SPEND,
CARD_SPEND,
AMEX_SPEND,
PAYTM_SPEND,
GIFT_CARD_SPEND,
MOBIKWIK_SPEND,
ONLINE_SPEND,
OTHER_PAYMENT_SPEND,
TICKET_WITH_GIFT_CARD,
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
ZERO_AMOUNT_TICKET_COUNT,
ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
ONLY_GIFT_CARD_TICKET)
select 
t.CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
COALESCE(TICKET_COUNT,0),
COALESCE(CANCELLED_TICKET_COUNT,0),
COALESCE(TICKET_WITH_OFFER,0),
COALESCE(TICKET_WITH_REDEMPTION,0),
COALESCE(DINE_IN_TICKET,0),
COALESCE(DELIVERY_TICKET,0),
COALESCE(TAKE_AWAY_TICKET,0),
COALESCE(ZOMATO_TICKET,0),
COALESCE(SWIGGY_TICKET,0),
COALESCE(FOOD_PANDA_TICKET,0),
COALESCE(UBER_EATS_TICKET,0),
COALESCE(OLD_APP_TICKET,0),
COALESCE(WEB_APP_TICKET,0),
COALESCE(CALL_CENTER_TICKET,0),
COALESCE(OTHER_PARTNER_TICKET,0),
FIRST_UNIT_ID,
LAST_UNIT_ID,
COALESCE(PEOPLE_PER_TICKET,0),
COALESCE(MINIMUM_PEOPLE_PER_ORDER,0),
COALESCE(MAXIMUM_PEOPLE_PER_ORDER,0),
COALESCE(DAY_GAP_SINCE_LAST_ORDER,0),
COALESCE(TICKET_WITH_FOOD,0),
COALESCE(TICKET_WITH_VEG,0),
COALESCE(TICKET_WITH_NON_VEG,0),
COALESCE(TICKET_WITH_HOT,0),
COALESCE(TICKET_WITH_COLD,0),
COALESCE(TICKET_WITH_BAKERY,0),
COALESCE(TICKET_WITH_COMBO,0),
COALESCE(TICKET_WITH_MERCHANDISE,0),
COALESCE(TICKET_WITH_OTHER,0),
COALESCE(TICKET_ON_MONDAY,0),
COALESCE(TICKET_ON_TUESDAY,0),
COALESCE(TICKET_ON_WEDNESDAY,0),
COALESCE(TICKET_ON_THURSDAY,0),
COALESCE(TICKET_ON_FRIDAY,0),
COALESCE(TICKET_ON_SATURDAY,0),
COALESCE(TICKET_ON_SUNDAY,0),
COALESCE(TICKET_ON_WEEKDAY,0),
COALESCE(TICKET_ON_WEEKEND,0),
COALESCE(TICKET_IN_BREAKFAST,0),
COALESCE(TICKET_IN_LUNCH,0),
COALESCE(TICKET_IN_EVENING,0),
COALESCE(TICKET_IN_DINNER,0),
COALESCE(TICKET_IN_POST_DINNER,0),
COALESCE(TICKET_IN_NIGHT,0),
COALESCE(CASH_TICKET,0),
COALESCE(CARD_TICKET,0),
COALESCE(AMEX_TICKET,0),
COALESCE(PAYTM_TICKET,0),
COALESCE(GIFT_CARD_TICKET,0),
COALESCE(MOBIKWIK_TICKET,0),
COALESCE(ONLINE_PAYMENT_TICKET,0),
COALESCE(OTHER_PAYMENT_TICKET,0),
COALESCE(SPLIT_PAYMENT_TICKET,0),
COALESCE(LAST_PAYMENT_MODE,0),
COALESCE(ONE_NPS_TICKET,0),
COALESCE(TWO_NPS_TICKET,0),
COALESCE(THREE_NPS_TICKET,0),
COALESCE(FOUR_NPS_TICKET,0),
COALESCE(FIVE_NPS_TICKET,0),
COALESCE(SIX_NPS_TICKET,0),
COALESCE(SEVEN_NPS_TICKET,0),
COALESCE(EIGHT_NPS_TICKET,0),
COALESCE(NINE_NPS_TICKET,0),
COALESCE(TEN_NPS_TICKET,0),
LAST_NPS_SCORE,
COALESCE(NEGATIVE_NPS_TICKET,0),
COALESCE(POSITIVE_NPS_TICKET,0),
COALESCE(NEUTRAL_NPS_TICKET,0),
COALESCE(ONE_FEEDBACK_TICKET,0),
COALESCE(TWO_FEEDBACK_TICKET,0),
COALESCE(THREE_FEEDBACK_TICKET,0),
COALESCE(FOUR_FEEDBACK_TICKET,0),
COALESCE(FIVE_FEEDBACK_TICKET,0),
LAST_FEEDBACK_SCORE,
COALESCE(TOTAL_SPEND,0),
COALESCE(TOTAL_DISCOUNT,0),
COALESCE(TOTAL_APC,0),
COALESCE(MINIMUM_APC,0),
COALESCE(MAXIMUM_APC,0),
COALESCE(DELIVERY_SPEND,0),
COALESCE(DELIVERY_DISCOUNT,0),
COALESCE(DELIVERY_APC,0),
COALESCE(DELIVERY_MINIMUM_APC,0),
COALESCE(DELIVERY_MAXIMUM_APC,0),
COALESCE(DINE_IN_SPEND,0),
COALESCE(DINE_IN_DISCOUNT,0),
COALESCE(DINE_IN_APC,0),
COALESCE(DINE_IN_MINIMUM_APC,0),
COALESCE(DINE_IN_MAXIMUM_APC,0),
COALESCE(CASH_SPEND,0),
COALESCE(CARD_SPEND,0),
COALESCE(AMEX_SPEND,0),
COALESCE(PAYTM_SPEND,0),
COALESCE(GIFT_CARD_SPEND,0),
COALESCE(MOBIKWIK_SPEND,0),
COALESCE(ONLINE_SPEND,0),
COALESCE(OTHER_PAYMENT_SPEND,0),
COALESCE(TICKET_WITH_GIFT_CARD,0),
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
COALESCE(ZERO_AMOUNT_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT,0),
COALESCE(ONLY_GIFT_CARD_TICKET,0)
from CLM_ANALYTICS.TEMP_CUSTOMER_DATA t, CLM_ANALYTICS.ACTIVE_CUSTOMERS a
where a.CUSTOMER_ID = t.CUSTOMER_ID
and a.IS_NEW = 'Y'
;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS a,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA t,
    CLM_ANALYTICS.ACTIVE_CUSTOMERS m 
SET 
    a.TOTAL_UNITS_VISITED = t.TOTAL_UNITS_VISITED,
    a.UNIQUE_VISIT_DAYS = t.UNIQUE_VISIT_DAYS,
    a.LAST_ORDER_ID = t.LAST_ORDER_ID,
    a.LAST_ORDER_DATE = t.LAST_ORDER_DATE,
    a.TICKET_COUNT = a.TICKET_COUNT + COALESCE(t.TICKET_COUNT, 0),
    a.CANCELLED_TICKET_COUNT = a.CANCELLED_TICKET_COUNT + COALESCE(t.CANCELLED_TICKET_COUNT, 0),
    a.TICKET_WITH_OFFER = a.TICKET_WITH_OFFER + COALESCE(t.TICKET_WITH_OFFER, 0),
    a.TICKET_WITH_REDEMPTION = a.TICKET_WITH_REDEMPTION + COALESCE(t.TICKET_WITH_REDEMPTION, 0),
    a.DINE_IN_TICKET = a.DINE_IN_TICKET + COALESCE(t.DINE_IN_TICKET, 0),
    a.DELIVERY_TICKET = a.DELIVERY_TICKET + COALESCE(t.DELIVERY_TICKET, 0),
    a.TAKE_AWAY_TICKET = a.TAKE_AWAY_TICKET + COALESCE(t.TAKE_AWAY_TICKET, 0),
    a.ZOMATO_TICKET = a.ZOMATO_TICKET + COALESCE(t.ZOMATO_TICKET, 0),
    a.SWIGGY_TICKET = a.SWIGGY_TICKET + COALESCE(t.SWIGGY_TICKET, 0),
    a.FOOD_PANDA_TICKET = a.FOOD_PANDA_TICKET + COALESCE(t.FOOD_PANDA_TICKET, 0),
    a.UBER_EATS_TICKET = a.UBER_EATS_TICKET + COALESCE(t.UBER_EATS_TICKET, 0),
    a.OLD_APP_TICKET = a.OLD_APP_TICKET + COALESCE(t.OLD_APP_TICKET, 0),
    a.WEB_APP_TICKET = a.WEB_APP_TICKET + COALESCE(t.WEB_APP_TICKET, 0),
    a.CALL_CENTER_TICKET = a.CALL_CENTER_TICKET + COALESCE(t.CALL_CENTER_TICKET, 0),
    a.OTHER_PARTNER_TICKET = a.OTHER_PARTNER_TICKET + COALESCE(t.OTHER_PARTNER_TICKET, 0),
    a.LAST_UNIT_ID = t.LAST_UNIT_ID,
    a.PEOPLE_PER_TICKET = COALESCE(t.PEOPLE_PER_TICKET, 0),
    a.MINIMUM_PEOPLE_PER_ORDER = COALESCE(t.MINIMUM_PEOPLE_PER_ORDER, 0),
    a.MAXIMUM_PEOPLE_PER_ORDER = COALESCE(t.MAXIMUM_PEOPLE_PER_ORDER, 0),
    a.DAY_GAP_SINCE_LAST_ORDER = COALESCE(t.DAY_GAP_SINCE_LAST_ORDER, 0),
    a.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD + COALESCE(t.TICKET_WITH_FOOD, 0),
    a.TICKET_WITH_VEG = a.TICKET_WITH_VEG + COALESCE(t.TICKET_WITH_VEG, 0),
    a.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG + COALESCE(t.TICKET_WITH_NON_VEG, 0),
    a.TICKET_WITH_HOT = a.TICKET_WITH_HOT + COALESCE(t.TICKET_WITH_HOT, 0),
    a.TICKET_WITH_COLD = a.TICKET_WITH_COLD + COALESCE(t.TICKET_WITH_COLD, 0),
    a.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY + COALESCE(t.TICKET_WITH_BAKERY, 0),
    a.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO + COALESCE(t.TICKET_WITH_COMBO, 0),
    a.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE + COALESCE(t.TICKET_WITH_MERCHANDISE, 0),
    a.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER + COALESCE(t.TICKET_WITH_OTHER, 0),
    a.TICKET_ON_MONDAY = a.TICKET_ON_MONDAY + COALESCE(t.TICKET_ON_MONDAY, 0),
    a.TICKET_ON_TUESDAY = a.TICKET_ON_TUESDAY + COALESCE(t.TICKET_ON_TUESDAY, 0),
    a.TICKET_ON_WEDNESDAY = a.TICKET_ON_WEDNESDAY + COALESCE(t.TICKET_ON_WEDNESDAY, 0),
    a.TICKET_ON_THURSDAY = a.TICKET_ON_THURSDAY + COALESCE(t.TICKET_ON_THURSDAY, 0),
    a.TICKET_ON_FRIDAY = a.TICKET_ON_FRIDAY + COALESCE(t.TICKET_ON_FRIDAY, 0),
    a.TICKET_ON_SATURDAY = a.TICKET_ON_SATURDAY + COALESCE(t.TICKET_ON_SATURDAY, 0),
    a.TICKET_ON_SUNDAY = a.TICKET_ON_SUNDAY + COALESCE(t.TICKET_ON_SUNDAY, 0),
    a.TICKET_ON_WEEKDAY = a.TICKET_ON_WEEKDAY + COALESCE(t.TICKET_ON_WEEKDAY, 0),
    a.TICKET_ON_WEEKEND = a.TICKET_ON_WEEKEND + COALESCE(t.TICKET_ON_WEEKEND, 0),
    a.TICKET_IN_BREAKFAST = a.TICKET_IN_BREAKFAST + COALESCE(t.TICKET_IN_BREAKFAST, 0),
    a.TICKET_IN_LUNCH = a.TICKET_IN_LUNCH + COALESCE(t.TICKET_IN_LUNCH, 0),
    a.TICKET_IN_EVENING = a.TICKET_IN_EVENING + COALESCE(t.TICKET_IN_EVENING, 0),
    a.TICKET_IN_DINNER = a.TICKET_IN_DINNER + COALESCE(t.TICKET_IN_DINNER, 0),
    a.TICKET_IN_POST_DINNER = a.TICKET_IN_POST_DINNER + COALESCE(t.TICKET_IN_POST_DINNER, 0),
    a.TICKET_IN_NIGHT = a.TICKET_IN_NIGHT + COALESCE(t.TICKET_IN_NIGHT, 0),
    a.CASH_TICKET = a.CASH_TICKET + COALESCE(t.CASH_TICKET, 0),
    a.CARD_TICKET = a.CARD_TICKET + COALESCE(t.CARD_TICKET, 0),
    a.AMEX_TICKET = a.AMEX_TICKET + COALESCE(t.AMEX_TICKET, 0),
    a.PAYTM_TICKET = a.PAYTM_TICKET + COALESCE(t.PAYTM_TICKET, 0),
    a.GIFT_CARD_TICKET = a.GIFT_CARD_TICKET + COALESCE(t.GIFT_CARD_TICKET, 0),
    a.MOBIKWIK_TICKET = a.MOBIKWIK_TICKET + COALESCE(t.MOBIKWIK_TICKET, 0),
    a.ONLINE_PAYMENT_TICKET = a.ONLINE_PAYMENT_TICKET + COALESCE(t.ONLINE_PAYMENT_TICKET, 0),
    a.OTHER_PAYMENT_TICKET = a.OTHER_PAYMENT_TICKET + COALESCE(t.OTHER_PAYMENT_TICKET, 0),
    a.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET + COALESCE(t.SPLIT_PAYMENT_TICKET, 0),
    a.LAST_PAYMENT_MODE = t.LAST_PAYMENT_MODE,
    a.ONE_NPS_TICKET = COALESCE(t.ONE_NPS_TICKET, 0),
    a.TWO_NPS_TICKET = COALESCE(t.TWO_NPS_TICKET, 0),
    a.THREE_NPS_TICKET = COALESCE(t.THREE_NPS_TICKET, 0),
    a.FOUR_NPS_TICKET = COALESCE(t.FOUR_NPS_TICKET, 0),
    a.FIVE_NPS_TICKET = COALESCE(t.FIVE_NPS_TICKET, 0),
    a.SIX_NPS_TICKET = COALESCE(t.SIX_NPS_TICKET, 0),
    a.SEVEN_NPS_TICKET = COALESCE(t.SEVEN_NPS_TICKET, 0),
    a.EIGHT_NPS_TICKET = COALESCE(t.EIGHT_NPS_TICKET, 0),
    a.NINE_NPS_TICKET = COALESCE(t.NINE_NPS_TICKET, 0),
    a.TEN_NPS_TICKET = COALESCE(t.TEN_NPS_TICKET, 0),
    a.LAST_NPS_SCORE = t.LAST_NPS_SCORE,
    a.NEGATIVE_NPS_TICKET = COALESCE(t.NEGATIVE_NPS_TICKET, 0),
    a.POSITIVE_NPS_TICKET = COALESCE(t.POSITIVE_NPS_TICKET, 0),
    a.NEUTRAL_NPS_TICKET = COALESCE(t.NEUTRAL_NPS_TICKET, 0),
    a.ONE_FEEDBACK_TICKET = COALESCE(t.ONE_FEEDBACK_TICKET, 0),
    a.TWO_FEEDBACK_TICKET = COALESCE(t.TWO_FEEDBACK_TICKET, 0),
    a.THREE_FEEDBACK_TICKET = COALESCE(t.THREE_FEEDBACK_TICKET, 0),
    a.FOUR_FEEDBACK_TICKET = COALESCE(t.FOUR_FEEDBACK_TICKET, 0),
    a.FIVE_FEEDBACK_TICKET = COALESCE(t.FIVE_FEEDBACK_TICKET, 0),
    a.LAST_FEEDBACK_SCORE = t.LAST_FEEDBACK_SCORE,
    a.TOTAL_SPEND = a.TOTAL_SPEND + COALESCE(t.TOTAL_SPEND, 0),
    a.TOTAL_DISCOUNT = a.TOTAL_DISCOUNT + COALESCE(t.TOTAL_DISCOUNT, 0),
    a.TOTAL_APC = COALESCE(t.TOTAL_APC, 0),
    a.MINIMUM_APC = COALESCE(t.MINIMUM_APC, 0),
    a.MAXIMUM_APC = COALESCE(t.MAXIMUM_APC, 0),
    a.DELIVERY_SPEND = a.DELIVERY_SPEND + COALESCE(t.DELIVERY_SPEND, 0),
    a.DELIVERY_DISCOUNT = a.DELIVERY_DISCOUNT + COALESCE(t.DELIVERY_DISCOUNT, 0),
    a.DELIVERY_APC = COALESCE(t.DELIVERY_APC, 0),
    a.DELIVERY_MINIMUM_APC = COALESCE(t.DELIVERY_MINIMUM_APC, 0),
    a.DELIVERY_MAXIMUM_APC = COALESCE(t.DELIVERY_MAXIMUM_APC, 0),
    a.DINE_IN_SPEND = a.DINE_IN_SPEND + COALESCE(t.DINE_IN_SPEND, 0),
    a.DINE_IN_DISCOUNT = a.DINE_IN_DISCOUNT + COALESCE(t.DINE_IN_DISCOUNT, 0),
    a.DINE_IN_APC = COALESCE(t.DINE_IN_APC, 0),
    a.DINE_IN_MINIMUM_APC = COALESCE(t.DINE_IN_MINIMUM_APC, 0),
    a.DINE_IN_MAXIMUM_APC = COALESCE(t.DINE_IN_MAXIMUM_APC, 0),
    a.CASH_SPEND = a.CASH_SPEND + COALESCE(t.CASH_SPEND, 0),
    a.CARD_SPEND = a.CARD_SPEND + COALESCE(t.CARD_SPEND, 0),
    a.AMEX_SPEND = a.AMEX_SPEND + COALESCE(t.AMEX_SPEND, 0),
    a.PAYTM_SPEND = a.PAYTM_SPEND + COALESCE(t.PAYTM_SPEND, 0),
    a.GIFT_CARD_SPEND = a.GIFT_CARD_SPEND + COALESCE(t.GIFT_CARD_SPEND, 0),
    a.MOBIKWIK_SPEND = a.MOBIKWIK_SPEND + COALESCE(t.MOBIKWIK_SPEND, 0),
    a.ONLINE_SPEND = a.ONLINE_SPEND + COALESCE(t.ONLINE_SPEND, 0),
    a.OTHER_PAYMENT_SPEND = a.OTHER_PAYMENT_SPEND + COALESCE(t.OTHER_PAYMENT_SPEND, 0),
    a.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD + COALESCE(t.TICKET_WITH_GIFT_CARD, 0),
    a.LAST_UNIT_NAME = t.LAST_UNIT_NAME,
    a.ZERO_AMOUNT_TICKET_COUNT = a.ZERO_AMOUNT_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT = a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT = a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0),
    a.ONLY_GIFT_CARD_TICKET = a.ONLY_GIFT_CARD_TICKET + COALESCE(t.ONLY_GIFT_CARD_TICKET, 0)
WHERE
    a.CUSTOMER_ID = t.CUSTOMER_ID
        AND t.CUSTOMER_ID = m.CUSTOMER_ID
        AND m.IS_NEW = 'N';
delete from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS WHERE LAST_ORDER_DATE < DATE_ADD(CURR_DATE, INTERVAL -30 DAY);


END$$
DELIMITER ;


DROP PROCEDURE IF EXISTS KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_15_DAYS;
DELIMITER $$
CREATE PROCEDURE KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_15_DAYS()
proc_label : BEGIN

DECLARE TILL_DATE DATE;
DECLARE CURR_DATE DATE;

select case when max(BUSINESS_DATE) is null then '2018-01-18' else max(BUSINESS_DATE) end into CURR_DATE from KETTLE_DUMP.ORDER_DETAIL;
select case when max(LAST_ORDER_DATE) is null then '2018-01-18' else max(LAST_ORDER_DATE) end into TILL_DATE from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS;


IF CURR_DATE IS NOT NULL AND CURR_DATE <= TILL_DATE
THEN 
	LEAVE proc_label;
END IF;

DROP TABLE IF EXISTS CLM_ANALYTICS.ACTIVE_CUSTOMERS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS AS SELECT DISTINCT od.CUSTOMER_ID FROM
    KETTLE_DUMP.ORDER_DETAIL od
WHERE
    od.BUSINESS_DATE = CURR_DATE
        AND od.CUSTOMER_ID > 5
        AND CUSTOMER_ID NOT IN (67456,142315);

CREATE INDEX CUSTOMER_ID_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(CUSTOMER_ID) USING BTREE;

ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS ADD COLUMN IS_NEW VARCHAR(1) NOT NULL DEFAULT 'N';

CREATE INDEX IS_NEW_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(IS_NEW) USING BTREE;

update CLM_ANALYTICS.ACTIVE_CUSTOMERS a 
left outer join CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c
ON a.CUSTOMER_ID = c.CUSTOMER_ID 
SET IS_NEW = case when c.CUSTOMER_ID IS NOT NULL THEN 'N' ELSE 'Y' END;

DROP TABLE IF EXISTS CLM_ANALYTICS.TEMP_CUSTOMER_DATA;
CREATE TABLE CLM_ANALYTICS.TEMP_CUSTOMER_DATA (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    UNIQUE_VISIT_DAYS INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_TEMP_CUSTOMER_ID ON CLM_ANALYTICS.TEMP_CUSTOMER_DATA(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.TEMP_CUSTOMER_DATA
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_DISCOUNT
)

  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT
  FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  AND od.BUSINESS_DATE = CURR_DATE
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
(select
	od.CUSTOMER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC
 FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
   AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -15 DAY)
  AND od.BUSINESS_DATE <= CURR_DATE
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  GROUP BY od.CUSTOMER_ID
  )a
  SET 
  c.TOTAL_UNITS_VISITED = a.TOTAL_UNITS_VISITED,
  c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS,
  c.MINIMUM_APC = a.MINIMUM_APC,
  c.MAXIMUM_APC = a.MAXIMUM_APC,
  c.DELIVERY_MINIMUM_APC = a.DELIVERY_MINIMUM_APC,
  c.DELIVERY_MAXIMUM_APC = a.DELIVERY_MAXIMUM_APC,
  c.DINE_IN_MINIMUM_APC = a.DINE_IN_MINIMUM_APC,
  c.DINE_IN_MAXIMUM_APC = a.DINE_IN_MAXIMUM_APC
  WHERE a.CUSTOMER_ID = c.CUSTOMER_ID;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
        WHERE o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os, KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
    od.ORDER_ID = os.ORDER_ID AND
    od.CUSTOMER_ID = a.CUSTOMER_ID
     AND    od.ORDER_STATUS <> 'CANCELLED'
     AND od.BUSINESS_DATE = CURR_DATE
     GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
        AND o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY o.CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        a.CUSTOMER_ID,
            SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN
                    a.HOT_QUANTITY = 0
                        AND a.HOT_QUANTITY = 0
                        AND a.FOOD_QUANTITY = 0
                        AND a.COLD_QUANTITY = 0
                        AND a.BAKERY_QUANTITY = 0
                        AND a.COMBO_QUANTITY = 0
                        AND a.MERCHANDISE_QUANTITY = 0
                        AND a.OTHERS_QUANTITY = 0
                        AND a.GIFT_CARD_QUANTITY > 0
                THEN
                    1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND od.BUSINESS_DATE = CURR_DATE 
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET = a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -15 DAY)
             AND od.BUSINESS_DATE <= CURR_DATE
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
        AND od.CUSTOMER_ID = a.CUSTOMER_ID
        
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = CURR_DATE
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;



update CLM_ANALYTICS.TEMP_CUSTOMER_DATA c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c1,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

INSERT INTO CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS
(CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
TICKET_COUNT,
CANCELLED_TICKET_COUNT,
TICKET_WITH_OFFER,
TICKET_WITH_REDEMPTION,
DINE_IN_TICKET,
DELIVERY_TICKET,
TAKE_AWAY_TICKET,
ZOMATO_TICKET,
SWIGGY_TICKET,
FOOD_PANDA_TICKET,
UBER_EATS_TICKET,
OLD_APP_TICKET,
WEB_APP_TICKET,
CALL_CENTER_TICKET,
OTHER_PARTNER_TICKET,
FIRST_UNIT_ID,
LAST_UNIT_ID,
PEOPLE_PER_TICKET,
MINIMUM_PEOPLE_PER_ORDER,
MAXIMUM_PEOPLE_PER_ORDER,
DAY_GAP_SINCE_LAST_ORDER,
TICKET_WITH_FOOD,
TICKET_WITH_VEG,
TICKET_WITH_NON_VEG,
TICKET_WITH_HOT,
TICKET_WITH_COLD,
TICKET_WITH_BAKERY,
TICKET_WITH_COMBO,
TICKET_WITH_MERCHANDISE,
TICKET_WITH_OTHER,
TICKET_ON_MONDAY,
TICKET_ON_TUESDAY,
TICKET_ON_WEDNESDAY,
TICKET_ON_THURSDAY,
TICKET_ON_FRIDAY,
TICKET_ON_SATURDAY,
TICKET_ON_SUNDAY,
TICKET_ON_WEEKDAY,
TICKET_ON_WEEKEND,
TICKET_IN_BREAKFAST,
TICKET_IN_LUNCH,
TICKET_IN_EVENING,
TICKET_IN_DINNER,
TICKET_IN_POST_DINNER,
TICKET_IN_NIGHT,
CASH_TICKET,
CARD_TICKET,
AMEX_TICKET,
PAYTM_TICKET,
GIFT_CARD_TICKET,
MOBIKWIK_TICKET,
ONLINE_PAYMENT_TICKET,
OTHER_PAYMENT_TICKET,
SPLIT_PAYMENT_TICKET,
LAST_PAYMENT_MODE,
ONE_NPS_TICKET,
TWO_NPS_TICKET,
THREE_NPS_TICKET,
FOUR_NPS_TICKET,
FIVE_NPS_TICKET,
SIX_NPS_TICKET,
SEVEN_NPS_TICKET,
EIGHT_NPS_TICKET,
NINE_NPS_TICKET,
TEN_NPS_TICKET,
LAST_NPS_SCORE,
NEGATIVE_NPS_TICKET,
POSITIVE_NPS_TICKET,
NEUTRAL_NPS_TICKET,
ONE_FEEDBACK_TICKET,
TWO_FEEDBACK_TICKET,
THREE_FEEDBACK_TICKET,
FOUR_FEEDBACK_TICKET,
FIVE_FEEDBACK_TICKET,
LAST_FEEDBACK_SCORE,
TOTAL_SPEND,
TOTAL_DISCOUNT,
TOTAL_APC,
MINIMUM_APC,
MAXIMUM_APC,
DELIVERY_SPEND,
DELIVERY_DISCOUNT,
DELIVERY_APC,
DELIVERY_MINIMUM_APC,
DELIVERY_MAXIMUM_APC,
DINE_IN_SPEND,
DINE_IN_DISCOUNT,
DINE_IN_APC,
DINE_IN_MINIMUM_APC,
DINE_IN_MAXIMUM_APC,
CASH_SPEND,
CARD_SPEND,
AMEX_SPEND,
PAYTM_SPEND,
GIFT_CARD_SPEND,
MOBIKWIK_SPEND,
ONLINE_SPEND,
OTHER_PAYMENT_SPEND,
TICKET_WITH_GIFT_CARD,
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
ZERO_AMOUNT_TICKET_COUNT,
ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
ONLY_GIFT_CARD_TICKET)
select 
t.CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
COALESCE(TICKET_COUNT,0),
COALESCE(CANCELLED_TICKET_COUNT,0),
COALESCE(TICKET_WITH_OFFER,0),
COALESCE(TICKET_WITH_REDEMPTION,0),
COALESCE(DINE_IN_TICKET,0),
COALESCE(DELIVERY_TICKET,0),
COALESCE(TAKE_AWAY_TICKET,0),
COALESCE(ZOMATO_TICKET,0),
COALESCE(SWIGGY_TICKET,0),
COALESCE(FOOD_PANDA_TICKET,0),
COALESCE(UBER_EATS_TICKET,0),
COALESCE(OLD_APP_TICKET,0),
COALESCE(WEB_APP_TICKET,0),
COALESCE(CALL_CENTER_TICKET,0),
COALESCE(OTHER_PARTNER_TICKET,0),
FIRST_UNIT_ID,
LAST_UNIT_ID,
COALESCE(PEOPLE_PER_TICKET,0),
COALESCE(MINIMUM_PEOPLE_PER_ORDER,0),
COALESCE(MAXIMUM_PEOPLE_PER_ORDER,0),
COALESCE(DAY_GAP_SINCE_LAST_ORDER,0),
COALESCE(TICKET_WITH_FOOD,0),
COALESCE(TICKET_WITH_VEG,0),
COALESCE(TICKET_WITH_NON_VEG,0),
COALESCE(TICKET_WITH_HOT,0),
COALESCE(TICKET_WITH_COLD,0),
COALESCE(TICKET_WITH_BAKERY,0),
COALESCE(TICKET_WITH_COMBO,0),
COALESCE(TICKET_WITH_MERCHANDISE,0),
COALESCE(TICKET_WITH_OTHER,0),
COALESCE(TICKET_ON_MONDAY,0),
COALESCE(TICKET_ON_TUESDAY,0),
COALESCE(TICKET_ON_WEDNESDAY,0),
COALESCE(TICKET_ON_THURSDAY,0),
COALESCE(TICKET_ON_FRIDAY,0),
COALESCE(TICKET_ON_SATURDAY,0),
COALESCE(TICKET_ON_SUNDAY,0),
COALESCE(TICKET_ON_WEEKDAY,0),
COALESCE(TICKET_ON_WEEKEND,0),
COALESCE(TICKET_IN_BREAKFAST,0),
COALESCE(TICKET_IN_LUNCH,0),
COALESCE(TICKET_IN_EVENING,0),
COALESCE(TICKET_IN_DINNER,0),
COALESCE(TICKET_IN_POST_DINNER,0),
COALESCE(TICKET_IN_NIGHT,0),
COALESCE(CASH_TICKET,0),
COALESCE(CARD_TICKET,0),
COALESCE(AMEX_TICKET,0),
COALESCE(PAYTM_TICKET,0),
COALESCE(GIFT_CARD_TICKET,0),
COALESCE(MOBIKWIK_TICKET,0),
COALESCE(ONLINE_PAYMENT_TICKET,0),
COALESCE(OTHER_PAYMENT_TICKET,0),
COALESCE(SPLIT_PAYMENT_TICKET,0),
COALESCE(LAST_PAYMENT_MODE,0),
COALESCE(ONE_NPS_TICKET,0),
COALESCE(TWO_NPS_TICKET,0),
COALESCE(THREE_NPS_TICKET,0),
COALESCE(FOUR_NPS_TICKET,0),
COALESCE(FIVE_NPS_TICKET,0),
COALESCE(SIX_NPS_TICKET,0),
COALESCE(SEVEN_NPS_TICKET,0),
COALESCE(EIGHT_NPS_TICKET,0),
COALESCE(NINE_NPS_TICKET,0),
COALESCE(TEN_NPS_TICKET,0),
LAST_NPS_SCORE,
COALESCE(NEGATIVE_NPS_TICKET,0),
COALESCE(POSITIVE_NPS_TICKET,0),
COALESCE(NEUTRAL_NPS_TICKET,0),
COALESCE(ONE_FEEDBACK_TICKET,0),
COALESCE(TWO_FEEDBACK_TICKET,0),
COALESCE(THREE_FEEDBACK_TICKET,0),
COALESCE(FOUR_FEEDBACK_TICKET,0),
COALESCE(FIVE_FEEDBACK_TICKET,0),
LAST_FEEDBACK_SCORE,
COALESCE(TOTAL_SPEND,0),
COALESCE(TOTAL_DISCOUNT,0),
COALESCE(TOTAL_APC,0),
COALESCE(MINIMUM_APC,0),
COALESCE(MAXIMUM_APC,0),
COALESCE(DELIVERY_SPEND,0),
COALESCE(DELIVERY_DISCOUNT,0),
COALESCE(DELIVERY_APC,0),
COALESCE(DELIVERY_MINIMUM_APC,0),
COALESCE(DELIVERY_MAXIMUM_APC,0),
COALESCE(DINE_IN_SPEND,0),
COALESCE(DINE_IN_DISCOUNT,0),
COALESCE(DINE_IN_APC,0),
COALESCE(DINE_IN_MINIMUM_APC,0),
COALESCE(DINE_IN_MAXIMUM_APC,0),
COALESCE(CASH_SPEND,0),
COALESCE(CARD_SPEND,0),
COALESCE(AMEX_SPEND,0),
COALESCE(PAYTM_SPEND,0),
COALESCE(GIFT_CARD_SPEND,0),
COALESCE(MOBIKWIK_SPEND,0),
COALESCE(ONLINE_SPEND,0),
COALESCE(OTHER_PAYMENT_SPEND,0),
COALESCE(TICKET_WITH_GIFT_CARD,0),
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
COALESCE(ZERO_AMOUNT_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT,0),
COALESCE(ONLY_GIFT_CARD_TICKET,0)
from CLM_ANALYTICS.TEMP_CUSTOMER_DATA t, CLM_ANALYTICS.ACTIVE_CUSTOMERS a
where a.CUSTOMER_ID = t.CUSTOMER_ID
and a.IS_NEW = 'Y'
;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS a,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA t,
    CLM_ANALYTICS.ACTIVE_CUSTOMERS m 
SET 
    a.TOTAL_UNITS_VISITED = t.TOTAL_UNITS_VISITED,
    a.UNIQUE_VISIT_DAYS = t.UNIQUE_VISIT_DAYS,
    a.LAST_ORDER_ID = t.LAST_ORDER_ID,
    a.LAST_ORDER_DATE = t.LAST_ORDER_DATE,
    a.TICKET_COUNT = a.TICKET_COUNT + COALESCE(t.TICKET_COUNT, 0),
    a.CANCELLED_TICKET_COUNT = a.CANCELLED_TICKET_COUNT + COALESCE(t.CANCELLED_TICKET_COUNT, 0),
    a.TICKET_WITH_OFFER = a.TICKET_WITH_OFFER + COALESCE(t.TICKET_WITH_OFFER, 0),
    a.TICKET_WITH_REDEMPTION = a.TICKET_WITH_REDEMPTION + COALESCE(t.TICKET_WITH_REDEMPTION, 0),
    a.DINE_IN_TICKET = a.DINE_IN_TICKET + COALESCE(t.DINE_IN_TICKET, 0),
    a.DELIVERY_TICKET = a.DELIVERY_TICKET + COALESCE(t.DELIVERY_TICKET, 0),
    a.TAKE_AWAY_TICKET = a.TAKE_AWAY_TICKET + COALESCE(t.TAKE_AWAY_TICKET, 0),
    a.ZOMATO_TICKET = a.ZOMATO_TICKET + COALESCE(t.ZOMATO_TICKET, 0),
    a.SWIGGY_TICKET = a.SWIGGY_TICKET + COALESCE(t.SWIGGY_TICKET, 0),
    a.FOOD_PANDA_TICKET = a.FOOD_PANDA_TICKET + COALESCE(t.FOOD_PANDA_TICKET, 0),
    a.UBER_EATS_TICKET = a.UBER_EATS_TICKET + COALESCE(t.UBER_EATS_TICKET, 0),
    a.OLD_APP_TICKET = a.OLD_APP_TICKET + COALESCE(t.OLD_APP_TICKET, 0),
    a.WEB_APP_TICKET = a.WEB_APP_TICKET + COALESCE(t.WEB_APP_TICKET, 0),
    a.CALL_CENTER_TICKET = a.CALL_CENTER_TICKET + COALESCE(t.CALL_CENTER_TICKET, 0),
    a.OTHER_PARTNER_TICKET = a.OTHER_PARTNER_TICKET + COALESCE(t.OTHER_PARTNER_TICKET, 0),
    a.LAST_UNIT_ID = t.LAST_UNIT_ID,
    a.PEOPLE_PER_TICKET = COALESCE(t.PEOPLE_PER_TICKET, 0),
    a.MINIMUM_PEOPLE_PER_ORDER = COALESCE(t.MINIMUM_PEOPLE_PER_ORDER, 0),
    a.MAXIMUM_PEOPLE_PER_ORDER = COALESCE(t.MAXIMUM_PEOPLE_PER_ORDER, 0),
    a.DAY_GAP_SINCE_LAST_ORDER = COALESCE(t.DAY_GAP_SINCE_LAST_ORDER, 0),
    a.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD + COALESCE(t.TICKET_WITH_FOOD, 0),
    a.TICKET_WITH_VEG = a.TICKET_WITH_VEG + COALESCE(t.TICKET_WITH_VEG, 0),
    a.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG + COALESCE(t.TICKET_WITH_NON_VEG, 0),
    a.TICKET_WITH_HOT = a.TICKET_WITH_HOT + COALESCE(t.TICKET_WITH_HOT, 0),
    a.TICKET_WITH_COLD = a.TICKET_WITH_COLD + COALESCE(t.TICKET_WITH_COLD, 0),
    a.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY + COALESCE(t.TICKET_WITH_BAKERY, 0),
    a.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO + COALESCE(t.TICKET_WITH_COMBO, 0),
    a.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE + COALESCE(t.TICKET_WITH_MERCHANDISE, 0),
    a.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER + COALESCE(t.TICKET_WITH_OTHER, 0),
    a.TICKET_ON_MONDAY = a.TICKET_ON_MONDAY + COALESCE(t.TICKET_ON_MONDAY, 0),
    a.TICKET_ON_TUESDAY = a.TICKET_ON_TUESDAY + COALESCE(t.TICKET_ON_TUESDAY, 0),
    a.TICKET_ON_WEDNESDAY = a.TICKET_ON_WEDNESDAY + COALESCE(t.TICKET_ON_WEDNESDAY, 0),
    a.TICKET_ON_THURSDAY = a.TICKET_ON_THURSDAY + COALESCE(t.TICKET_ON_THURSDAY, 0),
    a.TICKET_ON_FRIDAY = a.TICKET_ON_FRIDAY + COALESCE(t.TICKET_ON_FRIDAY, 0),
    a.TICKET_ON_SATURDAY = a.TICKET_ON_SATURDAY + COALESCE(t.TICKET_ON_SATURDAY, 0),
    a.TICKET_ON_SUNDAY = a.TICKET_ON_SUNDAY + COALESCE(t.TICKET_ON_SUNDAY, 0),
    a.TICKET_ON_WEEKDAY = a.TICKET_ON_WEEKDAY + COALESCE(t.TICKET_ON_WEEKDAY, 0),
    a.TICKET_ON_WEEKEND = a.TICKET_ON_WEEKEND + COALESCE(t.TICKET_ON_WEEKEND, 0),
    a.TICKET_IN_BREAKFAST = a.TICKET_IN_BREAKFAST + COALESCE(t.TICKET_IN_BREAKFAST, 0),
    a.TICKET_IN_LUNCH = a.TICKET_IN_LUNCH + COALESCE(t.TICKET_IN_LUNCH, 0),
    a.TICKET_IN_EVENING = a.TICKET_IN_EVENING + COALESCE(t.TICKET_IN_EVENING, 0),
    a.TICKET_IN_DINNER = a.TICKET_IN_DINNER + COALESCE(t.TICKET_IN_DINNER, 0),
    a.TICKET_IN_POST_DINNER = a.TICKET_IN_POST_DINNER + COALESCE(t.TICKET_IN_POST_DINNER, 0),
    a.TICKET_IN_NIGHT = a.TICKET_IN_NIGHT + COALESCE(t.TICKET_IN_NIGHT, 0),
    a.CASH_TICKET = a.CASH_TICKET + COALESCE(t.CASH_TICKET, 0),
    a.CARD_TICKET = a.CARD_TICKET + COALESCE(t.CARD_TICKET, 0),
    a.AMEX_TICKET = a.AMEX_TICKET + COALESCE(t.AMEX_TICKET, 0),
    a.PAYTM_TICKET = a.PAYTM_TICKET + COALESCE(t.PAYTM_TICKET, 0),
    a.GIFT_CARD_TICKET = a.GIFT_CARD_TICKET + COALESCE(t.GIFT_CARD_TICKET, 0),
    a.MOBIKWIK_TICKET = a.MOBIKWIK_TICKET + COALESCE(t.MOBIKWIK_TICKET, 0),
    a.ONLINE_PAYMENT_TICKET = a.ONLINE_PAYMENT_TICKET + COALESCE(t.ONLINE_PAYMENT_TICKET, 0),
    a.OTHER_PAYMENT_TICKET = a.OTHER_PAYMENT_TICKET + COALESCE(t.OTHER_PAYMENT_TICKET, 0),
    a.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET + COALESCE(t.SPLIT_PAYMENT_TICKET, 0),
    a.LAST_PAYMENT_MODE = t.LAST_PAYMENT_MODE,
    a.ONE_NPS_TICKET = COALESCE(t.ONE_NPS_TICKET, 0),
    a.TWO_NPS_TICKET = COALESCE(t.TWO_NPS_TICKET, 0),
    a.THREE_NPS_TICKET = COALESCE(t.THREE_NPS_TICKET, 0),
    a.FOUR_NPS_TICKET = COALESCE(t.FOUR_NPS_TICKET, 0),
    a.FIVE_NPS_TICKET = COALESCE(t.FIVE_NPS_TICKET, 0),
    a.SIX_NPS_TICKET = COALESCE(t.SIX_NPS_TICKET, 0),
    a.SEVEN_NPS_TICKET = COALESCE(t.SEVEN_NPS_TICKET, 0),
    a.EIGHT_NPS_TICKET = COALESCE(t.EIGHT_NPS_TICKET, 0),
    a.NINE_NPS_TICKET = COALESCE(t.NINE_NPS_TICKET, 0),
    a.TEN_NPS_TICKET = COALESCE(t.TEN_NPS_TICKET, 0),
    a.LAST_NPS_SCORE = t.LAST_NPS_SCORE,
    a.NEGATIVE_NPS_TICKET = COALESCE(t.NEGATIVE_NPS_TICKET, 0),
    a.POSITIVE_NPS_TICKET = COALESCE(t.POSITIVE_NPS_TICKET, 0),
    a.NEUTRAL_NPS_TICKET = COALESCE(t.NEUTRAL_NPS_TICKET, 0),
    a.ONE_FEEDBACK_TICKET = COALESCE(t.ONE_FEEDBACK_TICKET, 0),
    a.TWO_FEEDBACK_TICKET = COALESCE(t.TWO_FEEDBACK_TICKET, 0),
    a.THREE_FEEDBACK_TICKET = COALESCE(t.THREE_FEEDBACK_TICKET, 0),
    a.FOUR_FEEDBACK_TICKET = COALESCE(t.FOUR_FEEDBACK_TICKET, 0),
    a.FIVE_FEEDBACK_TICKET = COALESCE(t.FIVE_FEEDBACK_TICKET, 0),
    a.LAST_FEEDBACK_SCORE = t.LAST_FEEDBACK_SCORE,
    a.TOTAL_SPEND = a.TOTAL_SPEND + COALESCE(t.TOTAL_SPEND, 0),
    a.TOTAL_DISCOUNT = a.TOTAL_DISCOUNT + COALESCE(t.TOTAL_DISCOUNT, 0),
    a.TOTAL_APC = COALESCE(t.TOTAL_APC, 0),
    a.MINIMUM_APC = COALESCE(t.MINIMUM_APC, 0),
    a.MAXIMUM_APC = COALESCE(t.MAXIMUM_APC, 0),
    a.DELIVERY_SPEND = a.DELIVERY_SPEND + COALESCE(t.DELIVERY_SPEND, 0),
    a.DELIVERY_DISCOUNT = a.DELIVERY_DISCOUNT + COALESCE(t.DELIVERY_DISCOUNT, 0),
    a.DELIVERY_APC = COALESCE(t.DELIVERY_APC, 0),
    a.DELIVERY_MINIMUM_APC = COALESCE(t.DELIVERY_MINIMUM_APC, 0),
    a.DELIVERY_MAXIMUM_APC = COALESCE(t.DELIVERY_MAXIMUM_APC, 0),
    a.DINE_IN_SPEND = a.DINE_IN_SPEND + COALESCE(t.DINE_IN_SPEND, 0),
    a.DINE_IN_DISCOUNT = a.DINE_IN_DISCOUNT + COALESCE(t.DINE_IN_DISCOUNT, 0),
    a.DINE_IN_APC = COALESCE(t.DINE_IN_APC, 0),
    a.DINE_IN_MINIMUM_APC = COALESCE(t.DINE_IN_MINIMUM_APC, 0),
    a.DINE_IN_MAXIMUM_APC = COALESCE(t.DINE_IN_MAXIMUM_APC, 0),
    a.CASH_SPEND = a.CASH_SPEND + COALESCE(t.CASH_SPEND, 0),
    a.CARD_SPEND = a.CARD_SPEND + COALESCE(t.CARD_SPEND, 0),
    a.AMEX_SPEND = a.AMEX_SPEND + COALESCE(t.AMEX_SPEND, 0),
    a.PAYTM_SPEND = a.PAYTM_SPEND + COALESCE(t.PAYTM_SPEND, 0),
    a.GIFT_CARD_SPEND = a.GIFT_CARD_SPEND + COALESCE(t.GIFT_CARD_SPEND, 0),
    a.MOBIKWIK_SPEND = a.MOBIKWIK_SPEND + COALESCE(t.MOBIKWIK_SPEND, 0),
    a.ONLINE_SPEND = a.ONLINE_SPEND + COALESCE(t.ONLINE_SPEND, 0),
    a.OTHER_PAYMENT_SPEND = a.OTHER_PAYMENT_SPEND + COALESCE(t.OTHER_PAYMENT_SPEND, 0),
    a.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD + COALESCE(t.TICKET_WITH_GIFT_CARD, 0),
    a.LAST_UNIT_NAME = t.LAST_UNIT_NAME,
    a.ZERO_AMOUNT_TICKET_COUNT = a.ZERO_AMOUNT_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT = a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT = a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0),
    a.ONLY_GIFT_CARD_TICKET = a.ONLY_GIFT_CARD_TICKET + COALESCE(t.ONLY_GIFT_CARD_TICKET, 0)
WHERE
    a.CUSTOMER_ID = t.CUSTOMER_ID
        AND t.CUSTOMER_ID = m.CUSTOMER_ID
        AND m.IS_NEW = 'N';

delete from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS WHERE LAST_ORDER_DATE < DATE_ADD(CURR_DATE, INTERVAL -15 DAY);

END$$
DELIMITER ;


DROP PROCEDURE IF EXISTS KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_7_DAYS;
DELIMITER $$
CREATE PROCEDURE KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_7_DAYS()
proc_label : BEGIN

DECLARE TILL_DATE DATE;
DECLARE CURR_DATE DATE;

select case when max(BUSINESS_DATE) is null then '2018-01-18' else max(BUSINESS_DATE) end into CURR_DATE from KETTLE_DUMP.ORDER_DETAIL;
select case when max(LAST_ORDER_DATE) is null then '2018-01-18' else max(LAST_ORDER_DATE) end into TILL_DATE from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS;


IF CURR_DATE IS NOT NULL AND CURR_DATE <= TILL_DATE
THEN 
	LEAVE proc_label;
END IF;

DROP TABLE IF EXISTS CLM_ANALYTICS.ACTIVE_CUSTOMERS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS AS SELECT DISTINCT od.CUSTOMER_ID FROM
    KETTLE_DUMP.ORDER_DETAIL od
WHERE
    od.BUSINESS_DATE = CURR_DATE
        AND od.CUSTOMER_ID > 5
        AND CUSTOMER_ID NOT IN (67456,142315);

CREATE INDEX CUSTOMER_ID_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(CUSTOMER_ID) USING BTREE;

ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMERS ADD COLUMN IS_NEW VARCHAR(1) NOT NULL DEFAULT 'N';

CREATE INDEX IS_NEW_ACTIVE_CUSTOMERS ON CLM_ANALYTICS.ACTIVE_CUSTOMERS(IS_NEW) USING BTREE;

update CLM_ANALYTICS.ACTIVE_CUSTOMERS a 
left outer join CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS c
ON a.CUSTOMER_ID = c.CUSTOMER_ID 
SET IS_NEW = case when c.CUSTOMER_ID IS NOT NULL THEN 'N' ELSE 'Y' END;

DROP TABLE IF EXISTS CLM_ANALYTICS.TEMP_CUSTOMER_DATA;
CREATE TABLE CLM_ANALYTICS.TEMP_CUSTOMER_DATA (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    UNIQUE_VISIT_DAYS INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_TEMP_CUSTOMER_ID ON CLM_ANALYTICS.TEMP_CUSTOMER_DATA(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.TEMP_CUSTOMER_DATA
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_DISCOUNT
)

  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT
  FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  AND od.BUSINESS_DATE = CURR_DATE
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
(select
	od.CUSTOMER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC
 FROM KETTLE_DUMP.ORDER_DETAIL od,
       CLM_ANALYTICS.ACTIVE_CUSTOMERS ac
  WHERE od.CUSTOMER_ID > 5
   AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -7 DAY)
  AND od.BUSINESS_DATE <= CURR_DATE
  AND od.CUSTOMER_ID = ac.CUSTOMER_ID
  GROUP BY od.CUSTOMER_ID
  )a
  SET 
  c.TOTAL_UNITS_VISITED = a.TOTAL_UNITS_VISITED,
  c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS,
  c.MINIMUM_APC = a.MINIMUM_APC,
  c.MAXIMUM_APC = a.MAXIMUM_APC,
  c.DELIVERY_MINIMUM_APC = a.DELIVERY_MINIMUM_APC,
  c.DELIVERY_MAXIMUM_APC = a.DELIVERY_MAXIMUM_APC,
  c.DINE_IN_MINIMUM_APC = a.DINE_IN_MINIMUM_APC,
  c.DINE_IN_MAXIMUM_APC = a.DINE_IN_MAXIMUM_APC
  WHERE a.CUSTOMER_ID = c.CUSTOMER_ID;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
        WHERE o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os, KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
    od.ORDER_ID = os.ORDER_ID AND
    od.CUSTOMER_ID = a.CUSTOMER_ID
     AND    od.ORDER_STATUS <> 'CANCELLED'
     AND od.BUSINESS_DATE = CURR_DATE
     GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        o.CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o,
        CLM_ANALYTICS.ACTIVE_CUSTOMERS a
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
        AND o.CUSTOMER_ID = a.CUSTOMER_ID
    GROUP BY o.CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID
;



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        a.CUSTOMER_ID,
            SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN
                    a.HOT_QUANTITY = 0
                        AND a.HOT_QUANTITY = 0
                        AND a.FOOD_QUANTITY = 0
                        AND a.COLD_QUANTITY = 0
                        AND a.BAKERY_QUANTITY = 0
                        AND a.COMBO_QUANTITY = 0
                        AND a.MERCHANDISE_QUANTITY = 0
                        AND a.OTHERS_QUANTITY = 0
                        AND a.GIFT_CARD_QUANTITY > 0
                THEN
                    1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND od.BUSINESS_DATE = CURR_DATE 
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET = a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;

    UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od,CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND od.BUSINESS_DATE >= DATE_ADD(CURR_DATE, INTERVAL -7 DAY)
             AND od.BUSINESS_DATE <= CURR_DATE
            AND od.CUSTOMER_ID = a.CUSTOMER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;


UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, CLM_ANALYTICS.ACTIVE_CUSTOMERS a, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
        AND od.CUSTOMER_ID = a.CUSTOMER_ID
        
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = CURR_DATE
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        

UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';



UPDATE CLM_ANALYTICS.TEMP_CUSTOMER_DATA 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;



update CLM_ANALYTICS.TEMP_CUSTOMER_DATA c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c1,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

INSERT INTO CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS
(CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
TICKET_COUNT,
CANCELLED_TICKET_COUNT,
TICKET_WITH_OFFER,
TICKET_WITH_REDEMPTION,
DINE_IN_TICKET,
DELIVERY_TICKET,
TAKE_AWAY_TICKET,
ZOMATO_TICKET,
SWIGGY_TICKET,
FOOD_PANDA_TICKET,
UBER_EATS_TICKET,
OLD_APP_TICKET,
WEB_APP_TICKET,
CALL_CENTER_TICKET,
OTHER_PARTNER_TICKET,
FIRST_UNIT_ID,
LAST_UNIT_ID,
PEOPLE_PER_TICKET,
MINIMUM_PEOPLE_PER_ORDER,
MAXIMUM_PEOPLE_PER_ORDER,
DAY_GAP_SINCE_LAST_ORDER,
TICKET_WITH_FOOD,
TICKET_WITH_VEG,
TICKET_WITH_NON_VEG,
TICKET_WITH_HOT,
TICKET_WITH_COLD,
TICKET_WITH_BAKERY,
TICKET_WITH_COMBO,
TICKET_WITH_MERCHANDISE,
TICKET_WITH_OTHER,
TICKET_ON_MONDAY,
TICKET_ON_TUESDAY,
TICKET_ON_WEDNESDAY,
TICKET_ON_THURSDAY,
TICKET_ON_FRIDAY,
TICKET_ON_SATURDAY,
TICKET_ON_SUNDAY,
TICKET_ON_WEEKDAY,
TICKET_ON_WEEKEND,
TICKET_IN_BREAKFAST,
TICKET_IN_LUNCH,
TICKET_IN_EVENING,
TICKET_IN_DINNER,
TICKET_IN_POST_DINNER,
TICKET_IN_NIGHT,
CASH_TICKET,
CARD_TICKET,
AMEX_TICKET,
PAYTM_TICKET,
GIFT_CARD_TICKET,
MOBIKWIK_TICKET,
ONLINE_PAYMENT_TICKET,
OTHER_PAYMENT_TICKET,
SPLIT_PAYMENT_TICKET,
LAST_PAYMENT_MODE,
ONE_NPS_TICKET,
TWO_NPS_TICKET,
THREE_NPS_TICKET,
FOUR_NPS_TICKET,
FIVE_NPS_TICKET,
SIX_NPS_TICKET,
SEVEN_NPS_TICKET,
EIGHT_NPS_TICKET,
NINE_NPS_TICKET,
TEN_NPS_TICKET,
LAST_NPS_SCORE,
NEGATIVE_NPS_TICKET,
POSITIVE_NPS_TICKET,
NEUTRAL_NPS_TICKET,
ONE_FEEDBACK_TICKET,
TWO_FEEDBACK_TICKET,
THREE_FEEDBACK_TICKET,
FOUR_FEEDBACK_TICKET,
FIVE_FEEDBACK_TICKET,
LAST_FEEDBACK_SCORE,
TOTAL_SPEND,
TOTAL_DISCOUNT,
TOTAL_APC,
MINIMUM_APC,
MAXIMUM_APC,
DELIVERY_SPEND,
DELIVERY_DISCOUNT,
DELIVERY_APC,
DELIVERY_MINIMUM_APC,
DELIVERY_MAXIMUM_APC,
DINE_IN_SPEND,
DINE_IN_DISCOUNT,
DINE_IN_APC,
DINE_IN_MINIMUM_APC,
DINE_IN_MAXIMUM_APC,
CASH_SPEND,
CARD_SPEND,
AMEX_SPEND,
PAYTM_SPEND,
GIFT_CARD_SPEND,
MOBIKWIK_SPEND,
ONLINE_SPEND,
OTHER_PAYMENT_SPEND,
TICKET_WITH_GIFT_CARD,
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
ZERO_AMOUNT_TICKET_COUNT,
ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
ONLY_GIFT_CARD_TICKET)
select 
t.CUSTOMER_ID,
TOTAL_UNITS_VISITED,
UNIQUE_VISIT_DAYS,
FIRST_ORDER_ID,
LAST_ORDER_ID,
FIRST_ORDER_DATE,
LAST_ORDER_DATE,
COALESCE(TICKET_COUNT,0),
COALESCE(CANCELLED_TICKET_COUNT,0),
COALESCE(TICKET_WITH_OFFER,0),
COALESCE(TICKET_WITH_REDEMPTION,0),
COALESCE(DINE_IN_TICKET,0),
COALESCE(DELIVERY_TICKET,0),
COALESCE(TAKE_AWAY_TICKET,0),
COALESCE(ZOMATO_TICKET,0),
COALESCE(SWIGGY_TICKET,0),
COALESCE(FOOD_PANDA_TICKET,0),
COALESCE(UBER_EATS_TICKET,0),
COALESCE(OLD_APP_TICKET,0),
COALESCE(WEB_APP_TICKET,0),
COALESCE(CALL_CENTER_TICKET,0),
COALESCE(OTHER_PARTNER_TICKET,0),
FIRST_UNIT_ID,
LAST_UNIT_ID,
COALESCE(PEOPLE_PER_TICKET,0),
COALESCE(MINIMUM_PEOPLE_PER_ORDER,0),
COALESCE(MAXIMUM_PEOPLE_PER_ORDER,0),
COALESCE(DAY_GAP_SINCE_LAST_ORDER,0),
COALESCE(TICKET_WITH_FOOD,0),
COALESCE(TICKET_WITH_VEG,0),
COALESCE(TICKET_WITH_NON_VEG,0),
COALESCE(TICKET_WITH_HOT,0),
COALESCE(TICKET_WITH_COLD,0),
COALESCE(TICKET_WITH_BAKERY,0),
COALESCE(TICKET_WITH_COMBO,0),
COALESCE(TICKET_WITH_MERCHANDISE,0),
COALESCE(TICKET_WITH_OTHER,0),
COALESCE(TICKET_ON_MONDAY,0),
COALESCE(TICKET_ON_TUESDAY,0),
COALESCE(TICKET_ON_WEDNESDAY,0),
COALESCE(TICKET_ON_THURSDAY,0),
COALESCE(TICKET_ON_FRIDAY,0),
COALESCE(TICKET_ON_SATURDAY,0),
COALESCE(TICKET_ON_SUNDAY,0),
COALESCE(TICKET_ON_WEEKDAY,0),
COALESCE(TICKET_ON_WEEKEND,0),
COALESCE(TICKET_IN_BREAKFAST,0),
COALESCE(TICKET_IN_LUNCH,0),
COALESCE(TICKET_IN_EVENING,0),
COALESCE(TICKET_IN_DINNER,0),
COALESCE(TICKET_IN_POST_DINNER,0),
COALESCE(TICKET_IN_NIGHT,0),
COALESCE(CASH_TICKET,0),
COALESCE(CARD_TICKET,0),
COALESCE(AMEX_TICKET,0),
COALESCE(PAYTM_TICKET,0),
COALESCE(GIFT_CARD_TICKET,0),
COALESCE(MOBIKWIK_TICKET,0),
COALESCE(ONLINE_PAYMENT_TICKET,0),
COALESCE(OTHER_PAYMENT_TICKET,0),
COALESCE(SPLIT_PAYMENT_TICKET,0),
COALESCE(LAST_PAYMENT_MODE,0),
COALESCE(ONE_NPS_TICKET,0),
COALESCE(TWO_NPS_TICKET,0),
COALESCE(THREE_NPS_TICKET,0),
COALESCE(FOUR_NPS_TICKET,0),
COALESCE(FIVE_NPS_TICKET,0),
COALESCE(SIX_NPS_TICKET,0),
COALESCE(SEVEN_NPS_TICKET,0),
COALESCE(EIGHT_NPS_TICKET,0),
COALESCE(NINE_NPS_TICKET,0),
COALESCE(TEN_NPS_TICKET,0),
LAST_NPS_SCORE,
COALESCE(NEGATIVE_NPS_TICKET,0),
COALESCE(POSITIVE_NPS_TICKET,0),
COALESCE(NEUTRAL_NPS_TICKET,0),
COALESCE(ONE_FEEDBACK_TICKET,0),
COALESCE(TWO_FEEDBACK_TICKET,0),
COALESCE(THREE_FEEDBACK_TICKET,0),
COALESCE(FOUR_FEEDBACK_TICKET,0),
COALESCE(FIVE_FEEDBACK_TICKET,0),
LAST_FEEDBACK_SCORE,
COALESCE(TOTAL_SPEND,0),
COALESCE(TOTAL_DISCOUNT,0),
COALESCE(TOTAL_APC,0),
COALESCE(MINIMUM_APC,0),
COALESCE(MAXIMUM_APC,0),
COALESCE(DELIVERY_SPEND,0),
COALESCE(DELIVERY_DISCOUNT,0),
COALESCE(DELIVERY_APC,0),
COALESCE(DELIVERY_MINIMUM_APC,0),
COALESCE(DELIVERY_MAXIMUM_APC,0),
COALESCE(DINE_IN_SPEND,0),
COALESCE(DINE_IN_DISCOUNT,0),
COALESCE(DINE_IN_APC,0),
COALESCE(DINE_IN_MINIMUM_APC,0),
COALESCE(DINE_IN_MAXIMUM_APC,0),
COALESCE(CASH_SPEND,0),
COALESCE(CARD_SPEND,0),
COALESCE(AMEX_SPEND,0),
COALESCE(PAYTM_SPEND,0),
COALESCE(GIFT_CARD_SPEND,0),
COALESCE(MOBIKWIK_SPEND,0),
COALESCE(ONLINE_SPEND,0),
COALESCE(OTHER_PAYMENT_SPEND,0),
COALESCE(TICKET_WITH_GIFT_CARD,0),
FIRST_UNIT_NAME,
LAST_UNIT_NAME,
COALESCE(ZERO_AMOUNT_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT,0),
COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT,0),
COALESCE(ONLY_GIFT_CARD_TICKET,0)
from CLM_ANALYTICS.TEMP_CUSTOMER_DATA t, CLM_ANALYTICS.ACTIVE_CUSTOMERS a
where a.CUSTOMER_ID = t.CUSTOMER_ID
and a.IS_NEW = 'Y'
;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS a,
    CLM_ANALYTICS.TEMP_CUSTOMER_DATA t,
    CLM_ANALYTICS.ACTIVE_CUSTOMERS m 
SET 
    a.TOTAL_UNITS_VISITED = t.TOTAL_UNITS_VISITED,
    a.UNIQUE_VISIT_DAYS = t.UNIQUE_VISIT_DAYS,
    a.LAST_ORDER_ID = t.LAST_ORDER_ID,
    a.LAST_ORDER_DATE = t.LAST_ORDER_DATE,
    a.TICKET_COUNT = a.TICKET_COUNT + COALESCE(t.TICKET_COUNT, 0),
    a.CANCELLED_TICKET_COUNT = a.CANCELLED_TICKET_COUNT + COALESCE(t.CANCELLED_TICKET_COUNT, 0),
    a.TICKET_WITH_OFFER = a.TICKET_WITH_OFFER + COALESCE(t.TICKET_WITH_OFFER, 0),
    a.TICKET_WITH_REDEMPTION = a.TICKET_WITH_REDEMPTION + COALESCE(t.TICKET_WITH_REDEMPTION, 0),
    a.DINE_IN_TICKET = a.DINE_IN_TICKET + COALESCE(t.DINE_IN_TICKET, 0),
    a.DELIVERY_TICKET = a.DELIVERY_TICKET + COALESCE(t.DELIVERY_TICKET, 0),
    a.TAKE_AWAY_TICKET = a.TAKE_AWAY_TICKET + COALESCE(t.TAKE_AWAY_TICKET, 0),
    a.ZOMATO_TICKET = a.ZOMATO_TICKET + COALESCE(t.ZOMATO_TICKET, 0),
    a.SWIGGY_TICKET = a.SWIGGY_TICKET + COALESCE(t.SWIGGY_TICKET, 0),
    a.FOOD_PANDA_TICKET = a.FOOD_PANDA_TICKET + COALESCE(t.FOOD_PANDA_TICKET, 0),
    a.UBER_EATS_TICKET = a.UBER_EATS_TICKET + COALESCE(t.UBER_EATS_TICKET, 0),
    a.OLD_APP_TICKET = a.OLD_APP_TICKET + COALESCE(t.OLD_APP_TICKET, 0),
    a.WEB_APP_TICKET = a.WEB_APP_TICKET + COALESCE(t.WEB_APP_TICKET, 0),
    a.CALL_CENTER_TICKET = a.CALL_CENTER_TICKET + COALESCE(t.CALL_CENTER_TICKET, 0),
    a.OTHER_PARTNER_TICKET = a.OTHER_PARTNER_TICKET + COALESCE(t.OTHER_PARTNER_TICKET, 0),
    a.LAST_UNIT_ID = t.LAST_UNIT_ID,
    a.PEOPLE_PER_TICKET = COALESCE(t.PEOPLE_PER_TICKET, 0),
    a.MINIMUM_PEOPLE_PER_ORDER = COALESCE(t.MINIMUM_PEOPLE_PER_ORDER, 0),
    a.MAXIMUM_PEOPLE_PER_ORDER = COALESCE(t.MAXIMUM_PEOPLE_PER_ORDER, 0),
    a.DAY_GAP_SINCE_LAST_ORDER = COALESCE(t.DAY_GAP_SINCE_LAST_ORDER, 0),
    a.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD + COALESCE(t.TICKET_WITH_FOOD, 0),
    a.TICKET_WITH_VEG = a.TICKET_WITH_VEG + COALESCE(t.TICKET_WITH_VEG, 0),
    a.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG + COALESCE(t.TICKET_WITH_NON_VEG, 0),
    a.TICKET_WITH_HOT = a.TICKET_WITH_HOT + COALESCE(t.TICKET_WITH_HOT, 0),
    a.TICKET_WITH_COLD = a.TICKET_WITH_COLD + COALESCE(t.TICKET_WITH_COLD, 0),
    a.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY + COALESCE(t.TICKET_WITH_BAKERY, 0),
    a.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO + COALESCE(t.TICKET_WITH_COMBO, 0),
    a.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE + COALESCE(t.TICKET_WITH_MERCHANDISE, 0),
    a.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER + COALESCE(t.TICKET_WITH_OTHER, 0),
    a.TICKET_ON_MONDAY = a.TICKET_ON_MONDAY + COALESCE(t.TICKET_ON_MONDAY, 0),
    a.TICKET_ON_TUESDAY = a.TICKET_ON_TUESDAY + COALESCE(t.TICKET_ON_TUESDAY, 0),
    a.TICKET_ON_WEDNESDAY = a.TICKET_ON_WEDNESDAY + COALESCE(t.TICKET_ON_WEDNESDAY, 0),
    a.TICKET_ON_THURSDAY = a.TICKET_ON_THURSDAY + COALESCE(t.TICKET_ON_THURSDAY, 0),
    a.TICKET_ON_FRIDAY = a.TICKET_ON_FRIDAY + COALESCE(t.TICKET_ON_FRIDAY, 0),
    a.TICKET_ON_SATURDAY = a.TICKET_ON_SATURDAY + COALESCE(t.TICKET_ON_SATURDAY, 0),
    a.TICKET_ON_SUNDAY = a.TICKET_ON_SUNDAY + COALESCE(t.TICKET_ON_SUNDAY, 0),
    a.TICKET_ON_WEEKDAY = a.TICKET_ON_WEEKDAY + COALESCE(t.TICKET_ON_WEEKDAY, 0),
    a.TICKET_ON_WEEKEND = a.TICKET_ON_WEEKEND + COALESCE(t.TICKET_ON_WEEKEND, 0),
    a.TICKET_IN_BREAKFAST = a.TICKET_IN_BREAKFAST + COALESCE(t.TICKET_IN_BREAKFAST, 0),
    a.TICKET_IN_LUNCH = a.TICKET_IN_LUNCH + COALESCE(t.TICKET_IN_LUNCH, 0),
    a.TICKET_IN_EVENING = a.TICKET_IN_EVENING + COALESCE(t.TICKET_IN_EVENING, 0),
    a.TICKET_IN_DINNER = a.TICKET_IN_DINNER + COALESCE(t.TICKET_IN_DINNER, 0),
    a.TICKET_IN_POST_DINNER = a.TICKET_IN_POST_DINNER + COALESCE(t.TICKET_IN_POST_DINNER, 0),
    a.TICKET_IN_NIGHT = a.TICKET_IN_NIGHT + COALESCE(t.TICKET_IN_NIGHT, 0),
    a.CASH_TICKET = a.CASH_TICKET + COALESCE(t.CASH_TICKET, 0),
    a.CARD_TICKET = a.CARD_TICKET + COALESCE(t.CARD_TICKET, 0),
    a.AMEX_TICKET = a.AMEX_TICKET + COALESCE(t.AMEX_TICKET, 0),
    a.PAYTM_TICKET = a.PAYTM_TICKET + COALESCE(t.PAYTM_TICKET, 0),
    a.GIFT_CARD_TICKET = a.GIFT_CARD_TICKET + COALESCE(t.GIFT_CARD_TICKET, 0),
    a.MOBIKWIK_TICKET = a.MOBIKWIK_TICKET + COALESCE(t.MOBIKWIK_TICKET, 0),
    a.ONLINE_PAYMENT_TICKET = a.ONLINE_PAYMENT_TICKET + COALESCE(t.ONLINE_PAYMENT_TICKET, 0),
    a.OTHER_PAYMENT_TICKET = a.OTHER_PAYMENT_TICKET + COALESCE(t.OTHER_PAYMENT_TICKET, 0),
    a.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET + COALESCE(t.SPLIT_PAYMENT_TICKET, 0),
    a.LAST_PAYMENT_MODE = t.LAST_PAYMENT_MODE,
    a.ONE_NPS_TICKET = COALESCE(t.ONE_NPS_TICKET, 0),
    a.TWO_NPS_TICKET = COALESCE(t.TWO_NPS_TICKET, 0),
    a.THREE_NPS_TICKET = COALESCE(t.THREE_NPS_TICKET, 0),
    a.FOUR_NPS_TICKET = COALESCE(t.FOUR_NPS_TICKET, 0),
    a.FIVE_NPS_TICKET = COALESCE(t.FIVE_NPS_TICKET, 0),
    a.SIX_NPS_TICKET = COALESCE(t.SIX_NPS_TICKET, 0),
    a.SEVEN_NPS_TICKET = COALESCE(t.SEVEN_NPS_TICKET, 0),
    a.EIGHT_NPS_TICKET = COALESCE(t.EIGHT_NPS_TICKET, 0),
    a.NINE_NPS_TICKET = COALESCE(t.NINE_NPS_TICKET, 0),
    a.TEN_NPS_TICKET = COALESCE(t.TEN_NPS_TICKET, 0),
    a.LAST_NPS_SCORE = t.LAST_NPS_SCORE,
    a.NEGATIVE_NPS_TICKET = COALESCE(t.NEGATIVE_NPS_TICKET, 0),
    a.POSITIVE_NPS_TICKET = COALESCE(t.POSITIVE_NPS_TICKET, 0),
    a.NEUTRAL_NPS_TICKET = COALESCE(t.NEUTRAL_NPS_TICKET, 0),
    a.ONE_FEEDBACK_TICKET = COALESCE(t.ONE_FEEDBACK_TICKET, 0),
    a.TWO_FEEDBACK_TICKET = COALESCE(t.TWO_FEEDBACK_TICKET, 0),
    a.THREE_FEEDBACK_TICKET = COALESCE(t.THREE_FEEDBACK_TICKET, 0),
    a.FOUR_FEEDBACK_TICKET = COALESCE(t.FOUR_FEEDBACK_TICKET, 0),
    a.FIVE_FEEDBACK_TICKET = COALESCE(t.FIVE_FEEDBACK_TICKET, 0),
    a.LAST_FEEDBACK_SCORE = t.LAST_FEEDBACK_SCORE,
    a.TOTAL_SPEND = a.TOTAL_SPEND + COALESCE(t.TOTAL_SPEND, 0),
    a.TOTAL_DISCOUNT = a.TOTAL_DISCOUNT + COALESCE(t.TOTAL_DISCOUNT, 0),
    a.TOTAL_APC = COALESCE(t.TOTAL_APC, 0),
    a.MINIMUM_APC = COALESCE(t.MINIMUM_APC, 0),
    a.MAXIMUM_APC = COALESCE(t.MAXIMUM_APC, 0),
    a.DELIVERY_SPEND = a.DELIVERY_SPEND + COALESCE(t.DELIVERY_SPEND, 0),
    a.DELIVERY_DISCOUNT = a.DELIVERY_DISCOUNT + COALESCE(t.DELIVERY_DISCOUNT, 0),
    a.DELIVERY_APC = COALESCE(t.DELIVERY_APC, 0),
    a.DELIVERY_MINIMUM_APC = COALESCE(t.DELIVERY_MINIMUM_APC, 0),
    a.DELIVERY_MAXIMUM_APC = COALESCE(t.DELIVERY_MAXIMUM_APC, 0),
    a.DINE_IN_SPEND = a.DINE_IN_SPEND + COALESCE(t.DINE_IN_SPEND, 0),
    a.DINE_IN_DISCOUNT = a.DINE_IN_DISCOUNT + COALESCE(t.DINE_IN_DISCOUNT, 0),
    a.DINE_IN_APC = COALESCE(t.DINE_IN_APC, 0),
    a.DINE_IN_MINIMUM_APC = COALESCE(t.DINE_IN_MINIMUM_APC, 0),
    a.DINE_IN_MAXIMUM_APC = COALESCE(t.DINE_IN_MAXIMUM_APC, 0),
    a.CASH_SPEND = a.CASH_SPEND + COALESCE(t.CASH_SPEND, 0),
    a.CARD_SPEND = a.CARD_SPEND + COALESCE(t.CARD_SPEND, 0),
    a.AMEX_SPEND = a.AMEX_SPEND + COALESCE(t.AMEX_SPEND, 0),
    a.PAYTM_SPEND = a.PAYTM_SPEND + COALESCE(t.PAYTM_SPEND, 0),
    a.GIFT_CARD_SPEND = a.GIFT_CARD_SPEND + COALESCE(t.GIFT_CARD_SPEND, 0),
    a.MOBIKWIK_SPEND = a.MOBIKWIK_SPEND + COALESCE(t.MOBIKWIK_SPEND, 0),
    a.ONLINE_SPEND = a.ONLINE_SPEND + COALESCE(t.ONLINE_SPEND, 0),
    a.OTHER_PAYMENT_SPEND = a.OTHER_PAYMENT_SPEND + COALESCE(t.OTHER_PAYMENT_SPEND, 0),
    a.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD + COALESCE(t.TICKET_WITH_GIFT_CARD, 0),
    a.LAST_UNIT_NAME = t.LAST_UNIT_NAME,
    a.ZERO_AMOUNT_TICKET_COUNT = a.ZERO_AMOUNT_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT = a.ZERO_AMOUNT_DINE_IN_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0),
    a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT = a.ZERO_AMOUNT_DELIVERY_TICKET_COUNT + COALESCE(t.ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0),
    a.ONLY_GIFT_CARD_TICKET = a.ONLY_GIFT_CARD_TICKET + COALESCE(t.ONLY_GIFT_CARD_TICKET, 0)
WHERE
    a.CUSTOMER_ID = t.CUSTOMER_ID
        AND t.CUSTOMER_ID = m.CUSTOMER_ID
        AND m.IS_NEW = 'N';

delete from CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS WHERE LAST_ORDER_DATE < DATE_ADD(CURR_DATE, INTERVAL -7 DAY);

END$$
DELIMITER ;


